# Mira AI 医生助手项目需求分析与实现规划报告

## 1. 需求分析总结

### 1.1 项目背景与目标
- **产品定位**：轻量级 AI 医生效能工具，面向尚未使用 ABC 诊所管家的大型医院
- **核心价值**：通过"语音对话转录病历"和"病历内容一键导入 HIS"功能，显著降低医生病历文书撰写成本
- **战略意图**：作为引流产品，最终引导用户使用 ABC 诊所管家；同时探索海外中医市场

### 1.2 核心功能需求

#### 1.2.1 用户登录流程
- **登录方式**：微信扫码、验证码登录（不提供账密登录）
- **用户注册**：首次使用自动完成新用户注册
- **账户体系**：独立于 ABC 诊所管家，但复用账户表结构

#### 1.2.2 语音录音功能
- **录音形态**：最小化录音器界面，包含：
  - 波形图显示
  - 录音时间显示
  - 暂停/停止录音控制
  - 实时语音转文字
- **界面特性**：录音时自动收缩，不遮挡 HIS 系统

#### 1.2.3 语音识别与处理
- **实时转录**：录音过程中实时显示语音转文字结果
- **对话记录**：保存完整的医患对话文字记录
- **音频回放**：支持历史对话音频播放，播放时高亮对应文字

#### 1.2.4 AI 生成病历功能
- **智能解析**：AI 根据对话内容自动整理病历字段：
  - 患者信息（可自动补充和手动修改）
  - 主诉
  - 病史
  - 体格检查
  - 诊断
  - 其他信息
- **问诊标题**：AI 自动生成问诊标题和概要
- **病历导出**：支持手动复制和自动复制（RPA）到 HIS 系统

#### 1.2.5 历史记录管理
- **问诊列表**：展示历史问诊记录，包含 AI 生成的标题和概要
- **筛选功能**：支持按录制时间、症状信息、录音时长筛选
- **详情查看**：支持在"病历"和"对话"页面间切换

#### 1.2.6 设置界面需求
- **账户管理**：账户切换、注销登录
- **设备配置**：麦克风和扬声器选择，实时音量状态显示
- **扩展功能**：购买专业麦克风、连接微信（后续功能）

## 2. 技术架构契合度分析

### 2.1 现有架构优势
✅ **Electron 桌面应用框架**：完美契合 Windows 客户端需求  
✅ **插件化架构**：MiraApp 可作为独立业务模块集成  
✅ **窗口管理机制**：支持最小化录音器界面需求  
✅ **Vue 技术栈**：前端开发技术栈成熟  

### 2.2 需要扩展的能力
🔧 **语音处理能力**：需要集成语音录制、ASR（自动语音识别）功能  
🔧 **AI 接口集成**：需要对接 AI 服务进行病历生成  
🔧 **RPA 功能**：需要开发 HIS 系统自动化操作能力  
🔧 **本地存储**：需要设计问诊记录的本地存储方案  

## 3. 设计稿分析（基于 Figma 组件信息）

### 3.1 UI 组件兼容性
✅ **@abc/ui-pc 组件库**：设计稿中大量使用了 ABC 组件库的组件：
- `AbcFlex`：布局组件
- `AbcText`：文本组件  
- `AbcButton`：按钮组件
- `AbcIcon`：图标组件
- `AbcSpace`：间距组件
- `AbcSelect`：下拉选择组件
- `AbcInput`：输入框组件
- `AbcList`：列表组件
- `AbcTabs`：标签页组件

### 3.2 关键界面状态
从设计稿分析可以看出包含以下关键状态：
- **折叠态**：最小化录音器界面
- **录音态**：显示波形、时间、控制按钮
- **暂停态**：录音暂停状态
- **设置弹窗**：麦克风调试、设备选择界面

## 4. 详细实现规划

### 4.1 开发优先级与里程碑

#### 阶段一：基础框架搭建（1-2周）
1. **MiraApp 类扩展**
   - 扩展窗口管理功能
   - 实现多窗口状态管理
   - 集成现有的 abc-vox-web 前端页面

2. **登录系统实现**
   - 复用 ABC 账户体系
   - 实现微信扫码和验证码登录
   - 建立独立的用户会话管理

#### 阶段二：核心录音功能（2-3周）
1. **语音录制模块**
   - 集成 Web Audio API 或 Node.js 音频库
   - 实现录音控制（开始/暂停/停止）
   - 开发波形可视化组件

2. **录音界面开发**
   - 实现最小化录音器界面
   - 开发实时转录显示
   - 确保界面不遮挡其他应用

#### 阶段三：AI 功能集成（2-3周）
1. **ASR 语音识别**
   - 对接语音识别服务 API
   - 实现实时语音转文字
   - 优化识别准确率

2. **AI 病历生成**
   - 对接 AI 服务接口
   - 实现病历字段智能解析
   - 开发问诊标题自动生成

#### 阶段四：数据管理与界面完善（2周）
1. **历史记录管理**
   - 设计本地数据存储方案
   - 实现问诊记录 CRUD 操作
   - 开发筛选和搜索功能

2. **界面完善**
   - 完成问诊列表页面
   - 实现问诊详情页面
   - 开发设置页面

#### 阶段五：RPA 功能与优化（3-4周）
1. **RPA 自动化**
   - 开发 HIS 系统识别能力
   - 实现自动复制粘贴功能
   - 提供操作进度反馈

2. **系统优化**
   - 性能优化和内存管理
   - 错误处理和异常恢复
   - 用户体验优化

### 4.2 前端页面结构建议

基于现有的 `src/pages` 结构，建议扩展以下页面：

```
src/pages/
├── login/                    # 登录页面（已存在）
│   ├── index.vue
│   └── index.js
├── voice-record/            # 录音页面（已存在，需扩展）
│   ├── index.vue
│   └── index.js
├── consultation-list/       # 问诊列表页面（新增）
│   ├── index.vue
│   └── index.js
├── consultation-detail/     # 问诊详情页面（新增）
│   ├── index.vue
│   └── index.js
├── settings/               # 设置页面（新增）
│   ├── index.vue
│   └── index.js
└── recording-widget/       # 录音小组件（新增）
    ├── index.vue
    └── index.js
```

## 5. 风险评估与建议

### 5.1 技术风险
- **语音识别准确率**：需要选择合适的 ASR 服务提供商
- **RPA 兼容性**：不同 HIS 系统的适配工作量较大
- **性能优化**：音频处理和 AI 调用的性能优化

### 5.2 开发建议
1. **MVP 优先**：先实现核心录音和病历生成功能
2. **模块化设计**：各服务模块独立，便于测试和维护
3. **渐进式开发**：先支持主流 HIS 系统，再逐步扩展
4. **用户反馈**：及早收集种子用户反馈，快速迭代

## 6. 总结

基于当前的 Electron + abc-vox-desktop-addon + abc-vox-web 架构，项目具备良好的技术基础来实现 Mira AI 医生助手的需求。关键在于：

1. **合理扩展 MiraApp 类**，增加语音、AI、RPA 等服务模块
2. **充分利用现有的 @abc/ui-pc 组件库**，快速构建符合设计稿的界面
3. **采用分阶段开发策略**，优先实现核心功能，再逐步完善
4. **重视用户体验**，特别是录音界面的流畅性和 HIS 系统的兼容性

这个架构设计为后续的精准开发实现提供了清晰的路线图和技术方案。
