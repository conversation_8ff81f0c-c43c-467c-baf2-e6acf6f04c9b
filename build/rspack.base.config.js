'use strict';
const path = require('path');
const { VueLoaderPlugin } = require('vue-loader');
const rspack = require('@rspack/core');
const { resolve, DEFINE_CONFIG, createHtmlWebpackPlugin } = require('./helper')
const AbcIconfontSvgInjectPlugin = require('./iconfont-svg-plugin')
const { autoPagesGenerator } = require('./auto-pages')

// 自动生成页面配置
const autoEntries = autoPagesGenerator.generateEntries();
const autoHtmlPlugins = autoPagesGenerator.generateHtmlPlugins();

module.exports = {
    context: path.resolve(__dirname, '../'),
    entry: autoEntries,
    output: {
        path: resolve('dist'),
        filename: 'js/[name].[chunkhash:8].js',
        uniqueName: 'vox',
    },
    resolve: {
        extensions: ['.js', '.vue', '.json', '.scss'],
        alias: {
            'vue$': 'vue/dist/vue.esm.js',
            '@': resolve('src'),
            'src': path.resolve(__dirname, '../src'),
        },
    },
    module: {
        rules: [
            {
                test: /\.vue$/,
                loader: 'vue-loader',
                options: {
                    experimentalInlineMatchResource: true,
                },
            },
            {
                test: /\.js$/,
                use: [
                    {
                        loader: 'babel-loader',
                        options: {
                            rootMode: 'upward',
                            cacheDirectory: true,
                        },
                    },
                ],
                exclude: [
                    /node_modules/,
                ],
            },
            {
                test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
                type: 'asset',
                generator: {
                    filename: 'img/[name].[hash:7].[ext]'
                },
                parser: {
                    dataUrlCondition: {
                        maxSize: 10000
                    }
                }
            },
            {
                test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
                type: 'asset',
                generator: {
                    filename: 'media/[name].[hash:7].[ext]'
                },
                parser: {
                    dataUrlCondition: {
                        maxSize: 10000
                    }
                }
            },
            {
                test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
                type: 'asset',
                generator: {
                    filename: 'fonts/[name].[hash:7].[ext]'
                },
                parser: {
                    dataUrlCondition: {
                        maxSize: 10000
                    }
                }
            }
        ],
    },
    plugins: [
        new rspack.ProgressPlugin(),
        new rspack.NoEmitOnErrorsPlugin(),
        new rspack.WarnCaseSensitiveModulesPlugin(),
        new VueLoaderPlugin(),

        new rspack.DefinePlugin({
            'process.env': DEFINE_CONFIG,
        }),

        new AbcIconfontSvgInjectPlugin(),

        // 自动生成的HTML插件
        ...autoHtmlPlugins
    ],
}
