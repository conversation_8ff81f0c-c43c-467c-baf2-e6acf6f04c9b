const path = require('path');
const { OSS, AbcWebpackOSSPlugin } = require('abc-fed-build-tool');
const HtmlWebpackPlugin = require('html-webpack-plugin');

const BUILD_ENV = process.env.BUILD_ENV;
const DEFINE_CONFIG = {
    BUILD_ENV: JSON.stringify(process.env.BUILD_ENV),

    buildInfo: JSON.stringify({
        BUILD_TIME: new Date().toLocaleString(),
        BUILD_ENV: process.env.BUILD_ENV,
        BUILD_TAG: process.env.BUILD_TAG
    } )
}


function resolve(dir) {
    return path.join(__dirname, '..', dir);
}

function getPublicPath(prefix = '') {
    const { url } = OSS.getOSSInfo(BUILD_ENV, prefix);
    return url;
}

function isLocal() {
    return !BUILD_ENV
}

function createHtmlWebpackPlugin({ filename, template, chunks, inject = true, config }) {
    return new HtmlWebpackPlugin({
        filename,
        template,
        inject,
        cache: true,
        chunks,
        scriptLoading: 'defer',
        minify: {
            removeComments: true,
            collapseWhitespace: true,
            removeRedundantAttributes: true,
            useShortDoctype: true,
            removeEmptyAttributes: true,
            removeStyleLinkTypeAttributes: true,
            keepClosingSlash: true,
            minifyJS: true,
            minifyCSS: true,
            minifyURLs: true
        },
        ...config && { config }
    });
}

function getOSSUploadInfo() {
    const { bucket, region, secretId, secretKey } = OSS.getOSSInfo(BUILD_ENV);

    return  {
        Bucket: bucket,
        Region: region,
        SecretId: secretId,
        SecretKey: secretKey,
    }
}


module.exports = {
    resolve,
    getPublicPath,
    isLocal,
    DEFINE_CONFIG,
    createHtmlWebpackPlugin,
    AbcWebpackOSSPlugin,
    getOSSUploadInfo
}
