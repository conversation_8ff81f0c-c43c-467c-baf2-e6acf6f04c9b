const HtmlWebpackPlugin = require("html-webpack-plugin");
module.exports =  class AbcIconfontSvgInjectPlugin {
    apply(compiler) {
        compiler.hooks.compilation.tap('AbcIconfontSvgInjectPlugin', (compilation) => {
            const HtmlWebpackPlugin = require('html-webpack-plugin');
            const hooks = HtmlWebpackPlugin.getHooks(compilation);
            hooks.alterAssetTags.tapAsync('AbcIconfontSvgInjectPlugin', (htmlPluginData, callback) => {
                htmlPluginData.assetTags.scripts.unshift({
                    tagName: 'script',
                    closeTag: true,
                    attributes: {
                        type: "text/javascript",
                        src: "https://static-common-cdn.abcyun.cn/iconfont/pc/font_4448419_98nx2mczw3t/iconfont.js",
                        defer: true
                    },
                });

                if (callback) {
                    callback(null, htmlPluginData);
                } else {
                    return Promise.resolve(htmlPluginData);
                }
            });
        })
    }
}