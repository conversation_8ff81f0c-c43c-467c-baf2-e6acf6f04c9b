const path = require("path");
const { merge } = require('webpack-merge');
const baseConfig = require('./rspack.base.config')

// 创建一个新的配置，避免重复的module.rules
const devConfig = {
    mode: 'development',
    output: {
        publicPath: '/'
    },
    watch: true,
    devtool: 'cheap-module-source-map',
    
    watchOptions: { // 最小化监控范围
        ignored: [
            '**/node_modules',
        ],
        poll: false,
        aggregateTimeout: 1500,
    },

    performance: false,

    // 优化选项关闭
    optimization: {
        removeAvailableModules: false,
        removeEmptyChunks: false,
        splitChunks: false, // 分包
        minimize: false, // 压缩代码
        concatenateModules: false, // 模块合并
        usedExports: false, // Tree-shaking
        runtimeChunk: 'single',
    },

    experiments: {
        css: false,
        lazyCompilation: false,
        futureDefaults: true,
        incremental: 'safe',
        parallelCodeSplitting: false,
        parallelLoader: true,
        cache: {
            type: 'persistent',
            storage: {
                type: 'filesystem',
                directory: 'node_modules/.cache/rspack',
            },
        }
    },

    devServer: {
        client: {
            overlay: false,
        },
        hot: true,
        compress: false,
        port: 3700,
        open: ['/login.html'],
    },
}

// 合并配置，但是对module.rules使用替换策略
const mergedConfig = merge(baseConfig, devConfig);

// 添加开发环境特有的CSS和SCSS规则到现有的rules中
mergedConfig.module.rules.push(
    {
        test: /\.css$/,
        use: [
            { loader: 'vue-style-loader' },
            { loader: 'css-loader', options: { sourceMap: false } },
            {
                loader: 'builtin:lightningcss-loader',
                options: {
                    targets: 'ie 10',
                },
            },
        ]
    },
    {
        test: /\.scss$/,
        use: [
            { loader: 'vue-style-loader' },
            { loader: 'css-loader', options: { sourceMap: false } },
            {
                loader: 'builtin:lightningcss-loader',
                options: {
                    targets: 'ie 10',
                },
            },
            {
                loader: 'sass-loader',
                options: {
                    sourceMap: false
                }
            }
        ]
    }
);

module.exports = mergedConfig;
