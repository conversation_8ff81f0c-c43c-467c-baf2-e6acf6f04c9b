rem @echo off

cd ..


set ELECTRON_CACHE=electron-tmp-cache

git clean -fd
git co .
rem git pull --rebase
rd /q /s dist
rd /q /s node_modules
rd /q /s third-party\app-builder-lib\node_modules
rd /q /s third-party\electron-builder\node_modules
rd /q /s third-party\abc-vox-desktop-addon\node_modules
rd /q /s third-party\abc-vox-desktop-addon\dist
rd /q /s %ELECTRON_CACHE%
mkdir %ELECTRON_CACHE%

echo UPLOAD_OSS=%UPLOAD_OSS%

for /F %%i in ('node -v') do ( set NodeVersion=%%i)
echo node version: %NodeVersion%

echo ELECTRON_MIRROR = %ELECTRON_MIRROR%
echo ELECTRON_BUILDER_CACHE = %ELECTRON_BUILDER_CACHE%


for /F %%i in ('npm config get registry') do ( set NpmRegistry=%%i)
echo npm registry=%NpmRegistry%


for /F %%i in ('node buildscripts\get-package-info.js') do ( set APPVERSION=%%i)

if "%time:~0,2%" lss "10" (set hh=0%time:~1,1%) else (set hh=%time:~0,2%)
echo %hh%:%time:~3,2%
SET TimeStamp=%date:~0,4%%date:~5,2%%date:~8,2%%hh%%time:~3,2%%time:~6,2%
echo TimeStamp=%TimeStamp%


for /f "delims=" %%i in ('git rev-parse HEAD') do set COMMIT=%%i
echo const BuildConfig = {> src/build-config.ts
echo 	gitCommit:"%COMMIT%", >> src/build-config.ts
echo 	buildTime:"%TimeStamp%" >> src/build-config.ts
echo };>> src/build-config.ts

echo export {BuildConfig};>> src/build-config.ts

set BUILD_TAG=%COMMIT%

echo current pwd =%cd%
set force_no_cache=true
call npm ci
cd third-party\abc-vox-desktop-addon
call npm ci
cd ..\..
echo third-party\abc-vox-desktop-addon npm ci done
echo current pwd =%cd%


call npm run dist-win-32
echo npm run dist-win-32 done

copy /y dist\abcyun-desktop-win-%APPVERSION%.exe dist\abcyun-desktop-win-%APPVERSION%_%TimeStamp%.exe
copy /y dist\abcyun-desktop-win-%APPVERSION%.exe dist\abcyun-desktop-win-latest.exe
copy /y dist\abcyun-desktop-win-%APPVERSION%.exe dist\abcyun-desktop-win-print-service-latest.exe


if "%UPLOAD_OSS%"=="true" (call npm run uploadCI)
if "%UPLOAD_TOLATEST%"=="true" (call npm run uploadReleaseToLatest)
if "%UPLOAD_TOLATEST_EXT_LIB%"=="true" (call npm run uploadPackDevExtLibs)
if "%UPLOAD_TOLATEST_EXT_DLL%"=="true" (call npm run uploadPackExtDlls)
pause
