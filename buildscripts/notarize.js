const {exec} = require("child_process");
const fs = require('fs');

async function notarizing() {

    const packageJsonStr = fs.readFileSync("package.json").toString();
    const {version} = JSON.parse(packageJsonStr);
    const outDir = `${process.cwd()}/dist`;
    const targetPgkName = `abcyun-desktop-mac-${version}.pkg`
    const cmd = `xcrun notarytool submit ${outDir}/${targetPgkName} --apple-id <EMAIL> --team-id 2AXBKZQ9AH --password obzp-jnhq-nbjf-mcmq`;
    console.log('cmd:', cmd)
    const exec = require('child_process').exec;

    const cmdProcess = exec(cmd, function (error, stdout, stderr) {
        if (error) {
            console.error('exec error: ' + error);
            return;
        }
        console.log('stdout: ' + stdout);
        console.log('stderr: ' + stderr);
    });

    await new Promise((resolve, reject) => {
        cmdProcess.on('exit', (code) => {
            console.log('child process exited with code ' + code);
            resolve();
        });
    });
}

notarizing().then(() => {
    console.log('notarizing done');
}).catch((e) => {
    console.error('notarizing error', e);
});
