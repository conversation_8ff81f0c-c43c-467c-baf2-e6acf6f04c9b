!define INSTALL_DIR_NAME "abcyun-mira"
!define ABC_PRODUCT_NAME "ABCMiraDesktop"
!define TOP_DIR "C:\Bytestream"
!macro preInit
  	; This macro is inserted at the beginning of the NSIS .OnInit callback
 	SetRegView 64
 	WriteRegExpandStr HKLM "${INSTALL_REGISTRY_KEY}" InstallLocation "${TOP_DIR}\${INSTALL_DIR_NAME}"
 	WriteRegExpandStr HKCU "${INSTALL_REGISTRY_KEY}" InstallLocation "${TOP_DIR}\${INSTALL_DIR_NAME}"

 	SetRegView 32
 	WriteRegExpandStr HKLM "${INSTALL_REGISTRY_KEY}" InstallLocation "${TOP_DIR}\${INSTALL_DIR_NAME}"
 	WriteRegExpandStr HKCU "${INSTALL_REGISTRY_KEY}" InstallLocation "${TOP_DIR}\${INSTALL_DIR_NAME}"
!macroend
