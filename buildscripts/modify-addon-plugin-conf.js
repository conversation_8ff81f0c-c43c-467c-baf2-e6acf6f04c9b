const fs = require("fs");
async function main() {
    const fs = require('fs');
    const confFile = './resources/abc-plugins/conf.json';
    const confString = fs.readFileSync(confFile).toString();

    const addOnConfFile = fs.readFileSync(`./third-party/abc-vox-desktop-addon/dist/conf.json`).toString();

    const addonConf = JSON.parse(addOnConfFile);

    const config = JSON.parse(confString);
    const entry = config.plugins.find(it => it.name === "abc-vox-desktop-addon");
    entry.buildTime = addonConf.buildTime;

    const confJSONString = JSON.stringify(config, null, 2);
    fs.writeFileSync(confFile, confJSONString, {
        encoding: "utf8"
    });
}


main().then();
