; 此文件从node_modules\app-builder-lib\templates\nsis\installer.nsi复制过来，用于解决覆盖安装时，保存之前的配置
; 并修改了NsisTarget.js里的computeScriptAndSignUninstaller方法中的if (customScriptPath != null) 返回条件
Var newStartMenuLink
Var oldStartMenuLink
Var newDesktopLink
Var oldDesktopLink
Var oldShortcutName
Var oldMenuDirectory
Var ROAMING_FOLDER_ROOT
Var ROAMING_FOLDER_ROOT_PARENT
Var appName
Var oldAppName
Var abcServiceExeName
Var printLinkName
Var printService


!include "common.nsh"
!include "MUI2.nsh"
!include "multiUser.nsh"
!include "allowOnlyOneInstallerInstance.nsh"
!include "WordFunc.nsh"


!macro customFinishPage
    !ifndef HIDE_RUN_AFTER_FINISH
        Function StartApp
            ; 从打印场景安装的，此时需要启动打印服务
            ${if} $printService != "true"
                Exec "$INSTDIR\$abcServiceExeName.exe --abcyun-launch"
            ${endIf}
        FunctionEnd

        !define MUI_FINISHPAGE_RUN
        !define MUI_FINISHPAGE_RUN_FUNCTION "StartApp"
    !endif
    !insertmacro MUI_PAGE_FINISH
!macroend


!macro _IsNonEmptyDirectory _a _b _t _f
!insertmacro _LOGICLIB_TEMP
!insertmacro _IncreaseCounter
Push $0
FindFirst $0 $_LOGICLIB_TEMP "${_b}\*"
_IsNonEmptyDirectory_loop${LOGICLIB_COUNTER}:
    StrCmp "" $_LOGICLIB_TEMP _IsNonEmptyDirectory_done${LOGICLIB_COUNTER}
    StrCmp "." $_LOGICLIB_TEMP +2
    StrCmp ".." $_LOGICLIB_TEMP 0 _IsNonEmptyDirectory_done${LOGICLIB_COUNTER}
    FindNext $0 $_LOGICLIB_TEMP
    Goto _IsNonEmptyDirectory_loop${LOGICLIB_COUNTER}
_IsNonEmptyDirectory_done${LOGICLIB_COUNTER}:
FindClose $0
Pop $0
!insertmacro _!= "" $_LOGICLIB_TEMP `${_t}` `${_f}`
!macroend
!define IsNonEmptyDirectory `"" IsNonEmptyDirectory`




!macro STOP_APP APP_NAME
  ${GetProcessInfo} 0 $0 $1 $2 $3 $4
  ${if} $3 != "${APP_NAME}"
    ${nsProcess::FindProcess} "${APP_NAME}" $R0
    ${if} $R0 == 0
      DetailPrint `正在关闭 "${APP_NAME}"...`

      # https://github.com/electron-userland/electron-builder/issues/2516#issuecomment-372009092
      nsExec::Exec `taskkill /im "${APP_NAME}" /fi "PID ne $0"` $R0
      # to ensure that files are not "in-use"
      Sleep 300

      ${nsProcess::FindProcess} "${APP_NAME}" $R0
      ${if} $R0 == 0
        # wait to give a chance to exit gracefully
        Sleep 1000
        nsExec::Exec `taskkill /f /im "${APP_NAME}" /fi "PID ne $0"` $R0
        ${If} $R0 != 0
          DetailPrint `等待 "${APP_NAME}" 关闭 (taskkill exit code $R0).`
          Sleep 2000
        ${endIf}
      ${endIf}
    ${endIf}
    DetailPrint ``
  ${endIf}
!macroend


!macro STOP_ALL_APP
    ; stop app running before copy
    ;ABC数字医疗云服务
    !insertmacro STOP_APP "ABC数字医疗云服务.exe"

    !insertmacro STOP_APP ${APP_EXECUTABLE_FILENAME}

    ;之前老版本的ABC诊所管家
    !insertmacro STOP_APP "ABC诊所管家.exe"

    !insertmacro STOP_APP "ABC后台服务.exe"

    ;ABC蓝牙通信.exe
    !insertmacro STOP_APP "ABC蓝牙通信.exe"

    ;ABC专网安全.exe
    !insertmacro STOP_APP "ABC专网安全.exe"

    ;ABC截图
    !insertmacro STOP_APP "ABC截图.exe"

    ;ABC远程协助.exe
    !insertmacro STOP_APP "ABC远程协助.exe"

    ; ABC数字医疗云.exe
    !insertmacro STOP_APP "ABC数字医疗云.exe"
    ; abcdesk.exe
    !insertmacro STOP_APP "abcdesk.exe"
!macroend

!ifdef INSTALL_MODE_PER_ALL_USERS
  !ifdef BUILD_UNINSTALLER
    RequestExecutionLevel user
  !else
    RequestExecutionLevel admin
  !endif
!else
  RequestExecutionLevel user
!endif

!ifdef BUILD_UNINSTALLER
  SilentInstall silent
!else
  Var appExe
  Var launchLink
!endif




!ifdef ONE_CLICK
  !include "oneClick.nsh"
!else
  !include "assistedInstaller.nsh"
!endif

!insertmacro addLangs

!ifmacrodef customHeader
  !insertmacro customHeader
!endif

Function .onInit
  !ifmacrodef preInit
    !insertmacro preInit
  !endif

  !ifdef DISPLAY_LANG_SELECTOR
    !insertmacro MUI_LANGDLL_DISPLAY
  !endif

  !ifdef BUILD_UNINSTALLER
    WriteUninstaller "${UNINSTALLER_OUT_FILE}"
    !insertmacro quitSuccess
  !else
    !insertmacro check64BitAndSetRegView

    !ifdef ONE_CLICK
      !insertmacro ALLOW_ONLY_ONE_INSTALLER_INSTANCE
    !else
      ${IfNot} ${UAC_IsInnerInstance}
        !insertmacro ALLOW_ONLY_ONE_INSTALLER_INSTANCE
      ${EndIf}
    !endif

    !insertmacro initMultiUser

    !ifmacrodef customInit
      !insertmacro customInit
    !endif

    !ifmacrodef addLicenseFiles
      InitPluginsDir
      !insertmacro addLicenseFiles
    !endif
  !endif
FunctionEnd

!ifndef BUILD_UNINSTALLER
  !include "installUtil.nsh"
!endif

!ifndef BUILD_UNINSTALLER
 ; GetParent
 ; input, top of stack  (e.g. C:\Program Files\Poop)
 ; output, top of stack (replaces, with e.g. C:\Program Files)
 ; modifies no other variables.
 ;
 ; Usage:
 ;   Push "C:\Program Files\Directory\Whatever"
 ;   Call GetParent
 ;   Pop $R0
 ;   ; at this point $R0 will equal "C:\Program Files\Directory"
Function GetParent
  Exch $R0
  Push $R1
  Push $R2
  Push $R3

  StrCpy $R1 0
  StrLen $R2 $R0

  loop:
    IntOp $R1 $R1 + 1
    IntCmp $R1 $R2 get 0 get
    StrCpy $R3 $R0 1 -$R1
    StrCmp $R3 "\" get
  Goto loop

  get:
    StrCpy $R0 $R0 -$R1

    Pop $R3
    Pop $R2
    Pop $R1
    Exch $R0

FunctionEnd
!endif

Section "install"
   SetShellVarContext current
   StrCpy $ROAMING_FOLDER_ROOT "$APPDATA\${ABC_PRODUCT_NAME}"
   StrCpy $ROAMING_FOLDER_ROOT_PARENT "$APPDATA"

   StrCpy $appName "ABC数字医疗云"

   StrCpy $abcServiceExeName "ABC数字医疗云服务"
   StrCpy $oldAppName "ABC诊所管家"
   StrCpy $printLinkName "ABC数字医疗云打印服务"

   ; 判断文件名里否包含包含print-service,包含表示是从打印场景启动的
   ${StrContains} $printService "print-service" $EXEFILE
   ${if} $printService != ""
       StrCpy $printService "true"
   ${else}
       StrCpy $printService "false"
   ${endIf}

   SetShellVarContext all

  !ifndef BUILD_UNINSTALLER
    !insertmacro STOP_ALL_APP

    ${WordFind} "$INSTDIR" "\" "-1" $R0
    StrCmp $R0 "${INSTALL_DIR_NAME}" 0 +3
      Push $INSTDIR
      Call GetParent
      Pop $INSTDIR
  !endif

  !ifndef BUILD_UNINSTALLER
    StrCpy "$INSTDIR" "$INSTDIR\${INSTALL_DIR_NAME}"
  !endif


  !ifndef BUILD_UNINSTALLER
    ; 写入一个空文件installing,表示当前正在安装
    FileOpen $0 "$ROAMING_FOLDER_ROOT\installing" w
    FileClose $0
    DetailPrint ""
    !include "installSection.nsh"
    CopyFiles /SILENT  "$INSTDIR\resources\conf\abc-conf.ini" "$ROAMING_FOLDER_ROOT\abc-conf_new.ini"
    CopyFiles /SILENT  "$INSTDIR\resources\conf\abc-conf.ini" "$ROAMING_FOLDER_ROOT\abc-conf-template.ini"

    RMDIR /r "$ROAMING_FOLDER_ROOT\GPUCache"
    RMDIR /r "$ROAMING_FOLDER_ROOT\Cache"
    RMDIR /r "$ROAMING_FOLDER_ROOT\DawnCache"
    RMDIR /r "$ROAMING_FOLDER_ROOT\Session Storage"
    RMDIR /r "$ROAMING_FOLDER_ROOT\Code Cache"
    RMDIR /r "$ROAMING_FOLDER_ROOT\FontLookupTableCache"
    RMDIR /r "$ROAMING_FOLDER_ROOT\Dictionaries"


    Delete "$desktop\$appName.lnk"
    Delete "$desktop\$oldAppName.lnk"

    ;CreateShortcut "$desktop\$appName.lnk" "$INSTDIR\$appName.exe"
    CreateShortcut "$desktop\$appName.lnk" "$INSTDIR\$abcServiceExeName.exe" "--abcyun-launch"

    ; 从打印场景安装的，此时需要启动打印服务
    ${if} $printService == "true"
        ;创建开机启动项任务, 并指定为打印服务的守护进程
        ExecWait "$INSTDIR\$abcServiceExeName.exe --create-launch-plan --print-service-daemon --app-data-dir=$ROAMING_FOLDER_ROOT_PARENT"

        ;启动打印服务守护进程并显示打印服务窗口
        Exec "$INSTDIR\$abcServiceExeName.exe --print-service-daemon --show-print-daemon-window"
    ${else}
        ;创建开机启动项任务
        ExecWait "$INSTDIR\$abcServiceExeName.exe --create-launch-plan --app-data-dir=$ROAMING_FOLDER_ROOT_PARENT"

        ;拉起ABC服务守护进程
        Exec "$INSTDIR\$abcServiceExeName.exe --app-data-dir=$ROAMING_FOLDER_ROOT_PARENT"
    ${endIf}

    ;检查是否为Windows 7系统，如果是则删除可能导致问题的字体文件，该字体文件导致win 7下abcdesk程序启动失败
    ${If} ${IsWin7}
        Delete "$INSTDIR\ABC远程协助\data\flutter_assets\fonts\MaterialIcons-Regular.otf"
    ${EndIf}

    ;删除之前写入的空文件installing
    Delete "$ROAMING_FOLDER_ROOT\installing"
  !endif
SectionEnd


!macro customUnInstall
    !insertmacro STOP_ALL_APP
    RMDir /r "${TOP_DIR}\${INSTALL_DIR_NAME}"
    ${If} ${IsNonEmptyDirectory} "${TOP_DIR}"
    ${Else}
      RMDir /r "${TOP_DIR}"
    ${EndIf}

    SetShellVarContext current
    StrCpy $ROAMING_FOLDER_ROOT "$APPDATA\${ABC_PRODUCT_NAME}"
    ;如果"$ROAMING_FOLDER_ROOT\installing"文件存在, 说明当前是覆盖安装，需要保留之前的配置
    ${IfNot} ${FileExists} "$ROAMING_FOLDER_ROOT\installing"
        RMDIR /r "$ROAMING_FOLDER_ROOT\front-end-offline-bundle"
    ${EndIf}
!macroend

!ifdef BUILD_UNINSTALLER
  !include "uninstaller.nsh"
!endif
