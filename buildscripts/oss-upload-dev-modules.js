const path = require('path');
const webpack = require('webpack');
const WebpackAliOSSPlugin = require('webpack-oss')

const accessKeyId = "LTAI5t8jWB7k484hkfNg5y9r";
const accessKeySecret = "******************************";
const region = "oss-cn-shanghai";
const bucket = "cis-static-common";


const plugins = [new webpack.NamedModulesPlugin()];
plugins.push(
    new WebpackAliOSSPlugin({
        accessKeyId: accessKeyId,
        accessKeySecret: accessKeySecret,
        region: region,
        bucket: bucket,
        prefix: `apks/abc_pc_upgrade/ext-libs`,
        deleteAll: false,	  // 优先匹配format配置项
        local: true,   // 上传打包输出目录里的文件,
        output: path.resolve(__dirname, '../dist/ext-libs/'),
    }));

module.exports = {
    entry: "./buildscripts/dummy.js",
    mode: 'production',
    bail: true,
    output: {
        library: 'hippyReactBase',
    },
    plugins: plugins
};
