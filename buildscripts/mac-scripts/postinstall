#!/bin/sh

DATA_DIR="/Users/<USER>/Library/Application Support/ABCClinicDesktop"
LOG_FILE=/Users/<USER>/install.txt

echo "haha" > $LOG_FILE

if [ ! -d "$DATA_DIR" ]; then
  mkdir "$DATA_DIR"
fi


echo "haha2" >> $LOG_FILE

cp -f /Applications/ABC数字医疗云.app/Contents/Resources/conf/abc-conf.ini "$DATA_DIR/abc-conf_new.ini" >> $LOG_FILE
cp -f /Applications/ABC数字医疗云.app/Contents/Resources/conf/abc-conf.ini "$DATA_DIR/abc-conf-template.ini" >> $LOG_FILE


chown $USER "$DATA_DIR" >> $LOG_FILE
chown $USER "$DATA_DIR/abc-conf_new.ini" >> $LOG_FILE
chown $USER "$DATA_DIR/abc-conf-template.ini" >> $LOG_FILE

exit 0
