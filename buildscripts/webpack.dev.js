const path = require('path');
const webpack = require('webpack');
const pkg = require('../package.json');
const CopyPlugin = require('copy-webpack-plugin');

module.exports = {
    mode: 'development',
    devtool: 'cheap-module-eval-source-map',
    watch: true,
    watchOptions: {
        aggregateTimeout: 1500,
    },
    target: "node",
    output: {
        path: path.resolve('./dist/'),
    },
    plugins: [
        new webpack.DefinePlugin({
            'process.env': {
                NODE_ENV: JSON.stringify('development'),
                HOST: JSON.stringify(process.env.DEV_HOST || '127.0.0.1'),
                PORT: JSON.stringify(process.env.DEV_PORT || 38989),
            },
            __PLATFORM__: null,
        }),

        // new CopyPlugin([
        //     {from: 'src/assets', to: './assets/'},
        // ]),
    ],
    module: {
        rules: [
            {
                test: /\.tsx?$/,
                // exclude: /node_modules/,
                use: 'ts-loader',
                exclude: /node_modules/,
            }
        ],
    },
    resolve: {
        extensions: ['.js', '.jsx', '.json', '.ts', '.tsx'],
        modules: [path.resolve(__dirname, '../node_modules')],
        alias: {
        },
    },
};
