@echo off
setlocal enabledelayedexpansion

echo ========================================
echo ABC Installer Build Script
echo ========================================

:: Check CMake installation
cmake --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: CMake not found, please install CMake first
    pause
    exit /b 1
)

:: Check Visual Studio installation
where cl >nul 2>&1
if errorlevel 1 (
    echo Setting up Visual Studio environment...

    :: Try to find Visual Studio 2022
    set "VS_FOUND=0"

    if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
        set "VS_FOUND=1"
    )

    if "!VS_FOUND!"=="0" (
        if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
            call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
            set "VS_FOUND=1"
        )
    )

    if "!VS_FOUND!"=="0" (
        if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat" (
            call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat"
            set "VS_FOUND=1"
        )
    )

    if "!VS_FOUND!"=="0" (
        echo ERROR: Visual Studio 2022 not found, please install Visual Studio
        pause
        exit /b 1
    )
)

:: Create build directory
if not exist "build" mkdir build
cd build

:: Configure project
echo Configuring project...
cmake .. -G "Visual Studio 17 2022" -A x64
if errorlevel 1 (
    echo ERROR: CMake configuration failed
    pause
    exit /b 1
)

:: Build project (Release version)
echo Building Release version...
cmake --build . --config Release
if errorlevel 1 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

:: Build project (Debug version)
echo Building Debug version...
cmake --build . --config Debug
if errorlevel 1 (
    echo WARNING: Debug build failed, but Release build succeeded
)

echo.
echo ========================================
echo Build completed!
echo.
echo Release version: build\bin\Release\ABCInstaller.exe
echo Debug version:   build\bin\Debug\ABCInstaller.exe
echo ========================================

:: Copy to parent directory
if exist "bin\Release\ABCInstaller.exe" (
    copy "bin\Release\ABCInstaller.exe" "..\ABCInstaller.exe" >nul
    echo Copied to: ABCInstaller.exe
)

pause
