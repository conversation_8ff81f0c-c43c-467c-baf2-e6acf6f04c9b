#pragma once

#include <windows.h>
#include <string>

enum class Architecture {
    X86,
    X64,
    Unknown
};

class PlatformDetector {
public:
    // 获取系统架构
    static Architecture GetSystemArchitecture();
    
    // 检查是否为WOW64进程
    static bool IsWow64Process();
    
    // 获取操作系统版本
    static std::wstring GetOSVersion();
    
    // 检查是否为64位系统
    static bool Is64BitSystem();
    
    // 获取处理器架构字符串
    static std::wstring GetArchitectureString(Architecture arch);

private:
    // 内部辅助函数
    static bool IsWow64ProcessInternal(HANDLE hProcess);
};
