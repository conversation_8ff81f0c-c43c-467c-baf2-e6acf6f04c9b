#include "ui_helper.h"
#include <cmath>

// 简单的min/max函数
template<typename T>
T min_val(T a, T b) { return (a < b) ? a : b; }

template<typename T>
T max_val(T a, T b) { return (a > b) ? a : b; }

using namespace Gdiplus;

// 静态成员初始化
GdiplusStartupInput UIHelper::s_gdiplusStartupInput;
ULONG_PTR UIHelper::s_gdiplusToken = 0;
bool UIHelper::s_gdiplusInitialized = false;

bool UIHelper::InitializeGDIPlus()
{
    if (!s_gdiplusInitialized) {
        Status status = GdiplusStartup(&s_gdiplusToken, &s_gdiplusStartupInput, nullptr);
        s_gdiplusInitialized = (status == Ok);
    }
    return s_gdiplusInitialized;
}

void UIHelper::ShutdownGDIPlus()
{
    if (s_gdiplusInitialized) {
        GdiplusShutdown(s_gdiplusToken);
        s_gdiplusInitialized = false;
    }
}

HFONT UIHelper::CreateFont(int size, bool bold, const std::wstring& fontName)
{
    HDC hdc = GetDC(nullptr);
    int logPixelsY = GetDeviceCaps(hdc, LOGPIXELSY);
    ReleaseDC(nullptr, hdc);

    return ::CreateFontW(
        -MulDiv(size, logPixelsY, 72),
        0, 0, 0,
        bold ? FW_BOLD : FW_NORMAL,
        FALSE, FALSE, FALSE,
        DEFAULT_CHARSET,
        OUT_TT_PRECIS,
        CLIP_DEFAULT_PRECIS,
        CLEARTYPE_QUALITY,
        DEFAULT_PITCH | FF_DONTCARE,
        fontName.c_str()
    );
}

void UIHelper::DrawGradientBackground(HDC hdc, RECT rect, COLORREF color1, COLORREF color2)
{
    FillGradientRect(hdc, rect, color1, color2, true);
}

void UIHelper::FillGradientRect(HDC hdc, RECT rect, COLORREF color1, COLORREF color2, bool vertical)
{
    int width = rect.right - rect.left;
    int height = rect.bottom - rect.top;
    
    int r1 = GetRValue(color1), g1 = GetGValue(color1), b1 = GetBValue(color1);
    int r2 = GetRValue(color2), g2 = GetGValue(color2), b2 = GetBValue(color2);
    
    if (vertical) {
        for (int y = 0; y < height; y++) {
            float ratio = static_cast<float>(y) / height;
            int r = static_cast<int>(r1 + (r2 - r1) * ratio);
            int g = static_cast<int>(g1 + (g2 - g1) * ratio);
            int b = static_cast<int>(b1 + (b2 - b1) * ratio);
            
            HBRUSH brush = CreateSolidBrush(RGB(r, g, b));
            RECT lineRect = { rect.left, rect.top + y, rect.right, rect.top + y + 1 };
            FillRect(hdc, &lineRect, brush);
            DeleteObject(brush);
        }
    } else {
        for (int x = 0; x < width; x++) {
            float ratio = static_cast<float>(x) / width;
            int r = static_cast<int>(r1 + (r2 - r1) * ratio);
            int g = static_cast<int>(g1 + (g2 - g1) * ratio);
            int b = static_cast<int>(b1 + (b2 - b1) * ratio);
            
            HBRUSH brush = CreateSolidBrush(RGB(r, g, b));
            RECT lineRect = { rect.left + x, rect.top, rect.left + x + 1, rect.bottom };
            FillRect(hdc, &lineRect, brush);
            DeleteObject(brush);
        }
    }
}

void UIHelper::DrawRoundedRect(HDC hdc, RECT rect, int radius, COLORREF color)
{
    HPEN pen = CreatePen(PS_SOLID, 2, color);
    HPEN oldPen = static_cast<HPEN>(SelectObject(hdc, pen));
    
    // 绘制圆角矩形（简化版本）
    MoveToEx(hdc, rect.left + radius, rect.top, nullptr);
    LineTo(hdc, rect.right - radius, rect.top);
    LineTo(hdc, rect.right, rect.top + radius);
    LineTo(hdc, rect.right, rect.bottom - radius);
    LineTo(hdc, rect.right - radius, rect.bottom);
    LineTo(hdc, rect.left + radius, rect.bottom);
    LineTo(hdc, rect.left, rect.bottom - radius);
    LineTo(hdc, rect.left, rect.top + radius);
    LineTo(hdc, rect.left + radius, rect.top);
    
    SelectObject(hdc, oldPen);
    DeleteObject(pen);
}

void UIHelper::DrawText(HDC hdc, const std::wstring& text, RECT rect, HFONT hFont, COLORREF color, UINT format)
{
    HFONT oldFont = static_cast<HFONT>(SelectObject(hdc, hFont));
    COLORREF oldColor = SetTextColor(hdc, color);
    int oldBkMode = SetBkMode(hdc, TRANSPARENT);

    RECT tempRect = rect;
    ::DrawTextW(hdc, text.c_str(), -1, &tempRect, format);

    SetBkMode(hdc, oldBkMode);
    SetTextColor(hdc, oldColor);
    SelectObject(hdc, oldFont);
}

void UIHelper::DrawSpinner(HDC hdc, RECT rect, int frame, COLORREF color)
{
    int centerX = (rect.left + rect.right) / 2;
    int centerY = (rect.top + rect.bottom) / 2;
    int radius = min_val(rect.right - rect.left, rect.bottom - rect.top) / 4;
    
    HPEN pen = CreatePen(PS_SOLID, 3, color);
    HPEN oldPen = static_cast<HPEN>(SelectObject(hdc, pen));
    
    // 绘制旋转的线条
    for (int i = 0; i < 8; i++) {
        float angle = (frame + i * 45) * 3.14159f / 180.0f;
        int alpha = 255 - (i * 30);
        if (alpha < 50) alpha = 50;
        
        COLORREF lineColor = RGB(
            (GetRValue(color) * alpha) / 255,
            (GetGValue(color) * alpha) / 255,
            (GetBValue(color) * alpha) / 255
        );
        
        DeleteObject(pen);
        pen = CreatePen(PS_SOLID, 3, lineColor);
        SelectObject(hdc, pen);
        
        int x1 = centerX + static_cast<int>(radius * 0.5f * cos(angle));
        int y1 = centerY + static_cast<int>(radius * 0.5f * sin(angle));
        int x2 = centerX + static_cast<int>(radius * cos(angle));
        int y2 = centerY + static_cast<int>(radius * sin(angle));
        
        MoveToEx(hdc, x1, y1, nullptr);
        LineTo(hdc, x2, y2);
    }
    
    SelectObject(hdc, oldPen);
    DeleteObject(pen);
}

void UIHelper::DrawPulsingCircle(HDC hdc, RECT rect, int frame, COLORREF color)
{
    int centerX = (rect.left + rect.right) / 2;
    int centerY = (rect.top + rect.bottom) / 2;
    int baseRadius = min_val(rect.right - rect.left, rect.bottom - rect.top) / 6;
    
    // 脉冲效果
    float pulse = 0.5f + 0.5f * sin(frame * 0.1f);
    int radius = static_cast<int>(baseRadius * (0.8f + 0.4f * pulse));
    
    HBRUSH brush = CreateSolidBrush(color);
    HBRUSH oldBrush = static_cast<HBRUSH>(SelectObject(hdc, brush));
    
    Ellipse(hdc, centerX - radius, centerY - radius, centerX + radius, centerY + radius);
    
    SelectObject(hdc, oldBrush);
    DeleteObject(brush);
}

COLORREF UIHelper::BlendColors(COLORREF color1, COLORREF color2, float ratio)
{
    ratio = max_val(0.0f, min_val(1.0f, ratio));
    
    int r = static_cast<int>(GetRValue(color1) * (1 - ratio) + GetRValue(color2) * ratio);
    int g = static_cast<int>(GetGValue(color1) * (1 - ratio) + GetGValue(color2) * ratio);
    int b = static_cast<int>(GetBValue(color1) * (1 - ratio) + GetBValue(color2) * ratio);
    
    return RGB(r, g, b);
}

COLORREF UIHelper::AdjustBrightness(COLORREF color, float factor)
{
    int r = min_val(255, static_cast<int>(GetRValue(color) * factor));
    int g = min_val(255, static_cast<int>(GetGValue(color) * factor));
    int b = min_val(255, static_cast<int>(GetBValue(color) * factor));
    
    return RGB(r, g, b);
}
