#include "platform_detector.h"
#include <iostream>

typedef BOOL (WINAPI *LPFN_ISWOW64PROCESS) (HANDLE, PBOOL);

Architecture PlatformDetector::GetSystemArchitecture()
{
    SYSTEM_INFO si;
    GetNativeSystemInfo(&si);
    
    switch (si.wProcessorArchitecture) {
    case PROCESSOR_ARCHITECTURE_AMD64:
        return Architecture::X64;
    case PROCESSOR_ARCHITECTURE_INTEL:
        return Architecture::X86;
    default:
        return Architecture::Unknown;
    }
}

bool PlatformDetector::IsWow64Process()
{
    return IsWow64ProcessInternal(GetCurrentProcess());
}

bool PlatformDetector::IsWow64ProcessInternal(HANDLE hProcess)
{
    BOOL bIsWow64 = FALSE;
    
    // 获取IsWow64Process函数指针
    LPFN_ISWOW64PROCESS fnIsWow64Process = 
        (LPFN_ISWOW64PROCESS)GetProcAddress(
            GetModuleHandle(TEXT("kernel32")), "IsWow64Process");
    
    if (fnIsWow64Process != nullptr) {
        if (!fnIsWow64Process(hProcess, &bIsWow64)) {
            // 处理错误
            return false;
        }
    }
    
    return bIsWow64 != FALSE;
}

bool PlatformDetector::Is64BitSystem()
{
    Architecture arch = GetSystemArchitecture();
    return arch == Architecture::X64;
}

std::wstring PlatformDetector::GetOSVersion()
{
    OSVERSIONINFOEX osvi;
    ZeroMemory(&osvi, sizeof(OSVERSIONINFOEX));
    osvi.dwOSVersionInfoSize = sizeof(OSVERSIONINFOEX);
    
    // 注意：GetVersionEx在Windows 8.1+已被弃用，这里仅作示例
    // 实际项目中应使用VerifyVersionInfo或其他方法
    if (GetVersionEx((OSVERSIONINFO*)&osvi)) {
        wchar_t version[256];
        swprintf_s(version, L"Windows %d.%d Build %d", 
                  osvi.dwMajorVersion, osvi.dwMinorVersion, osvi.dwBuildNumber);
        return std::wstring(version);
    }
    
    return L"Unknown Windows Version";
}

std::wstring PlatformDetector::GetArchitectureString(Architecture arch)
{
    switch (arch) {
    case Architecture::X86:
        return L"ia32";
    case Architecture::X64:
        return L"x64";
    default:
        return L"unknown";
    }
}
