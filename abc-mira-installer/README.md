# ABC医生助手安装引导器

这是一个用C++编写的Windows安装引导器，用于自动下载和安装ABC医生助手应用程序。

## 功能特性

### 1. 平台检测
- 自动检测Windows系统架构（x86/x64）
- 根据系统架构选择对应的安装包
- 支持Windows 7及以上版本

### 2. 网络下载
- 使用WinHTTP API实现HTTP/HTTPS下载
- 支持下载进度显示
- 支持SOCKS5代理
- 自动重试机制
- 下载完整性验证

### 3. 用户界面
- 现代化的Windows UI设计
- 平滑的进度条动画
- 优雅的加载动画效果
- 渐变背景和圆角边框
- 支持高DPI显示

### 4. 代理支持
- 支持SOCKS5代理协议
- 自动检测特定网关（172.22.33.1）并启用代理
- 智能代理配置：检测到特定网络环境时自动启用
- 代理连接测试功能

## 构建要求

### 系统要求
- Windows 10/11 (推荐)
- Windows 7 SP1 或更高版本 (最低要求)

### 开发环境
- Visual Studio 2022 (Community/Professional/Enterprise)
- CMake 3.16 或更高版本
- Windows SDK 10.0 或更高版本

### 依赖库
- WinHTTP (系统自带)
- GDI+ (系统自带)
- Winsock2 (系统自带)
- Common Controls (系统自带)

## 构建步骤

### 方法1: 使用批处理脚本（推荐）
```batch
# 运行构建脚本
build.bat
```

### 方法2: 手动构建
```batch
# 1. 创建构建目录
mkdir build
cd build

# 2. 配置项目
cmake .. -G "Visual Studio 17 2022" -A x64

# 3. 构建项目
cmake --build . --config Release
```

## 配置选项

### 下载配置
安装器会从以下URL下载安装包：
```
https://releases.abcyun.cn/abc-mira-desktop/abcyun-desktop-win-{arch}-latest.exe
```

其中 `{arch}` 会根据系统架构自动替换为 `ia32` 或 `x64`。

### 代理配置

#### 自动网关检测
安装器会自动检测网络环境：
- 检测网卡中是否包含 `172.22.33.1` 网关
- 如果检测到该网关，自动启用代理：
  - 代理服务器：`127.0.0.1`
  - 代理端口：`1080`
- 如果未检测到该网关，则不使用代理

#### 手动配置
如需修改代理设置，可以编辑 `installer.cpp` 中的 `InitializeConfiguration` 方法：

```cpp
// 修改代理服务器地址和端口
m_config.proxyServer = L"your-proxy-server";
m_config.proxyPort = 1080;
```

#### 测试网关检测
运行测试脚本查看当前网络配置：
```batch
test_gateway.bat
```

## 文件结构

```
abc-mira-installer/
├── main.cpp              # 程序入口点
├── installer.h           # 主安装器类声明
├── installer.cpp         # 主安装器类实现
├── platform_detector.h   # 平台检测类声明
├── platform_detector.cpp # 平台检测类实现
├── ui_helper.h           # UI辅助类声明
├── ui_helper.cpp         # UI辅助类实现
├── socks5_proxy.h        # SOCKS5代理类声明
├── socks5_proxy.cpp      # SOCKS5代理类实现
├── resource.h            # 资源定义
├── installer.rc          # 资源文件
├── CMakeLists.txt        # CMake构建配置
├── build.bat             # Windows构建脚本
└── README.md             # 说明文档
```

## 使用方法

1. **编译安装器**
   ```batch
   build.bat
   ```

2. **运行安装器**
   ```batch
   ABCInstaller.exe
   ```

3. **调试模式运行**
   ```batch
   run_with_debug.bat
   ```

4. **查看调试日志**
   ```batch
   debug_viewer.bat
   ```

## 日志功能

### 日志文件输出
安装器会自动在当前执行目录生成详细的日志文件 `ABCInstaller.log`，记录以下信息：
- 程序启动和关闭时间
- 配置初始化过程
- 网关检测详细结果
- 代理设置状态
- 网络连接过程
- 下载进度和状态
- 窗口创建过程
- 错误信息和异常

### 日志级别
- **DEBUG**: 详细的调试信息
- **INFO**: 一般信息和状态
- **WARN**: 警告信息
- **ERROR**: 错误信息

### 查看日志
1. **使用日志查看脚本**：
   ```batch
   show_log.bat        # 交互式日志查看器
   test_logger.bat     # 测试日志功能
   ```

2. **直接查看日志文件**：
   ```batch
   type ABCInstaller.log                    # 显示全部日志
   findstr "ERROR" ABCInstaller.log         # 只显示错误
   notepad ABCInstaller.log                 # 用记事本打开
   ```

3. **使用DebugView（实时查看）**：
   - 下载Microsoft Sysinternals DebugView
   - 以管理员身份运行DebugView
   - 启用"Capture Win32"和"Capture Global Win32"
   - 运行安装器，实时查看调试输出

### 日志文件示例
```
2024-01-15 14:30:25.123 [INFO ] === ABC Installer 日志开始 ===
2024-01-15 14:30:25.125 [INFO ] 日志文件: C:\path\to\ABCInstaller.log
2024-01-15 14:30:25.127 [INFO ] 构造函数开始
2024-01-15 14:30:25.130 [INFO ] InitializeConfiguration() 开始
2024-01-15 14:30:25.132 [INFO ] 基础配置: URL=https://..., 应用名=ABC医生助手, 版本=latest
2024-01-15 14:30:25.135 [INFO ] 开始检测网关 172.22.33.1
2024-01-15 14:30:25.140 [DEBUG] 发现网关: 192.168.1.1
2024-01-15 14:30:25.142 [INFO ] ✗ 未检测到网关 172.22.33.1，不使用代理
2024-01-15 14:30:25.145 [INFO ] InitializeConfiguration() 完成
```

## 技术实现

### 架构检测
使用 `GetNativeSystemInfo()` API检测系统架构，支持：
- x86 (32位)
- x64 (64位)
- WOW64检测

### 网络下载
- 使用WinHTTP API进行HTTP/HTTPS下载
- 支持分块下载和进度回调
- 实现连接超时和重试机制

### SOCKS5代理
- 实现完整的SOCKS5协议握手
- 支持域名解析和连接建立
- 提供连接测试功能

### UI动画
- 使用GDI+绘制现代化界面
- 实现旋转加载动画
- 支持渐变背景和圆角效果

## 故障排除

### 常见问题

1. **构建失败**
   - 确保安装了Visual Studio 2022
   - 确保安装了CMake
   - 检查Windows SDK版本

2. **下载失败**
   - 检查网络连接
   - 验证下载URL是否正确
   - 检查防火墙设置

3. **代理连接失败**
   - 验证代理服务器地址和端口
   - 确保代理服务器支持SOCKS5协议
   - 检查代理服务器是否需要认证

### 调试模式
编译Debug版本以获取详细的调试信息：
```batch
cmake --build . --config Debug
```

## 许可证

版权所有 (C) 2024 成都字节流科技有限公司

本软件仅供内部使用，未经授权不得分发。
