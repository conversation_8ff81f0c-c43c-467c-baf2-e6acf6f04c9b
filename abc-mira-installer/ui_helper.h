#pragma once

#include <windows.h>
#include <gdiplus.h>
#include <string>

#pragma comment(lib, "gdiplus.lib")

class UIHelper {
public:
    // 初始化GDI+
    static bool InitializeGDIPlus();
    static void ShutdownGDIPlus();
    
    // 字体创建
    static HFONT CreateFont(int size, bool bold = false, const std::wstring& fontName = L"Microsoft YaHei UI");
    
    // 绘制函数
    static void DrawGradientBackground(HDC hdc, RECT rect, COLORREF color1, COLORREF color2);
    static void DrawRoundedRect(HDC hdc, RECT rect, int radius, COLORREF color);
    static void DrawText(HDC hdc, const std::wstring& text, RECT rect, HFONT hFont, COLORREF color, UINT format = DT_CENTER | DT_VCENTER | DT_SINGLELINE);
    
    // 图像处理
    static HBITMAP LoadPNGFromResource(HINSTANCE hInstance, int resourceId);
    static HBITMAP LoadPNGFromFile(const std::wstring& filePath);
    static void DrawBitmap(HDC hdc, HBITMAP hBitmap, int x, int y, int width = 0, int height = 0);
    
    // 动画相关
    static void DrawSpinner(HDC hdc, RECT rect, int frame, COLORREF color = RGB(0, 120, 215));
    static void DrawPulsingCircle(HDC hdc, RECT rect, int frame, COLORREF color = RGB(0, 120, 215));
    
    // 颜色工具
    static COLORREF BlendColors(COLORREF color1, COLORREF color2, float ratio);
    static COLORREF AdjustBrightness(COLORREF color, float factor);
    
    // 窗口效果
    static void SetWindowRoundedCorners(HWND hwnd, int radius);
    static void SetWindowBlur(HWND hwnd, bool enable);

private:
    static Gdiplus::GdiplusStartupInput s_gdiplusStartupInput;
    static ULONG_PTR s_gdiplusToken;
    static bool s_gdiplusInitialized;
    
    // 内部绘制辅助函数
    static void FillGradientRect(HDC hdc, RECT rect, COLORREF color1, COLORREF color2, bool vertical = true);
};
