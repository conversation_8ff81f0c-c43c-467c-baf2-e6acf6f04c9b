#include "resource.h"
#include <windows.h>

/////////////////////////////////////////////////////////////////////////////
// 中文(简体，中国) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_CHS)
LANGUAGE LANG_CHINESE, SUBLANG_CHINESE_SIMPLIFIED

/////////////////////////////////////////////////////////////////////////////
//
// Icon
//

// Icon with lowest ID value placed first to ensure application icon
// remains consistent on all systems.
IDI_MAIN_ICON           ICON                    "icon.ico"

/////////////////////////////////////////////////////////////////////////////
//
// Bitmap
//

IDB_LOGO                BITMAP                  "logo.bmp"

/////////////////////////////////////////////////////////////////////////////
//
// String Table
//

STRINGTABLE
BEGIN
    IDS_APP_TITLE           "ABC医生助手安装向导"
    IDS_DOWNLOADING         "正在下载安装包..."
    IDS_INSTALLING          "正在安装..."
    IDS_COMPLETE            "安装完成"
    IDS_ERROR               "安装过程中发生错误"
    IDS_CANCEL              "取消"
END

/////////////////////////////////////////////////////////////////////////////
//
// Version
//

VS_VERSION_INFO VERSIONINFO
 FILEVERSION 1,0,0,0
 PRODUCTVERSION 1,0,0,0
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x40004L
 FILETYPE 0x1L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "080404B0"
        BEGIN
            VALUE "CompanyName", "成都字节流科技有限公司"
            VALUE "FileDescription", "ABC医生助手安装向导"
            VALUE "FileVersion", "*******"
            VALUE "InternalName", "ABCInstaller"
            VALUE "LegalCopyright", "Copyright (C) 2024 成都字节流科技有限公司"
            VALUE "OriginalFilename", "ABCInstaller.exe"
            VALUE "ProductName", "ABC医生助手"
            VALUE "ProductVersion", "*******"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x804, 1200
    END
END

#endif    // 中文(简体，中国) resources
/////////////////////////////////////////////////////////////////////////////
