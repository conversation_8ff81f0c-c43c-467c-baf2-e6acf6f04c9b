#include <winsock2.h>
#include <ws2tcpip.h>
#include <iphlpapi.h>
#include <windows.h>

#include "installer.h"
#include "platform_detector.h"
#include "ui_helper.h"
#include "socks5_proxy.h"
#include <commctrl.h>
#include <shellapi.h>
#include <shlobj.h>
#include <thread>
#include <fstream>

#pragma comment(lib, "iphlpapi.lib")
#pragma comment(lib, "ws2_32.lib")

#define ID_PROGRESS_BAR 1001
#define ID_STATUS_LABEL 1002
#define ID_CANCEL_BUTTON 1003
#define ID_ANIMATION_WND 1004
#define TIMER_ANIMATION 1005

ABCInstaller::ABCInstaller(HINSTANCE hInstance)
    : m_hInstance(hInstance)
    , m_hMainWnd(nullptr)
    , m_hProgressBar(nullptr)
    , m_hStatusLabel(nullptr)
    , m_hCancelButton(nullptr)
    , m_hAnimationWnd(nullptr)
    , m_animationFrame(0)
    , m_animationTimer(0)
    , m_hSession(nullptr)
    , m_hConnect(nullptr)
    , m_hRequest(nullptr)
    , m_isDownloading(false)
    , m_isCancelled(false)
{

    // 初始化配置
    InitializeConfiguration();

    // 获取临时目录
    m_downloadPath = GetTempPath();

    wchar_t debugMsg[512];
    swprintf_s(debugMsg, L"[ABCInstaller] 临时目录: %s\n", m_downloadPath.c_str());
}

ABCInstaller::~ABCInstaller()
{
    CleanupResources();
}

int ABCInstaller::Run()
{
    if (!InitializeWindow()) {
        ShowError(L"无法初始化安装界面");
        return -1;
    }

    // 检测平台架构
    OutputDebugStringW(L"[ABCInstaller] 检测平台架构...\n");
    Architecture arch = DetectArchitecture();
    if (arch == Architecture::Unknown) {
        ShowError(L"无法检测系统架构");
        return -1;
    }
    OutputDebugStringW(L"[ABCInstaller] 平台架构检测成功\n");

    // 获取安装包文件名
    std::wstring installerFileName = GetInstallerFileName(arch);
    std::wstring downloadUrl = m_config.baseUrl + installerFileName;
    m_installerPath = m_downloadPath + installerFileName;

    wchar_t debugMsg[1024];
    swprintf_s(debugMsg, L"[ABCInstaller] 下载URL: %s\n", downloadUrl.c_str());
    OutputDebugStringW(debugMsg);
    swprintf_s(debugMsg, L"[ABCInstaller] 本地路径: %s\n", m_installerPath.c_str());
    OutputDebugStringW(debugMsg);

    // 显示窗口
    ShowWindow(m_hMainWnd, SW_SHOW);
    UpdateWindow(m_hMainWnd);

    // 开始动画
    StartAnimation();

    // 初始化网络
    if (!InitializeWinHTTP()) {
        OutputDebugStringW(L"[ABCInstaller] 网络初始化失败\n");
        ShowError(L"无法初始化网络连接");
        return -1;
    }
    OutputDebugStringW(L"[ABCInstaller] 网络初始化成功\n");

    // 开始下载
    UpdateStatus(L"正在下载安装包...");
    
    std::thread downloadThread([this, downloadUrl]() {
        bool success = DownloadInstaller(downloadUrl, m_installerPath, 
            [this](int percentage, const std::wstring& status) {
                PostMessage(m_hMainWnd, WM_USER + 1, percentage, 
                    reinterpret_cast<LPARAM>(status.c_str()));
            });

        PostMessage(m_hMainWnd, WM_USER + 2, success ? 1 : 0, 0);
    });
    
    downloadThread.detach();

    // 消息循环
    MSG msg;
    while (GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    return static_cast<int>(msg.wParam);
}

bool ABCInstaller::InitializeWindow()
{

    // 注册窗口类
    WNDCLASSEXW wc = {};
    wc.cbSize = sizeof(WNDCLASSEXW);
    wc.style = CS_HREDRAW | CS_VREDRAW;
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = m_hInstance;
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wc.hbrBackground = CreateSolidBrush(RGB(240, 240, 240));
    wc.lpszClassName = L"ABCInstallerWindow";
    wc.hIcon = LoadIcon(m_hInstance, MAKEINTRESOURCE(101));
    wc.hIconSm = LoadIcon(m_hInstance, MAKEINTRESOURCE(101));

    if (!RegisterClassExW(&wc)) {
        OutputDebugStringW(L"[ABCInstaller] 窗口类注册失败\n");
        return false;
    }

    // 创建主窗口
    int windowWidth = 500;
    int windowHeight = 300;
    int screenWidth = GetSystemMetrics(SM_CXSCREEN);
    int screenHeight = GetSystemMetrics(SM_CYSCREEN);
    int x = (screenWidth - windowWidth) / 2;
    int y = (screenHeight - windowHeight) / 2;

    m_hMainWnd = CreateWindowExW(
        WS_EX_LAYERED | WS_EX_TOPMOST,
        L"ABCInstallerWindow",
        m_config.appName.c_str(),
        WS_POPUP | WS_VISIBLE,
        x, y, windowWidth, windowHeight,
        nullptr, nullptr, m_hInstance, this
    );

    if (!m_hMainWnd) {
        OutputDebugStringW(L"[ABCInstaller] 主窗口创建失败\n");
        return false;
    }

    // 设置窗口透明度和圆角效果
    SetLayeredWindowAttributes(m_hMainWnd, 0, 250, LWA_ALPHA);

    return true;
}

LRESULT CALLBACK ABCInstaller::WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    ABCInstaller* installer = nullptr;
    
    if (uMsg == WM_NCCREATE) {
        CREATESTRUCT* cs = reinterpret_cast<CREATESTRUCT*>(lParam);
        installer = static_cast<ABCInstaller*>(cs->lpCreateParams);
        SetWindowLongPtr(hwnd, GWLP_USERDATA, reinterpret_cast<LONG_PTR>(installer));
    } else {
        installer = reinterpret_cast<ABCInstaller*>(GetWindowLongPtr(hwnd, GWLP_USERDATA));
    }

    if (installer) {
        return installer->HandleMessage(hwnd, uMsg, wParam, lParam);
    }

    return DefWindowProc(hwnd, uMsg, wParam, lParam);
}

LRESULT ABCInstaller::HandleMessage(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    switch (uMsg) {
    case WM_CREATE:
        {
            // 创建UI控件
            HFONT hFont = UIHelper::CreateFont(12);
            
            // 状态标签
            m_hStatusLabel = CreateWindowW(
                L"STATIC", L"正在准备下载...",
                WS_CHILD | WS_VISIBLE | SS_CENTER,
                50, 80, 400, 30,
                hwnd, reinterpret_cast<HMENU>(ID_STATUS_LABEL), m_hInstance, nullptr
            );
            SendMessage(m_hStatusLabel, WM_SETFONT, reinterpret_cast<WPARAM>(hFont), TRUE);

            // 进度条
            m_hProgressBar = CreateWindowW(
                L"msctls_progress32", nullptr,
                WS_CHILD | WS_VISIBLE | PBS_SMOOTH,
                50, 130, 400, 25,
                hwnd, reinterpret_cast<HMENU>(ID_PROGRESS_BAR), m_hInstance, nullptr
            );
            if (m_hProgressBar) {
                SendMessage(m_hProgressBar, PBM_SETRANGE, 0, MAKELPARAM(0, 100));
            } else {
                OutputDebugStringW(L"[ABCInstaller] 进度条创建失败\n");
            }

            // 取消按钮
            m_hCancelButton = CreateWindowW(
                L"BUTTON", L"取消",
                WS_CHILD | WS_VISIBLE | BS_PUSHBUTTON,
                200, 200, 100, 35,
                hwnd, reinterpret_cast<HMENU>(ID_CANCEL_BUTTON), m_hInstance, nullptr
            );
            SendMessage(m_hCancelButton, WM_SETFONT, reinterpret_cast<WPARAM>(hFont), TRUE);

            // 动画窗口
            m_hAnimationWnd = CreateWindowW(
                L"STATIC", nullptr,
                WS_CHILD | WS_VISIBLE | SS_OWNERDRAW,
                200, 30, 100, 40,
                hwnd, reinterpret_cast<HMENU>(ID_ANIMATION_WND), m_hInstance, nullptr
            );
        }
        break;

    case WM_PAINT:
        {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hwnd, &ps);
            
            // 绘制渐变背景
            RECT rect;
            GetClientRect(hwnd, &rect);
            UIHelper::DrawGradientBackground(hdc, rect, RGB(245, 245, 245), RGB(235, 235, 235));
            
            // 绘制圆角边框
            UIHelper::DrawRoundedRect(hdc, rect, 10, RGB(200, 200, 200));
            
            EndPaint(hwnd, &ps);
        }
        break;

    case WM_COMMAND:
        if (LOWORD(wParam) == ID_CANCEL_BUTTON) {
            m_isCancelled = true;
            PostQuitMessage(0);
        }
        break;

    case WM_USER + 1: // 进度更新
        {
            int percentage = static_cast<int>(wParam);
            const wchar_t* status = reinterpret_cast<const wchar_t*>(lParam);
            UpdateProgress(percentage);
            UpdateStatus(status);
        }
        break;

    case WM_USER + 2: // 下载完成
        {
            StopAnimation();
            bool success = (wParam == 1);
            if (success && !m_isCancelled) {
                UpdateStatus(L"下载完成，正在启动安装...");
                if (ExecuteInstaller()) {
                    PostQuitMessage(0);
                } else {
                    ShowError(L"无法启动安装程序");
                }
            } else if (!m_isCancelled) {
                ShowError(L"下载失败，请检查网络连接");
            }
        }
        break;

    case WM_TIMER:
        if (wParam == TIMER_ANIMATION) {
            UpdateAnimation();
        }
        break;

    case WM_CLOSE:
        m_isCancelled = true;
        PostQuitMessage(0);
        break;

    case WM_DESTROY:
        CleanupResources();
        PostQuitMessage(0);
        break;

    default:
        return DefWindowProc(hwnd, uMsg, wParam, lParam);
    }

    return 0;
}

void ABCInstaller::CleanupResources()
{
    if (m_animationTimer) {
        KillTimer(m_hMainWnd, TIMER_ANIMATION);
        m_animationTimer = 0;
    }

    if (m_hRequest) {
        WinHttpCloseHandle(m_hRequest);
        m_hRequest = nullptr;
    }

    if (m_hConnect) {
        WinHttpCloseHandle(m_hConnect);
        m_hConnect = nullptr;
    }

    if (m_hSession) {
        WinHttpCloseHandle(m_hSession);
        m_hSession = nullptr;
    }

    DeleteTempFiles();
}

Architecture ABCInstaller::DetectArchitecture()
{
    return PlatformDetector::GetSystemArchitecture();
}

std::wstring ABCInstaller::GetInstallerFileName(Architecture arch)
{
    
    std::wstring archStr = PlatformDetector::GetArchitectureString(arch);
    return L"abcyun-desktop-win-" + archStr + L"-" + m_config.version + L".exe";
}

bool ABCInstaller::InitializeWinHTTP()
{
    // 创建WinHTTP会话
    m_hSession = WinHttpOpen(
        L"ABC Installer/1.0",
        WINHTTP_ACCESS_TYPE_DEFAULT_PROXY,
        WINHTTP_NO_PROXY_NAME,
        WINHTTP_NO_PROXY_BYPASS,
        0
    );

    if (!m_hSession) {
        OutputDebugStringW(L"[ABCInstaller] WinHTTP会话创建失败\n");
        return false;
    }

    // 设置超时
    DWORD timeout = 30000; // 30秒
    WinHttpSetOption(m_hSession, WINHTTP_OPTION_CONNECT_TIMEOUT, &timeout, sizeof(timeout));
    WinHttpSetOption(m_hSession, WINHTTP_OPTION_SEND_TIMEOUT, &timeout, sizeof(timeout));
    WinHttpSetOption(m_hSession, WINHTTP_OPTION_RECEIVE_TIMEOUT, &timeout, sizeof(timeout));

    // 设置代理（如果配置了）
    if (m_config.useProxy && !m_config.proxyServer.empty()) {
        bool proxyResult = Socks5Proxy::SetupSocks5Proxy(m_hSession, m_config.proxyServer, m_config.proxyPort);
        if (!proxyResult) {
            OutputDebugStringW(L"[ABCInstaller] 代理设置失败\n");
        }
        return proxyResult;
    }

    return true;
}

bool ABCInstaller::DownloadInstaller(const std::wstring& url, const std::wstring& localPath, ProgressCallback callback)
{
    m_isDownloading = true;

    // 解析URL
    URL_COMPONENTS urlComp = {};
    urlComp.dwStructSize = sizeof(urlComp);
    urlComp.dwSchemeLength = -1;
    urlComp.dwHostNameLength = -1;
    urlComp.dwUrlPathLength = -1;
    urlComp.dwExtraInfoLength = -1;

    if (!WinHttpCrackUrl(url.c_str(), static_cast<DWORD>(url.length()), 0, &urlComp)) {
        OutputDebugStringW(L"[ABCInstaller] URL解析失败\n");
        return false;
    }

    std::wstring hostName(urlComp.lpszHostName, urlComp.dwHostNameLength);
    std::wstring urlPath(urlComp.lpszUrlPath, urlComp.dwUrlPathLength);

    // 连接到服务器
    m_hConnect = WinHttpConnect(m_hSession, hostName.c_str(), urlComp.nPort, 0);
    if (!m_hConnect) {
        OutputDebugStringW(L"[ABCInstaller] 服务器连接失败\n");
        return false;
    }

    // 创建请求
    DWORD flags = (urlComp.nScheme == INTERNET_SCHEME_HTTPS) ? WINHTTP_FLAG_SECURE : 0;
    m_hRequest = WinHttpOpenRequest(
        m_hConnect,
        L"GET",
        urlPath.c_str(),
        nullptr,
        WINHTTP_NO_REFERER,
        WINHTTP_DEFAULT_ACCEPT_TYPES,
        flags
    );

    if (!m_hRequest) {
        return false;
    }

    // 发送请求
    if (!WinHttpSendRequest(m_hRequest, WINHTTP_NO_ADDITIONAL_HEADERS, 0, WINHTTP_NO_REQUEST_DATA, 0, 0, 0)) {
        return false;
    }

    // 接收响应
    if (!WinHttpReceiveResponse(m_hRequest, nullptr)) {
        return false;
    }

    // 获取文件大小
    DWORD statusCode = 0;
    DWORD statusCodeSize = sizeof(statusCode);
    WinHttpQueryHeaders(m_hRequest, WINHTTP_QUERY_STATUS_CODE | WINHTTP_QUERY_FLAG_NUMBER,
                       WINHTTP_HEADER_NAME_BY_INDEX, &statusCode, &statusCodeSize, WINHTTP_NO_HEADER_INDEX);

    if (statusCode != 200) {
        return false;
    }

    DWORD contentLength = 0;
    DWORD contentLengthSize = sizeof(contentLength);
    WinHttpQueryHeaders(m_hRequest, WINHTTP_QUERY_CONTENT_LENGTH | WINHTTP_QUERY_FLAG_NUMBER,
                       WINHTTP_HEADER_NAME_BY_INDEX, &contentLength, &contentLengthSize, WINHTTP_NO_HEADER_INDEX);

    // 创建本地文件
    HANDLE hFile = CreateFileW(
        localPath.c_str(),
        GENERIC_WRITE,
        0,
        nullptr,
        CREATE_ALWAYS,
        FILE_ATTRIBUTE_NORMAL,
        nullptr
    );

    if (hFile == INVALID_HANDLE_VALUE) {
        return false;
    }

    // 下载文件
    DWORD totalBytesRead = 0;
    DWORD bytesRead = 0;
    BYTE buffer[8192];
    bool success = true;

    while (success && !m_isCancelled) {
        if (!WinHttpReadData(m_hRequest, buffer, sizeof(buffer), &bytesRead)) {
            success = false;
            break;
        }

        if (bytesRead == 0) {
            break; // 下载完成
        }

        DWORD bytesWritten;
        if (!WriteFile(hFile, buffer, bytesRead, &bytesWritten, nullptr) || bytesWritten != bytesRead) {
            success = false;
            break;
        }

        totalBytesRead += bytesRead;

        // 更新进度
        if (contentLength > 0) {
            int percentage = static_cast<int>((totalBytesRead * 100) / contentLength);
            callback(percentage, L"正在下载安装包...");
        }
    }

    CloseHandle(hFile);

    if (!success || m_isCancelled) {
        DeleteFileW(localPath.c_str());
        return false;
    }

    m_isDownloading = false;
    return true;
}

void ABCInstaller::UpdateProgress(int percentage)
{
    if (m_hProgressBar) {
        SendMessage(m_hProgressBar, PBM_SETPOS, percentage, 0);
    }
}

void ABCInstaller::UpdateStatus(const std::wstring& status)
{
    if (m_hStatusLabel) {
        SetWindowTextW(m_hStatusLabel, status.c_str());
    }
}

void ABCInstaller::ShowError(const std::wstring& message)
{
    MessageBoxW(m_hMainWnd, message.c_str(), L"错误", MB_OK | MB_ICONERROR);
}

void ABCInstaller::StartAnimation()
{
    m_animationFrame = 0;
    m_animationTimer = SetTimer(m_hMainWnd, TIMER_ANIMATION, 50, nullptr); // 20 FPS
}

void ABCInstaller::StopAnimation()
{
    if (m_animationTimer) {
        KillTimer(m_hMainWnd, TIMER_ANIMATION);
        m_animationTimer = 0;
    }
}

void ABCInstaller::UpdateAnimation()
{
    m_animationFrame = (m_animationFrame + 1) % 360;

    if (m_hAnimationWnd) {
        InvalidateRect(m_hAnimationWnd, nullptr, TRUE);
    }
}

bool ABCInstaller::ExecuteInstaller()
{
    if (!FileExists(m_installerPath)) {
        return false;
    }

    // 启动安装程序
    SHELLEXECUTEINFOW sei = {};
    sei.cbSize = sizeof(sei);
    sei.fMask = SEE_MASK_NOCLOSEPROCESS;
    sei.lpVerb = L"open";
    sei.lpFile = m_installerPath.c_str();
    sei.nShow = SW_SHOW;

    if (!ShellExecuteExW(&sei)) {
        return false;
    }

    // 等待安装程序启动
    if (sei.hProcess) {
        WaitForSingleObject(sei.hProcess, 2000); // 等待2秒
        CloseHandle(sei.hProcess);
    }

    return true;
}

std::wstring ABCInstaller::GetTempPath()
{
    wchar_t tempPath[MAX_PATH];
    ::GetTempPathW(MAX_PATH, tempPath);
    return std::wstring(tempPath);
}

bool ABCInstaller::FileExists(const std::wstring& path)
{
    DWORD attributes = GetFileAttributesW(path.c_str());
    return (attributes != INVALID_FILE_ATTRIBUTES && !(attributes & FILE_ATTRIBUTE_DIRECTORY));
}

void ABCInstaller::DeleteTempFiles()
{
    if (!m_installerPath.empty() && FileExists(m_installerPath)) {
        DeleteFileW(m_installerPath.c_str());
    }
}

bool ABCInstaller::DetectSpecificGateway(const std::wstring& targetGateway)
{
    // 初始化Winsock
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        OutputDebugStringW(L"[ABCInstaller] WSAStartup 失败\n");
        return false;
    }

    bool found = false;
    DWORD dwSize = 0;
    DWORD dwRetVal = 0;

    // 获取适配器信息所需的缓冲区大小
    if (GetAdaptersInfo(nullptr, &dwSize) == ERROR_BUFFER_OVERFLOW) {
        PIP_ADAPTER_INFO pAdapterInfo = (IP_ADAPTER_INFO*)malloc(dwSize);
        if (pAdapterInfo != nullptr) {
            // 获取适配器信息
            if ((dwRetVal = GetAdaptersInfo(pAdapterInfo, &dwSize)) == NO_ERROR) {
                PIP_ADAPTER_INFO pAdapter = pAdapterInfo;

                // 遍历所有网络适配器
                while (pAdapter) {
                    // 检查网关地址
                    if (strlen(pAdapter->GatewayList.IpAddress.String) > 0) {
                        // 将多字节字符串转换为宽字符串
                        wchar_t gatewayW[16];
                        MultiByteToWideChar(CP_ACP, 0, pAdapter->GatewayList.IpAddress.String, -1, gatewayW, 16);

                        // 比较网关地址
                        if (wcscmp(gatewayW, targetGateway.c_str()) == 0) {
                            found = true;
                            break;
                        }
                    }
                    pAdapter = pAdapter->Next;
                }
            } else {
                OutputDebugStringW(L"[ABCInstaller] GetAdaptersInfo 失败\n");
            }
            free(pAdapterInfo);
        }
    } else {
        OutputDebugStringW(L"[ABCInstaller] 获取适配器信息缓冲区大小失败\n");
    }

    WSACleanup();
    return found;
}

void ABCInstaller::InitializeConfiguration()
{
    // 直接在代码中设置配置
    m_config.baseUrl = L"https://cis-static-common.oss-cn-shanghai.aliyuncs.com/apks/abc_pc/";
    m_config.appName = L"ABC医生助手";
    m_config.version = L"latest";

    // 检测是否存在特定网关，如果有则启用代理
    if (DetectSpecificGateway(L"172.22.33.1")) {
        // 检测到特定网关，启用代理
        m_config.useProxy = true;
        m_config.proxyServer = L"127.0.0.1";  // 默认本地代理
        m_config.proxyPort = 1080;
    } else {
        // 未检测到特定网关，不使用代理
        m_config.useProxy = false;
        m_config.proxyServer = L"";
        m_config.proxyPort = 1080;
    }
}
