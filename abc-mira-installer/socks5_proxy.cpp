#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif

#include <windows.h>
#include <winhttp.h>
#include "socks5_proxy.h"
#include <iostream>
#include <vector>

#pragma comment(lib, "winhttp.lib")

bool Socks5Proxy::SetupSocks5Proxy(HINTERNET hSession, const std::wstring& server, int port)
{
    if (!hSession || server.empty() || port <= 0) {
        return false;
    }

    // 构建代理字符串
    std::wstring proxyString = server + L":" + std::to_wstring(port);

    // 创建可修改的字符串副本
    std::wstring proxyBypass = L"<local>";

    // 设置代理
    WINHTTP_PROXY_INFO proxyInfo = {};
    proxyInfo.dwAccessType = WINHTTP_ACCESS_TYPE_NAMED_PROXY;
    proxyInfo.lpszProxy = const_cast<LPWSTR>(proxyString.c_str());
    proxyInfo.lpszProxyBypass = const_cast<LPWSTR>(proxyBypass.c_str());

    // 应用代理设置
    BOOL result = WinHttpSetOption(
        hSession,
        WINHTTP_OPTION_PROXY,
        &proxyInfo,
        sizeof(proxyInfo)
    );

    // 注意：WinHTTP 本身不直接支持 SOCKS5
    // 这里简化实现，仅设置基本HTTP代理
    // 实际的 SOCKS5 支持需要更复杂的实现

    return result != FALSE;
}

bool Socks5Proxy::TestConnection(const std::wstring& server, int port)
{
    // 简化实现：仅检查服务器地址和端口是否有效
    if (server.empty() || port <= 0 || port > 65535) {
        return false;
    }

    // 这里可以添加更复杂的连接测试逻辑
    // 暂时返回 true 表示基本验证通过
    return true;
}

bool Socks5Proxy::LoadProxySettings(std::wstring& server, int& port)
{
    // 从注册表读取代理设置
    HKEY hKey;
    if (RegOpenKeyExW(HKEY_CURRENT_USER,
                     L"Software\\ABCMiraInstaller\\Proxy",
                     0, KEY_READ, &hKey) == ERROR_SUCCESS) {

        DWORD dataSize = 256 * sizeof(wchar_t);
        wchar_t serverBuffer[256];
        if (RegQueryValueExW(hKey, L"Server", nullptr, nullptr,
                           (LPBYTE)serverBuffer, &dataSize) == ERROR_SUCCESS) {
            server = serverBuffer;

            DWORD portValue;
            dataSize = sizeof(DWORD);
            if (RegQueryValueExW(hKey, L"Port", nullptr, nullptr,
                               (LPBYTE)&portValue, &dataSize) == ERROR_SUCCESS) {
                port = static_cast<int>(portValue);
                RegCloseKey(hKey);
                return true;
            }
        }
        RegCloseKey(hKey);
    }
    
    return false;
}

bool Socks5Proxy::DetectSystemProxy(std::wstring& server, int& port)
{
    WINHTTP_CURRENT_USER_IE_PROXY_CONFIG proxyConfig = {};
    
    if (WinHttpGetIEProxyConfigForCurrentUser(&proxyConfig)) {
        if (proxyConfig.lpszProxy) {
            std::wstring proxyString = proxyConfig.lpszProxy;
            
            // 查找SOCKS代理设置
            size_t socksPos = proxyString.find(L"socks=");
            if (socksPos != std::wstring::npos) {
                size_t startPos = socksPos + 6; // "socks="的长度
                size_t endPos = proxyString.find(L';', startPos);
                if (endPos == std::wstring::npos) {
                    endPos = proxyString.length();
                }
                
                std::wstring socksProxy = proxyString.substr(startPos, endPos - startPos);
                size_t colonPos = socksProxy.find(L':');
                if (colonPos != std::wstring::npos) {
                    server = socksProxy.substr(0, colonPos);
                    port = _wtoi(socksProxy.substr(colonPos + 1).c_str());
                    
                    // 清理资源
                    if (proxyConfig.lpszProxy) GlobalFree(proxyConfig.lpszProxy);
                    if (proxyConfig.lpszProxyBypass) GlobalFree(proxyConfig.lpszProxyBypass);
                    if (proxyConfig.lpszAutoConfigUrl) GlobalFree(proxyConfig.lpszAutoConfigUrl);
                    
                    return true;
                }
            }
        }
        
        // 清理资源
        if (proxyConfig.lpszProxy) GlobalFree(proxyConfig.lpszProxy);
        if (proxyConfig.lpszProxyBypass) GlobalFree(proxyConfig.lpszProxyBypass);
        if (proxyConfig.lpszAutoConfigUrl) GlobalFree(proxyConfig.lpszAutoConfigUrl);
    }
    
    return false;
}

bool Socks5Proxy::SendSocks5Handshake(int sock)
{
    // 简化实现
    return true;
}

bool Socks5Proxy::SendSocks5ConnectRequest(int sock, const std::string& host, int port)
{
    // 简化实现
    return true;
}

bool Socks5Proxy::ReceiveSocks5Response(int sock)
{
    // 简化实现
    return true;
}
