#pragma once

#include <windows.h>
#include <winhttp.h>
#include <string>

class Socks5Proxy {
public:
    // 设置SOCKS5代理
    static bool SetupSocks5Proxy(HINTERNET hSession, const std::wstring& server, int port);
    
    // 测试代理连接
    static bool TestConnection(const std::wstring& server, int port);
    
    // 从配置文件或注册表读取代理设置
    static bool LoadProxySettings(std::wstring& server, int& port);
    
    // 检测系统代理设置
    static bool DetectSystemProxy(std::wstring& server, int& port);
    
    // 验证代理服务器是否支持SOCKS5
    static bool ValidateSocks5Proxy(const std::wstring& server, int port);

private:
    // SOCKS5协议相关常量
    static const BYTE SOCKS5_VERSION = 0x05;
    static const BYTE SOCKS5_CMD_CONNECT = 0x01;
    static const BYTE SOCKS5_ATYP_DOMAINNAME = 0x03;
    static const BYTE SOCKS5_AUTH_NONE = 0x00;
    
    // 内部辅助函数
    static bool SendSocks5Handshake(int sock);
    static bool SendSocks5ConnectRequest(int sock, const std::string& host, int port);
    static bool ReceiveSocks5Response(int sock);
};
