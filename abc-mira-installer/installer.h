#pragma once

#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif

#include <windows.h>
#include <winhttp.h>
#include <string>
#include <functional>
#include <memory>

// 安装器配置
struct InstallerConfig {
    std::wstring baseUrl = L"https://cis-static-common.oss-cn-shanghai.aliyuncs.com/apks/abc_pc/";
    std::wstring appName = L"ABC医生助手";
    std::wstring version = L"latest";
    std::wstring proxyServer;
    int proxyPort = 0;
    bool useProxy = false;
};

// 下载进度回调
using ProgressCallback = std::function<void(int percentage, const std::wstring& status)>;

// 前向声明
enum class Architecture;
class PlatformDetector;
class UIHelper;
class Socks5Proxy;

// 主安装器类
class ABCInstaller {
public:
    ABCInstaller(HINSTANCE hInstance);
    ~ABCInstaller();

    int Run();

private:
    // 窗口相关
    HINSTANCE m_hInstance;
    HWND m_hMainWnd;
    HWND m_hProgressBar;
    HWND m_hStatusLabel;
    HWND m_hCancelButton;
    
    // 动画相关
    HWND m_hAnimationWnd;
    int m_animationFrame;
    UINT_PTR m_animationTimer;
    
    // 下载相关
    HINTERNET m_hSession;
    HINTERNET m_hConnect;
    HINTERNET m_hRequest;
    
    // 配置和状态
    InstallerConfig m_config;
    std::wstring m_downloadPath;
    std::wstring m_installerPath;
    bool m_isDownloading;
    bool m_isCancelled;
    
    // 窗口过程
    static LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
    LRESULT HandleMessage(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
    
    // 初始化和清理
    bool InitializeWindow();
    void CleanupResources();
    void InitializeConfiguration();
    
    // 平台检测
    Architecture DetectArchitecture();
    std::wstring GetInstallerFileName(Architecture arch);
    
    // 网络功能
    bool InitializeWinHTTP();
    bool SetupProxy();
    bool DownloadInstaller(const std::wstring& url, const std::wstring& localPath, ProgressCallback callback);
    
    // UI更新
    void UpdateProgress(int percentage);
    void UpdateStatus(const std::wstring& status);
    void ShowError(const std::wstring& message);
    
    // 动画
    void StartAnimation();
    void StopAnimation();
    void UpdateAnimation();
    
    // 安装执行
    bool ExecuteInstaller();
    
    // 工具函数
    std::wstring GetTempPath();
    bool FileExists(const std::wstring& path);
    void DeleteTempFiles();

    // 网络检测
    bool DetectSpecificGateway(const std::wstring& targetGateway);
};


