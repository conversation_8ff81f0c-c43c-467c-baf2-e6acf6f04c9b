#include "installer.h"
#include <windows.h>
#include <commctrl.h>

#pragma comment(lib, "comctl32.lib")
#pragma comment(lib, "winhttp.lib")
#pragma comment(lib, "gdi32.lib")
#pragma comment(lib, "user32.lib")

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow)
{
    // 尝试使用简单的初始化方法
    InitCommonControls();

    // 如果需要更多控件，再尝试扩展初始化
    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_PROGRESS_CLASS | ICC_STANDARD_CLASSES | ICC_WIN95_CLASSES;

    BOOL result = InitCommonControlsEx(&icex);
    if (!result) {
        // 如果扩展初始化失败，记录但不退出程序
        OutputDebugStringW(L"[Main] 扩展通用控件初始化失败，使用基本控件\n");
    }

    try {
        // 创建安装器实例
        ABCInstaller installer(hInstance);

        // 运行安装器
        int result = installer.Run();
        return result;
    }
    catch (...) {
        OutputDebugStringW(L"[Main] 安装器创建或运行异常\n");
        return -1;
    }
}
