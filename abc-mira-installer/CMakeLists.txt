cmake_minimum_required(VERSION 3.16)
project(ABCInstaller)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# 添加Windows特定设置
if(WIN32)
    # 设置为Windows应用程序（而不是控制台应用程序）
    set(CMAKE_WIN32_EXECUTABLE TRUE)
    
    # 添加Windows SDK路径
    if(MSVC)
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /utf-8")
        set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} /utf-8")
    endif()
endif()

# 源文件
set(SOURCES
    main.cpp
    installer.cpp
    platform_detector.cpp
    ui_helper.cpp
    socks5_proxy.cpp
)

# 头文件
set(HEADERS
    installer.h
    platform_detector.h
    ui_helper.h
    socks5_proxy.h
    resource.h
)

# 资源文件
set(RESOURCES
    installer.rc
)

# 创建可执行文件
add_executable(ABCInstaller WIN32 ${SOURCES} ${HEADERS} ${RESOURCES})

# 链接库
target_link_libraries(ABCInstaller
    comctl32
    winhttp
    gdi32
    user32
    gdiplus
    ws2_32
    shell32
    advapi32
    iphlpapi
)

# 设置编译器特定选项
if(MSVC)
    # 设置字符集为Unicode
    target_compile_definitions(ABCInstaller PRIVATE UNICODE _UNICODE)
    
    # 设置Windows版本
    target_compile_definitions(ABCInstaller PRIVATE 
        WINVER=0x0601 
        _WIN32_WINNT=0x0601
        _WIN32_IE=0x0800
    )
    
    # 优化设置
    if(CMAKE_BUILD_TYPE STREQUAL "Release")
        target_compile_options(ABCInstaller PRIVATE /O2 /GL)
        target_link_options(ABCInstaller PRIVATE /LTCG)
    endif()
    
    # 设置子系统为Windows
    target_link_options(ABCInstaller PRIVATE /SUBSYSTEM:WINDOWS)
    
    # 设置清单文件
    target_link_options(ABCInstaller PRIVATE 
        /MANIFESTUAC:"level='requireAdministrator' uiAccess='false'"
    )
endif()

# 复制资源文件到输出目录
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/icon.ico")
    configure_file("${CMAKE_CURRENT_SOURCE_DIR}/icon.ico" 
                   "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/icon.ico" COPYONLY)
endif()

if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/logo.bmp")
    configure_file("${CMAKE_CURRENT_SOURCE_DIR}/logo.bmp" 
                   "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/logo.bmp" COPYONLY)
endif()

# 安装规则
install(TARGETS ABCInstaller
    RUNTIME DESTINATION bin
)

# 打包设置
set(CPACK_PACKAGE_NAME "ABCInstaller")
set(CPACK_PACKAGE_VERSION "1.0.0")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "ABC医生助手安装向导")
set(CPACK_PACKAGE_VENDOR "成都字节流科技有限公司")

include(CPack)
