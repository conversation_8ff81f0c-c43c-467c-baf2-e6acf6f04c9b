{"name": "abc-vox-desktop-addon", "version": "0.0.1", "main": "./dist/index.cjs.js", "author": "feihe <<EMAIL>>", "license": "MIT", "scripts": {"dev": "concurrently \"webpack --config ./scripts/webpack.dev.render.js\" \"tsc -w \" \"webpack --config ./scripts/webpack.dev.js\"", "build": "rimraf dist_for_zip && rimraf dist && webpack --config ./scripts/webpack.prod.render.js&&tsc --build tsconfig.json &&  webpack --config ./scripts/webpack.prod.js", "buildAndPack": "npm run build && node scripts/pack.js", "publish": "npm run build && node scripts/publish.js"}, "files": ["dist", "package.json"], "dependencies": {"@types/lodash": "^4.14.191", "core-js": "^3.34.0", "lodash": "^4.17.21", "qs": "^6.11.2"}, "devDependencies": {"@babel/core": "^7.13.10", "@babel/preset-env": "^7.7.6", "@babel/preset-react": "^7.7.4", "@babel/preset-typescript": "^7.8.3", "@types/node": "^22.13.5", "@typescript-eslint/eslint-plugin": "^3.4.0", "@typescript-eslint/parser": "^3.4.0", "@vue/compiler-sfc": "^3.0.4", "abc-fed-build-tool": "^0.6.6", "adm-zip": "^0.5.2", "archiver-zip-encrypted": "2.0.0", "axios": "^0.27.2", "babel-loader": "^8.2.2", "case-sensitive-paths-webpack-plugin": "^2.2.0", "concurrently": "^6.5.1", "copy-webpack-plugin": "^5.1.2", "css-loader": "^5.1.3", "json-loader": "^0.5.7", "style-loader": "^2.0.0", "ts-loader": "^8.4.0", "ts-node": "^10.9.2", "typescript": "~4.9.5", "unicode-loader": "^1.0.7", "vue": "^3.2.26", "vue-loader": "^16.1.2", "vue-router": "^4.0.5", "webpack": "^4.41.3", "webpack-cli": "^3.3.10", "webpack-oss": "^2.1.6"}, "lint-staged": {"*.{ts,js}": ["npx prettier --write", "eslint --fix"]}}