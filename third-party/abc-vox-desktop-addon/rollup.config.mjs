import resolve from "rollup-plugin-node-resolve";
import commonjs from "rollup-plugin-commonjs";
import del from "rollup-plugin-delete";
import replace from "rollup-plugin-replace";
import json from '@rollup/plugin-json'
import {babel} from "@rollup/plugin-babel";
import {terser} from "rollup-plugin-terser";

const isLocal =!process.env.BUILD_ENV;

export default {
    input: 'src/index.ts',
    output: [
        {
            dir: 'dist',
            entryFileNames: 'index.cjs.js',
            format: 'cjs',
        },
    ],
    plugins: [
        del({targets: 'dist/*'}),
        babel({
            include: ['src/**/*'],
            exclude: 'node_modules/**',
            extensions: ['.js', '.ts'],
            babelHelpers: "runtime"
        }),
        json(),
        replace({
            preventAssignment: true,
            'process.env.BUILD_ENV': JSON.stringify(process.env.BUILD_ENV),
            'process.env.BUILD_TAG': JSON.stringify(process.env.BUILD_TAG),
            'process.env.BUILD_TIME': JSON.stringify(new Date().toLocaleString())
        }),
        resolve({
            extensions: ['.js', '.ts'],
            browser: true
        }),
        commonjs(),
        isLocal ? null : terser({
            compress: {
                pure_getters: true,
                unsafe: true,
                unsafe_comps: true,
                warnings: false,
                drop_debugger: true,
                drop_console: ['debug'],
            }
        }),
    ]
}
