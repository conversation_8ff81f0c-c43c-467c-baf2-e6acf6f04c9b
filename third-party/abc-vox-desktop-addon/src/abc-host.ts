import {
    IAbcEnvironment,
    IAddInitOptions,
    IElectron,
    IHostLogger,
    INetworkUtils,
    IPathUtils, ISharedPreferences,
} from "./abc-desktop-addon";
import {IElectronAbcApp} from "./abc-app";
const kUUID = 'uuid_key';


class AbcHost {
    isFirstLaunch: boolean = false;
    version!: string;
    buildConfig!: {
        gitCommit: string,
        buildTime: string
    };

    networkUtils!: INetworkUtils;
    logger!: IHostLogger;

    abcEnvironment!: IAbcEnvironment;
    electron!: IElectron;

    pathUtils!: IPathUtils;

    electronAbcApp!: IElectronAbcApp;
    sharedPreferences!: ISharedPreferences;
    _hostRequire!: (module: string) => any;

    init(options: IAddInitOptions) {
        this.version = options.version;
        this.buildConfig = options.buildConfig;
        this.networkUtils = options.networkUtils;
        this.logger = options.logger;
        this.abcEnvironment = options.abcEnvironment;
        this.electron = options.electron;
        this.pathUtils = options.pathUtils;
        this.electronAbcApp = this.electron.app;
        this.sharedPreferences = options.sharedPreferences;
        this.isFirstLaunch = options.isFirstLaunch;
        this._hostRequire = options.hostRequire

        if (!this.abcEnvironment.uuid) {
            this.abcEnvironment.uuid = () => {
                let uuid = this.sharedPreferences.getObject(kUUID);
                if (!uuid) {
                    uuid = require('uuid').v4();
                    this.sharedPreferences.setObject(kUUID, uuid);
                }

                return uuid;
            }
        }
    }

    hostRequire(module: string): any {
        return this._hostRequire(module);
    }
}


const abcHost = new AbcHost();

const hostRequire = (module: string) => {
    return abcHost.hostRequire(module)
}

export {abcHost, hostRequire};