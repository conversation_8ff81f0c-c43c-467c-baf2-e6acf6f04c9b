import {AbcDesktopAddon, IAddInitOptions} from "./abc-desktop-addon";
import {abcHost, hostRequire} from "./abc-host";
import {offlineBundleManager, OfflineBundleTester} from "./sub-project/front-end-offline-bundle";
import {Constants,  kDefaultEntryUrl} from "./constants";
import {abcUpdater} from "./sub-project/upgrade";
import {entryUrl, getCLIOption, getCLIOptions, hasCLIParams, openDevTools} from "./sub-project/conf/clisettings";
import utils, {isNil} from "./sub-project/common/utils";
import {DownloadManagerBridge} from "./sub-project/jsapi-host";
import {MiraApp} from "./sub-project/mira";
import {Dialog} from "./sub-project/component/dialog";
import {
    abcTraceReport,
    AddonPathUtils,
    Completer,
    delay,
    FileUtils,
    ignore,
    logger,
    timeoutAction
} from "./sub-project/common";
import path from "path";
import {abcConfig} from "./sub-project/conf/abc-conf";
import {pluginManager} from "./sub-project/plugin-manager/plugin-manager";
import {OsUtils} from "./sub-project/os-utils/os-utils";
import {fileSchemeProcessor} from "./sub-project/file-scheme/file-scheme-processor";
import {requestTracker} from "./sub-project/request-tracker";

const kCypressTestKey = "--cypress-test";
const kSharePreferenceBindDeviceId = "remote_control_device_id";
const kZombieProcessCacheKey = "zombie-process";

const kAddonBuildInfo = {
    BUILD_ENV: process.env.BUILD_ENV || "unknown",
    BUILD_TIME: process.env.BUILD_TIME || "unknown",
    BUILD_TAG: process.env.BUILD_TAG || "unknown",
}

export class ABCDesktopAddonImpl implements AbcDesktopAddon {
    hasTriggerUpdater: boolean;

    mainWindow?: any/*BrowserWindow*/;
    splashWindow?: any/*BrowserWindow*/;
    canClosed = false;
    firstLaunchPage = true;

    runEnv = "prod"; // test|| dev|| prod

    downloadManagerBridge?: DownloadManagerBridge;
    loadingAnimationFinished?: Completer<boolean>;

    private _applicationKeyEvents: string[] = [];

    private _checkSystemIdleTimer!: any;
    private _chromiumArgsReady = new Completer<boolean>();

    private miraApp?: MiraApp;

    constructor() {}


    // private _createWindows() {
    //     this.firstLaunchPage = true;
    //     this.createMainWindow();
    // }


    // createMainWindow() {
    //     this.mainWindow = AbcAppHelper.createFromLaunchSize();
    //
    //     const {electronAbcApp, abcEnvironment} = abcHost;
    //
    //     electronAbcApp.mainWindow = this.mainWindow;
    //     let envName = "";
    //     if (abcEnvironment.isTest) {
    //         envName = `(测试环境)`;
    //     } else if (abcEnvironment.isDev) {
    //         envName = `开发环境`;
    //     }
    //
    //     logger.log(`createMainWindow`);
    //     // 程序名称
    //     const appName = Constants.appName;
    //     this.mainWindow.setTitle(`${appName}${abcConfig.titleSuffix ? ('-' + abcConfig.titleSuffix) : ''}-${abcHost.version}${envName}`);
    //     this.mainWindow.on('page-title-updated', function (e: any) {
    //         e.preventDefault();
    //     });
    //
    //     // and load the index.ejs of the app.
    //     // const userAgent = mainWindow.webContents.getUserAgent()  + ' ABCClinicDesktop/' + app.getVersion();
    //     // mainWindow.webContents.setUserAgent(userAgent);
    //     //mainWindow.loadURL('file://' + utils.getProjectPath() +  '/src/html/index.ejs');
    //     let url = this.getEntryUrl();
    //     // url = 'http://6.ftest.abczs.cn';
    //     logger.log("entryUrl =" + url);
    //
    //     electronAbcApp.runEnv = this.runEnv;
    //
    //     if (this.runEnv === 'dev' || openDevTools) {
    //         this.mainWindow.webContents.openDevTools();
    //     }
    //
    //     electronAbcApp.entryUrl = url;
    //
    //     ///监听主窗口异常重启
    //     this.mainWindow.webContents.on('render-process-gone', (event: any, detail: any) => {
    //         logger.log("render process crashed: detail: " + detail.reason);
    //         if (abcConfig.restartWhenRenderProcessError) {
    //             setTimeout(() => {
    //                 this.mainWindow?.reload();
    //             }, 1500)
    //         }
    //     });
    //
    //     this.mainWindow.webContents.on('dom-ready', () => {
    //         logger.log(`dom-ready event`);
    //         this.markAppLaunched();
    //     });
    //
    //     this.mainWindow.on('close', async (e: any) => {
    //         logger.log("this.mainWindow close");
    //         if (abcUpdater.isUpdating)
    //             return;
    //
    //         // 即将关闭
    //         if (!this.canClosed) {
    //             e.preventDefault();
    //             const result = await Dialog.show(this.mainWindow, {
    //                 buttons: ['确认', '取消'],
    //                 title: '提示',
    //                 message: '确认退出系统？',
    //             });
    //             if (result == "positive") {
    //                 this.canClosed = true;
    //                 this.mainWindow?.close();
    //
    //                 if (this._checkSystemIdleTimer) {
    //                     clearInterval(this._checkSystemIdleTimer);
    //                 }
    //             }
    //         }
    //     });
    //
    //
    //     // Emitted when the window is closed.
    //     this.mainWindow.once('closed', () => {
    //         this.mainWindow = undefined;
    //
    //         abcHost.electron.app.quit();
    //     });
    //
    //     this.mainWindow.loadURL("https://www.baidu.com").then();
    //
    //     this.registerApplicationEeyEvents();
    // }

    /**
     * 注册应用的内的快捷键
     * @param accelerator
     * @param callback
     */
    // registerApplicationKeyEvent(accelerator: string, callback: () => void): boolean {
    //     this._applicationKeyEvents.push(accelerator);
    //     return abcHost.electron.globalShortcut.register(accelerator, callback);
    // }

    //注册应用内的快捷键
    // registerApplicationEeyEvents() {
    //     this.registerApplicationKeyEvent("Ctrl+F12", () => {
    //         let mainWindow = abcHost.electron.BrowserWindow.getFocusedWindow();
    //         if (mainWindow) {
    //             const mainBrowserView = mainWindow.getBrowserView();
    //             if (mainBrowserView && mainBrowserView.webContents) {
    //                 mainBrowserView.webContents.openDevTools();
    //             } else if (mainWindow.webContents) {
    //                 mainWindow.webContents.openDevTools({mode: "detach"});
    //             }
    //         }
    //     });
    //
    //     const openHelpPage = () => {
    //         const helperWindow = utils.createBrowserWindow({
    //             width: 860,
    //             height: 640,
    //             show: true,
    //             modal: true,
    //             frame: true,
    //             //mac上由于parent设置后frame:false，子窗口也将没有frame
    //             parent: "win32" === process.platform ? this.mainWindow : undefined,
    //         });
    //
    //         const helpPage = 'file://' + path.resolve(AddonPathUtils.getRootPath(), 'renderer/index.html') + "#help";
    //         helperWindow.loadURL(helpPage).then();
    //
    //         helperWindow.on('closed', () => {
    //             console.log("help page closed");
    //         })
    //     }
    //     this.registerApplicationKeyEvent("Ctrl+F1", openHelpPage);
    //
    //     //兼容mac上使用远程桌面工具后，Ctrl+F1不能打开,改为Alt+F1
    //     this.registerApplicationKeyEvent("Alt+F1", openHelpPage);
    // }


    /**
     * 注销应用内的快捷键
     */
    // _unregisterApplicationKeyEvents() {
    //     this._applicationKeyEvents.forEach(accelerator => {
    //         abcHost.electron.globalShortcut.unregister(accelerator);
    //     });
    //
    //     this._applicationKeyEvents = [];
    // }

    // unregisterGlobalKeyboardEvent() {
    //     abcHost.electron.globalShortcut.unregisterAll();
    // }


    // async markAppLaunched() {
    //     if (this.firstLaunchPage) {
    //         //启动成功后，保存当前的网络相关配置
    //         this.firstLaunchPage = false;
    //         this.mainWindow?.show();
    //
    //
    //         //abcyun本地资源加载模式，等待前端资源加载完成，再隐藏闪屏
    //         const isAbcyunScheme = abcConfig.entryUrl?.startsWith(Constants.abcyunScheme);
    //
    //         if (this.loadingAnimationFinished)
    //             await this.loadingAnimationFinished.promise;
    //
    //         if (this.splashWindow) {
    //             this.splashWindow.hide();
    //             this.splashWindow.close();
    //             this.splashWindow = undefined;
    //         }
    //         abcHost.electronAbcApp.ready = true;
    //     }
    // }


    /**
     * 返回入口地址
     * @return {string}
     */
    getEntryUrl() {
        //cypress测试模式,url通过最后一个参数传入
        const cypressTest = hasCLIParams(kCypressTestKey);
        if (cypressTest) {
            return process.argv[process.argv.length - 1];
        }

        //支持命令传参数
        if (entryUrl.length > 0)
            return entryUrl;

        /**
         * @type{string}
         */
        return abcConfig.entryUrl;
    }

    /**
     * 主要是给前端 js api调用提供一些支持
     */
    private _initJSAPIEnv() {
        const {electronAbcApp, sharedPreferences, electron} = abcHost;
        electronAbcApp.ready = false;
        electronAbcApp.jsApiFile = utils.getJSApiFile();

        electronAbcApp.taskBarIconPath = utils.getTaskBarIconPath();

        electronAbcApp.getSharedPreference = function (key: string) {
            return sharedPreferences.getObject(key);
        }

        electronAbcApp.setSharedPreference = function (key: string, value: string) {
            return sharedPreferences.setObject(key, value);
        }

        electronAbcApp.ipcRenderer = electron.ipcRenderer;
        electronAbcApp.logger = logger;
        const app = electron.app;
        electronAbcApp.relaunchApp = () => {
            //过滤掉--background参数，不然会导致重启后无法打开窗口
            const argv = process.argv.slice(1).filter(item => item.indexOf("--background") == -1);
            app.relaunch({
                execPath: process.execPath,
                args: argv
            });
            app.exit(0);
        };

        electronAbcApp.abcUpdater = abcUpdater;
        electronAbcApp.clearCache = () => {
            this.mainWindow?.webContents.session.clearCache().then(() => {
                electronAbcApp.relaunchApp?.();
            });
        }

        electronAbcApp.onAbcConfigChanged = () => {
            abcConfig.loadWithDir();
        }

        electronAbcApp.isAppClosing = () => {
            return this.canClosed;
        }

        electronAbcApp.getAbcPluginList = () => {
            return pluginManager.getInstalledPluginList();
        }

        electronAbcApp.loadPluginUrl = (pluginName: string, params: string) => {
            pluginManager.preparePluginForWindow(this.mainWindow!, pluginName, params).then();
        }

        electronAbcApp.getPluginUrl = async (name, waitUpdate) => {
            return pluginManager.getPluginUrl(name, waitUpdate ?? true)
        };

        electronAbcApp.offlineBundler = offlineBundleManager;
        electronAbcApp.testOfflineBundle = async () => {
            await new OfflineBundleTester().start();
        }

        electronAbcApp.buildInfo = {
            hostInfo: abcHost.buildConfig,
            addon: {
                BUILD_ENV: process.env.BUILD_ENV,
                BUILD_TIME: process.env.BUILD_TIME,
                BUILD_TAG: process.env.BUILD_TAG,
            }
        };

        electronAbcApp.testUncaughtException = () => {
            setTimeout(() => {
                throw new Error("testUncaughtException");
            }, 1000);
        }

        electronAbcApp.registerAbcyunSchemeForSession = (sessionName: string) => {
            const ses = abcHost.electron.session.fromPartition(sessionName);
            offlineBundleManager.registerSchemesForProtocol(ses.protocol);
        }

        electronAbcApp.registerFileSchemeForAutoFillAbcyunCookie = (url: string) => {
            fileSchemeProcessor.registerFileSchemeForAutoFillAbcyunCookie(url);
        }

        electronAbcApp.getAbcyunRequestRecords = () => {
            return requestTracker.getRecords();
        };

        electronAbcApp.setDebugMode = (enable: boolean) => {
            abcConfig.debug = enable;
        }

        electronAbcApp.getProxyConfig = () =>{
            return abcHost.abcEnvironment.networkConfig?.proxyServer;
        }

        electronAbcApp.loadNativeLib = (libName: string) => {
            return pluginManager.loadAsyncLib(libName);
        }
    }

    /**
     * 处理一些electron app对角上的事件
     * @private
     */
    // _handleElectronAppEvent() {
    //     const app = abcHost.electron.app;
    //     app.on('second-instance', (event: any, commandLine: any, workingDirectory: any) => {
    //         logger.info(`commandLine ${commandLine}, workingDirectory = ${workingDirectory}`)
    //         const background = commandLine.find((item: any) => item.startsWith("--background=true"));
    //         if (background) return;
    //         // 当运行第二个实例时,将会聚焦到mainWindow这个窗口
    //         if (this.mainWindow) {
    //             if (this.mainWindow.isMinimized()) this.mainWindow.restore();
    //             this.mainWindow.focus()
    //         } else {
    //             this._createWindows();
    //         }
    //     });
    //
    //     app.on('activate', () => {
    //         console.log(`active`);
    //         // On macOS it's common to re-create a window in the app when the
    //         // dock icon is clicked and there are no other windows open.
    //         if (this.mainWindow === null) {
    //             this.createMainWindow();
    //         }
    //     });
    //
    //     // 有焦点时才注册快捷键
    //     app.on('browser-window-focus', (e: any, window: any) => {
    //         if (window === this.mainWindow) {
    //             this.registerApplicationEeyEvents();
    //         }
    //     });
    //
    //     app.on('browser-window-blur', (e: any, window: any) => {
    //         if (window === this.mainWindow) {
    //             this._unregisterApplicationKeyEvents();
    //         }
    //     });
    //
    //     app.on('quit', () => {
    //         this.unregisterGlobalKeyboardEvent();
    //     });
    // }


    private _onActive() {

    }

    private _onQuit() {
        this.miraApp?.stop();
    }

    init(options: IAddInitOptions) {
        abcHost.init(options);
        logger.hookAbcHostLogger();
        abcConfig.init();
        AddonPathUtils.setRootPath(options.rootDir);

        logger.addMeta("addonBuildInfo", JSON.stringify(kAddonBuildInfo));
        logger.addMeta("buildInfo", JSON.stringify(abcHost.buildConfig));
        logger.addMeta("uuid", abcHost.abcEnvironment.uuid());
        const deviceId = abcHost.sharedPreferences.getObject(kSharePreferenceBindDeviceId);
        if (deviceId)
            logger.addMeta("remoteControlDeviceId", deviceId);

        logger.setClinicId(abcHost.abcEnvironment._lastLoginClinicId);

        //异步上报启动事件
        setTimeout(async () => {
            //获取c盘磁盘空间信息（总大小，空闲大小）
            let osUtils = new OsUtils();
            const osInfo = await timeoutAction(async () => {
                return await osUtils.getOsInfo()
            }, 2000);
            abcTraceReport.addLog("appStart", {
                addonBuildInfo: kAddonBuildInfo,
                buildInfo: abcHost.buildConfig,
                remoteControlDeviceId: deviceId,
                osInfo: osInfo
            });
        }, 1000);


        //安装后还没有启用过离线包，如果首次是https地址，需要调整为abcyun://
        if (abcHost.isFirstLaunch) {
            if (abcHost.abcEnvironment.isRelease && !abcConfig.entryUrl?.startsWith(Constants.abcyunScheme)) {
                abcConfig.entryUrl =  kDefaultEntryUrl;
                abcConfig.set("entryUrl", abcConfig.entryUrl);
            }
        }
    }

    async onBeforeElectronAppReady(): Promise<void> {
        abcHost.electron.crashReporter.start({
            submitURL: 'https://abcyun.cn',
            uploadToServer: false,
            companyName: "ABCYun"
        });
        this._initUncaughtException();
        await this._initChromiumArgs().catch(ignore => {
        });

        const privileges = {secure: true, standard: true, supportFetchAPI: true, allowServiceWorkers: true};
        //这里注册自定义scheme
        //这里要注意，不能分开注册，会有影响,后面注册的会影响前面的注册，之前引导起api请求中url参数没有带到后台的问题
        abcHost.electron.protocol.registerSchemesAsPrivileged([{
            scheme: Constants.abcyunScheme,
            privileges: privileges
        }]);
    }

    async onAfterElectronAppReady(): Promise<void> {
        logger.log('electron ready');
        fileSchemeProcessor.init();

        //初始化js api相关接口
        this._initJSAPIEnv();

        //首次启动以后台方式启动
        // const launchBackground = getCLIOption("background", "false") === "true";
        // if (!launchBackground)
        //     this._createWindows();
        //
        // this._handleElectronAppEvent();

        this.miraApp = new MiraApp(abcHost.electronAbcApp);
        this.miraApp.start();

        // offlineBundleManager.init().then(() => {
        //     //如果已经明确指定了region，再次启动时检查插件更新，防止直接检查会被认为默认分区，对其它分区的用户会下载一些无用的插件
        //     if (!isNil(abcHost.sharedPreferences.getObject("last_region")))
        //         offlineBundleManager.scheduleCheckUpdate();
        // });

        this._executeTaskWhenStartToIdle().then().catch(ignored => ignore(ignored));

        //阻止应用程序挂起
        this._disableSuspendApp();

        this._checkAndRestoreAppConfigFileValid();

        // 清理非当前主进程关联的ABC程序
        this._cleanZombieProcess();
    }

    /**
     * 初始化异常处理dump
     * @private
     */
    _initUncaughtException() {
        process.on("uncaughtException", (err) => {
            logger.logToServerWithRecentLogs('uncaughtException err: ' + err.message + ", stack:" + err.stack);
        });

        //处理未处理的reject
        process.on('unhandledRejection', (reason, p) => {
            logger.logToServerWithRecentLogs('unhandledRejection at:' + p + 'reason:' + reason + ", stack:" + (reason as any).stack);
        });

    }

    /**
     * 添加chromium内核启动参数
     * @private
     */
    async _initChromiumArgs() {
        //智慧医保访问的链接带的端口87、82  比较小, 放开限制（un_safe_port）

        const app = abcHost.electron.app;
        const {logger, abcEnvironment} = abcHost;
        app.commandLine.appendSwitch('explicitly-allowed-ports', '82,87');

        //强制添加忽略证书选项,解决lodop打印组件地址证书错误问题
        let commandLines = abcConfig.chromium?.commandLines ?? '';
        const kIgnoreCertificateError = "--ignore-certificate-errors";
        if (commandLines.indexOf(kIgnoreCertificateError) <= 0) {
            commandLines = `${commandLines} ${kIgnoreCertificateError}`;
        }
        console.log(`commandLines = ${commandLines}`);
        getCLIOptions(commandLines.split(" ")).forEach(((value, key) => {
            logger.log(`_initChromiumArgs add extra commandLine value = ${value}, key = ${key}`);
            app.commandLine.appendSwitch(key, value);
        }));

        this._chromiumArgsReady.resolve(true);
    }

    /**
     * 启动后等一段时间执行，不影响启动速度
     * @private
     */
    private async _executeTaskWhenStartToIdle() {
        await delay(30 * 1000);
        this._removeDesktopInstaller().then().catch(ignored => ignore(ignored));
    }

    /**
     * 删除桌面上的安装包，防止用户不小心双击安装
     * @private
     */
    private async _removeDesktopInstaller() {
        const path = hostRequire("path");
        const desktopPath = abcHost.electron.app.getPath('desktop');
        //遍历桌面程序，找到以 abcyun-desktop-winxxx.exe格式的文件，删除
        const files = hostRequire('fs').readdirSync(desktopPath);
        for (const file of files) {
            if (file.endsWith(".exe") && file.startsWith("abcyun-desktop-win")) {
                FileUtils.deleteFileSync(path.join(desktopPath, file));
            }
        }
    }

    private _disableSuspendApp() {
        try {
            const powerSaveBlocker = abcHost.electron.powerSaveBlocker;
            const id = powerSaveBlocker.start('prevent-app-suspension')
            logger.log(`阻止应用挂起 id = ${id}`);
        } catch (e) {
            ignore(e)
        }
    }

    /**
     * 检查app config文件是否合法
     * @private
     */

    private _checkAndRestoreAppConfigFileValid() {
        //检查appConfig里的key是否是\x00字符
        const keys = Object.keys(abcConfig);
        let hasInvalidKey = false;
        for (let i = 0; i < keys.length; i++) {
            const key = keys[i];
            if (key.indexOf('\x00') >= 0) {
                hasInvalidKey = true;

                delete abcConfig[key];
            }
        }

        if (hasInvalidKey) {
            const fileCotnent = hostRequire('fs').readFileSync(abcConfig.configFile(), 'utf-8');
            logger.logToServer('配置文件包含非法字符 文件内容：' + fileCotnent);

            abcConfig.saveConfigFile();
        }
    }

    private _cleanZombieProcess() {
        // 获取当前主进程的pid
        const currentProcessId = process.pid;
        const execCommand = `wmic process where "Name='ABC数字医疗云.exe' AND (ProcessId != ${currentProcessId} AND ParentProcessId != ${currentProcessId})" call terminate`;
        hostRequire('child_process').exec(execCommand, (error) => {
            if (error) {
                logger.error(`${kZombieProcessCacheKey} wmic清理僵尸进程失败：${error}`);
                logger.info(`${kZombieProcessCacheKey} 使用powershell清理`);
                // 新版windows不再内置wmic命令，使用powershell代替
                const powershellCommand = `powershell -Command "Get-CimInstance Win32_Process -Filter \\"Name='ABC数字医疗云.exe' AND ProcessId != ${currentProcessId} AND ParentProcessId != ${currentProcessId}\\" | ForEach-Object { Stop-Process $_.ProcessId -Force -ErrorAction SilentlyContinue }"`;
                hostRequire('child_process').exec(powershellCommand, (error) => {
                    if (error) {
                        logger.error(`${kZombieProcessCacheKey} powershell清理僵尸进程失败：${error}`);
                        return;
                    }
                    logger.info(`${kZombieProcessCacheKey} powershell清理僵尸进程成功`);
                })
                return;
            }
        })
    }
}
