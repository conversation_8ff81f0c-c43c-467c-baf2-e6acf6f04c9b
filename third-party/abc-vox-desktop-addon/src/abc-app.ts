import {abcHost, hostRequire} from "./abc-host";
import utils from "./sub-project/common/utils";
import {
    IOfflineBundleInfos,
    IUpdateLocalOffBundleResult,
    OfflineBundleUpdateMsg
} from "./sub-project/front-end-offline-bundle";
import path from "path";
import {AddonPathUtils} from "./sub-project/common";
import {UiUtils} from "./sub-project/component/ui-utils";
import { ConfirmResult, ConfirmOptions } from "./sub-project/component/confirm";

export interface ISwitchClinicOptions {
    region?: string,
    env?: "pre" | "gray" | "prod",
    chainId?: string,
    clinicId?: string,
    employeeId?: string,

    onBundleUpdateProgress?: any;
}


export interface IElectronAbcApp {
    screenshotInfo?: {
        fileName: string;
        windowTitle: string;
    };

    runEnv: string;
    setSharedPreference: (key: string, value: string) => void;
    getSharedPreference: (key: string) => any;
    registerJSAPForBrowserWindow: () => Promise<void>;
    printer: any;
    printHtmlPath: string;
    taskBarIconPath: string;
    jsApiFile: string;
    errorFileURL: string;
    cssStr: string;
    ipcRenderer: any;/*Electron.IpcRenderer*/
    isAppClosing: () => boolean;
    logger: any;// LogUtils
    clearCache: () => void;
    onAbcConfigChanged: () => void;
    onClinicLogoutSuccess: () => void;
    onClinicLoginSuccess: () => void;
    onSwitchClinic: (options: ISwitchClinicOptions) => void;
    mainWindow?: any;/*BrowserWindow*/

    relaunchApp?: () => void;

    /**
     * PluginConf[]
     */
    getAbcPluginList: () => Promise<any[]>;

    /**
     * 加载
     * @param appName
     * @param params {string} url参数，作为入口url的参数传入
     */
    loadPluginUrl: (appName: string, params: string) => void;


    /**
     *  获取插件地址
     * @param name 插件名称
     * @param waitUpdate 是否等待更新结果
     */
    getPluginUrl: (name: string, waitUpdate: boolean) => Promise<string>;


    printWindow?: any;/*BrowserWindow*/
    abcUpdater: any//ABCUpdater;

    entryUrl: string;


    /**
     * 触发前端资源检查更新
     * @param name 要检查的前端资源名称,如果为空检查所有资源
     */
    checkFrontEndBundleUpdate: (name?: string,) => Promise<void>;

    createSplashView: () => any/*BrowserWindow*/

    launchShebaoAloneModeIfNeed: () => void;

    offlineBundler: {
        getOfflineBundleInfoList: () => Promise<IOfflineBundleInfos>;
        updateLocalOfflineBundle: () => Promise<IUpdateLocalOffBundleResult>; //


        /**
         * 根据名称更新本地离线包
         * @param name
         */
        updateLocalOfflineBundleByName: (name: string) => Promise<IUpdateLocalOffBundleResult>;

        /**
         *
         * 检查离线包更新
         * @param name插件名称
         */
        checkOfflineBundleUpdate: (name: string) => Promise<IUpdateLocalOffBundleResult>;


        /**
         * 收到来自oa的socket io消息通知有变更
         * @param updateMsg
         */
        onReceiveOfflineBundleUpdateMsg: (updateMsg: OfflineBundleUpdateMsg) => void;
    };//OfflineBundleManager
    testOfflineBundle: () => void;
    buildInfo: any;


    testUncaughtException: () => void;

    //闪屏结束后处理ready状态
    ready: boolean;

    //绑定远程操作识别码，日志上报时会带上此id
    setRemoteControlDeviceId: (deviceId: string) => void;

    /**
     * 对指定的session注册abcyun协议
     * @param sessionName {string} session名称
     */
    registerAbcyunSchemeForSession: (sessionName: string) => void;

    /**
     * 对于本地文件地址发地的网络请求，自动填充 abcyun cookie
     * @param url
     */
    registerFileSchemeForAutoFillAbcyunCookie: (url: string) => void;

    /**
     * 获取abcyun请求记录
     */
    getAbcyunRequestRecords: () => any[];


    setDebugMode: (debug: boolean) => void;

    /**
     * 对于一些请求进行拦截替换为其它url,以解决专网下，直接访问访问流量带宽问题
     */
    urlIntercept: (url:string)=>string;

    /**
     * 获取代理配置
     * @returns
     */
    getProxyConfig: ()=>string | undefined;

    /**
     * 注册本地库
     * @param libName
     */
    loadNativeLib: (libName: string) => any;

    /**
     * 原生确认框
     */
    nativeConfirm: (options: ConfirmOptions) => Promise<ConfirmResult>;
}


export class AbcAppHelper {
    public static getLaunchSize(): {
        width: number,
        height: number,
        showRestoreBtn: boolean,
        initialFullscreen: boolean
    } {
        const screen = abcHost.electron.screen;
        const {width: screenWidth, height: screenHeight} = screen.getPrimaryDisplay().size;

        let width = Math.min(screenWidth, 1440);
        let height = Math.min(screenHeight, 900);

        const ret = {
            width: width,
            height: height,
            showRestoreBtn: true,
            initialFullscreen: true,
            screenWidth: screenWidth,
            screenHeight: screenHeight,
        };

        console.log(`getLaunchSize = ${JSON.stringify(ret)}`);
        return ret;
    }

    public static createFromLaunchSize(params?: any/*Electron.BrowserWindowConstructorOptions*/): any/*BrowserWindow*/ {
        const launchSize = AbcAppHelper.getLaunchSize();
        let windowOpts = {
            frame: false,
            webPreferences: {
                nodeIntegration: true,
                webviewTag: true,
                contextIsolation: false,
            },
            ...launchSize,
            ...params,
            show: false
        };
        let window = utils.createBrowserWindow(windowOpts);
        if (launchSize.initialFullscreen) {
            window.maximize();
        }

        window.setResizable(launchSize.showRestoreBtn);
        window.show();

        return window;
    }

    public static createSplashWindowWithUrl(mainWindow: any/*BrowserWindow*/, url: string): any/*BrowserWindow*/ {
        let windowOpts = {};
        // windows平台
        if (process.platform === 'win32') {
            const size = AbcAppHelper.getLaunchSize();
            const barHeight = mainWindow.getSize()[1] - mainWindow.getContentSize()[1];
            windowOpts = {
                frame: false,
                transparent: true,
                width: size.width - 14,
                height: size.height - barHeight,
                parent: mainWindow,
                modal: true
            };
        }

        const splashWindow = AbcAppHelper.createFromLaunchSize(windowOpts);

        splashWindow.loadURL(url).then().catch(() => {
        });

        return splashWindow;
    }

    public static createSplashWindow(mainWindow: any/*BrowserWindow*/): any/*BrowserWindow*/ {
        return AbcAppHelper.createSplashWindowWithUrl(mainWindow, 'file://' + path.join(AddonPathUtils.getRootPath() + '/renderer/splash.html'));
    }


    public static async createProcessSplashWindow(mainWindow: any/*BrowserWindow*/)/*BrowserWindow*/ {
        const tmpFile = hostRequire('path').join(abcHost.electron.app.getPath('userData'), 'tmp/background-screenshot.png')
        await UiUtils.captureWindow(mainWindow, tmpFile);
        const splashURL = 'file://' + path.resolve(AddonPathUtils.getRootPath(), 'renderer/progress-splash.html') + "#help";
        return AbcAppHelper.createSplashWindowWithUrl(mainWindow, splashURL);
    }


    public static findProgressSplashWindow(): any/*BrowserWindow*/ {
        const wins = abcHost.electron.BrowserWindow.getAllWindows();
        for (let win of wins) {
            if (!win.isDestroyed() && win.isVisible() && win.webContents.getURL().indexOf("progress-splash") > 0)
                return win;
        }

        return null;
    }
}
