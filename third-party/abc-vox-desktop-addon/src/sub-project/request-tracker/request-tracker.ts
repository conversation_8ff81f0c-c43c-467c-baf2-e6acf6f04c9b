/**
 * 请求记录类
 */
interface RequestRecord {
    startTime: number;  // 请求开始时间戳
    duration: number;   // 请求耗时(毫秒)
    path: string;      // 请求路径
    isNetwork: boolean; // 是否是网络请求
}

/**
 * 请求追踪器 - 记录最近100条abcyun://相关请求
 */
export class RequestTracker {
    private static instance: RequestTracker;
    private records: RequestRecord[] = [];
    private readonly maxRecords = 100;

    private constructor() {}

    public static getInstance(): RequestTracker {
        if (!RequestTracker.instance) {
            RequestTracker.instance = new RequestTracker();
        }
        return RequestTracker.instance;
    }

    /**
     * 记录一个请求
     */
    public recordRequest(path: string, startTime: number, duration: number, isNetwork: boolean) {
        const record: RequestRecord = {
            startTime,
            duration,
            path,
            isNetwork
        };

        // 添加新记录到数组开头
        this.records.unshift(record);

        // 如果超过最大记录数,删除最旧的记录
        if (this.records.length > this.maxRecords) {
            this.records = this.records.slice(0, this.maxRecords);
        }
    }

    /**
     * 获取所有记录
     */
    public getRecords(): RequestRecord[] {
        return this.records;
    }

    /**
     * 清除所有记录
     */
    public clearRecords() {
        this.records = [];
    }
}

export const requestTracker = RequestTracker.getInstance();
