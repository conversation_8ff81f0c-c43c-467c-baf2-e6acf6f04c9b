/**
 * 用于读取abc-conf.ini里配置
 */
import { kDefaultEntryUrl } from "../../constants";
import {abcHost, hostRequire} from "../../abc-host";

const kConfigFileName = "abc-conf.ini";

class AbcConfig {
    network = {};
    titleSuffix?: string;
    debug?: boolean;

    //客户端环境
    env: "dev" | "test" | "prod" = "prod";
    pluginAutoUpdate = true;

    /**
     * 入口地址
     */
    entryUrl!: string;

    /**
     * 是否允许检查更新客户端
     */
    enableUpdateApp?: boolean = true;

    /**
     * 是否自动更新客户端js代码
     */
    enableAutoUpdateJS?: boolean = false;


    /**
     * 重启打印服务
     */
    restartPrintSpoolerWhenLaunch?: boolean = false;

    /**
     * 当render进程异常后，重启客户端
     */
    restartWhenRenderProcessError?: boolean = true;

    /**
     * 默认全屏方式启动
     */
    initialFullscreen?: boolean = false;

    /**
     * 客户端child-process时 stdio:ignore 选项
     */
    childProcessStdioIgnoreWhenShell?: boolean = true;

    /**
     * 自定义appData路径
     */
    extraAppDataPath?: string;


    /**
     * ABC后台服务相关参数
     */
    abcServer?: {
        autoFocus: boolean, //是否自动获取焦点
        defaultPort: number //默认端口号54321
    }

    /**
     * 针对chrome内核的一些配置
     */
    chromium?: {
        commandLines?: string; //命令行参数
        isMigrateSandbox?: boolean;
    }

    //是否支持右键打开调试
    enableDebugContextMenu?: boolean;

    frontEndOfflineBundle: {
        enableUpdateRouteConfig?: boolean
    } = {};

    /**
     * @param dataDir {string?} abc-conf.ini文本所在目录
     */
    constructor() {

    }

    init() {
        this.loadWithDir();
    }

    /**
     * 从指定目录中加载配置文件
     * @param dataDir {string}
     */
    loadWithDir(dataDir?: string) {
        let config: any = {}
        try {
            const fs = hostRequire('fs');
            const ini = hostRequire('ini');
            const file = this.configFile(dataDir);
            try {
                config = ini.parse(fs.readFileSync(file, 'utf-8'));
            } catch (e) {
            }
        } catch (e: any) {
            console.log(`ABCConfig read abc-conf.ini exception，e = ${e},e =${e.stack}`);
        }


        Object.assign(this, config);

        if (!this.env)
            this.env = "prod";

        if (!this.entryUrl) 
            this.entryUrl = kDefaultEntryUrl;
    }


    /**
     * 设置配置项
     * @param key 格式为：a.b.c
     * @param value
     */
    set(key: string, value: any): void {
        const domains = key.split(".");
        let currentValue: any = this;
        for (let i = 0; i < domains.length; ++i) {
            let domain = domains[i];
            if (currentValue[domain] == undefined) {
                currentValue[domain] = {}
            }
            if (i == domains.length - 1) {
                currentValue[domain] = value;
            }

            currentValue = currentValue[domain];
        }


        this.saveConfigFile();
    }

    public gatekeeperPort() {
        return 0;
    }

    public configFile(dataDir?: string) {
        const fs = hostRequire('fs');
        if (!dataDir) {
            const {app} = abcHost.electron;
            dataDir = app.getPath('userData');
        }
        
        return `${dataDir}/${kConfigFileName}`;
    }

    public saveConfigFile() {
        const fs = hostRequire('fs');
        const ini = hostRequire('ini');
        const _ = hostRequire('lodash');
        const {app} = abcHost.electron;
        const dataDir = app.getPath('userData');
        fs.writeFileSync(`${dataDir}/${kConfigFileName}`, ini.encode(_.omit(this, ["isFirstLaunch"])));
        this.loadWithDir();
    }
}


const abcConfig = new AbcConfig();
export {abcConfig};
