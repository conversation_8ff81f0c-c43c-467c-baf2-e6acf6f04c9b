import {abcHost, hostRequire} from "../../abc-host";

export class ProcessUtils {
    /**
     * 运行cmd，并返回输出内容
     */
    static runCmd(cmd: string): Promise<{
        exitCode: number,
        stdout: string,
        stderr: string
    }> {
        return new Promise((resolve) => {
            let outputData = '';
            let errorData = '';
            const encoding = 'cp936';
            const binaryEncoding = 'binary';
            // 调用Windows cmd 执行命令

            const childProcess =
                hostRequire('child_process')
                    .exec(`cmd.exe /c ${cmd}`, {encoding: binaryEncoding}, (err: any, stdout: any, stderr: any) => {
                        const iconv =hostRequire('iconv-lite');
                        if (stdout) outputData = iconv.decode(new Buffer(stdout, binaryEncoding), encoding);
                        if (stderr) errorData = iconv.decode(new Buffer(stderr, binaryEncoding), encoding);
                    });

            // 监听子进程关闭事件
            childProcess.on('close', (code: any) => {
                resolve({
                    exitCode: code,
                    stdout: outputData,
                    stderr: errorData
                });
            });
        });
    }
}