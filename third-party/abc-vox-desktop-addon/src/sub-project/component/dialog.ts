/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2021/12/7
 *
 * @description
 */

import {AddonPathUtils, Completer} from "../common";
import {UiUtils} from './ui-utils';
import {abcHost, hostRequire} from "../../abc-host";
import path from "path";

export type DialogAction = "positive" | "negative" | "close";


export class Dialog {
    static async show(parent: any/*BrowserWindow*/, params: {
        buttons: string[],
        title: string,
        message: string,
        backgroundBlur?: boolean
    }): Promise<DialogAction> {
        if (params.backgroundBlur && parent) {
            const tmpFile = hostRequire('path').join(abcHost.electron.app.getPath('userData'), 'tmp/background-screenshot.png')
            UiUtils.captureWindow(parent, tmpFile);
        }

        let child = new abcHost.electron.BrowserWindow(
            UiUtils.createFillParentParams(parent),
        );

        child.setResizable(false);
        child.setMovable(false);
        let timestamp = new Date().getTime().toString();
        let completer = new Completer<DialogAction>();
        const url = 'file://' + path.join(AddonPathUtils.getRootPath(), 'renderer/dialog.html') + '?windowId=' + timestamp + '&params=' + JSON.stringify(params);
        child.loadURL(url)
            .then(() => {
                abcHost.electron.ipcMain.on('close-dialog', (event: any, params: {
                    windowId: string,
                    action: "positive" | "negative",
                }) => {
                    console.log(`close-dialog windowId = ${JSON.stringify(params)}`);
                    const {windowId, action} = params;
                    if (windowId == timestamp) {
                        child.hide();
                        child.close();
                        completer.resolve(action);
                    }
                });
            })
            .catch(() => {
                child.close();
                Dialog.show(parent, params).then((result) => {
                    completer.resolve(result);
                })
            });

        if (parent.isMaximized()) {
            child.maximize();
        }

        child.show();
        //在关闭硬件加速的情况下不能先设置opacity，否则导致窗口不能显示，造成假死的问题，先关闭设置Opacity
        //系统窗口有个动画，先隐藏，300ms再显示出来
        // child.setOpacity(0.0);
        //
        // setTimeout(() => {
        //     child.setOpacity(1.0);
        // }, 300);

        return completer.promise;
    }
}
