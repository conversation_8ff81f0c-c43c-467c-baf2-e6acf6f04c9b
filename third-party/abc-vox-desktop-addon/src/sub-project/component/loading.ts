import utils from "../common/utils";
import { UiUtils } from "./ui-utils";
import path from "path";
import { AddonPathUtils } from "../common/file/addon-path-utils";

const TAG = "Loading";

export interface LoadingOptions {
    text?: string;
}

export function loading(mainWindow: any/*BrowserWindow*/, options: LoadingOptions): () => void {
    const loadingWindow = utils.createBrowserWindow({
        ...UiUtils.createFillParentParams(mainWindow),
        show: true,
        resizable: false,
        movable: false,
    });

    if (mainWindow.isMaximized()) {
        loadingWindow.maximize();
    }

    const sendId = `${TAG}:_loading${loadingWindow.id}`;

    loadingWindow.loadURL(`file://${path.resolve(AddonPathUtils.getRootPath(), `renderer/loading.html?sendId=${sendId}&text=${options.text}`)}`).then();


    const onClose = () => {
        loadingWindow.close();
    }

    return onClose
}