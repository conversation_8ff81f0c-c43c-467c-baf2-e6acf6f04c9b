import { UiUtils } from "./ui-utils";
import utils from "../common/utils";
import path from "path";
import { AddonPathUtils } from "../common/file/addon-path-utils";
import { abcHost } from "../../abc-host";

const TAG = "Confirm";

export interface ConfirmOptions {
    type: "info" | "error" | "warn" | "success";
    title: string;
    content: string;
    onClose?: () => void;
    onConfirm?: () => void;
    onCancel?: () => void;
    confirmText?: string;
    cancelText?: string;
    showCancel?: boolean;
}

export type ConfirmResult = 'close' | 'confirm' | 'cancel';

export async function confirm(mainWindow: any/*BrowserWindow*/, options: ConfirmOptions): Promise<ConfirmResult> {
    return new Promise((resolve) => {
        const ipcMain = abcHost.electron.ipcMain;

        const confirmWindow = utils.createBrowserWindow({
            ...UiUtils.createFillParentParams(mainWindow),
            show: true,
            resizable: false,
            movable: false,
        });

        if (mainWindow.isMaximized()) {
            confirmWindow.maximize();
        }

        const sendId = `${TAG}:_confirm${confirmWindow.id}`;

        confirmWindow.loadURL(`file://${path.resolve(AddonPathUtils.getRootPath(), `renderer/confirm.html?title=${options.title}&content=${options.content}&sendId=${sendId}`)}&confirmText=${options.confirmText || '确定'}&cancelText=${options.cancelText || '取消'}&showCancel=${options.showCancel ? 1 : 0}`).then();

        ipcMain.once(sendId, (_event: any/*Event*/, arg: any) => {
             confirmWindow.close();
             if(arg.type === 'close') {
                 options.onClose?.();
             } else if (arg.type === 'confirm') {
                 options.onConfirm?.();
             } else if (arg.type === 'cancel') {
                 options.onCancel?.();
             }

             resolve(arg.type);
        });
    });
}