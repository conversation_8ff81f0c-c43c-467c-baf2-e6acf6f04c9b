/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2021/12/21
 *
 * @description
 */
import utils from "../common/utils";
import {hostRequire} from "../../abc-host";
import {FileUtils} from "../common";


export class UiUtils {
    public static createFillParentParams(parent: any/*BrowserWindow*/): any/*BrowserWindowConstructorOptions*/ {
        const size = parent.getSize();
        const pos = parent.getPosition();
        let offsetWidth = 0;
        let offsetHeight = 0;

        return {
            parent: parent,
            width: size[0] - offsetWidth,
            height: size[1] - offsetHeight,
            x: pos[0] + offsetWidth / 2,
            y: pos[1],
            hasShadow: false,
            modal: false,
            webPreferences: {
                nodeIntegration: true,
                webviewTag: true,
                contextIsolation: false,
                preload: utils.getJSApiFile()
            },
            frame: false,
            transparent: true,
            show: false,
        };
    }

    static async captureWindow (window, file) {
        // FileUtils.deleteFileSync(file);
        // const {Win32Utils} = hostRequire('native-lib');
        // if (Win32Utils && Win32Utils.captureWindowFromDesktopSnapshot) {
        //     const hwndBuffer = window.getNativeWindowHandle();
        //     await new Promise(resolve => {
        //         Win32Utils.captureWindowFromDesktopSnapshot(hwndBuffer, file, (success) => {
        //             resolve(true);
        //         });
        //     })
        // }
    }
}
