import {logger} from "../common";
import {IElectronAbcApp} from "../../abc-app";
import {WindowManager} from "./window-manager";

export class MiraApp {
    private app: IElectronAbcApp;
    private mainWindow?: any;
    private recordingWindow?: any;
    private settingsWindow?: any;
    private voiceService?: VoiceService;
    private aiService?: AIService;
    private rpaService?: RPAService;
    private dataService?: DataService;

    constructor(app: IElectronAbcApp) {
        this.app = app;
        // this._initServices();
    }

    private _initServices() {
        // this.voiceService = new VoiceService();
        // this.aiService = new AIService();
        // this.rpaService = new RPAService();
        // this.dataService = new DataService();
    }

    start() {
        WindowManager.getInstance().init(this.app);
        this._createMainWindow();
        this._registerEventHandlers();
    }

    private _createMainWindow() {
        this.mainWindow = WindowManager.getInstance().createWindow({
            width: 380,
            height: 520,
            show: false
        });
        logger.log("_createMainWindow", 'width 380', );

        // 根据用户登录状态决定加载的页面
        if (this._isUserLoggedIn()) {
            this.mainWindow.loadURL("http://localhost:3700/consultation-list.html");
        } else {
            // 设置登录窗口大小
            this.mainWindow.setSize(380, 520);
            this.mainWindow.loadURL("http://localhost:3700/login.html");
        }

        this.mainWindow.show();
    }

    createRecordingWindow() {
        if (this.recordingWindow) {
            this.recordingWindow.focus();
            return this.recordingWindow;
        }

        this.recordingWindow = WindowManager.getInstance().createWindow({
            width: 320,
            height: 120,
            frame: false,
            alwaysOnTop: true,
            resizable: false,
            skipTaskbar: true,
            show: false
        });

        this.recordingWindow.loadURL("http://localhost:3700/recording.html");
        this.recordingWindow.on('closed', () => {
            this.recordingWindow = null;
        });

        return this.recordingWindow;
    }

    createSettingsWindow() {
        if (this.settingsWindow) {
            this.settingsWindow.focus();
            return this.settingsWindow;
        }

        this.settingsWindow = WindowManager.getInstance().createWindow({
            width: 600,
            height: 500,
            modal: true,
            parent: this.mainWindow,
            show: false
        });

        this.settingsWindow.loadURL("http://localhost:3700/settings.html");
        this.settingsWindow.on('closed', () => {
            this.settingsWindow = null;
        });

        return this.settingsWindow;
    }

    private _registerEventHandlers() {
        // 注册录音相关事件
        this.app.ipcRenderer?.on('start-recording', () => {
            this.startRecording();
        });

        this.app.ipcRenderer?.on('stop-recording', () => {
            this.stopRecording();
        });

        this.app.ipcRenderer?.on('open-settings', () => {
            this.createSettingsWindow().show();
        });
    }

    async startRecording() {
        const recordingWin = this.createRecordingWindow();
        recordingWin.show();

        // 最小化主窗口
        if (this.mainWindow) {
            this.mainWindow.minimize();
        }

        // 开始录音
        await this.voiceService?.startRecording();
    }

    async stopRecording() {
        // 停止录音并获取结果
        const recordingResult = await this.voiceService?.stopRecording();

        if (recordingResult) {
            // 使用 AI 服务生成病历
            const medicalRecord = await this.aiService?.generateMedicalRecord(recordingResult);

            // 保存问诊记录
            // await this.dataService?.saveConsultationRecord({
            //     ...recordingResult,
            //     medicalRecord,
            //     timestamp: new Date()
            // });
        }

        // 关闭录音窗口
        if (this.recordingWindow) {
            this.recordingWindow.close();
        }

        // 恢复主窗口
        if (this.mainWindow) {
            this.mainWindow.restore();
            this.mainWindow.focus();
        }
    }

    private _isUserLoggedIn(): boolean {
        // 检查用户登录状态
        return !!this.app.getSharedPreference('user_token');
    }

    stop() {
        this.voiceService?.cleanup();
        this.aiService?.cleanup();
        this.rpaService?.cleanup();
        this.dataService?.cleanup();
    }

    _unregisterApplicationKeyEvents() {
        // 注销快捷键事件
    }

    _registerApplicationKeyEvent() {
        // 注册全局快捷键，如 F2 开始录音
    }
}

// 服务类接口定义
interface VoiceService {
    startRecording(): Promise<void>;
    stopRecording(): Promise<RecordingResult>;
    cleanup(): void;
}

interface AIService {
    generateMedicalRecord(recordingResult: RecordingResult): Promise<MedicalRecord>;
    cleanup(): void;
}

interface RPAService {
    copyToHIS(medicalRecord: MedicalRecord): Promise<boolean>;
    cleanup(): void;
}

interface DataService {
    saveConsultationRecord(record: ConsultationRecord): Promise<void>;
    getConsultationRecords(filter?: RecordFilter): Promise<ConsultationRecord[]>;
    cleanup(): void;
}

interface RecordingResult {
    audioData: ArrayBuffer;
    transcript: string;
    duration: number;
}

interface MedicalRecord {
    patientInfo: PatientInfo;
    chiefComplaint: string;
    historyOfPresentIllness: string;
    physicalExamination: string;
    diagnosis: string;
    otherInfo: string;
}

interface ConsultationRecord {
    id?: string;
    timestamp: Date;
    title: string;
    summary: string;
    audioData: ArrayBuffer;
    transcript: string;
    medicalRecord: MedicalRecord;
    duration: number;
}

interface PatientInfo {
    name?: string;
    age?: number;
    gender?: string;
    phone?: string;
}

interface RecordFilter {
    dateRange?: [Date, Date];
    symptoms?: string[];
    durationRange?: [number, number];
}
