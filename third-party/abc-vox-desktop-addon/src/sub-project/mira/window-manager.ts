import utils from "../common/utils";

export class WindowManager {
    private _windows = new Map<string, any>();
    private _app: any;

    static _instance: WindowManager;
    static getInstance() {
        if (!WindowManager._instance) {
            WindowManager._instance = new WindowManager();
        }

        return WindowManager._instance;
    }

    init(app: any) {
        this._app = app;
    }

    createWindow(options: any) {
        const winOpts = {
            frame: false,
            webPreferences: {
                nodeIntegration: true,
                webviewTag: true,
                contextIsolation: false,
            },
            ...options,
            show: false
        };
        const win = utils.createBrowserWindow(winOpts);
        win.show();

        this._windows.set(win.id, win);

        return win;
    }
}
