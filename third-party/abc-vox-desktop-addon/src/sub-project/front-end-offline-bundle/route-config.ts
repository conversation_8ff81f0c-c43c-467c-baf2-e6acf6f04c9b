/**
 * 路由规则配置示例
 * {
 *   "rules": [
 *     {
 *       "remote": "static-pre-cdn.abcyun.cn/abc-public-health/",
 *       "name": "abc-public-health"
 *     },
 *     {
 *       "remote": "www.abcyun.cn",
 *       "name": "pc",
 *       "rule": [
 *         "rewrite ^/$ /static/app.html",
 *         "rewrite ^/hospital/* /hospital-app.html",
 *         "rewrite /* /index.html"
 *       ],
 *       "id": 2
 *     },
 *     {
 *       "remote": "region2.abcyun.cn",
 *       "type": "rewrite",
 *       "target": 2
 *     },
 *     {
 *       "remote": "global.abcyun.cn",
 *       "name": "static-login"
 *     },
 *     {
 *       "remote": "mall.abcyun.cn/api",
 *       "type": "api"
 *     },
 *     {
 *       "remote": "www.abcyun.cn/api",
 *       "type": "api"
 *     }
 *   ]
 * }
 */
import {FileUtils} from "../common";

interface IPreInterceptor {
    path: string;
    script: string;
}

/**
 * 单条路由规则
 */
interface RouteRule {
    remote: string;
    path?: string; //api类型时，需要指定path
    filePrefix?: string;
    name: string;
    type?: "api" | "rewrite" | "bypass";
    preInterceptor?: IPreInterceptor[]; // 自定义脚本处理器
    rule?: RouteCommandRule[];
    id?: number;
    target?: number;
    targetProtocol?: string;//当type为bypass时，需要指定targetProtocol,表示指定请求的实际协议
    global?: boolean; //是否全局插件，不区分分区
}

/**
 * 定义具体路由跳转规则（rewrite）
 */
interface RouteCommandRule {
    type: "rewrite" | "redirect";
    from: string; //正则表达式
    to: string
}

/**
 * 路由配置
 */
interface RouteConfig {
    rules: RouteRule[];
}

/**
 * 读取指定json格式文件，并解析成RouteConfig对象
 */
function loadRouteConfig(file: string): RouteConfig | null {
    const content = FileUtils.readFileAsString(file);
    if (!content) {
        return null;
    }

    let config: RouteConfig | null = null;
    try {
        config = JSON.parse(content);
    } catch (e) {
        return null;
    }

    if (!config || !config.rules) {
        return null;
    }

    config.rules.forEach(rule => {
        if (rule.rule) {
            rule.rule = rule.rule.map(item => {
                const [type, from, to] = (item as any).split(' ');
                return {type, from, to}
            })
        }
    });

    return config;
}

export {loadRouteConfig, RouteConfig, RouteRule}
