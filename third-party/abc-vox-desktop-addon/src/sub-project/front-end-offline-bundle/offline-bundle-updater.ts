/**
 * 用于管理前端离线资源包下载，加载，路由
 */
import {
    abcTraceReport,
    Completer,
    CryptoUtils,
    delay,
    FileUtils,
    logger,
    NetworkUtils,
    timeLog,
    timeoutActionAndPeriodCallback
} from "../common";
import {OfflineBundleHelper} from "./offline-bundle-helper";
import {ILocalBundleUpdateInfo, IPluginConf, IUpdateLocalOffBundleResult} from "./offline-bundle-common-data";
import {abcHost, hostRequire} from "../../abc-host";
import {Constants, GrayEnv, GrayEnvMap, kProxyRegionHosts, kUrlConfig} from "../../constants";
import {abcConfig} from "../conf/abc-conf";
import {AbcAppHelper} from "../../abc-app";


interface PluginUpdateInfo {
    appId: string;
    name: string;
    env: string;
    version: string;
    url: string;
    url2: string; //备份url
    md5: string;
    patchUrl: string;
    patchUrl2: string;
    patchMd5: string;

    patchTargetVersion: string;
    forceUpdate: number;
    description: string;
    buildTime: string;
    patchFileSize?: number; //增量包大小
    fileSize?: number; //全量包大小
    asarMd5?: string;
}

export interface IBundleUpdateProgressOptions {
    bundleCount: number; //需要更新的bundle数量
    bundleIndex: number; //当前更新的bundle索引
    bundleName: string; //当前bundle名称
    currentBundleDownloadBytes: number; //当前bundle已更新的字节数
    currentBundleTotalBytes: number; //当前bundle总字节数
    currentTotalDownloadBytes: number; //所有已更新的bundle的总字节数
    totalBytes: number; //所有bundle的总字节
    successCount: number,
    failCount: number,
    finish: boolean; //是否全部完成
    error?: string[]; //是否出错
    totalUnzipTime: number;  //解压总时间
    totalDownloadTime: number; //下载总时间
    totalPatchTime: number; //patch总时间
}

export interface IBundleUpdateProgress {
    (options: IBundleUpdateProgressOptions): void;
}


interface IUpdatePluginInfoWithConfResult {
    success: boolean;
    totalDownloadSize: number;
    patchDiffFileSize: number;
    error?: string;
    downloadTime:number; //下载时间
    patchTime:number; //patch还原时间
    unzipTime:number; //解压时间
}

interface IUpdatePluginCallback {
    (finish: boolean, currentBytes?: number, totalBytes?: number): void;
}


const TAG = "前端离线资源管理 ";


interface IPluginUpdateProgressInfo {
    completer: Completer<IUpdatePluginInfoWithConfResult>;
    //正在等待的回调
    pendingCallbacks: IUpdatePluginCallback[];
}

export interface IPluginInfo {
    buildTime: string;
    updateTime: string;
    fileSize: number;
    name: string;
    version: string;
    onlineVersion: string;
    rootDir: string;
}

export class OfflineBundleUpdater {
    // bundle helper
    private _bundleHelper!: OfflineBundleHelper;
    private _updatingBundles = new Map<string, IPluginUpdateProgressInfo>();

    //计算region+env下是否完成一次完整的更新，用于控制没有完整更新时，不对已经下载的插件进行最终替换更新
    private _updateCompleteFinishFlags = new Set<string>();

    //全局插件
    private _splashWindow: any;

    private _pluginUpdateInfos = new Map<string, IPluginInfo>();

    // 强制更新完成器
    private _forceUpdateFinishCompleter: Map<string, Completer<void>> = new Map<string, Completer<void>>();


    public setBundleHelper(bundleHelper: OfflineBundleHelper) {
        this._bundleHelper = bundleHelper;
    }


    /**
     * app ready后初始化
     */
    public async init() {
        //如果当前不是entryUrl不是abcyun，则直接返回
        if (!abcConfig.entryUrl?.startsWith(Constants.abcyunScheme)) {
            return;
        }
    }

    public clearUpdatingBundles() {
        this._updatingBundles.clear();
    }


    /**
     * 检查对应region和env是否已经完成一次完整的更新
     * @param region
     * @param env
     */
    public checkRegionEnvUpdateHasComplete(region: string, env: GrayEnv) {
        const key = this._updateCompleteFinishKey(region, env);
        return this._updateCompleteFinishFlags.has(key);
    }


    public async updateLocalOfflineBundle(forceUpdate: boolean = false): Promise<IUpdateLocalOffBundleResult> {
        logger.log(TAG, `开始替换已经下载的bundle, 使最新版本生效`)
        //检查已经准备好的bundle(已经存在于xxx_new目录中了)，如果有就rename到正式目录
        const bundles = this._bundleHelper.bundles();
        const updateBundles: ILocalBundleUpdateInfo[] = [];

        const key = this._updateCompleteFinishKey(abcHost.abcEnvironment.region(), abcHost.abcEnvironment.grayEnv());
        const canUpdate = forceUpdate ? forceUpdate : this._updateCompleteFinishFlags.has(key);

        for (const bundle of bundles) {
            const name = bundle.name;
            if (!name) continue;

            //全局插件允许更新
            const isGlobal = this._bundleHelper.globalPlugins().has(name);
            if (!canUpdate && !isGlobal) {
                continue;
            }

            const updateInfo = this._updateLocalOfflineBundleByName(name);
            updateInfo && updateBundles.push(updateInfo);
        }

        const result = {
            updateBundles: updateBundles,
            hasUpdate: updateBundles.length > 0,
        }

        logger.log(TAG, `更新完成 ${JSON.stringify(result)}`);

        return result;
    }


    public async checkOfflineBundleUpdate(name: string): Promise<IUpdateLocalOffBundleResult> {
        logger.log(TAG, `检查指定插件更新信息 ${name}`);
        const result = {
            updateBundles: [],
            hasUpdate: false
        }

        //检查已经准备好的bundle(已经存在于xxx_new目录中了)，如果有就rename到正式目录
        const grayEnv = abcHost.abcEnvironment.grayEnv();
        const region = abcHost.abcEnvironment.region();
        const targetConfFile = this._bundleHelper.pluginConfFile(name, grayEnv, region);
        const newConfFile = targetConfFile + "_new";

        let oldVersion = '';
        let newVersion = '';
        if (!FileUtils.fileExist(newConfFile)) {
            return result;
        }

        const localConf = this._bundleHelper.localBundleConf(name, grayEnv, region) ?? {name: name} as IPluginConf;
        newVersion = localConf.version;

        if (FileUtils.fileExist(targetConfFile)) {
            const localConf = this._bundleHelper.inUseLocalBundleConf(name, grayEnv, region) ?? {name: name} as IPluginConf;
            oldVersion = localConf.version;
        }
        result.hasUpdate = true;
        result.updateBundles.push({
            name: name,
            oldVersion: oldVersion,
            newVersion: newVersion
        })

        return result;
    }


    public async updateLocalOfflineBundleByName(name: string): Promise<IUpdateLocalOffBundleResult> {
        logger.log(TAG, `开始替换已经下载的bundle${name}, 使最新版本生效`);

        const updateInfo = this._updateLocalOfflineBundleByName(name);
        const updateBundles: ILocalBundleUpdateInfo[] = [];
        if (updateInfo) {
            updateBundles.push(updateInfo);
        }
        const result = {
            updateBundles: updateBundles,
            hasUpdate: updateBundles.length > 0,
        }

        logger.log(TAG, `更新完成 ${name} ${JSON.stringify(result)}`);
        return result;
    }


    public _updateLocalOfflineBundleByName(name: string): ILocalBundleUpdateInfo | null {
        //检查已经准备好的bundle(已经存在于xxx_new目录中了)，如果有就rename到正式目录
        const grayEnv = abcHost.abcEnvironment.grayEnv();
        const region = abcHost.abcEnvironment.region();
        const targetDir = this._bundleHelper.pluginDir(name, grayEnv, region);
        const newDir = targetDir + "_new";
        const targetConfFile = this._bundleHelper.pluginConfFile(name, grayEnv, region);
        const newConfFile = targetConfFile + "_new";
        if (!FileUtils.fileExist(newConfFile)) {
            return null;
        }

        if (FileUtils.dirExistsSync(targetDir, false)) {
            FileUtils.deleteDirSync(targetDir);
        }

        if (FileUtils.dirExistsSync(newDir, false)) {
            try {
                hostRequire('fs').renameSync(newDir, targetDir);
            } catch (e) {
                logger.logToServer(TAG, `rename ${newDir} to ${targetDir} 失败`, e);
                //rename失败，直接copy
                FileUtils.mvDirSyncIgnoreError(newDir, targetDir);
                FileUtils.deleteDirSync(newDir);
                logger.logToServer(TAG, `rename ${newDir} to ${targetDir} 失败，直接copy`, e);
            }
        }

        let oldVersion = '';
        let newVersion: string = '';
        if (FileUtils.fileExist(targetConfFile)) {
            const localConf = this._bundleHelper.inUseLocalBundleConf(name, grayEnv, region) ?? {name: name} as IPluginConf;
            oldVersion = localConf.version;
            FileUtils.deleteFileSync(targetConfFile);
        }

        if (FileUtils.fileExist(newConfFile)) {
            hostRequire('fs').renameSync(newConfFile, targetConfFile);
            const localConf = this._bundleHelper.localBundleConf(name, grayEnv, region) ?? {name: name} as IPluginConf;
            newVersion = localConf.version;
            const info = this.getPluginUpdateInfo(name, grayEnv);
            info.version = localConf.version;
            info.updateTime = localConf.updateTime;
            info.buildTime = localConf.buildTime;
            info.rootDir = this._bundleHelper.pluginDir(name, grayEnv, region);
        }

        this._bundleHelper.resetInUseLocalBundleConf(name, grayEnv, region);
        return {name: name, oldVersion: oldVersion, newVersion: newVersion};
    }

    /**
     * 强制更新插件
     */
    public async forceUpdatePlugin(plugin: string, grayEnv: GrayEnv, region: string) {
        const key = `${region}-${grayEnv}-${plugin}`;
        if (this._forceUpdateFinishCompleter.has(key)) {
            logger.log(TAG, `插件${plugin}正在更新中，等待更新完成`);
            return this._forceUpdateFinishCompleter.get(key)!.promise;
        }
        logger.log(TAG, `开始强制更新插件${plugin}`);
        const completer = new Completer<void>();
        this._forceUpdateFinishCompleter.set(key, completer);
        return this.updatePlugin(plugin, grayEnv, region).then(() => {
            completer.resolve();
        }).catch((e) => {
            completer.reject(e);
        }).finally(() => {
            this._forceUpdateFinishCompleter.delete(key);
        });
    }

    /**
     * 更新插件忽略错误
     */
    public async updatePlugin(plugin: string, grayEnv: GrayEnv, region: string) {
        const isGlobal = this._bundleHelper.globalPlugins().has(plugin);
        let _region = region;
        if (isGlobal) {
            grayEnv = GrayEnv.PROD;
            _region = '';
        }
        const conf = this._bundleHelper.localBundleConf(plugin, grayEnv, region);
        const urlPrefix = kUrlConfig[abcConfig.env];
        //发起请求，检查是否有更新
        const url = `${urlPrefix}/api/v2/app/pluginv2/latest?t=${new Date().getTime()}&appId=${Constants.kOfflineBundleAppID}&hostVersion=${abcHost.version}&name=${plugin}&version=${conf.version ?? ''}&env=${grayEnv}&region=${_region}&fileFormat=1`;
        const updateInfo = await NetworkUtils.getAsStringAutoCookie(url);
        const pluginInfo: PluginUpdateInfo = JSON.parse(updateInfo).data;
        if (!pluginInfo.version) {
            logger.logToServer(`未找到插件 ${plugin} 的配置信息`);
            return;
        }

        await this.updatePluginInfoWithConf(plugin, grayEnv, region, conf, pluginInfo);
    }

    /**
     * 更新global插件
     */
    public async updateGlobalPlugins() {
        logger.log(TAG, `开始更新global插件`);

        const startTime = new Date().getTime();
        await this.updatePluginsWithGrayEnv(Array.from(this._bundleHelper.globalPlugins()), GrayEnv.PROD, false);

        const endTime = new Date().getTime();
        logger.logToServer(TAG, `更新global插件全部结束 ${GrayEnvMap[GrayEnv.PROD]}，耗时:${endTime - startTime}ms`);
    }


    /**
     * 更新指定插件
     * @param plugin
     * @param grayEnv
     * @param region
     * @param conf
     * @param pluginInfo
     * @param callback
     * @param traceId 用于日志输出时，方便后台查询
     * @private
     */
    private async updatePluginInfoWithConf(plugin: string, grayEnv: GrayEnv, region: string,
                                           conf: IPluginConf, pluginInfo: PluginUpdateInfo,
                                           callback?: IUpdatePluginCallback, traceId?: string)
        : Promise<IUpdatePluginInfoWithConfResult> {

        if (!traceId) {
            traceId = new Date().getTime().toString();
        }

        // 检查当前是否正在更新该插件了，如果已经在更新列表，加入等待列表，不触发更新
        const updateLockKey = this._pluginUpdateKey(plugin, grayEnv);
        let updatingInfo: IPluginUpdateProgressInfo | undefined = this._updatingBundles.get(updateLockKey);
        if (updatingInfo) {
            logger.log(TAG, traceId, `插件 ${plugin} granEnv=${grayEnv} region=${region}正在更新中，加入等待列表`);
            if (callback)
                updatingInfo.pendingCallbacks.push(callback);
            return updatingInfo.completer.promise;
        }


        // 新触发更新
        updatingInfo = {
            pendingCallbacks: [],
            completer: new Completer<IUpdatePluginInfoWithConfResult>(),
        };

        if (callback) {
            updatingInfo.pendingCallbacks.push(callback);
        }

        const result: IUpdatePluginInfoWithConfResult = {success: true, totalDownloadSize: 0, patchDiffFileSize: 0, unzipTime:0,
             downloadTime:0, patchTime:0};

        const key = this._pluginUpdateKey(plugin, grayEnv);
        this._updatingBundles.set(key, updatingInfo);


        logger.log(TAG, traceId, `开始加载插件 ${plugin}`);
        let startTime = new Date().getTime();
        //增量更新失败原因
        let patchUpdateFailedReason: string = '';

        //全量更新失败原因
        let fullUpdateFailedReason: string = '';

        let updateFailedReason = '';

        let updateSuccess = false;

        let patchUpdate = false;

        let downloadDiffTime = 0;
        let downloadFullTime = 0;
        let mergeDiffTime = 0;
        let unzipTime = 0;
        let oldVersion = '';
        let newVersion = '';
        let newZipFileSize = 0;
        let patchDiffFileSize = 0;

        let preCurrentBytes = 0;
        let preTotalBytes = 0;
        let targetConfFile = '';
        let targetDir = '';
        let bundleDownloadUrl = '';
        let patchDownloadUrl = '';


        //如果之前从来没有下载过，直接解压到指定目录，否则先解压到临时目录，因为当前前资源可能正在使用
        let directUpdate = !conf?.version;
        const notifyProgress = (currentBytes: number, totalBytes: number) => {
            preCurrentBytes = currentBytes;
            preTotalBytes = totalBytes;
            for (let callback of updatingInfo!.pendingCallbacks) {
                callback(false, currentBytes, totalBytes);
            }
        };

        const finish = (error: any) => {
            callback && callback(true, preCurrentBytes, preTotalBytes);
            // this._pluginReadyResolve(plugin);
            result.success = !error;
            result.error = error;
            updatingInfo!.completer.resolve(result);
            for (let callback of updatingInfo!.pendingCallbacks) {
                callback(true, preCurrentBytes, preTotalBytes);
            }

            this._updatingBundles.delete(updateLockKey);
        }

        logger.log(TAG, traceId, `本地插件 ${plugin} 版本信息:${JSON.stringify(conf)}`);

        const reportUpdateResult = () => {
            const now = Date.now()
            abcTraceReport.addLog('offlineBundleUpdateAsar', {
                bundleName: plugin,
                env: GrayEnvMap[grayEnv],
                region: region,
                success: updateSuccess,
                totalTime: now - startTime,
                patchDownloadUrl: patchDownloadUrl,
                bundleDownloadUrl: bundleDownloadUrl,
                patchUpdate: patchUpdate,
            });

            let updateInfo = `traceId: ${traceId} 更新插件 ${plugin} 更新结束 ${GrayEnvMap[grayEnv]}, ${oldVersion}-> ${newVersion},更新${updateSuccess ? '成功' : '失败'}, ${patchUpdate ? '增量更新' : '全量更新'}, 耗时:${now - startTime}ms,`;
            updateInfo += `增量包(${(patchDiffFileSize / 1024).toFixed(2)}KB)下载耗时:${downloadDiffTime}ms, 增量包还原耗时:${mergeDiffTime}ms, 全量包(${(newZipFileSize / 1024).toFixed(2)}KB)下载耗时:${downloadFullTime}ms, 解压耗时:${unzipTime}ms`;

            if (updateFailedReason) {
                updateInfo += `, 更新失败原因:${updateFailedReason}`;
            }

            if (patchUpdateFailedReason) {
                updateInfo += `, 增量更新失败原因:${patchUpdateFailedReason}`;
            }
            if (fullUpdateFailedReason) {
                updateInfo += `, 全量更新失败原因:${fullUpdateFailedReason}`;
            }
            if (updateSuccess)
                logger.logToServer(TAG, updateInfo);
            else
                logger.logToServerWithRecentLogs(TAG, updateInfo);
        }

        try {
            logger.log(TAG, traceId, `本地插件 ${plugin} 最新版本信息:${JSON.stringify(pluginInfo)}`);
            if (pluginInfo.version === conf.version) {
                logger.log(TAG, traceId, `插件 ${plugin} 已是最新版本，无需更新`);
                finish(null);
                result.success = false;
                return result;
            }


            oldVersion = conf.version;
            newVersion = pluginInfo.version;

            const path = abcHost.hostRequire('path');
            const fs = abcHost.hostRequire('fs');
            logger.log(TAG, traceId, `本地插件 ${plugin}，需要更新`);

            const asarName = (conf)=>{
                return conf.name + "-" + conf.version + ".asar";
            };

            const oldAsarFile = path.resolve(this._bundleHelper.pluginDir(plugin, grayEnv, region) + "/" + asarName(conf));

            let targetDir = this._bundleHelper.pluginDir(plugin, grayEnv, region);
            if (!directUpdate) {
                targetDir += "_new";
            }

            if (!fs.existsSync(targetDir)) {
                fs.mkdirSync(targetDir, { recursive: true });
            }


            //从压缩包里解析出来的asar文件，名称${name}.asar
            const asarFile = path.resolve(targetDir, `${pluginInfo.name}.asar`);

            //最终要更新生成的asar文件
            const finalAsarFile = path.resolve(targetDir, asarName(pluginInfo));

            //如果zip文件存在，但是md5不一致，删除文件，之前下载过，下载到一半退出可能出现这种情况
            if (FileUtils.fileExist(asarFile) && await CryptoUtils.fileMd5(asarFile) !== pluginInfo.asarMd5) {
                FileUtils.deleteFileSync(asarFile);
            }

            //先尝试走增量更新
            do {
                const oldAsarFileExist = FileUtils.fileExist(oldAsarFile)
                if (!oldAsarFileExist || !pluginInfo.patchUrl || !pluginInfo.patchMd5) {
                    logger.log(TAG, traceId, `${plugin} 更新，不满足增量更新条件，oldAsarFileExist:${oldAsarFileExist}, patchUrl:${pluginInfo.patchUrl}, patchMd5:${pluginInfo.patchMd5}`);
                    patchUpdateFailedReason = `不满足增量更新条件，oldAsarFileExist:${oldAsarFileExist}, patchUrl:${pluginInfo.patchUrl}, patchMd5:${pluginInfo.patchMd5}`;
                    break;
                }

                //从url里获取文件名
                const fileName = pluginInfo.patchUrl.substring(pluginInfo.patchUrl.lastIndexOf('/') + 1);
                const diffFile = path.resolve(this._bundleHelper.pluginConfDir(plugin, grayEnv, region) + "/" + fileName);

                //判断diff文件是否存在，如果存在，但是md5不一致，删除文件up
                if (FileUtils.fileExist(diffFile) && await CryptoUtils.fileMd5(diffFile) !== pluginInfo.patchMd5) {
                    FileUtils.deleteFileSync(diffFile);
                }

                logger.log(TAG, traceId, `开始下载插件 ${plugin}， 增量包:${diffFile}`);
                let downloadDiffTimeStart = new Date().getTime();
                let success = true;
                const privatePatchUrl = this._createBundlePatchUrl(plugin, region, grayEnv, fileName);
                const urls = [];
                // 重庆之类专网地址下载失败后，也不要走原地址下载，解决带宽打满问题
                if (privatePatchUrl) {
                    urls.push(privatePatchUrl);
                } else {
                    urls.push(...[pluginInfo.patchUrl, pluginInfo.patchUrl2]);
                }

                const patchDownloadResult = await this._downloadFile({
                    urls: urls,
                    filePath: diffFile,
                    md5: pluginInfo.patchMd5,
                    onProgress: (currentBytes, totalBytes) => {
                        result.patchDiffFileSize = currentBytes;
                        notifyProgress(currentBytes, totalBytes!);
                    }
                }).catch(e => {
                    logger.log(TAG, traceId, `${plugin} 增量包下载失败:${e}`);
                    patchUpdateFailedReason = `增量包下载失败:${e}`;
                    success = false;
                    return {
                        success: false,
                        url: ''
                    }
                });

                patchDownloadUrl = patchDownloadResult?.url;
                if (!success) {
                    logger.log(TAG, traceId, `${plugin} 增量包下载失败，删除文件:${diffFile}`);
                    FileUtils.deleteFileSync(diffFile);
                    break;
                }

                patchDiffFileSize = fs.statSync(diffFile).size;

                downloadDiffTime = new Date().getTime() - downloadDiffTimeStart;
                logger.log(TAG, traceId, `开始下载插件 ${plugin}， 增量包下载成功`);

                //使用bsdiff合并生成最新包
                const bsdiff = abcHost.hostRequire('bsdiff-node');
                let mergeDiffTimeStart = new Date().getTime();
                await bsdiff.patch(oldAsarFile, asarFile, diffFile);

                //计算生成的文件的md5与目标md5比较,如果不一致
                const fileMd5 = await CryptoUtils.fileMd5(asarFile);
                if (fileMd5 !== pluginInfo.asarMd5) {
                    logger.logToServer(TAG, traceId, `增量包合并失败，md5不一致，删除文件:${asarFile}`);
                    FileUtils.deleteFileSync(asarFile);
                    patchUpdateFailedReason = `增量包合并失败，md5不一致, 生成文件md5:${fileMd5}, 目标md5:${pluginInfo.asarMd5}`;
                }

                patchUpdate = true;
                FileUtils.deleteFileSync(diffFile);
                mergeDiffTime = new Date().getTime() - mergeDiffTimeStart;
            } while (0);

            //如果增量更新失败，尝试全量更新
            if (!FileUtils.fileExist(asarFile)) {
                //下载更新
                logger.log(TAG, traceId, `开始下载插件 ${plugin}, ${asarFile}`);
                patchUpdate = false;
                let success = true;
                let fullDownloadTimeStart = new Date().getTime();

                const tmpZipFile = path.resolve(this._bundleHelper.pluginConfDir(plugin, grayEnv, region) + "/" + pluginInfo.version + ".zip");

                //从zipFile
                const fileName = hostRequire('path').basename(tmpZipFile);
                const privateBundleUrl = this._createBundleUrl(plugin, region, grayEnv, fileName);
                const urls = [];
                // 重庆之类专网地址下载失败后，也不要走原地址下载，解决带宽打满问题
                if (privateBundleUrl) {
                    urls.push(privateBundleUrl);
                }
                else {
                    urls.push(...[pluginInfo.url, pluginInfo.url2]);
                }

                const bundleDownloadResult = await this._downloadFile({
                    urls: urls,
                    filePath: tmpZipFile,
                    md5: pluginInfo.md5,
                    onProgress: (currentBytes, totalBytes) => {
                        result.totalDownloadSize = currentBytes;
                        notifyProgress(currentBytes, totalBytes!);
                    }
                }).catch(err => {
                    success = false;
                    logger.logToServer(TAG, traceId, `${plugin} 全量包下载失败:${err}， url = ${pluginInfo.url}`);
                    fullUpdateFailedReason = `全量包下载失败:${err}`;
                    return {
                        success: false,
                        url: ''
                    }
                });


                result.totalDownloadSize += result.patchDiffFileSize;
                bundleDownloadUrl = bundleDownloadResult.url;
                downloadFullTime = new Date().getTime() - fullDownloadTimeStart;
                if (!success) {
                    finish(fullUpdateFailedReason);
                    result.success = false;
                    result.error = fullUpdateFailedReason;
                    reportUpdateResult();
                    return result;
                }


                //解压到临时目录
                let unzipTimeStart = new Date().getTime();
                if (FileUtils.dirExistsSync(targetDir, false)) {
                    logger.log(TAG, traceId, `删除旧的插件:${plugin}, ${targetDir}`);
                    FileUtils.deleteDirSync(targetDir);
                }

                FileUtils.createDirSync(targetDir, true);

                //解压
                const AdmZip = abcHost.hostRequire('adm-zip');
                const zip = new AdmZip(tmpZipFile);
                logger.log(TAG, traceId, `开始解压插件${plugin}, ${targetDir}`);
                newZipFileSize = fs.statSync(tmpZipFile).size;


                await new Promise((resolve, reject) => {
                    const mb = Math.max(1, Math.round(newZipFileSize / 1024 / 1024));
                    const timeInMills = 3000 * mb;
                    let timer = setTimeout(() => {
                        //如果文件夹不为空，也认为成功，后续文件找不到时会尝试从压缩包里解压
                        if (FileUtils.isEmptyDirSync(targetDir)) {
                            reject(`解压插件 ${plugin} 超时,${timeInMills}ms, fileSize =${newZipFileSize}`);
                        } else {
                            resolve(null);
                        }
                    }, timeInMills);
                    zip.extractAllToAsync(targetDir, true, false, (err: any) => {
                        resolve(null);
                        clearTimeout(timer);
                    });
                });

                FileUtils.deleteFileSync(tmpZipFile);

                unzipTime = new Date().getTime() - unzipTimeStart;
            }

            const asarFileMd5 = await CryptoUtils.fileMd5(asarFile)
            //检查生成的asar文件是否正确
            if (!FileUtils.fileExist(asarFile) || (asarFileMd5 !== pluginInfo.asarMd5)) {
                logger.log(TAG, traceId, `插件 ${plugin} 更新失败, md5不一致: ${asarFileMd5} !== ${pluginInfo.asarMd5}`);
                result.error = `插件 ${plugin} 更新失败, md5不一致: ${asarFileMd5} !== ${pluginInfo.asarMd5}`;
                finish(result.error);
                result.success = false;

                //清理临时文件
                FileUtils.deleteDirSync(targetDir);
                return result;
            }


            //asarFile-> finalAsarFile
            if (FileUtils.fileExist(finalAsarFile)) {
                FileUtils.deleteFileSync(finalAsarFile);
            }

            fs.renameSync(asarFile, finalAsarFile);

            //更新配置
            conf.version = pluginInfo.version;
            conf.md5 = pluginInfo.md5;
            conf.asarMd5 = pluginInfo.asarMd5;
            conf.updateTime = new Date().getTime().toString();

            //如果buildTime空，尝试从version里获取，version格式为timestamp-xxx e.g: 1715257177469-latest
            conf.buildTime = pluginInfo.buildTime;
            if (!conf.buildTime && pluginInfo.version && pluginInfo.version.indexOf("-") > 0) {
                const buildTime = pluginInfo.version.split('-')[0];
                if (buildTime) {
                    conf.buildTime = buildTime;
                }
            }
            targetConfFile = this._bundleHelper.pluginConfFile(plugin, grayEnv, region);
            if (!directUpdate) {
                targetConfFile += "_new";
            }
            await FileUtils.writeFileAsString(targetConfFile, JSON.stringify(conf));


            logger.log(TAG, traceId, ` 更新插件 ${plugin} 更新成功`);;

            if (directUpdate) {
                const info = this.getPluginUpdateInfo(plugin, grayEnv);
                info.version = conf.version;
                info.updateTime = conf.updateTime;
                info.buildTime = conf.buildTime;
            }

            updateSuccess = true;
        } catch (e) {
            //更新失败，删除临时文件
            if (FileUtils.dirExistsSync(targetDir, false)) {
                FileUtils.deleteDirSync(targetDir);
            }

            if (FileUtils.fileExist(targetConfFile)) {
                FileUtils.deleteFileSync(targetConfFile);
            }

            updateFailedReason = ` 更新插件 ${plugin} 更新失败: ${e}`;
            logger.logToServer(TAG, traceId, ` 更新插件 ${plugin} 更新失败: ${e}`);
        }

        finish(updateFailedReason);
        result.success = updateSuccess;
        result.error = updateFailedReason;
        result.downloadTime = downloadFullTime + downloadDiffTime;
        result.patchTime = patchUpdate ? mergeDiffTime : 0;
        result.unzipTime = unzipTime;


        if (directUpdate)
            this._bundleHelper.resetInUseLocalBundleConf(plugin, grayEnv, region);

        reportUpdateResult();
        return result;
    }

    private _pluginUpdateKey(plugin: string, grayEnv: GrayEnv) {
        if (this._bundleHelper.globalPlugins().has(plugin))
            return `${plugin}`;
        else {
            return `${plugin}_${abcHost.abcEnvironment.region()}_${grayEnv}`;
        }
    }

    public getPluginUpdateInfo(plugin: string, grayEnv: GrayEnv): IPluginInfo {
        const key = this._pluginUpdateKey(plugin, grayEnv);
        if (!this._pluginUpdateInfos.has(key)) {
            this._pluginUpdateInfos.set(key, {name: plugin} as IPluginInfo);
        }

        return this._pluginUpdateInfos.get(key)!;
    }


    /**
     * 下载批量文件，如果主url失败，尝试使用备份url
     * @return {Promise<{success: boolean, url: string}>}
     */
    private async _downloadFile(options:
                                    {
                                        urls: string[],
                                        filePath: string,
                                        md5: string,
                                        onProgress?: (currentBytes: number, totalBytes: number) => void
                                    }): Promise<{ success: boolean, url: string }> {
        const {urls, filePath, md5, onProgress} = options;
        let success = false;
        let error: any;
        let finalUrl = '';
        for (const url of urls) {
            if (!url) continue;
            finalUrl = url;
            success = true;
            await NetworkUtils.downloadFileWithChunk({
                url: url,
                filePath: filePath,
                md5: md5,
                chunkSize: 5 * 1024 * 1024,
                onProgress: onProgress
            }).catch(e => {
                logger.logToServer(TAG, `${url} 下载失败:${e}`);
                error = e;
                success = false;
            });

            if (!success) {
                success = true;
                await NetworkUtils.downloadFile({
                    url: url,
                    filePath: filePath,
                    md5: md5,
                    onProgress: onProgress
                }).catch(e => {
                    logger.logToServer(TAG, `${url} 下载失败:${e}`);
                    error = e;
                    success = false;
                });
            }

            if (success) break;

            //报了一个 EPERM: operation not permitted，可能和此有关，文件还没有来得急关闭
            await delay(200);
        }

        if (!success)
            throw error;

        return {
            success: success,
            url: finalUrl
        };
    }

    public async updateNonGlobalPluginsWithGrayEnv(grayEnv: GrayEnv, showLoading = false, onProgress?: IBundleUpdateProgress, waitLoadingForLoginSuccess?: Completer<boolean>, scene?: string) {
        const bundles = this._bundleHelper.bundles().filter(it => !it.global).map(it => it.name);

        const region = abcHost.abcEnvironment.region();
        const key = this._updateCompleteFinishKey(region, grayEnv);

        let lastProgress: IBundleUpdateProgressOptions | null = null;

        const onProgressIntercept = (options: IBundleUpdateProgressOptions) => {
            if (onProgress) onProgress(options);
            lastProgress = options;
        };

        const startTime = new Date().getTime();

        const result = await this.updatePluginsWithGrayEnv(bundles, grayEnv, showLoading, onProgressIntercept, waitLoadingForLoginSuccess)
            .finally(() => {
                this._updateCompleteFinishFlags.add(key);
            });


        const timeCost = new Date().getTime() - startTime;
        if (lastProgress && lastProgress.bundleCount > 0 && lastProgress.successCount == lastProgress.bundleCount) {
            abcTraceReport.addLog(`offlineBundleBatchUpdate-${scene??""}`, {
                region: region,
                env: GrayEnvMap[grayEnv],
                bundleCount: lastProgress.bundleCount,
                totalTime: timeCost,
                totalBytes: lastProgress.totalBytes,
                currentTotalDownloadBytes: lastProgress.currentTotalDownloadBytes,
                totalUnzipTime: lastProgress.totalUnzipTime,
                totalDownloadTime: lastProgress.totalDownloadTime,
                totalPatchTime: lastProgress.totalPatchTime
            });
        }

        return result;
    }

    private _updateCompleteFinishKey(region: string, env: GrayEnv): string {
        return `${region}:${env}`;
    }

    /**s
     * 更新指定分区
     * @param plugins 插件列表
     * @param grayEnv
     * @param showLoading
     * @param onProgress 进度回调
     */
    public async updatePluginsWithGrayEnv(plugins: string[], grayEnv: GrayEnv, showLoading = false, onProgress?: IBundleUpdateProgress, waitLoadingForLoginSuccess?: Completer<boolean>) {
        const traceId = new Date().getTime().toString();
        const kTimeLog = `插件更新-${GrayEnvMap[grayEnv]}-${traceId}-showLoading:${showLoading}`;
        logger.log(TAG, traceId, `开始批量更新插件 grayEnv=${GrayEnvMap[grayEnv]}, showLoading = ${showLoading}`);
        timeLog.time(kTimeLog);
        const timeCostStat: {
            readLocalConf: number,
            getOnlineInfo: number,
            pluginsConst: { name: string, cost: number }[],
            totalCostTime: number,
        } = {
            readLocalConf: 0,
            getOnlineInfo: 0,
            pluginsConst: [],
            totalCostTime: 0,
        };


        const region = abcHost.abcEnvironment.region();
        //收集所有本地bundle信息，并发起请求，检查是否有更新
        const allBundleConf: IPluginConf[] = plugins?.map(it => {
            const localConf = this._bundleHelper.localBundleConf(it, grayEnv, region) ?? {name: it} as IPluginConf;
            const info = this.getPluginUpdateInfo(it, grayEnv);
            info.version = localConf.version;
            info.updateTime = localConf.updateTime;
            info.buildTime = localConf.buildTime;
            info.rootDir = this._bundleHelper.pluginDir(it, grayEnv, region);

            return localConf
        }) ?? [];

        timeCostStat.readLocalConf = timeLog.timeLog(kTimeLog, "读取本地配置").cost;
        const urlPrefix = kUrlConfig[abcConfig.env];
        const url = `${urlPrefix}/api/v2/app/pluginv2/latest/batch?t=${traceId}`;
        const req: {
            list: { appId: string, name: string, hostVersion: string, env: GrayEnv, region: string, version: string }[]
        } = {list: []};

        const hostVersion: string = abcHost.version;
        req.list = allBundleConf.map(it => {
            const isGlobal = this._bundleHelper.globalPlugins().has(it.name);
            return {
                appId: Constants.kOfflineBundleAppID,
                name: it.name,
                hostVersion: hostVersion,
                env: grayEnv,
                region: isGlobal ? null : region,
                version: it.version ?? '',
                fileFormat: 1 //asar format
            }
        })


        const updateInfo = await NetworkUtils.post(url, {
            body: JSON.stringify(req),
            headers: {
                "Content-Type": "application/json"
            }
        }).catch(error => {
            logger.logToServer(TAG, `请求更新列表失败:${error}, url = ${url}`);
            return '{}';
        });


        logger.log(TAG, traceId, `更新列表: ${updateInfo}`);
        const updateInfos: PluginUpdateInfo[] = JSON.parse(updateInfo).data?.list ?? [];
        const allBundleConfMap = new Map<string, IPluginConf>();
        allBundleConf.forEach(it => {
            allBundleConfMap.set(it.name, it);
        });

        const needUpdateBundleInfos = updateInfos.filter(it => {
            if (!it.name)
                return false;

            const info = this.getPluginUpdateInfo(it.name, grayEnv);
            info.onlineVersion = it.version;
            return allBundleConfMap.get(it.name)?.version !== it.version;
        });

        const progress: IBundleUpdateProgressOptions = {
            bundleCount: 0,
            bundleIndex: 0,
            bundleName: '',
            currentBundleDownloadBytes: 0,
            currentBundleTotalBytes: 0,
            currentTotalDownloadBytes: 0,
            successCount: 0,
            failCount: 0,
            totalBytes: 0,
            finish: false,
            error: null,
            totalUnzipTime: 0,
            totalDownloadTime: 0,
            totalPatchTime: 0
        };

        timeLog.timeLog(kTimeLog, "更新列表");
        timeCostStat.getOnlineInfo = timeLog.timeLog(kTimeLog, "读取本地配置").cost;
        if (needUpdateBundleInfos.length === 0) {
            progress.finish = true;
            onProgress?.(progress);
            timeLog.timeEnd(kTimeLog);
            return;
        }


        if (showLoading)
            await this._showLoadingView();

        const splashWindow = this._splashWindow;
        const onProgressIntercept: IBundleUpdateProgress = (options) => {
            if (splashWindow && !splashWindow.isDestroyed()) {
                splashWindow.webContents.send(`__update_offline_bundle_update__`, options);
            }

            onProgress?.(options);
        }


        let count = needUpdateBundleInfos.length;

        //计算总的字节数
        for (let updateBundleInfo of needUpdateBundleInfos) {
            progress.totalBytes += (updateBundleInfo.patchFileSize ?? updateBundleInfo.fileSize ?? 0);
        }

        //离线包下载完后不代表更新完成，还需要等待登录成功，登录阶段模拟为10%进度
        let extraBytes = 0;
        if (waitLoadingForLoginSuccess) {
            extraBytes = Number((progress.totalBytes * 0.2).toFixed(0));
            progress.totalBytes += extraBytes;
        }

        progress.bundleCount = count;
        onProgressIntercept?.(progress);
        let totalCurrentBytes = 0;
        let successCnt = 0;
        let totalDownloadSize = 0;
        let totalPatchDiffFileSize = 0;
        let totalUnzipTime = 0;
        let totalDownloadTime = 0;
        let totalPatchTime = 0;

        for (let i = 0; i < needUpdateBundleInfos.length; i++) {
            let updateBundleInfo = needUpdateBundleInfos[i];
            const localConf = this._bundleHelper.localBundleConf(updateBundleInfo.name, grayEnv, region) ?? {name: updateBundleInfo.name} as IPluginConf;
            const fileSize = updateBundleInfo.patchFileSize ?? updateBundleInfo.fileSize ?? 0;
            progress.bundleIndex = i;
            progress.currentBundleTotalBytes = updateBundleInfo.patchFileSize ?? updateBundleInfo.fileSize ?? 0;
            progress.bundleName = updateBundleInfo.name;
            progress.currentBundleDownloadBytes = 0;
            onProgressIntercept?.(progress);
            const result = await this.updatePluginInfoWithConf(updateBundleInfo.name, grayEnv, region, localConf!, updateBundleInfo, (finish, currentBytes, totalBytes) => {
                currentBytes = currentBytes ?? 0;
                progress.currentBundleDownloadBytes = currentBytes;
                progress.currentTotalDownloadBytes = totalCurrentBytes + currentBytes;
                onProgressIntercept?.(progress);
            }, traceId);

            totalDownloadSize += result.totalDownloadSize ?? 0;
            totalPatchDiffFileSize += result.patchDiffFileSize ?? 0;
            totalUnzipTime += result.unzipTime ?? 0;
            totalDownloadTime += result.downloadTime;
            totalPatchTime += result.patchTime;

            if (result.success) {
                successCnt++;
                progress.successCount = successCnt;
            } else {
                progress.failCount++;
                if (!progress.error) {
                    progress.error = [];
                }
                progress.error.push(`${updateBundleInfo.name} 更新失败(${result.error})`);
            }

            onProgressIntercept?.(progress);
            totalCurrentBytes += fileSize;

            timeCostStat.pluginsConst.push({
                name: updateBundleInfo.name!,
                cost: timeLog.timeLog(kTimeLog, `更新插件 ${updateBundleInfo.name}`).cost
            })
        }

        if (splashWindow) {
            //等待登录成功后关闭loading
            if (waitLoadingForLoginSuccess) {
                const timeout = 10 * 1000;
                let preTotalDownloadBytes = progress.currentTotalDownloadBytes;
                timeoutActionAndPeriodCallback(async () => {
                        await waitLoadingForLoginSuccess.promise;
                    }, timeout,
                    300,
                    (timePassed) => {
                        progress.currentTotalDownloadBytes = preTotalDownloadBytes + Number(extraBytes * timePassed / timeout);
                        onProgressIntercept?.(progress);
                    }
                ).catch(error => {
                    logger.log(TAG, `等待登录成功超时:${error}`);
                }).finally
                (async () => {
                    progress.currentTotalDownloadBytes += progress.totalBytes;
                    onProgressIntercept?.(progress);
                    await delay(300);
                    if (splashWindow && !splashWindow.isDestroyed())
                        splashWindow.close()
                });
            } else if (!splashWindow.isDestroyed()) {
                splashWindow.close()
            }
        }

        timeCostStat.totalCostTime = timeLog.timeEnd(kTimeLog).cost;

        let updateLog = `traceId: ${traceId} 资源更新整体耗时 (${region}-${GrayEnvMap[grayEnv]}, showLoading=${showLoading}), ${timeCostStat.totalCostTime}ms, 读取本地配置:${timeCostStat.readLocalConf}ms, 获取在线信息:${timeCostStat.getOnlineInfo}ms`;
        updateLog += ` 更新插件总数: ${count} 成功:${successCnt} 失败:${count - successCnt} 总下载:${(totalDownloadSize / 1024 / 1024).toFixed(2)}MB(增量:${(totalPatchDiffFileSize / 1024 / 1024).toFixed(2)}MB) `;
        updateLog += ` 更新插件耗时: ${timeCostStat.pluginsConst.map(it => `${it.name}:${it.cost}`).join()}`;
        progress.successCount = successCnt;
        progress.failCount = count - successCnt;
        progress.finish = true;
        progress.totalDownloadTime = totalDownloadTime;
        progress.totalPatchTime = totalPatchTime;
        progress.totalUnzipTime = totalUnzipTime;

        onProgressIntercept?.(progress);
        logger.logToServer(TAG, updateLog);
    }

    private async _showLoadingView() {
        console.log('_showLoadingView start');
        if (abcHost.electronAbcApp.createSplashView)
            this._splashWindow = await AbcAppHelper.createProcessSplashWindow(abcHost.electronAbcApp.mainWindow);

        this._splashWindow.on('closed', () => {
            this._splashWindow = null;
        });
        this._splashWindow.show();
    }


    private _bundleUrlPrefix() {
       return "";
    }

    /**
     * 针对重庆等代理模式下，使用代理服务器上的bundle地址下载
     */
    private _createBundleUrl(name: string, region: string, grayEnv: GrayEnv, fileName: string) {
        const urlPrefix = this._bundleUrlPrefix();
        if (!urlPrefix) return;

        const isGlobal = this._bundleHelper.globalPlugins().has(name);
        if (isGlobal) {
            return `${urlPrefix}/global/${name}/bundles/${fileName}`;
        } else {
            return `${urlPrefix}/${region}/${GrayEnvMap[grayEnv]}/${name}/bundles/${fileName}`;
        }
    }

    /**
     * 针对重庆等代理模式下，使用代理服务器上的bundle地址下载
     */
    private _createBundlePatchUrl(name: string, region: string, grayEnv: GrayEnv, fileName: string) {
        const urlPrefix = this._bundleUrlPrefix();
        if (!urlPrefix) return;

        const isGlobal = this._bundleHelper.globalPlugins().has(name);
        if (isGlobal) {
            return `${urlPrefix}/global/${name}/patches/${fileName}`;
        } else {
            return `${urlPrefix}/${region}/${GrayEnvMap[grayEnv]}/${name}/patches/${fileName}`;
        }
    }
}
