import {offlineBundleManager} from "./offline-bundle-manager";
import {FileUtils, logger} from "../common";
import {abcHost} from "../../abc-host";

export class OfflineBundleTester {
    constructor() {
    }

    async start() {
        logger.log(`开始测试离线包更新功能`)
        const bundleUpdater = offlineBundleManager.bundleUpdater();
        const bundleHelper = offlineBundleManager.bundleHelper();
        const rootDir = bundleHelper.frontEndOfflineBundleDir(false);
        //测试100轮
        for (let i = 0; i < 100; i++) {
            logger.log(`第${i}轮测试`)

            //先清空目录
            logger.log(`清空目录 ${rootDir}`);
            if (FileUtils.dirExistsSync(rootDir, false)) {
                FileUtils.deleteDirSync(rootDir);
            }

            bundleUpdater.clearUpdatingBundles();
            const buildCount = bundleHelper.bundles().length;
            //触发更新
            await new Promise((resolve, reject) => {
                bundleUpdater.updateNonGlobalPluginsWithGrayEnv(abcHost.abcEnvironment.grayEnv(), false, (options) => {
                    if (options.finish) {
                        if (options.successCount === options.bundleCount && options.successCount === buildCount) {
                            resolve(true);
                        } else {
                            reject({...options, expectedCount: buildCount});
                        }
                    }
                });
            });
        }

        logger.log(`测试完成`);
    }
}