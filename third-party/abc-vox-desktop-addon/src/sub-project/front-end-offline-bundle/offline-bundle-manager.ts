/**
 * 用于管理前端离线资源包下载，加载，路由
 */
import {AddonPathUtils, Completer, delay, FileUtils, ignore, logger, NetworkUtils} from "../common";
import {loadRouteConfig, RouteConfig, RouteRule} from "./route-config";
import {Constants, GrayEnv, GrayEnvMap} from "../../constants";
import {pathToFileURL, fileURLToPath} from "url";
import {OfflineBundleHelper} from "./offline-bundle-helper";
import {
    IOfflineBundle,
    IOfflineBundleInfos,
    IUpdateLocalOffBundleResult,
    OfflineBundleUpdateMsg
} from "./offline-bundle-common-data";
import {IBundleUpdateProgress, IPluginInfo, OfflineBundleUpdater} from "./offline-bundle-updater";
import {abcHost, hostRequire} from "../../abc-host";
import {abcConfig} from "../conf/abc-conf";
import {requestTracker} from "../request-tracker"; // 导入 requestTracker

const kUpdateCheckInterval = 5 * 60 * 1000;  //定时检查更新5分钟

/**
 * 前端离线资源包管理f
 * 目录结构：
 * - front-end-offline-bundle
 *  - dev //环境,每个环境分为三个环境，pre, gray, prod
 *      - route-config-prod.json //路由配置
 *      - pre
 *          - pc
 *          - static-login
 *          ...
 *      - gray
 *          - pc
 *          - static-login
 *          ...
 *      - prod
 *          - pc
 *          - static-login
 *          ...
 *  - test //测试环境
 *      -route-config-prod.json
 *   ...
 *  - prod //生产环境
 *   ...
 *
 */

interface IUrlIntercerptResult {
    originalUrl: string;
    interceptedUrl: string;
    // plugin?: string;
    // intercept: boolean;
    route?: RouteRule;
    notFound?: boolean;
    redirect?: boolean;
    fileSize?: number;
}


const TAG = "前端离线资源管理 ";

type IPreInterceptHandler = (options: {
                                 target: string, //要访问的目标地址
                                 originalUrl: string, //原始请求地址, abcyun://www.abcyun.cn/xxxx
                                 pluginInfo: IPluginInfo, // 插件信息
                             }
) => Promise<{
    preventDefault: boolean, //是否继续以target继续后续处理, false:继续处理，true:以target为最终结果
    target: string //新的目标地址
}>;

class OfflineBundleManager {
    private _enable = false;
    private _routeConfig!: RouteConfig;
    private _bundleHelper: OfflineBundleHelper = new OfflineBundleHelper();
    private _bundleUpdater = new OfflineBundleUpdater();

    private _preInterceptHandlerCache = new Map<string, IPreInterceptHandler>();

    /**
     * api 规则
     */
    private _apiRules!: RouteRule[];

    // 全局离线资源包是否准备好
    private _globalBundleReady = new Completer<boolean>();

    // 不能使用memory-cache的资源列表
    private _noMemoryCacheList = [
        /^region\d+-(pre|gray|prod|test|dev)-his\.js$/ //用于匹配region1-pre-his.js之类js
    ];


    constructor() {
        this._bundleUpdater.setBundleHelper(this._bundleHelper);
    }


    public isEnable() {
        return this._enable;
    }

    public bundleHelper() {
        return this._bundleHelper;
    }

    public bundleUpdater() {
        return this._bundleUpdater;
    }

    /**
     * 如果离线资源包已经准备好了，这里进行最后的替换
     * @param forceUpdate {boolean} 是否强制更新
     */
    public updateLocalOfflineBundle(forceUpdate: boolean = false): Promise<IUpdateLocalOffBundleResult> {
        if (!this._enable)
            return Promise.resolve({hasUpdate: false});

        return this._bundleUpdater.updateLocalOfflineBundle(forceUpdate);
    }


    public checkOfflineBundleUpdate(name: string): Promise<IUpdateLocalOffBundleResult> {
        if (!this._enable)
            return Promise.resolve({hasUpdate: false});

        return this._bundleUpdater.checkOfflineBundleUpdate(name);
    }

    public updateLocalOfflineBundleByName(name: string): Promise<IUpdateLocalOffBundleResult> {
        if (!this._enable)
            return Promise.resolve({hasUpdate: false});

        return this._bundleUpdater.updateLocalOfflineBundleByName(name);
    }


    async onReceiveOfflineBundleUpdateMsg(updateMsg: OfflineBundleUpdateMsg) {
        if (!this._enable)
            return;

        logger.log(`onReceiveOfflineBundleUpdateMsg updateMsg = ${JSON.stringify(updateMsg)}`);
        const {appId, name, env, region} = updateMsg;
        if (appId !== Constants.kOfflineBundleAppID || env != abcHost.abcEnvironment.grayEnv() || region !== abcHost.abcEnvironment.region())
            return;

        // 收到更新消息，随机2分钟类一个时间
        setTimeout(() => {
            logger.log(`onReceiveOfflineBundleUpdateMsg updatePlugin = ${name}，oa socket io触发更新`);
            this._bundleUpdater.updatePlugin(name, env, region).then();
        }, Math.floor(2 * 60 * 1000 * Math.random()));
    }

    /**
     * 同步获取当前环境的插件列表
     * 支持新版本保持兼容
     */
    public getOfflineBundleInfoListSync() : IOfflineBundleInfos{
        return this._getOfflineBundleInfoListBase();
    }

    /**
     * 获取当前环境的插件列表
     */
    public async getOfflineBundleInfoList(): Promise<IOfflineBundleInfos> {
        return this._getOfflineBundleInfoListBase();
    }

    public _getOfflineBundleInfoListBase () {
        const abcEnvironment = abcHost.abcEnvironment;
        const grayEnv = abcEnvironment.grayEnv();
        const result: IOfflineBundleInfos = {
            region: abcEnvironment.region(),
            grayEnv: GrayEnvMap[grayEnv],
            list: []
        };

        const bundles = this._bundleHelper.bundles();
        const globalBundles = this._bundleHelper.globalPlugins();

        result.list = bundles.map(it => {
            const info = this._bundleUpdater.getPluginUpdateInfo(it.name, grayEnv);
            return {
                name: it.name,
                global: globalBundles.has(it.name),
                buildTime: info?.buildTime,
                updateTime: info?.updateTime,
                version: info.version,
                onlineVersion: info.onlineVersion,
                rootDir: info.rootDir
            }
        })

        return result;
    }

    /**
     * app ready后初始化
     */
    public async init() {
        //如果当前不是entryUrl不是abcyun，则直接返回， 独立端也直接返回
        if (!abcConfig.entryUrl?.startsWith(Constants.abcyunScheme)) {
            return;
        }

        this._enable = true;

        this.registerSchemesForProtocol(abcHost.electron.protocol);
        this._listenMainWindowSyntaxErrorMessage().catch();

        //加载路由配置
        this._loadRouteConfig();

        // 拦截请求，处理wss cookie未带上的问题
        this._interceptRequest();

        //是否启用在线更新路由配置
        const enableUpdateRouteConfig = abcConfig.frontEndOfflineBundle?.enableUpdateRouteConfig ?? true;
        if (enableUpdateRouteConfig)
            this._updateRouteConfig().then();


        //刚启动时，允许进行一次强制更新
        await this.updateLocalOfflineBundle(true).catch(() => {
        });

        //检查离线资源是否已经下载过,已经下载过则直接resolve，不用卡网络请求
        let anyGolbalBundleName = '';
        if (this._bundleHelper.globalPlugins().size > 0) {
            anyGolbalBundleName = Array.from(this._bundleHelper.globalPlugins())[0];
        }

        const region = abcHost.abcEnvironment.region();
        const grayEnv = abcHost.abcEnvironment.grayEnv();
        const asarFile = this._bundleHelper.pluginAsarFile(anyGolbalBundleName, region, grayEnv);
        if (asarFile && FileUtils.fileExist(asarFile)) {
            this._globalBundleReady.resolve(true);
            return;
        }
        const globalDir = this._bundleHelper.frontEndOfflineBundleGlobalDir();
        if (FileUtils.dirExistsSync(globalDir, false)) {
            this._globalBundleReady.resolve(true);
        } else {
            //先更新全局插件
            this._bundleUpdater.updateGlobalPlugins()
                .then(() => {
                    this._globalBundleReady.resolve(true);
                }).catch(e => {
                this._globalBundleReady.resolve(true);
            });
        }
    }

    public async scheduleCheckUpdate() {
        //先更新全局插件
        await this._bundleUpdater.updateGlobalPlugins().catch(() => {
        });

        await this._bundleUpdater.updateNonGlobalPluginsWithGrayEnv(abcHost.abcEnvironment.grayEnv(), false, null, null, "StartAutoSilentUpdate");
    }

    public waitGlobalBundleReady(): Promise<boolean> {
        return this._globalBundleReady.promise;
    }

    /**
     * 加载本地路由策略配置
     * @private
     */
    private _loadRouteConfig() {
        //从配置文件加载
        const baseConfigFile = `route-config-${abcConfig.env}-v2.json`;
        logger.log(TAG, `加载路由配置:${baseConfigFile}`);
        const configFile = this._bundleHelper.frontEndOfflineBundleDir() + '/' + baseConfigFile;

        const copyEmbeddedConfigFile = () => {
            //从resources复制一份到本地
            const srcFile = hostRequire('path').join(AddonPathUtils.getAddonConfPath(), baseConfigFile);
            if (FileUtils.fileExist(srcFile)) {
                hostRequire("fs").copyFileSync(srcFile, configFile);
            }
        }

        if (!FileUtils.fileExist(configFile)) {
            logger.log(TAG, `路由配置文件不存在,尝试从内置包复制一份`);
            copyEmbeddedConfigFile();
        }

        if (FileUtils.fileExist(configFile)) {
            this._routeConfig = loadRouteConfig(configFile)!;
            //配置文件出错，可能是由于下载了一个错误的配置文件，这里再次复制一份
            if (!this._routeConfig || !this._routeConfig.rules || this._routeConfig.rules.length === 0) {
                copyEmbeddedConfigFile();
                this._routeConfig = loadRouteConfig(configFile)!;
            }
            this._apiRules = this._routeConfig.rules.filter(rule => rule.type === 'api');

            // 从routeConfig中收集插件信息
            const bundles: IOfflineBundle[] = [];
            for (const rule of this._routeConfig.rules) {
                if (!rule.name)
                    continue;

                const existBundle = bundles.find(it => it.name === rule.name)
                if (existBundle) {
                    if (!existBundle.global && rule.global) {
                        existBundle.global = true;
                    }
                    continue;
                }

                bundles.push({
                    name: rule.name,
                    global: rule.global
                });
            }

            this._bundleHelper.setBundles(bundles);
        } else {
            logger.logToServer(TAG, `错误 路由加载失败 未找到路由配置文件:${configFile}`);
        }

        if (!this._routeConfig) {
            logger.logToServer(TAG, `错误 路由加载失败 加载规则失败`);
        }
        else{
            logger.log(TAG, `加载路由配置成功，配置}`);
        }
    }

    private async _updateRouteConfig() {
        logger.log(TAG, `开始更新在线路由配置`);
        //从配置文件加载
        const baseConfigFile = `route-config-${abcConfig.env}-v2.json`;
        const configFile = this._bundleHelper.frontEndOfflineBundleDir() + '/' + baseConfigFile;
        const configURL = `https://cis-static-common.oss-cn-shanghai.aliyuncs.com/apks/abc_pc/conf/${baseConfigFile}?t=${new Date().getTime()}`;
        try {
            const configText = await NetworkUtils.getAsString(configURL);
            const jsonConfig = JSON.parse(configText);
            if (jsonConfig && jsonConfig.rules) {
                await FileUtils.writeFileAsString(configFile, configText);
            }

            this._loadRouteConfig();
        } catch (e) {
            logger.logToServer(TAG, `错误更新失败:${e}`);
        }
    }


    private async _createStreamFromNetFetch(url: string, options: RequestInit, callback: (rsp: any, stream: any, error: any) => void) {
        const net = abcHost.electron.net;
        const req = net.request({
            url: url,
            ...options,
            session: NetworkUtils.getNetSession()
        });

        const {PassThrough} = hostRequire('stream');
        const rv = new PassThrough();
        req.on('response', (rsp: any) => {
            rsp.pipe(rv);
            callback(rsp, rv, null,);
        });

        req.on("error", (error: any) => {
            NetworkUtils.changeSessionNameWhenErrFailed(error);
            callback(null, rv, error);
        });
        if (options.body) {
            if (options.body instanceof Array) {
                for (const chunk of options.body) {
                    if (chunk.type === "rawData") {
                        req.write(chunk.bytes);
                    } else if (chunk.type === "file") {
                        await new Promise((resolve, reject) => {
                            // 文件可能很大，不适合一次性读取到内存
                            const stream = hostRequire('fs').createReadStream(chunk.file);
                            stream.pipe(req, {end: false});
                            stream.on('end', () => {
                                resolve(undefined);
                            });
                            stream.on('error', (e: any) => {
                                reject(e);
                            });
                        });
                    }
                    else if (chunk.type === "blob") {
                        const buffer = await abcHost.electron.session.defaultSession.getBlobData(chunk.blobUUID);
                        req.write(buffer);
                    }
                }
            }
        }
        req.end();
    }


    registerSchemesForProtocol(protocol: any/*electron.protocol*/) {
        logger.info('registerSchemesForProtocol');

        const protocolHandle = !!protocol.handle
        !protocolHandle && protocol.registerStreamProtocol(Constants.abcyunScheme, async (req, callback) => {
            const startTime = Date.now(); // 记录请求开始时间
            let abcyunInterceptError: any;
            const url = await this._interceptAbcyunRequest(req.url).catch(error => abcyunInterceptError = error);
            const pluginName = url.route?.name;
            const debug = abcConfig.debug ?? false;
            debug && logger.log(`abcyun scheme: bundle pluginName = ${pluginName ?? ''}, redirect = ${url.redirect ?? false}, ${req.url} -> ${url.interceptedUrl}`);
            const fileSize = url.fileSize ?? 0;

            // 包装callback以记录请求完成时间
            const recordRequestEnd = () => {
                const duration = Date.now() - startTime;
                requestTracker.recordRequest(req.url, startTime, duration, !(url.interceptedUrl ?? '').startsWith('file://'));
            };

            const listenSreamEnd = (stream: any) => {
                if (stream) {
                    stream.on('close', () => {
                        recordRequestEnd();
                        if(debug)logger.log(`abcyun scheme: bundle pluginName = ${pluginName ?? ''}, fileSize = ${(fileSize /1024).toFixed(2)}KB, cost = ${Date.now() - startTime}ms, redirect = ${url.redirect ?? false}, ${req.url} -> ${url.interceptedUrl}`);
                    });

                } else {
                    recordRequestEnd();
                }

            };


            if (abcyunInterceptError) {
                recordRequestEnd();
                callback({error: abcyunInterceptError});
                return;
            }

            if (url.notFound) {
                recordRequestEnd();
                callback({
                    statusCode: 404,
                    headers: {
                        'Content-Type': 'text/plain'
                    }
                });
                return;
            }

            if (url.redirect) {
                recordRequestEnd();
                callback({
                    statusCode: 302,
                    headers: {
                        Location: url.interceptedUrl
                    }
                });
                return;
            }

            const options: RequestInit = {
                method: req.method,
                headers: req.headers,
                body: req.uploadData
            }
            if (options.body) {
                // @ts-ignore
                options.duplex = "half";
            }

            const filePrefix = 'file:///';
            if (url.interceptedUrl.startsWith(filePrefix)) {
                const queryPos = url.interceptedUrl.indexOf('?');
                let file = url.interceptedUrl.substring(filePrefix.length, queryPos > 0 ? queryPos : url.interceptedUrl.length);
                file = decodeURIComponent(file);
                //如果是非windows系统，加上/
                if (process.platform !== 'win32') {
                    file = '/' + file;
                }
                const mimeType = FileUtils.guessMimeType(file);
                const fileData = await new Promise((resolve, reject) => {
                    hostRequire('fs').readFile(file, (err, data) => {
                        if (err) {
                            reject(err);
                        } else {
                            resolve(data);
                        }
                    });
                });
                const {Readable} = hostRequire('stream');
                const stream = Readable.from(fileData);
                // const stream = hostRequire('fs').createReadStream(file);
                const headers = {
                    "Content-Type": mimeType
                }

                const baseName = hostRequire('path').basename(file);
                let noCache = this._noMemoryCacheList.some(it => it.test(baseName));
                if (noCache) {
                    headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0';
                }

                listenSreamEnd(stream);

                callback({
                    statusCode: 200,
                    headers: headers,
                    data: stream
                });
                return;
            } else {
                const origin = new URL(url.interceptedUrl).origin;
                const cookie = (await abcHost.electron.session.defaultSession.cookies.get({url: origin}))?.map((item: any) => `${item.name}=${item.value}`)?.join(';');
                if (cookie) {
                    options.headers['Cookie'] = cookie;
                }

                let isFirstCallback = true;
                this._createStreamFromNetFetch(url.interceptedUrl, options, async (rsp, stream, error) => {
                    if (!isFirstCallback) {
                        return;
                    }

                    isFirstCallback = false;
                    if (error) {
                        recordRequestEnd();
                        callback({error});
                        return;
                    }
                    //从rsp header里提取cookie,写入到session
                    const headers = rsp.headers;
                    if (headers) {
                        const cookieStr = headers['set-cookie'];
                        const cookieArray = [];
                        if (cookieStr instanceof Array) {
                            cookieArray.push(...cookieStr);
                        } else if (typeof cookieStr === "string") {
                            cookieArray.push(cookieStr);
                        }
                        if (cookieArray.length) {
                            for (const cookieStr of cookieArray) {
                                const cookie = NetworkUtils.parseCookie(cookieStr);
                                await abcHost.electron.session.defaultSession.cookies.set({
                                    url: origin,
                                    name: cookie.name,
                                    value: cookie.value,
                                    domain: cookie.domain,
                                    path: cookie.path,
                                    secure: cookie.secure,
                                    httpOnly: cookie.httpOnly,
                                    expirationDate: cookie.expires

                                }).catch((e: any) => {
                                });
                            }
                        }
                    }

                    listenSreamEnd(stream);
                    callback({
                        statusCode: rsp.statusCode,
                        headers: rsp.headers,
                        data: stream
                    });
                })

            }
        });
    }

    private async _listenMainWindowSyntaxErrorMessage() {
        let webContents: any;
        while (true) {
            await delay(1000);
            const mainWindow = abcHost.electron.app.mainWindow;
            webContents = mainWindow?.getBrowserView()?.webContents;
            if (webContents) {
                break;
            }
        }

        webContents.on('console-message', async (event, level, message, line, sourceId) => {
            ignore(event, line);
            //检查本地abcyun资源出现的语法错误
            //发生后删除本地错误文件，下次加载时将会触发重新解压和加载
            if (level === 3 && message.startsWith('Uncaught SyntaxError:') && sourceId?.startsWith(Constants.abcyunScheme)) {
                const result = await this._interceptAbcyunRequest(sourceId);
                const filePrefix = 'file:///';
                if (result.interceptedUrl.startsWith(filePrefix)) {
                    logger.logToServer(TAG, ` 本地文件语法错误 ${sourceId}, message = ${message}`);
                    const file = fileURLToPath(result.interceptedUrl);
                    if (FileUtils.fileExist(file)) {
                        FileUtils.deleteFileSync(file);
                    }
                }
            }
        });
    }

    /**
     * 根据请求的目的地址，从路由配置中查找对应的地址
     */
    private async _interceptAbcyunRequest(url: string): Promise<IUrlIntercerptResult> {
        let result: IUrlIntercerptResult = {
            originalUrl: url,
            interceptedUrl: url,
            notFound: false,
            fileSize: 0,
        };
        let {protocol, host, pathname, searchParams} = new URL(url)
        let searchParamsStr = searchParams.toString();
        if (protocol !== (Constants.abcyunScheme + ":")) {
            return result;
        }

        let hostAndPath = host;
        if (pathname) {
            hostAndPath += pathname;
        }
        //优先判断api
        let route = this._apiRules?.find(rule => {
            return pathname.match(rule.path ?? '');
        });

        if (route) {
            result.interceptedUrl = url.replace(Constants.abcyunScheme, "https")
            return result;
        }


        route = this._routeConfig.rules.find(rule => {
            return hostAndPath.startsWith(rule.remote);
        });

        if (route) {
            pathname = hostAndPath.substring(route.remote.length);
        }

        //对type为"rewrite"的规则进行重写
        if (route && route.type === "rewrite") {
            route = this._routeConfig.rules.find(rule => {
                return rule.id === route!.target
            });
        }
        if (route && route.type === "bypass") {
            result.interceptedUrl = url.replace(Constants.abcyunScheme, route.targetProtocol ?? "https")
            return result;
        }

        if (!route) {
            if (url.startsWith(Constants.abcyunScheme)) {
                result.interceptedUrl = url.replace(Constants.abcyunScheme, 'https');
                logger.log(TAG, `错误 没有路由配置，回退到网络 ${url} -> ${result.interceptedUrl}`);
            } else {
                result.notFound = true;
            }
            return result;
        }

        const grayEnv = abcHost.abcEnvironment.grayEnv();
        const region = abcHost.abcEnvironment.region();

        let preventDefault = false;
        //优先调用预拦截器处理
        if (route.preInterceptor) {
            for (let interceptor of route.preInterceptor) {
                if (!interceptor.path || !interceptor.script)
                    continue;

                const matches = pathname.match(interceptor.path);
                if (!matches)
                    continue;


                let handler = this._preInterceptHandlerCache.get(interceptor.script);
                if (!handler) {
                    try {
                        handler = eval(interceptor.script);
                    } catch (e) {
                        logger.log(`路由拦截器，解析失败,e = `, e.message, ", scripts = " + interceptor.script);
                    }
                }

                if (!handler) {
                    handler = (() => {
                    }) as any;
                }

                const info = this._bundleUpdater.getPluginUpdateInfo(route.name, grayEnv);
                const debug = abcConfig.debug ?? false;
                if (debug) {
                    logger.log(`调用路由自定义拦截器 (${pathname}, ${JSON.stringify(info)})`);
                }
                const result = await handler({
                    target: pathname,
                    originalUrl: url,
                    pluginInfo: info
                });

                if (debug) {
                    logger.log(`调用路由自定义拦截器 结果 = ${JSON.stringify(result)}`);
                }

                if (!result)
                    continue;


                if (result.target) {
                    pathname = result.target;
                }

                if (result.preventDefault) {
                    preventDefault = true;
                    break;
                }
            }
        }

        if (route.rule && !preventDefault) {
            for (const rule of route.rule) {
                const matches = pathname.match(rule.from);
                if (matches) {
                    pathname = rule.to;
                    for (let i = 1; i < matches.length; i++) {
                        pathname = pathname.replace(`$${i}`, matches[i]);
                    }

                    //用原origin ${originalOrigin}替换
                    if (rule.type === "redirect") {
                        result.redirect = true
                        pathname = pathname.replace("${originalOrigin}", protocol + "//" + host);
                    }

                    break;
                }
            }
        }

        result.route = route;
        if (result.redirect) {
            result.interceptedUrl = pathname;
        } else {
            let filePath = this._bundleHelper.fileWithinPlugin(route.name, region, grayEnv, pathname, route.filePrefix);
            let asarFile = this._bundleHelper.pluginAsarFile(route.name, region, grayEnv);
            try {
                result.fileSize = hostRequire("fs").statSync(filePath).size;
            } catch (e) {
            }

            let isJsMapFile = pathname && pathname.endsWith(".js.map");
            //如果文件不存在且没有下载过，尝试触发更新一次
            if (!isJsMapFile && result.fileSize <= 0 && !FileUtils.fileExist(asarFile)) {
                this._bundleUpdater.forceUpdatePlugin(route.name, grayEnv, region).then().catch((e) => {
                    logger.log(TAG, `强制更新插件${route.name}失败`, e);
                });
            }

            if (result.fileSize <= 0) {
                if (!isJsMapFile) {
                    result.interceptedUrl = url.replace(Constants.abcyunScheme, 'https');
                    // 日志输出，方便追踪
                    let pluginVersion = '';
                    if (route?.name)
                        pluginVersion = this._bundleHelper.inUseLocalBundleConf(route.name, grayEnv, region)?.version;

                    logger.logToServer(TAG, `错误 本地文件丢失 回退到网络，${filePath}, url = ${url}, pluginName = ${route?.name},pluginVersion = ${pluginVersion}`);
                } else {
                    result.notFound = true;
                }
            } else {
                result.interceptedUrl = pathToFileURL(filePath).toString();
                if (searchParamsStr.length > 0) {
                    result.interceptedUrl += "?" + searchParamsStr;
                }
            }
        }

        return result;
    }


    public checkPluginIsReady(name: string): boolean {
        const file = this._bundleHelper.pluginConfFile(name, abcHost.abcEnvironment.grayEnv(), abcHost.abcEnvironment.region());
        return FileUtils.fileExist(file);
    }

    checkPluginsUpdateWithCurrentGrayEnv(showLoading: boolean, onProgress?: IBundleUpdateProgress, waitLoadingForLoginSuccess?: Completer<boolean>, scene?:string) {
        if (!this._enable)
            return;

        return this._bundleUpdater.updateNonGlobalPluginsWithGrayEnv(abcHost.abcEnvironment.grayEnv(), showLoading, onProgress, waitLoadingForLoginSuccess, scene);
    }

    public checkRegionEnvUpdateHasComplete(region: string, env: GrayEnv): boolean {
        return this._bundleUpdater.checkRegionEnvUpdateHasComplete(region, env);
    }

    private _interceptRequest() {
        const session = abcHost.electron.session.defaultSession;
        session.webRequest.onBeforeSendHeaders(async (details: any, callback: any) => {
            const url = new URL(details.url);
            const origin = url.origin;
            let finalHeaders = {...details.requestHeaders};

            //走abcyun自定义协议后，发起的wss请求，没有带上cookie，导致授权失败，这里手动加上
            if (details.url.startsWith('wss')) {
                const cookie = (await session.cookies.get({url: origin}))?.map((item: any) => `${item.name}=${item.value}`)?.join(';');
                logger.logIfDebugMode(`wss request ${details.url}, cookie = ${cookie}`);
                finalHeaders = {
                    ...details.requestHeaders,
                    Cookie: cookie,
                };
            }

            callback({
                requestHeaders: finalHeaders
            });
        });
    }
}

const offlineBundleManager = new OfflineBundleManager();
export {offlineBundleManager}
