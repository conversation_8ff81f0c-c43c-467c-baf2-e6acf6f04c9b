import {AbcExeUpdater} from "./abc-exe-updater";
import {UpgradeInfo} from "./upgrade-info";
import {ResourcesUpdater} from "./resources-updater";
import {logger} from "../common";
import {abcHost} from "../../abc-host";
import {abcConfig} from "../conf/abc-conf";


export class ABCUpdater {
    public isUpdating = false;
    private abcExeUpdater?: AbcExeUpdater;
    private resourcesUpdater?: ResourcesUpdater
    public upgradeInfo?: UpgradeInfo;


    /**
     * 检查升级
     */
    public async checkUpgrade(): Promise<UpgradeInfo | undefined> {
        if (abcConfig.enableAutoUpdateJS) {
            if (!this.resourcesUpdater)
                this.resourcesUpdater = new ResourcesUpdater();
            const upgradeInfo = await this.resourcesUpdater.checkAndUpdate();
            if (upgradeInfo) {
                this.upgradeInfo = upgradeInfo;
                this._emitUpgradeInfo();
                return this.upgradeInfo;
            }
        }

        if (abcConfig.enableUpdateApp) {
            if (!this.abcExeUpdater)
                this.abcExeUpdater = new AbcExeUpdater();

            const upgradeInfo = await this._checkExeUpgrade();
            if (upgradeInfo) {
                this.upgradeInfo = upgradeInfo;
                this._emitUpgradeInfo();
                return this.upgradeInfo;
            }
        }
    }


    /**
     * 触发升级
     */
    public upgrade() {
        logger.info('abc-exe-upgrade upgrade');
        if (!this.upgradeInfo) {
            return;
        }
        this.isUpdating = true;
        if (this.upgradeInfo.updateType === "exe") {
            this.abcExeUpdater?.upgrade()
        } else if (this.upgradeInfo.updateType === "jsbundle") {
            this.resourcesUpdater?.upgrade()
        }
    }

    /**
     * 检查安装包更新
     * @private
     */
    private async _checkExeUpgrade(): Promise<UpgradeInfo | undefined> {
        return this.abcExeUpdater?.checkForUpdates();
    }

    /**
     * 向前端推送有可用更新
     * @private
     */
    private _emitUpgradeInfo() {
        const wins = abcHost.electron.BrowserWindow.getAllWindows();
        for (let win of wins) {
            if (!win.isDestroyed() && win.isVisible() && win.webContents) {
                win.webContents.send(`__upgrade_update_available`, this.upgradeInfo);
            }
        }
    }
}

const abcUpdater = new ABCUpdater();
export {abcUpdater}
