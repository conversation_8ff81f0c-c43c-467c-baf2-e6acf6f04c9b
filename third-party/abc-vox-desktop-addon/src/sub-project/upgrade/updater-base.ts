import {AddonPathUtils, Completer, logger} from "../common";
import {abcHost} from "../../abc-host";
import utils from "../common/utils";
import {UiUtils} from "../component/ui-utils";
import {abcConfig} from "../conf/abc-conf";
import path from "path";

export interface LatestConfInfo {
    appId: string,
    version: string,
    name: string,
    md5: string,
    url: string,
    platform: string,
    arch: string,
    description: string,
    buildTime: string,
}

export class UpdaterBase {
    protected downloadCompleter?: Completer<boolean>;
    protected autoInstall = false;


    public getApiUrlWithAppId(appId: string): string {
        let architecture = (process.env as any).PROCESSOR_ARCHITECTURE;
        let url = new URL(abcConfig.entryUrl!);
        const browserViews = abcHost.electronAbcApp.mainWindow.getBrowserViews();
        if (browserViews) {
            for (const view of browserViews) {
                const currentUrl = view.webContents.getURL();

                if (!currentUrl.startsWith("file")
                    && currentUrl.indexOf("global") < 0 // 还在登录页面
                    && currentUrl.indexOf("127.0.0.1") < 0) //本地地址
                {
                    url = new URL(currentUrl);
                    break;
                }
            }
        }

        const platform = process.platform === 'win32' ? 'win' : 'mac';
        return `https://${url.host}/api/v2/app/pc/upgrade-info?appId=${appId}&version=${abcHost.version}&platform=${platform}&arch=${architecture === 'AMD64' ? 'x64' : 'x86'}&t=${new Date().getTime()}`;
    }

    protected showUpdateInfo() {
        let updateWindow = utils.createBrowserWindow(UiUtils.createFillParentParams(abcHost.electronAbcApp.mainWindow));
        const url = 'file://' + path.join(AddonPathUtils.getRootPath(), 'renderer/update.html');
        updateWindow.loadURL(url);

        updateWindow.setResizable(false);
        updateWindow.setMovable(false);

        const ipcMain = abcHost.electron.ipcMain;
        ipcMain.once('click-update-now', (/*event*/) => {
            console.log("update-now," + console.log);
            logger.log('update-now');
            this.download(true);
        });

        ipcMain.once('click-not-update', (/*events*/) => {
            if (updateWindow) {
                updateWindow.hide();
                updateWindow.close();
                abcHost.electronAbcApp.abcUpdater.isUpdating = false;
            }
        });


        updateWindow.show();


        if (abcHost.electronAbcApp.mainWindow.isMaximized()) {
            updateWindow.maximize();
        }
    }


    protected install() {

    }

    protected async download(autoInstall: boolean) {

    }

    protected async doDownload(options: { url: string, file: string, md5: string, autoInstall: boolean }) {
        const {url, file, md5, autoInstall} = options;
        logger.info(`doDownload options:${JSON.stringify(options)}`);
        this.autoInstall = autoInstall;

        if (this.downloadCompleter) {
            //正在下载中
            if (!this.downloadCompleter.finished)
                return;

            //下载成功了，直接调起安装
            if (this.downloadCompleter.value) {
                this.install();
                return;
            }
        }


        logger.log(`_autoInstall = ${this.autoInstall}, autoInstallAfterDownload=${autoInstall}`);
        logger.log(`升级安装地址 = ${url}`);
        this.downloadCompleter = new Completer<boolean>();
        await abcHost.networkUtils.downloadFile({
            url: url,
            md5: md5,
            filePath: file,
            onProgress: this._onDownloadProgress.bind(this)
        }).then(() => {
            logger.log(`安装包下载完成, 自动安装: ${this.autoInstall}`);
            this.downloadCompleter!.resolve(true);
            if (this.autoInstall) {
                this.install();
            }
        }).catch((error: any) => {
            this.downloadCompleter!.reject(error);
        });
    }


    /**
     * 通知前端页面下载进度
     * @param currentBytes
     * @param totalBytes
     * @private
     */
    private _onDownloadProgress(currentBytes: number, totalBytes?: number) {
        const wins = abcHost.electron.BrowserWindow.getAllWindows();
        for (let win of wins) {
            if (!win.isDestroyed() && win.isVisible() && win.webContents)
                win.webContents.send(`__upgrade_download_progress`, currentBytes, totalBytes);
        }
    }
}
