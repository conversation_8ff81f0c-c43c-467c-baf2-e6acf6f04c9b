import {logger, Version} from "../common";
import {UpgradeInfo} from "./upgrade-info";
import {LatestConfInfo, UpdaterBase} from "./updater-base";
import {abcHost, hostRequire} from "../../abc-host";


export class AbcExeUpdater extends UpdaterBase {
    mainWindow: any;
    private latestInfo?: LatestConfInfo;


    constructor() {
        super();
    }


    /**
     * 检查升级
     */
    async checkForUpdates(): Promise<UpgradeInfo | undefined> {
        logger.info(`开始检查客户端更新...`);
        const apiURL = this.getApiUrlWithAppId('abcyun-desktop-pc');
        const latestInfoStr = await abcHost.networkUtils.getAsStringAutoCookie(apiURL);

        /**
         * @type {LatestConfInfo}
         */
        const upgradeInfo = JSON.parse(latestInfoStr)?.data;
        this.latestInfo = upgradeInfo;
        logger.log(`upgradeInfo = ${JSON.stringify(upgradeInfo)}, packageJson.version = ${abcHost.version}`);
        //需要更新
        if (!upgradeInfo.version ||new Version(upgradeInfo.version).compareTo(abcHost.version) <= 0) {
            logger.log(`无需更新`);
            return;
        }

        //有更新，触发自动下载
        await this.download(false).then();

        return {
            version: upgradeInfo.version,
            releaseNotes: upgradeInfo.description,
            updateType: "exe"
        }
    }

    async upgrade() {
        logger.info("abc-exe-upgrade");
        super.showUpdateInfo();
    }

    get isPackaged() {
        return true;
        // return app.isPackaged === true;
    }


    onError(message: any) {
        console.error('There was a problem updating the application');
        console.error(message);
    }

    protected async download(autoInstall: boolean) {
        return this.doDownload({
            url: this.latestInfo!.url,
            md5: this.latestInfo!.md5,
            file: this._installAppPath(),
            autoInstall: autoInstall
        });
    }

    /**
     * 调起安装程序
     */
    protected async install() {
        const tmpFile = this._installAppPath();
        logger.info(`开始升级，执行升级包:${tmpFile}`);
        const installer = hostRequire("child_process").spawn(tmpFile, ['--updated'], {detached: true});
        installer.on("close", (code:any, signal:any) => {
            console.log(`安装完成, code=${code}, signal=${signal}`);

        });

        installer.stdout.on('data', (data:any) => {
            console.log(`${data}`);
        });

        installer.stderr.on('error', (data:any) => {
            console.error(`${data}`);
        });

        abcHost.electronAbcApp.mainWindow.close();
    }

    private _installAppPath(): string {
        return `${abcHost.pathUtils.tmpDir()}/abcyun-desktop-win.exe`;
    }
}

