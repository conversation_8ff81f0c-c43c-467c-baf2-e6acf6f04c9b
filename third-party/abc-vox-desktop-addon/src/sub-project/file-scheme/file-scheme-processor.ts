import {abcHost, hostRequire} from "../../abc-host";
import {logger} from "../common";
import {pluginManager} from "../plugin-manager/plugin-manager";
import {fileURLToPath} from "url";
import {Constants} from "../../constants";

/**
 * 用于处理file://协议的文件，以解决插件加载在文件丢失情况下，重新从zip包中解压并加载
 */
class FileSchemeProcessor {
    private _needAutoFillCookieUrl: string[] = [];

    constructor() {

    }

    public init() {
        this.registerFileScheme();
    }

    public registerFileScheme() {
        const protocol = abcHost.electron.protocol;
        protocol.interceptFileProtocol("file", async (req, callback, next) => {
            let file = hostRequire('url').fileURLToPath(req.url);
            pluginManager.restoreFileIfNeed(file).finally(() => {
                callback(file);
            });

            return true;
        });
    }

    /**
     * 针对一些由file://里发起的请求，自动填充cookie
     */
    public registerFileSchemeForAutoFillAbcyunCookie(url: string) {
        logger.log(`registerFileSchemeForAutoFillAbcyunCookie url = ${url}`);
        //提取url对应的文件路径
        let filePath = url;
        if (url.startsWith('file://')) {
            filePath = fileURLToPath(url);
        }
        else {
            filePath = hostRequire('path').resolve(url);
        }



        if (this._needAutoFillCookieUrl.indexOf(filePath) >= 0) {
            return;
        }

        this._needAutoFillCookieUrl.push(filePath);

        const first = this._needAutoFillCookieUrl.length === 1;
        first && abcHost.electron.session.defaultSession.webRequest.onBeforeSendHeaders(async (details: any, callback: any) => {
            let urlStr = details.url;
            if (urlStr && urlStr.startsWith(Constants.abcyunScheme)) {
                urlStr = urlStr.replace(Constants.abcyunScheme, 'https');
            }

            const url = new URL(urlStr);
            const origin = url.origin;
            let finalHeaders = {...details.requestHeaders};

            //只针对本地文件地址作处理, 满足条件时自动填充cookie
            const href = details.webContents?.getURL() ?? '';
            let shouldFillCookie = false;
            if (href && href.startsWith('file://')) {
                const currentFilePath = fileURLToPath(href);
                shouldFillCookie = !!this._needAutoFillCookieUrl.find((item) => currentFilePath.startsWith(item));
            }
            
            logger.logIfDebugMode(`registerFileSchemeForAutoFillAbcyunCookie shouldFillCookie  = ${shouldFillCookie}, href = ${href},url = ${url}`);
            if (shouldFillCookie) {
                const cookie = (await abcHost.electron.session.defaultSession.cookies.get({url: origin}))?.map((item: any) => `${item.name}=${item.value}`)?.join(';');
                finalHeaders = {
                    ...details.requestHeaders,
                    Origin: origin,
                    Cookie: cookie,
                };
            }

            callback({
                requestHeaders: finalHeaders
            });
        });
    }
}

const fileSchemeProcessor = new FileSchemeProcessor();
export {fileSchemeProcessor}
