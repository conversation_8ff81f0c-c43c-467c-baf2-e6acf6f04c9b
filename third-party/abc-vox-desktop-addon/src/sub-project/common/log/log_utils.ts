import {abcHost} from "../../../abc-host";
import {abcConfig} from "../../conf/abc-conf";
import {NetworkUtils} from "../network/network-utils";

class LogUtils {
    private _clinicId?: string; //诊所id，用于上报时区分不同诊所
    private _meta = new Map<string, any>();
    private _logsToReport: any[] = [];
    private _logTimer?: number;

    // 循环数组， 用于保存最近100条日志
    private _recentLogs: {
        startTime: number,
        log: string,
    }[] = [];


    private _originalLogger: {
        info: (...params: any[]) => void;
        log: (...params: any[]) => void;
        warn: (...params: any[]) => void;
        error: (...params: any[]) => void;
    };

    constructor() {
    }
    public hookAbcHostLogger() {
        this._originalLogger = {
            info: abcHost.logger.info.bind(abcHost.logger),
            log: abcHost.logger.log.bind(abcHost.logger),
            warn: abcHost.logger.warn.bind(abcHost.logger),
            error: abcHost.logger.error.bind(abcHost.logger)
        };

        abcHost.logger.info = this.info.bind(this);
        abcHost.logger.log = this.log.bind(this);
        abcHost.logger.warn = this.warn.bind(this);
        abcHost.logger.error = this.error.bind(this);
    }

    info(...params: any[]) {
        this._cacheRecentLogs(params.join(' '));
        this._originalLogger.info(...params);
    }

    log(...params: any[]) {
        this._cacheRecentLogs(params.join(' '));
        this._originalLogger.log(...params);
    }

    logIfDebugMode(...params: any[]) {
        if (abcConfig.debug) {
            this._cacheRecentLogs(params.join(' '));
            this._originalLogger.log(...params);
        }
    }


    warn(...params: any[]) {
        this._cacheRecentLogs(params.join(' '));
        this._originalLogger.warn(...params);
    }

    error(...params: any[]) {
        this._originalLogger.error(...params);
    }

    public addMeta(key: string, value: any) {
        this._meta.set(key, value);
    }

    private _cacheRecentLogs(log: string) {
        this._recentLogs.push({
            startTime: Date.now(),
            log: log,
        });
        if (this._recentLogs.length > 100) {
            this._recentLogs.shift();
        }
    }

    //返回: 2021-08-05 02:08:06.123
    private _formatTime(time: number) {
        const date = new Date(time);
        const padding = (num: number) => num.toString().padStart(2, '0');

        return `${date.getFullYear()}-${padding((date.getMonth() + 1))}-${padding(date.getDate())} ${padding(date.getHours())}:${padding(date.getMinutes())}:${padding(date.getSeconds())}.${date.getMilliseconds()}`;
    }
    /**
     * 上报日志并携带最近的日志
     */
    logToServerWithRecentLogs(...params: any[]) {
        this._originalLogger.log(...params);

        const meta =  JSON.stringify({
                "User-Agent": abcHost.electron.session.defaultSession.getUserAgent(),
                ...(this._meta.size > 0 ? Object.fromEntries(this._meta) : {})
            })
        const traceId =Date.now().toString();
        const clinicId =  this._clinicId ?? 'unknown-clinic-id';
        for (const log of this._recentLogs) {
            this._logsToReport.push({
                log: log.log,
                meta:meta,
                clinicId: clinicId,
                startTime: this._formatTime(log.startTime),
                traceId: traceId
            });
        }

        const log = params.join(' ');
        this._logsToReport.push({
            log: log,
            meta: meta,
            clinicId: clinicId,
            startTime: this._formatTime(Number(traceId)),
            traceId: traceId
        });
    }

    logToServer(...params: any[]) {
        this._originalLogger.log(...params);
        const log = params.join(' ');
        this._logsToReport.push({
            log: log,
            meta: JSON.stringify({
                "User-Agent": abcHost.electron.session.defaultSession.getUserAgent(),
                ...(this._meta.size > 0 ? Object.fromEntries(this._meta) : {})
            }),

            clinicId: this._clinicId ?? 'unknown-clinid-id',
            startTime: this._formatTime(Date.now()),
        });

        if (this._logTimer) {
            clearTimeout(this._logTimer);
        }

        //@ts-ignore
        this._logTimer = setTimeout(() => {
            if (this._logsToReport.length === 0) {
                return;
            }
            const logToReport = this._logsToReport;
            this._logsToReport = [];

            const url = `https://abc-fed-log.cn-shanghai.log.aliyuncs.com/logstores/${abcConfig.env}/track?APIVersion=0.6.0`;
            const logObj = {
                __topic__: "LogReport",
                __source__: "AbcMiraDesktop",
                __logs__: logToReport
            };
            NetworkUtils.post(url, {
                body: JSON.stringify(logObj)
            }).then(rsp => {
                console.log(rsp);
            }).catch(err => {
                console.log(err);
            });

        }, 30 * 1000);
    }

    public setClinicId(clinicId: string) {
        this._clinicId = clinicId;
    }
}

const logger = new LogUtils();
export {logger}
