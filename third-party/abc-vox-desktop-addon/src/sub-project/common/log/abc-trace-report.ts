import {abcHost} from "../../../abc-host";
import {abcConfig} from "../../conf/abc-conf";
import {NetworkUtils} from "../network/network-utils";

/**
 *
 */
class AbcTraceReport {
    private _logsToReport: any[] = [];

    private _logTimer?: number;

    constructor() {
    }


    public addLog(scene, options: {}) {
        this._logsToReport.push({
            attribute: JSON.stringify(this._assembleOTAttribute(scene, options)),
            service: 'cn.abcyun.app.desktop',
            start: Date.now().toString(),
            resource:
                JSON.stringify(
                    this._flattenObject({
                        version: abcHost.version,
                        addonInfo: {
                            BUILD_ENV: process.env.BUILD_ENV || "unknown",
                            BUILD_TIME: process.env.BUILD_TIME || "unknown",
                            BUILD_TAG: process.env.BUILD_TAG || "unknown",
                        },
                        uuid: abcHost.abcEnvironment.uuid(),
                    }))
        });


        if (this._logTimer) {
            clearTimeout(this._logTimer);
        }

        //@ts-ignore
        this._logTimer = setTimeout(() => {
            if (this._logsToReport.length === 0) {
                return;
            }

            const logToReport = this._logsToReport;
            this._logsToReport = [];

            const url = `https://abc-fed-log.cn-shanghai.log.aliyuncs.com/logstores/${abcConfig.env}-metrics-raw/track?APIVersion=0.6.0`;
            const logObj = {
                __topic__: "MetricReport",
                __source__: "ABCMiraDesktop",
                __logs__: logToReport
            };
            NetworkUtils.post(url, {
                body: JSON.stringify(logObj)
            }).then(rsp => {
                console.log(rsp);
            }).catch(err => {
                console.log(err);
            });
        }, 30 * 1000);
    }

    private _assembleOTAttribute(scene: string, data: any) {
        if (!scene) {
            return null;
        }
        const result = {
            scene,
            uid: abcHost.abcEnvironment.uuid(),
        };
        if (data == null) {
            return result;
        }
        // 如果data传的字符串，使用data作为key
        if (typeof data !== "object") {
            data = {
                data,
            };
        }
        Object.keys(data).map((key) => {
            result[`${scene}.${key}`] = data[key];
        });
        return result;
    }

    /**
     * 将对象扁平化
     * @param obj
     * @private
     * e.g {a: {b: 1}} => {a.b: 1}
     */
    private _flattenObject(obj: any) {
        const result: any = {};

        function recurse(cur: any, prop: string) {
            if (Object(cur) !== cur) {
                result[prop] = cur;
            } else if (Array.isArray(cur)) {
                let i = 0, l = cur.length;
                for (; i < l; i++) {
                    recurse(cur[i], `${prop}[${i}]`);
                }
                if (l === 0) {
                    result[prop] = [];
                }
            } else {
                let isEmpty = true;
                for (const p in cur) {
                    isEmpty = false;
                    recurse(cur[p], prop ? `${prop}.${p}` : p);
                }
                if (isEmpty && prop) {
                    result[prop] = {};
                }
            }
        }

        recurse(obj, "");
        return result;

    }
}

const abcTraceReport = new AbcTraceReport();
export {abcTraceReport}