import {Stats} from "fs";
import {abcHost, hostRequire} from "../../../abc-host";
import {ignore} from "../complier";

const fs = () => abcHost.hostRequire('fs');
const originalFs = () => abcHost.hostRequire('original-fs');
const path = () => abcHost.hostRequire('path');
const fsWithFile = (file: string) => file.endsWith('.asar') ? originalFs() : fs();

const kCommonMimeType = {
    '.html': 'text/html',
    '.txt': 'text/plain',
    '.js': 'application/javascript',
    '.wasm': 'application/wasm',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
};

export class FileUtils {
    static guessMimeType(file: string) {
        const dotPos = file.lastIndexOf('.');
        const ext = dotPos > 0 ? file.substring(dotPos) : '.html';
        return kCommonMimeType[ext.toLowerCase()];
    }

    /**
     * 读取路径信息
     * @param {string} path 路径
     */
    static getStat(path: string): Promise<Stats | false> {
        return new Promise((resolve) => {
            fs().stat(path, (err: any, stats: any) => {
                if (err) {
                    resolve(false);
                } else {
                    resolve(stats);
                }
            })
        })
    }


    /**
     * 创建路径
     * @param {string} dir 路径
     */
    static mkdir(dir: string) {
        return new Promise((resolve) => {
            fs().mkdir(dir, (err: any) => {
                if (err) {
                    resolve(false);
                } else {
                    resolve(true);
                }
            })
        })
    }

    static mkdirSync(dir: string) {
        return fs().mkdirSync(dir);
    }

    /**
     * 路径是否存在，不存在则创建
     * @param {string} dir 路径
     */
    static async dirExists(dir: string) {
        let isExists = await FileUtils.getStat(dir);
        //如果该路径且不是文件，返回true
        if (isExists && isExists.isDirectory()) {
            return true;
        } else if (isExists) {     //如果该路径存在但是文件，返回false
            return false;
        }
        //如果该路径不存在
        let tempDir = abcHost.hostRequire('path').parse(dir).dir;      //拿到上级路径
        //递归判断，如果上级目录也不存在，则会代码会在此处继续循环执行，直到目录存在
        let status = await FileUtils.dirExists(tempDir);
        let mkdirStatus;
        if (status) {
            mkdirStatus = await FileUtils.mkdir(dir);
        }
        return mkdirStatus;
    }

    static dirExistsSync(dir: string, createIfNotExist: boolean) {
        let stat = null;
        try {
            stat = fs().statSync(dir);
        } catch (e) {
        }

        //如果该路径且不是文件，返回true
        if (stat && stat.isDirectory()) {
            return true;
        } else if (stat) {     //如果该路径存在但是文件，返回false
            return false;
        }

        if (createIfNotExist) {
            fs().mkdirSync(dir, {recursive: true});
        }

        return createIfNotExist ?? false;
    }

    static createDirSync(dir: string, recursive: boolean) {
        let status = FileUtils.dirExistsSync(dir, false);
        if (!status) {
            fs().mkdirSync(dir, {recursive: true});
        }
    }


    /**
     * 判断文件是否存存
     * @param file
     * @returns {boolean} true文件存在,false文件不存在
     */
    static fileExist(file: string) {
        try {
            let ignore = fsWithFile(file).statSync(file);
            return true;
        } catch (e) {
        }

        return false;
    }


    /**
     * 获取文件大小
     * @param file
     */
    static fileSizeSync(file: string): number {
        const stats = fs().statSync(file)
        return stats.size;
    }

    /**
     *
     * @param filename{string}
     * @param encode {string |undefined}
     * @returns {string}
     */
    static readFileAsString(filename: string, encode?: string) {
        if (!encode || encode === 'utf8')
            return fs().readFileSync(filename, 'utf8');

        const Iconv = abcHost.hostRequire('iconv').Iconv;
        const gbk_to_utf8 = new Iconv(encode, 'UTF8');
        const buffer = gbk_to_utf8.convert(fs().readFileSync(filename));

        return buffer.toString();
    }

    /**
     *
     * 读取指定目录下的所有文件
     * @param dir {string}
     * @returns {Promise<string[]>}
     */
    static lsDir(dir: string): Promise<string[]> {
        return new Promise((resolve, reject) => {
            fs().readdir(dir, (err: any, files: string[]) => {
                if (err) reject(err);
                else resolve(files);
            });
        });
    }

    /**
     *
     * @param filename {string}
     * @param content{string}
     * @param [encode] {string}
     *
     * @returns Promise<boolean>
     */
    static writeFileAsString(filename: string, content: string, encode?: string) {
        return new Promise((resolve, reject) => {
            encode = encode ? encode : 'utf8';
            fs().writeFile(filename, content, (err: any) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(true);
                }
            });
        });


    }

    /**
     * 异步读取文件
     * @param filename{string}
     * @param encode
     * @returns {Promise<string>}
     */
    static readFileAsStringAsync(filename: string, encode?: string): Promise<string> {
        return new Promise((resolve, reject) => {
            if (!encode || encode === 'utf8') {
                fs().readFile(filename, 'utf8', (err: any, data: string) => {
                    if (err) reject(err);
                    else resolve(data);
                });
            } else {
                resolve("支持的编码类型");
            }
        });

    }


    /**
     *
     * @param filename {string}
     * @param content {string}
     * @param encode {string | undefined} , value can be "utf8"
     * @return {Promise<boolean>}, success true, otherwise reject
     */
    static appendStrToFile(filename: string, content: string, encode?: string): Promise<boolean> {
        return new Promise((resolve, reject) => {
            fs().appendFile(filename, content, (err: any) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(true);
                }
            });
        });
    }


    /**
     * 删除文件夹功能
     * @param  {string} url  文件路径，绝对路径
     * @return {void}
     */
    static async deleteDir(url: string) {
        let files: string[] = [];
        if (await FileUtils.fileExist(url)) {  //判断给定的路径是否存在
            files = await FileUtils.lsDir(url);   //返回文件和子目录的数组
            for (let file of files) {
                let curPath = path().join(url, file);
                if (fsWithFile(curPath).statSync(curPath).isDirectory()) { //同步读取文件夹文件，如果是文件夹，则函数回调
                    await FileUtils.deleteDir(curPath);
                } else {
                    await fsWithFile(curPath).unlinkSync(curPath);    //是指定文件，则删除
                }
            }

            await fsWithFile(url).rmdirSync(url); //清除文件夹
        } else {
            console.log("给定的路径不存在！");
        }
    }

    /**
     * 删除文件
     * @param file
     */
    static deleteFileSync(file: string): void {
        try {
            fsWithFile(file).unlinkSync(file);    //是指定文件，则删除
        } catch (ignored) {

        }
    }

    static isEmptyDirSync(dir: string): boolean {
        try {
            let files = fsWithFile(dir).readdirSync(dir);
            return files.length === 0;
        } catch (e) {
            return true;
        }
    }

    static deleteDirSync(url: string): boolean {
        let success = false;
        try {
            let files = [];
            if (FileUtils.fileExist(url)) {  //判断给定的路径是否存在
                files = fsWithFile(url).readdirSync(url);   //返回文件和子目录的数组
                for (let file of files) {
                    let curPath = path().join(url, file);
                    let isDir = false;
                    try {
                        //asar文件发，如果调用statSync后，文件将会被占用，后续无法删除
                        if (!file.endsWith('.asar')) {
                            isDir = fsWithFile(curPath).statSync(curPath).isDirectory();
                        }
                    } catch (e) {
                        ignore(e);
                    }
                    if (isDir) { //同步读取文件夹文件，如果是文件夹，则函数回调
                        FileUtils.deleteDirSync(curPath);
                    } else {
                        fsWithFile(curPath).unlinkSync(curPath);    //是指定文件，则删除
                    }
                }

                fsWithFile(url).rmdirSync(url); //清除文件夹
            } else {
                console.log("给定的路径不存在！");
            }
            success = true;
        } catch (e) {
            ignore(e);
        }

        return success;
    }

    /**
     * 递归拷贝文件夹
     */
    static copyDirSyncIgnoreError(src: string, dst: string, overwrite: boolean = false) {
        try {
            hostRequire('fs-extra').copySync(src, dst, {overwrite: overwrite});
        } catch (e) {
            ignore(e);
        }
    }

    /**
     * 将文件夹的文件移动到另一个文件夹下
     */
    static mvDirSyncIgnoreError(src: string, dst: string) {
        let files = fsWithFile(src).readdirSync(src);
        for (let file of files) {
            let curPath = path().join(src, file);
            let dstPath = path().join(dst, file);
            try {
            fsWithFile(curPath).renameSync(curPath, dstPath);    //是指定文件，则删除
            }
            catch(e) {
                ignore(e);
            }
        }
    }
    

    /**
     * 检查文件内容是否合法，有时遇解压出来的文件，全是0字节，这里判断前面16个字节是否全是0
     */
    public static checkFileContentValid(filePath: string): boolean {
        let valid = true;
        try {
            //只对css/js/html文件进行检查
            const fileExtensions = ['.js', '.css', '.html', '.htm'];
            let fileExtension = filePath.substring(filePath.lastIndexOf('.')) ?? '';
            fileExtension = fileExtension.toLowerCase();
            if (fileExtensions.find(it => it === fileExtension) === undefined)
                return valid;

            const fs = hostRequire('fs');
            const fd = fs.openSync(filePath, 'r');
            const checkSize = 16;
            const buffer = Buffer.alloc(checkSize);
            fs.readSync(fd, buffer, 0, checkSize, 0);
            fs.closeSync(fd);
            valid = !buffer.every(it => it === 0);
        } catch (e) {
            valid = false;
        }

        return valid;
    }
}
