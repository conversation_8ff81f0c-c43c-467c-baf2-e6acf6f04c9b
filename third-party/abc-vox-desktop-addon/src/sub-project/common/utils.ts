import {abcHost, hostRequire} from "../../abc-host";
import {logger} from "./log/log_utils";
import {AddonPathUtils} from "./file/addon-path-utils";


function isMainProcess() {
    const processAny: any = process;
    return processAny.type === "browser";
}

function getErrorPageURL() {
    let errorPageUrl = 'file:///' + abcHost.pathUtils.getProjectPath().replace('\\', '/') + '/src/renderer/pages/errorpage/error.html';
    return errorPageUrl.replace(/\\/g, "/");
}


/**
 * 获取js api 文件路径
 * @return {string}
 */
function getJSApiFile() {
    return hostRequire('path').join(AddonPathUtils.getRootPath(), 'renderer/jsapi.js');
}


function getTaskBarIconPath() {
    return hostRequire('path').resolve(abcHost.pathUtils.getResourcesDir(), "flat_resources/icon.ico");
}

/**
 *
 * @param windowOpts
 * @param injectApi {boolean?}
 * @returns {BrowserWindow}
 */
function createBrowserWindow(windowOpts: any/*Electron.BrowserWindowConstructorOptions*/, injectApi?: boolean) {
    let window: any = null;
    injectApi = (injectApi === undefined || injectApi === null) ? true : injectApi;
    // windowOpts.webPreferences
    const webPreferences: any/*Electron.WebPreferences*/ = {
        //@ts-ignore
        enableRemoteModule: true,
        nodeIntegration: true,
        webviewTag: true,
        contextIsolation: false,
        ...windowOpts.webPreferences
    }
    windowOpts = {...windowOpts, webPreferences: webPreferences};
    if (injectApi && !webPreferences.preload) {
        webPreferences.preload = getJSApiFile();
    }
    if (isMainProcess()) {
        const {BrowserWindow} = abcHost.hostRequire('electron');
        window = new BrowserWindow(windowOpts);
    } else {
        const {BrowserWindow} = (abcHost.hostRequire('electron') as any).remote;
        window = new BrowserWindow(windowOpts);
    }

    let originalReload = window.reload.bind(window);
    window.reload = function () {
        let pageUrl = decodeURIComponent(window.webContents.getURL());
        let errorPageUrl = getErrorPageURL();
        logger.log(`reload trigger = ${pageUrl}, errorPageUrl = ${errorPageUrl}`);
        if (pageUrl.startsWith(errorPageUrl)) {
            let url = new URL(pageUrl);
            let realURL = url.searchParams.get("realurl");
            // let decodedUrl = decodeURIComponent(encodedRealURL);
            console.log(`reload reload ${realURL}`);
            window.loadURL(realURL);
            return;
        }

        originalReload();
    };

    let retryTimes = 0;
    window.webContents.on('did-fail-load', function (event: any, errorCode: any, errorDescription: any, validatedURL: any, isMainFrame: any) {
        const logger = abcHost.hostRequire('electron-log');
        logger.log("did-fail-load event=", event + ", errorCode= " + errorCode + ",errorDescription=" + errorDescription + ",validatedURL=" + validatedURL + ", isMainFrame = " + isMainFrame);
        if (errorCode !== -3 && isMainFrame) {
            let url = window.webContents.getURL();
            if (!!url && validatedURL === url) {
                console.log(`did-fail-load url, retryTimes = ${retryTimes}`);
                if (retryTimes++ < 3) {
                    window.reload();
                } else {
                    window.loadURL(getErrorPageURL() + '?realurl=' + encodeURIComponent(url));
                }
            }
        }
    });
    let path = getTaskBarIconPath();
    //设置任务栏图标
    window.setAppDetails && window.setAppDetails({
        appId: 'cn.abcyun.app.desktop-pc', //这里app id需要和package.json里的id不一样,否则会强制使用应用图标(icon.png),设置不生效
        appIconPath: path,
        appIconIndex: 0,
    });

    //windows only, mac上设置ico图标会报异常
    //顶部左上角图标
    if (process.platform === 'win32') {
        window.setIcon(path);
    }


    return window;
}

const isNil = (value: any) => (value === undefined || value === null);

export {isNil}
export default {
    isNil,
    getJSApiFile,
    getTaskBarIconPath,
    createBrowserWindow
};


