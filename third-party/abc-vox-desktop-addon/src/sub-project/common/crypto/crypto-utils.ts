import {abcHost} from "../../../abc-host";
import {FileUtils} from "../file/file_utils";

export class CryptoUtils {
    /**
     * 计算文件的md5值
     * @param file {string}
     * @return Promise<string>
     */
    static async fileMd5(file: string) {
        return new Promise((resolve, reject) => {
            if (!FileUtils.fileExist(file)) {
                resolve('');
                return;
            }

            try {
                const crypto = abcHost.hostRequire('crypto');
                // 使用 original-fs 来绕过 asar 的虚拟文件系统
                const fs = file.endsWith('.asar') ? abcHost.hostRequire('original-fs') : abcHost.hostRequire('fs');
                const md5Generator = crypto.createHash('md5');
                const fd = fs.createReadStream(file);
                md5Generator.setEncoding("hex");
                fd.pipe(md5Generator);

                fd.on('end', () => {
                    md5Generator.end();
                    resolve(md5Generator.read());
                });
                fd.on("error", (error: Error) => {
                    reject(error);
                });
            } catch (error) {
                reject(error);
            }
        })
    }

    /**
     * 加密字符串
     */
    static encrypt(text, key) {
        const crypto = require('crypto');
        const iv = crypto.randomBytes(16); // 初始化向量
        const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(key), iv);
        let encrypted = cipher.update(text);
        encrypted = Buffer.concat([encrypted, cipher.final()]);
        return { iv: iv.toString('hex'), encryptedData: encrypted.toString('hex') , key: key.toString('hex')};
    }

    /**
     * 解密字符串
     */
    static decrypt(encrypted, key) {
        const crypto = require('crypto');
        const iv = Buffer.from(encrypted.iv, 'hex');
        const encryptedText = Buffer.from(encrypted.encryptedData, 'hex');
        const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(key), iv);
        let decrypted = decipher.update(encryptedText);
        decrypted = Buffer.concat([decrypted, decipher.final()]);
        return decrypted.toString();
    }

    /**
     * 加密字符串
     */
    static savePass(pass: string) {
        const crypto = require('crypto');
        const key = crypto.randomBytes(32);
        return this.encrypt(pass, key);
    }

    /**
     * 解密字符串
     */
    static loadPass(encrypted, key) {
        const {iv, encryptedData} = encrypted;
        const pass = this.decrypt({iv, encryptedData}, Buffer.from(key, 'hex'));
        return pass;
    }
}
