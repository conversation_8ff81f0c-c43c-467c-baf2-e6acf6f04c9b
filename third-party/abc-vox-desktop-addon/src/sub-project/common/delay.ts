/**
 *
 * @param timeInMills{number}
 * @returns {Promise<void>}
 */
function delay(timeInMills: number) {
    return new Promise((resolve) => {
        setTimeout(function () {
            resolve(undefined);
        }, timeInMills);
    });
}


function timeoutAction<T>(action: () => T | Promise<T>, timeout: number): Promise<T> {
    return new Promise(async (resolve, reject) => {
        let finished = false;
        (async function () {
            const result = await action();
            if (!finished) {
                finished = true;
                resolve(result);
            }
        })().catch(error=>{
            reject(error);
        });

        setTimeout(() => {
            if (!finished) {
                finished = true;
                reject(`timeout : ${timeout}ms`);
            }
        }, timeout);
    });
}

function timeoutActionAndPeriodCallback<T>(action: () => T | Promise<T>, timeout: number, callbackInterval: number, intervalCallback: (timePassed: number) => void): Promise<T> {
    return new Promise(async (resolve, reject) => {
        let startTime = new Date().getTime();
        let interval = setInterval(() => {
            intervalCallback(new Date().getTime() - startTime);
        }, callbackInterval);

        let finished = false;
        (async function () {
            const result = await action();
            if (!finished) {
                finished = true;
                clearInterval(interval);
                resolve(result);
            }
        })().then(() => {
        });


        setTimeout(() => {
            if (!finished) {
                finished = true;
                clearInterval(interval);
                reject(`timeout : ${timeout}ms`);
            }
        }, timeout);
    });
}

export {delay, timeoutAction, timeoutActionAndPeriodCallback}
