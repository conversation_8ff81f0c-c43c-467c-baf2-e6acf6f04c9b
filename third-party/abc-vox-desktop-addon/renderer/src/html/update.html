<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>更新</title>
    <link rel="stylesheet" href="./static/css/index.css">
</head>
<style>
    .update-detail {
        height: 90px;
        font-size: 14px;
        line-height: 20px;
        color: #4D7967;
        padding: 24px;
    }

    .update-detail::-webkit-scrollbar {
        display: none;
    }

    .update-btn {
        height: 32px;
        width: 92px;
        border-radius: 3px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        font-weight: 400;
        line-height: 14px;
        cursor: pointer;
    }

    .update-positive-btn {
        background: #0090FF;
        color: white;
        border: 1px solid #0090FF;
    }

    .update-negative-btn {
        color: #005ED9;
        border: 1px solid #D9DBE3;
        margin-left: 8px;
        margin-right: 24px;
    }

    .update-positive-btn:hover {
        background: #409BFF;
    }

    .update-positive-btn:active {
        background: #0087F0;
    }

    .update-negative-btn:hover {
        background: #D9EBFF;
        border: 1px solid #80BDFF;
        border-radius: 3px;
    }

    .update-negative-btn:active {
        background: #BFDEFF;
        border: 1px solid #80BDFF;
        border-radius: 3px;
    }

    .update-icon {
        width: 87px;
        height: 12px;
        margin-right: 8px;
        opacity: 0.5;
    }

    .header-bar {
        height: 140px;
        width: 100%;
        background: url("./static/image/update-header-bg.png");
        background-size: cover;
    }

    .bottom-bar {
        display: flex;
        flex-direction: row;
        align-items: center;
        height: 64px;
        width:100%;
        border-top: 1px solid #EFF3F6;
        background: white;
    }

    .update-container {
        width: 500px;
        min-height: 308px;
        display: flex;
        flex-direction: column;
        align-items: center;
        background: transparent;
        position: relative;
        border-radius: 6px;
        overflow:hidden;
    }

    .update-content {
        width: 100%;
        flex: 1;
        background: white;
    }
</style>

<body class="body-with-mask" oncontextmenu="return false">
<div class="update-container">
    <div class="header-bar">
        <div style="margin: 24px">
            <img src="./static/image/abc-update-logo.png" class="update-icon">
            <div style="margin-top: 14px; font-size: 22px;line-height:26px;color: white;">
                新版本已准备就绪
            </div>
        </div>
    </div>
    <div class="update-content">
        <div class="update-detail">
        </div>
    </div>

    <div class="bottom-bar">
        <div style="flex:1"></div>
        <div class="update-btn update-positive-btn" onclick="clickUpdateNow(this)">重启升级</div>
        <div class="update-btn update-negative-btn" onclick="clickNotUpdate(this)">稍后再说</div>
    </div>
</div>
</body>

<script>
    const ipcRenderer = require('electron').ipcRenderer;
    window.electron.abcUpgrade.setUpgradeListener({
        updateAvailable:(updateInfo)=> {
            const {releaseNotes} = updateInfo;
            let updateDetail = document.querySelector('.update-detail');
            // let versionEl = document.querySelector('.version');
            // versionEl.innerText = version || '';
            updateDetail.innerHTML = releaseNotes || '';
        }
    });

    const params = new URLSearchParams(window.location.search);
    ipcRenderer.on('__upgrade_download_progress', function (event, currentBytes, totalBytes) {
        console.log(`__upgrade_download_progress: ${currentBytes}, totalBytes`);
        const progress = ((currentBytes / totalBytes) * 100).toFixed(0);
        let progressEl = document.querySelector('.progress');
        progressEl.innerText = `${progress}%`;

    });

    function clickUpdateNow(e) {
        ipcRenderer.send('click-update-now');
    }

    function clickNotUpdate(e) {
        ipcRenderer.send('click-not-update');
    }

</script>
</html>
