<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
</head>
<style>
    html, body {
        padding: 0;
        margin: 0;
        width: 100%;
        height: 100%;
    }


    .container {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        background: white;
    }

    .tab-logo {
        height: 22px;
        width: 56px;
        position: absolute;
    }

    .prompt-container {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: 50%;
    }

    @keyframes loading-animation {
        0%, 15.625% {
            transform: rotate(0deg);
        }

        25%, 40.625% {
            transform: rotate(90deg);
        }

        50%, 65.625% {
            transform: rotate(180deg);
        }

        75%, 90.625% {
            transform: rotate(270deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    .loading-background {
        animation: loading-animation linear 3.2s infinite;
        height: 80px;
        width: 80px;
        display: inline-block;
        background-color: #007aff;
        position: absolute;
        border-radius: 12px;
        border: 1px transparent solid;
    }
</style>
<body>
<div class="container">
    <div class="prompt-container">
        <svg class="loading-background" viewBox="25 25 50 50"/>
        <img src="./static/image/logo_white.png" class="tab-logo">
    </div>
</div>
</body>
</html>
