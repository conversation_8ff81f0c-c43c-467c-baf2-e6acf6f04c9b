<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>专网设置</title>
</head>

<body>

    <div id="abc-private-network-setup"></div>

    <div id="form" style="display: flex; flex-direction: column;gap: 10px;width: 200px;"></div>

    <script>
        const TAG = "NetworkHelper";
        const ipcRenderer = window.electron.ipcRenderer;

        const messageType = `${TAG}:abc-private-network-setup`;


        const setupPPPOEForm = () => {
            // 创建输入框询问专网拨号账号和密码
            const input = document.createElement("input");
            input.type = "text";
            input.placeholder = "请输入专网拨号账号";
            form.appendChild(input);

            const passwordInput = document.createElement("input");
            passwordInput.type = "password";
            passwordInput.placeholder = "请输入专网拨号密码";
            form.appendChild(passwordInput);

            const button = document.createElement("button");
            button.textContent = "继续连接";
            button.onclick = () => {
                const account = input.value;
                const password = passwordInput.value;
                ipcRenderer.send(messageType, { account, password });
                form.innerHTML = "";
            }
            form.appendChild(button);
        }

        // 静态ip需要填写ip 子网掩码 网关
        const setupStaticForm = () => {
            const ipInput = document.createElement("input");
            ipInput.type = "text";
            ipInput.placeholder = "请输入专网ip";
            ipInput.value = "***********";
            form.appendChild(ipInput);

            const maskInput = document.createElement("input");
            maskInput.type = "text";
            maskInput.placeholder = "请输入专网子网掩码";
            maskInput.value = "*************";
            form.appendChild(maskInput);

            const gatewayInput = document.createElement("input");
            gatewayInput.type = "text";
            gatewayInput.placeholder = "请输入专网网关";
            gatewayInput.value = "***********";
            form.appendChild(gatewayInput);

            const button = document.createElement("button");
            button.textContent = "继续连接";
            button.onclick = () => {
                const ip = ipInput.value;
                const mask = maskInput.value;
                const gateway = gatewayInput.value;
                ipcRenderer.send(messageType, { ipaddr: ip, netmask: mask, gateway });
                form.innerHTML = "";
            }
            form.appendChild(button);
        }

        ipcRenderer.on(messageType, (event, arg) => {
            document.getElementById("abc-private-network-setup").innerHTML += JSON.stringify(arg) + '<br>';

            if (arg.type === "PRIVATE_NETWORK_SNIFF_RESULT" && arg.ok === true && arg.message === "PPPOE") {
                setupPPPOEForm();
            }

            if (arg.type === "PRIVATE_NETWORK_SNIFF_RESULT" && arg.ok === true && arg.message === "STATIC") {
                setupStaticForm();
            }
        })
    </script>
</body>

</html>