<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>confirm</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            width: 100%;
            position: fixed;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .loading-container {
            position: absolute;
            top: 50%;
            left: 50%;
            border-radius: 4px;
            box-shadow: 0 2px 20px 0 rgba(0, 0, 0, .1);
            transform: translate3d(-50%, -50%, 1px);
            width: 100px;
            height: 100px;
            background: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            flex-direction: column;
        }

        .loading-icon {
            height: 24px;
            width: 24px;
        }

        @keyframes loading-rotate {
            100% {
                transform: rotate(360deg);
            }
        }

        .loading-icon img {
            width: 100%;
            animation: loading-rotate 1s linear infinite;
        }

        .loading-text {
            font-size: 12px;
            color: #7a8794;
            line-height: 16px;
            font-weight: 400;
            text-align: center;
        }
        
    </style>
</head>
<body>
    <div class="loading-container">
        <div class="loading-icon">
            <img src="./static/image/loading-blue.png" alt="">
        </div>
        <div class="loading-text">
            正在重启网络...
        </div>
    </div>

    <script>
         const urlParams = new URLSearchParams(window.location.search);
         const sendId = urlParams.get('sendId');
         const text = urlParams.get('text') || '加载中...';

         document.querySelector('.loading-text').textContent = text;
       
    </script>
</body>
</html>