<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <link rel="stylesheet" href="./static/css/index.css">
</head>
<style>
    .body-with-blur {
        display: flex;
        align-items: center;
        justify-content: center;
        background-size: cover;
        backdrop-filter: blur(4px);
        background-position: center;
    }

    .background-mask {
        position: absolute;
        background-color: rgba(0, 0, 0, 0.5);
        width:100%;
        height:100%;
    }


    .container {
        position: absolute;
        display: flex;
        flex-direction: column;
        background: #ffffff;
        border-radius: 6px;
        width: 360px;
        border: gray solid 1px;
    }

    .content {
        padding: 24px;
        flex-direction: row;
        display: inline-flex;
        align-items: center;
    }

    .dialogIcon {
        width: 16px;
        height: 16px;
        margin-right: 8px;
        margin-top: 2px;
    }

    .messageView {
        flex: 1;
        font-size: 16px;
        font-weight: bold;
    }

    .bottomBar {
        padding: 10px 16px;
        display: flex;
        flex-direction: row;
        align-items: center;
        border-top: #E6EAEE solid 1px;
        justify-content: flex-end;
    }

    .btn {
        border: 1px solid #C1C9D0;
        border-radius: 3px;
        padding: 6px 18px;
        font-size: 14px;
    }

    .positiveBtn {
        background: #007AFF;
        color: #FFFFFF;
        margin-right: 8px;
        border-width: 0;
    }

    .positiveBtn:hover {
        background: #409BFF;
    }

    .positiveBtn:active {
        background: #005ED9;
    }

    .negativeBtn {
        background: white;
        color: #005ED9;
    }

    .negativeBtn:hover {
        background: #EFF3F6;
    }

    .hide {
        display: none;
    }

    .negativeBtn:active {
        background: #D3DBE1;
    }
</style>

<body style="user-select: none" class="body-with-blur">
<div class = "background-mask"></div>
<div class="container" oncontextmenu="return false">
    <div class="content">
        <img src="./static/image/dialog_info.png" class="dialogIcon"/>
        <span id='messageView' class="messageView"></span>
    </div>

    <div class="bottomBar">
        <span id='positiveBtn' class="btn positiveBtn" onclick="clickButton('positive')">关闭</span>
        <span id='negativeBtn' class="btn negativeBtn" onclick="clickButton('negative')">取消</span>
    </div>
</div>
</body>

<script>
    const ipcRenderer = require('electron').ipcRenderer;
    const params = new URLSearchParams(window.location.search);

    const dialogParams = params.get("params");
    console.log(`dialogParams = ${dialogParams}`)

    function clickButton(action) {
        console.log(`clickButton params = ${params}`);
        ipcRenderer.send('close-dialog', {
            windowId: params.get("windowId"),
            action: action,
        });
    }

    const {buttons, title, message, backgroundBlur} = JSON.parse(dialogParams);
    positiveBtn.innerHTML = buttons[0];
    if (buttons.length === 1) {
        negativeBtn.classList.add('hide');
    } else {
        negativeBtn.classList.remove('hide');
        negativeBtn.innerHTML = buttons[1];
    }


    if (backgroundBlur) {
        const bg = require('path').join(electron.remote.app.getPath('userData'), 'tmp/background-screenshot.png').replaceAll("\\", "/");
        document.body.style['background-image'] = `url('${bg}')`
    }
    messageView.innerHTML = message;

    window.addEventListener('keydown', (event) => {
        if (event.key === 'Escape' || event.keyCode === 27) {
            clickButton('negative');
        }

        if (event.key === 'Enter' || event.keyCode === 13) {
            clickButton('positive');
        }
    });
</script>
</html>
