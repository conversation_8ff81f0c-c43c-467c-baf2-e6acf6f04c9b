<!DOCTYPE html>
<html lang="en">
<head>
    <style>

        @media print {
            @page {
                margin: 0;
            }
        }

        html > body {
            margin: 0 30pt;
            padding: 0;
            width: auto;
        }
    </style>
</head>
<body>
加载中...
</body>

<script>

    const {ipc<PERSON><PERSON><PERSON>, remote} = require('electron');
    const fs = require('fs');
    const path = require('path');
    const logger = window.electron.logger;

    ipcRenderer.on('print_window_render_print', (event, html, options) => {
        logger.log(`收到打印渲染请求  options: ${JSON.stringify(options)}`);
        //let size = 'A4';
        let deviceName = '';
        let sizeNumber = 9;
        let orientationNumber = 1;
        let paperHeight = 0;
        let paperWidth = 0;
        let count = 1; //打印份数
        if (options) {
            if (options.size) {
                sizeNumber = options.size.id || 9;
                paperWidth = options.size.width || 0;
                paperHeight = options.size.height || 0;

            }
            if (!!options.landscape) {
                orientationNumber = 2;
            }

            if (options.count > 1)
                count = options.count;

            deviceName = options.deviceName || '';
        }

        // count = 3;
        // let style = document.createElement('style');
        // style.innerText = `@page {size: ${size}; margin: 0;}`;
        // style.type = 'text/css';
        // document.head.appendChild(style);
        document.getElementsByTagName('body')[0].innerHTML = html;

        setTimeout(async () => {
            logger.info(`handle print_window_render_print msg,count = ${count}`);
            var printer = remote.app.printer;
            let beforeDefaultPrinterName = await printer.GetDefaultPrinterName();

            // 设置前应当保存之前的设置，以便于在打印完成后还原
            // 这个接口里面会把 deviceName 设置为默认打印机
            await printer.SetDefaultPrinter(deviceName);
            let originalInfo = await printer.SetPrinter(deviceName, sizeNumber, orientationNumber, paperWidth, paperHeight);

            //console.log(`this : ${value}`);
            //  静默打印 不能设置打印机名字 否则不能使用默认打印机的默认配置 deviceName: deviceName
            // silent = true, 并且 不设置deviceName electron才会使用默认打印机的默认配置打印
            async function  print() {
                logger.info(`this is print : count = ${count}`);
                remote.app.printWindow.webContents.print({silent: true, deviceName:deviceName, landscape: !!options.landscape}, async (success, failureReason) => {
                    count--;
                    logger.info(`this is print callback : success: ${success}, count = ${count}, failureReason = ${failureReason}`);
                    //还没有打印完，继续一份打印
                    if (count >= 1) {
                        await print();
                        return;
                    }

                    logger.info(`print finish, restore beforeDefaultPrinterName =${beforeDefaultPrinterName}`);
                    // 还原默认打印机配置
                    await printer.SetDefaultPrinter(beforeDefaultPrinterName);
                    // 还原打印机默认的其他配置 --> 纸张 方向等
                    if (originalInfo && originalInfo.name && originalInfo.paperSizeNumber) {
                        await printer.SetPrinter(originalInfo.name, originalInfo.paperSizeNumber, originalInfo.orientation || 0, originalInfo.paperWidth || 0, originalInfo.paperHeight || 0);
                    }
                    // 继续下一个任务
                });
            }

            await print();
        }, 100);

        logger.info('handle print_window_render_print finish');
    });
</script>
</html>
