<style scoped>
/* Tooltip container */
.tooltip {
  position: relative;
  display: inline-block;
}

/* Tooltip text */
.tooltip .tooltiptext {
  visibility: hidden;
  width: 200px;
  bottom: 100%;
  left: 50%;
  margin-left: -60px;
  background-color: black;
  color: #fff;
  text-align: center;
  padding: 5px 0;
  border-radius: 6px;

  /* Position the tooltip text - see examples below! */
  position: absolute;
  z-index: 1;
}

/* Show the tooltip text when you mouse over the tooltip container */
.tooltip:hover .tooltiptext {
  visibility: visible;
}

.section {
  border: black solid 1px;
  margin: 15px 0;
}

.sectionTitle {
  margin: 5px 5px;
  font-weight: bold;
  font-size: 16px;
}

.item {
  margin-bottom: 20px;
}
</style>

<template>
  <div class="item">
    <a href="javascript:void(0)" v-on:click="openAppDir">打开配置文件目录</a>
  </div>

  <div class="item">
        <span class="tooltip">
            <label for="env">环境：</label>
            <span class="tooltiptext">客户端环境：</span>
        </span>
    <select id="env" v-model="env.selected" v-on:change="onEnvChanged">
      <option v-for="option in env.options" :value="option">{{ option }}</option>
    </select>
  </div>

  <div class="item">
        <span class="tooltip">
            <label for="entryUrlRef">入口地址：</label>
            <span class="tooltiptext">客户端启动时的入口地址：</span>
        </span>
    <select id="entryUrlRef" v-model="entryUrl.url">
      <option v-for="option in entryUrlOptions" :value="option.url">{{ option.url }}({{ option.comment }})</option>
    </select>
  </div>

  <div class="section">
    <div class="sectionTitle">
      客户端Electron设置
    </div>
    <div class="item">
            <span class="tooltip">
                <label for="initialFullscreenRef">全屏启动：</label>
                <span class="tooltiptext">是否全屏启动，关闭时程序将会根据当前电脑分辨率自动判断</span>
            </span>
      <input id="initialFullscreenRef" v-model="initialFullscreen" type="checkbox"
             :checked="initialFullscreen"/>
    </div>

    <div class="item">
            <span class="tooltip">
                <label for="chromiumDisableGpuRef">硬件加速 :</label>
                <span class="tooltiptext">部分白屏，黑屏，花屏可尝试关闭硬件加速解决</span>
            </span>
      <input id="chromiumDisableGpuRef"
             :value="!chromium.disableGpu"
             v-on:input="e => chromium.disableGpu = !e.target.checked"
             type="checkbox"
             v-on:change="onClickDisableGpu"
             :checked="!chromium.disableGpu"/>
    </div>

    <div class="item">
            <span class="tooltip">
                <label for="chromiumSandboxRef">沙盒模式 :</label>
                <span class="tooltiptext">进入打印队列又突然退出，可尝试关闭</span>
            </span>
      <input id="chromiumSandboxRef"
             :value="chromium.sandbox"
             v-on:input="e => chromium.sandbox = e.target.checked"
             type="checkbox"
             v-on:change="onChromiumSandboxChanged"
             :checked="chromium.sandbox"/>
    </div>

    <div class="item">
            <span class="tooltip">
                <label for="chromiumIgnoreCertificateErrorsRef">忽略证书错误：</label>
                <span class="tooltiptext">部分电脑安装lodop后,客户端访问会报错，可尝试关闭</span>
            </span>
      <input id="chromiumIgnoreCertificateErrorsRef" v-model="chromium.ignoreCertificateErrors" type="checkbox"
             v-on:change="onClickIgnoreCertificateErrors"
             :checked="chromium.ignoreCertificateErrors"/>
    </div>

    <div class="item">
            <span class="tooltip">
                <label for="chromiumNetworkLogRef">网络日志：</label>
                <span class="tooltiptext">开启网络日志，可查看网络请求详细情况</span>
            </span>
      <input id="chromiumNetworkLogRef" v-model="chromium.networkLog" type="checkbox"
             v-on:change="onClickLogNetLog"
             :checked="chromium.networkLog"/>
    </div>

    <div class="item">
            <span class="tooltip">
                <label for="chromiumLogRef">chrome日志：</label>
                <span class="tooltiptext">开启chrome日志，可查看chrome详细情况</span>
            </span>
      <input id="chromiumLogRef" v-model="chromium.log" type="checkbox"
             v-on:change="onClickLogChromium"
             :checked="chromium.log"/>
    </div>

    <div class="item">
            <span class="tooltip">
                <label for="chromiumCommandLinesRef">命令行参数：</label>
                <textarea id="chromiumCommandLinesRef" v-model="chromium.commandLines" style="width: 400px;" rows="5"></textarea>
                <span class="tooltiptext">客户端启动参数多个参数间用空格分格</span>
            </span>
    </div>

    <div class="item">
            <span class="tooltip">
                <label for="restartWhenRenderProcessErrorRef">render进程异常后重启：</label>
              <input id="restartWhenRenderProcessErrorRef" v-model="restartWhenRenderProcessError" type="checkbox"
                     :checked="restartWhenRenderProcessError"/>
                <span class="tooltiptext">render进程异常后自动重启</span>
            </span>
    </div>
    <div class="item">
            <span class="tooltip">
                <label for="childProcessStdioIgnoreWhenShellRef">Shell进程stdio ignore</label>
              <input id="childProcessStdioIgnoreWhenShellRef" v-model="childProcessStdioIgnoreWhenShell" type="checkbox"
                     :checked="childProcessStdioIgnoreWhenShell"/>
                <span class="tooltiptext">以Shell开启进程时stdio ignore(解决部分电脑后台服务进程无法调起问题)</span>
            </span>
    </div>
  </div>

  <div class="section" v-if="isWin32">
    <div class="sectionTitle">数据存储</div>
    <div class="item">
      <span class="tooltip">
          <span class="tooltiptext">设备程序数据目录</span>
          <label for="extraAppDataPathRefRef">自定义程序文件路径：</label>
          <select id="extraAppDataPathRef" v-model="extraAppDataPath" v-on:change="onExtraAppDataPathChanged">
            <option v-for="option in driverOptions" :value="option">{{ option }}</option>
          </select>
      </span>
    </div>
  </div>

  <div>
    <button v-on:click="onClickSave"> 保存并重启</button>
  </div>
</template>
<script>
import {useRouter} from 'vue-router'
import _ from 'lodash'

const appConfig = window.electron.appConfig;


const kEnv = "env";
const KEntryUrl = "entryUrl";
const kInitialFullscreen = "initialFullscreen";
const kRestartWhenRenderProcessError = "restartWhenRenderProcessError";
const kChildProcessStdioIgnoreWhenShell = "childProcessStdioIgnoreWhenShell";
const kExtraAppDataPath = "extraAppDataPath";

const kChromiumCommandLines = "chromium.commandLines";

const kDisableGpuOption = "--disable-gpu";
const kSandboxOption = "--no-sandbox";
const kIgnoreCertificateErrors = "--ignore-certificate-errors";
const kLogNetLog = "--log-net-log";
const kLogChromium = "--enable-logging";

const commandLines = appConfig.get(kChromiumCommandLines) || "";

const entryUrlConfigs = [{
  env: "dev",
  urls: [
    {
      url: "abcyun://dev.abczs.cn/login",
      comment: '默认开发地址'
    },
    {
      url: "https://dev.abczs.cn/login",
      comment: '默认开发地址'
    },
    ..._.range(1, 100).map(item => {
      return {
        url: `http://${item}.own.abczs.cn/login`,
        comment: `开发地址${item}`
      };
    })
  ],
},
  {
    env: "test",
    urls: [
      {
        url: "abcyun://test.abczs.cn/login",
        comment: '默认测试地址'
      },
      {
        url: "https://test.abczs.cn/login",
        comment: '默认测试地址'
      },
      ..._.range(1, 100).map(item => {
        return {
          url: `http://${item}.ftest.abczs.cn/login`,
          comment: `测试地址${item}`
        };
      })
    ]
  }
  , {
    env: "prod",
    urls: [
      {
        url: "abcyun://www.abcyun.cn/login",
        comment: '默认地址'
      },
      {
        url: "https://www.abcyun.cn/login",
        comment: "备用地址",
      },
      {
        url: "https://abcyun.cn/login",
        comment: "备用地址2",
      },

      {
        url: "https://abcyun.cn/call/display",
        comment: "叫号屏地址",
      }
    ]
  }];


const getAppConfig = (key, defaultValue) => {
  const value = appConfig.get(key);
  if (value === undefined || value === null) {
    return defaultValue;
  }

  return value;
}

export default {
  name: 'Setting',

  setup() {
    const router = useRouter()
    const toHome = (() => {
      router.push({
        name: 'home'
      })
    })
    return {
      toHome
    }
  },

  data() {
    const currentURL = appConfig.get(KEntryUrl);
    let env = appConfig.get(kEnv);
    if (!env)
      env = "prod";

    return {
      isWin32: window.process.platform === 'win32',
      env: {
        selected: env,
        options: ["dev", "test", "prod"]
      },
      entryUrl: {
        url: currentURL
      },
      initialFullscreen: appConfig.get(kInitialFullscreen),
      restartWhenRenderProcessError: appConfig.get(kRestartWhenRenderProcessError),
      childProcessStdioIgnoreWhenShell: getAppConfig(kChildProcessStdioIgnoreWhenShell, true),
      extraAppDataPath: appConfig.get(kExtraAppDataPath),
      chromium: {
        sandbox: commandLines.indexOf(kSandboxOption) == -1,
        disableGpu: commandLines.indexOf(kDisableGpuOption) >= 0,
        ignoreCertificateErrors: commandLines.indexOf(kIgnoreCertificateErrors) >= 0,
        commandLines: commandLines,
        networkLog: commandLines.indexOf(kLogNetLog) >= 0,
        log: commandLines.indexOf(kLogChromium) >= 0,
      },
    }
  },

  computed: {
    entryUrlOptions() {
      const options = [...entryUrlConfigs.find(item => item.env === this.env.selected).urls];

      if (this.entryUrl.url && options.findIndex(item => item.url === this.entryUrl.url) < 0)
        options.push({url: this.entryUrl.url, comment: '自定义地址'});

      return options;
    },

    driverOptions() {
      if (window.process.platform === 'win32') {
        const driveLetters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        let drives = [];
        for (let i = 0; i < driveLetters.length; i++) {
          const drivePath = `${driveLetters[i]}:\\`;
          try {
            window.require('fs').accessSync(drivePath);
            drives.push(drivePath);
          } catch (err) {
            // Drive does not exist
          }
        }

        return drives;
      }
    }
  },
  components: {},

  methods: {
    openAppDir: function () {
      window.remote.shell.showItemInFolder(`${window.require('path').resolve(window.remote.app.getPath('userData'), "abc-conf.ini")}`);
    },

    onEnvChanged: function () {
      this.entryUrl.url = entryUrlConfigs.find(item => item.env === this.env.selected).urls[0].url;
    },

    onClickLogNetLog: function (event) {
      const logPath = window.require('path').resolve(window.remote.app.getPath('userData'), "./logs/netlog.json");
      if (this.chromium.networkLog) {
        if (this.chromium.commandLines.indexOf(kLogNetLog) === -1) {
          this.chromium.commandLines += `${this.chromium.commandLines ? " " : ""}${kLogNetLog}=${logPath}`;
        }
      } else {
        if (this.chromium.commandLines.indexOf(kLogNetLog) >= 0) {
          this.chromium.commandLines = this.chromium.commandLines.replace(new RegExp(`${kLogNetLog}=.*netlog\.json`), "");
        }
      }

      this.chromium.commandLines = this.chromium.commandLines.trim();
    },

    onClickLogChromium: function (event) {
      const logPath = window.require('path').resolve(window.remote.app.getPath('userData'), "./logs/chromium.log");
      if (this.chromium.log) {
        if (this.chromium.commandLines.indexOf(kLogChromium) === -1) {
          this.chromium.commandLines += `${this.chromium.commandLines ? " " : ""}${kLogChromium} --v=2 --log-file=${logPath}`;
        }
      } else {
        if (this.chromium.commandLines.indexOf(kLogChromium) >= 0) {
          this.chromium.commandLines = this.chromium.commandLines.replace(new RegExp(`${kLogChromium} --v=2 --log-file=.*chromium\.log`), "");
        }
      }

      this.chromium.commandLines = this.chromium.commandLines.trim();
    },

    onChromiumSandboxChanged: function (event) {
      if (!this.chromium.sandbox) {
        if (this.chromium.commandLines.indexOf(kSandboxOption) === -1) {
          this.chromium.commandLines += `${this.chromium.commandLines ? " " : ""}${kSandboxOption}`;
        }
      } else {
        if (this.chromium.commandLines.indexOf(kSandboxOption) >= 0) {
          this.chromium.commandLines = this.chromium.commandLines.replace(kSandboxOption, "");
        }
      }

      this.chromium.commandLines = this.chromium.commandLines.trim();
    },

    onClickDisableGpu: function () {
      if (this.chromium.disableGpu) {
        if (this.chromium.commandLines.indexOf(kDisableGpuOption) === -1) {
          this.chromium.commandLines += `${this.chromium.commandLines ? " " : ""}${kDisableGpuOption}`;
        }
      } else {
        if (this.chromium.commandLines.indexOf(kDisableGpuOption) >= 0) {
          this.chromium.commandLines = this.chromium.commandLines.replace(kDisableGpuOption, "");
        }
      }

      this.chromium.commandLines = this.chromium.commandLines.trim();
    },

    onClickSave: async function () {
      const env = this.env.selected;
      if (env !== appConfig.get(KEntryUrl)) {
        appConfig.set(kEnv, env);
      }

      const entryUrl = this.entryUrl.url;
      if (entryUrl !== appConfig.get(KEntryUrl)) {
        appConfig.set(KEntryUrl, entryUrl);
      }

      if (this.initialFullscreen !== appConfig.get(kInitialFullscreen)) {
        appConfig.set(kInitialFullscreen, this.initialFullscreen);
      }

      if (this.restartWhenRenderProcessError !== appConfig.get(kRestartWhenRenderProcessError)) {
        appConfig.set(kRestartWhenRenderProcessError, this.restartWhenRenderProcessError);
      }

      if (this.childProcessStdioIgnoreWhenShell !== appConfig.get(kChildProcessStdioIgnoreWhenShell)) {
        appConfig.set(kChildProcessStdioIgnoreWhenShell, this.childProcessStdioIgnoreWhenShell);
      }

      if (this.chromium.commandLines !== appConfig.get(kChromiumCommandLines)) {
        appConfig.set(kChromiumCommandLines, this.chromium.commandLines);
      }

      if (this.extraAppDataPath !== appConfig.get(kExtraAppDataPath)) {
        appConfig.set(kExtraAppDataPath, this.extraAppDataPath);
      }

      remote.app.relaunchApp();
    },

    onClickIgnoreCertificateErrors: function () {
      if (this.chromium.ignoreCertificateErrors) {
        if (this.chromium.commandLines.indexOf(kIgnoreCertificateErrors) === -1) {
          this.chromium.commandLines += `${this.chromium.commandLines ? " " : ""}${kIgnoreCertificateErrors}`;
        }
      } else {
        if (this.chromium.commandLines.indexOf(kIgnoreCertificateErrors) >= 0) {
          this.chromium.commandLines = this.chromium.commandLines.replace(kIgnoreCertificateErrors, "");
        }
      }

      this.chromium.commandLines = this.chromium.commandLines.trim();
    },
  },
  beforeCreate() {
    document.title = `设置`;
  }
}
</script>
<style scoped>
</style>
