<template>
  <div
      style="flex-direction: row;align-items: center; display: flex; height: 45px; border-bottom: gray 2px solid; margin-bottom: 20px">
    <button v-on:click="onClickBack" style="margin-right: 40px">后退</button>
    <button v-on:click="onClickForward">前进</button>
  </div>
  <router-view v-slot="{ Component }">
    <keep-alive>
      <component :is="Component"/>
    </keep-alive>
  </router-view>
</template>

<script>
export default {
  setup() {
  },

  name: 'App',
  components: {},
  methods: {
    onClickBack: function () {
      window.history.back();

    },

    onClickForward: function () {
      window.history.forward();
    }
  },
  beforeCreate() {
    document.title = ``;
  }
}
</script>
