import {electron} from "./jsaipienv";
import {Completer} from "./common/completer";
import {electronRequire, windowAny} from "./common/compiler";

const promiseIpc = electronRequire('electron-promise-ipc');

const ipcRenderer = windowAny.require('electron').ipcRenderer;

const kChannelName = "call_download_manager";
const kChannelCallbackName = "call_download_manager_callback";


interface OnProcessCallback {
    (currentBytes: number, totalBytes?: number): void;
}

interface OnCompleteCallback {
    (error?: any): void;
}


interface EventBase {
    type: "progress" | "complete"
}

interface ProgressEvent extends EventBase {
    currentBytes: number;
    totalBytes?: number
}


interface CompleteEvent extends EventBase {
    error?: any;
    fullPath: string;
}


class Downloader {
    private _waitCompleter = new Completer<{ fullPath: string }>();

    constructor(private id: number, private onProgress?: OnProcessCallback, private onComplete?: OnCompleteCallback) {
        ipcRenderer.on(kChannelCallbackName, (sysEvent, id: number, event: EventBase) => {
            if (this.id != id) return;


            switch (event.type) {
                case "complete": {
                    const completeEvent = event as CompleteEvent;
                    this.onComplete && this.onComplete(completeEvent.error);
                    if (completeEvent.error) {
                        this._waitCompleter.reject(completeEvent.error);
                    } else {
                        this._waitCompleter.resolve({fullPath: completeEvent.fullPath});
                    }
                    break;
                }

                case "progress": {
                    const progressEvent = event as ProgressEvent;
                    this.onProgress && this.onProgress(progressEvent.currentBytes, progressEvent.totalBytes);
                    break;
                }
            }
        });
    }

    public cancel(): void {
        promiseIpc.send(kChannelName, 'cancelDownload', this.id);
    }

    public waitComplete(): Promise<{ fullPath: string }> {
        return this._waitCompleter.promise;
    }
}

class DownloadManager {
    downloadFile(options: { url: string, file: string, md5?: string; onProcess: OnProcessCallback, onComplete: OnCompleteCallback }): Promise<Downloader> {
        const {url, file, md5, onProcess, onComplete} = options;
        return promiseIpc.send(kChannelName, 'downloadFile', {url: url, file: file, md5: md5}).then(rsp => {
            const idNum: number = this._handleResult(rsp);
            return new Downloader(idNum, onProcess, onComplete);
        });
    }

    private _handleResult(ret: any): any {
        if (ret) {
            if (ret.success)
                return ret.ret;
            else
                throw ret.ret;
        } else {
            throw "unknow";
        }
    }
}

electron.downloadManager = new DownloadManager();

