/**
 * 用于读取应用配置
 */

import {electron} from "./jsaipienv";
import {electronRequire} from "./common/compiler";

const {remote} = electron;

class AppConfigJSApi {
    private readonly _abcConfig: { [key: string]: any };

    constructor() {
        const fs = electronRequire('fs');
        const ini = electronRequire('ini');
        this._abcConfig = ini.parse(fs.readFileSync(this._appConfigFile(), 'utf-8'));
    }

    /**
     * 读取客户端配置文件key-value
     * @param key {string} 格式: xx.xx
     * e.g: electron.appConfig.get('proxy.proxyCarrier')
     * @return {string|boolean}
     */
    get(key: string): any {
        if (!(this instanceof AppConfigJSApi)) {
            return electron.appConfig.get(key);
        }
        const domains = key.split(".");
        let currentValue = this._abcConfig;
        for (const domain of domains) {
            currentValue = currentValue[domain];
            if (currentValue === undefined) return undefined;
        }

        return currentValue;
    }

    set(key: string, value: any): void {
        if (!(this instanceof AppConfigJSApi)) {
            return;
        }

        electron.logger.log(`appConfig.set(${key}, ${value})`);
        
        const domains = key.split(".");
        let currentValue = this._abcConfig;
        for (let i = 0; i < domains.length; ++i) {
            let domain = domains[i];
            if (currentValue[domain] == undefined) {
                currentValue[domain] = {}
            }
            if (i == domains.length - 1) {
                currentValue[domain] = value;
            }

            currentValue = currentValue[domain];
        }


        const fs = electronRequire('fs');
        const ini = electronRequire('ini');
        fs.writeFileSync(this._appConfigFile(), ini.encode(this._abcConfig));
    }

    private _appConfigFile(): string {
        return `${remote.app.getPath('userData')}${electronRequire('path').sep}abc-conf.ini`
    }
}

electron.appConfig = new AppConfigJSApi();
