//@ts-ignore
const windowAny = window as any;
const electronRequire = (module)=>{
    try {
        return windowAny.require(module);
    }
    catch (e) {
        if (module === "@electron/remote") {
            return require("@electron/remote");
        }
        else if (module === "electron") {
            return require("electron");
        }
        else if (module === "ini") {
            return require("ini");
        }
        else if (module === "iconv-lite") {
            return require("iconv-lite");
        }
        else if (module === "adm-zip") {
            return require("adm-zip");
        }

        console.error(`Failed to require module ${module}`);
    }
};
export {windowAny, electronRequire}