class Completer<T> {
    value?:T;
    error:any;
    promise: Promise<T>;

    finished = false;

    _resolve: (value?: T | PromiseLike<T>) => void;
    _reject: (reason?: any) => void;

    constructor() {
        this.promise = new Promise(((resolve, reject) => {
            this._resolve = resolve;
            this._reject = reject;
        }));
    }

    /**
     * @param value
     */
    resolve(value: T) {
        if (this.finished)
            return;

        this.finished = true;
        this._resolve(value);
        this.value = value;
    }

    /**
     *
     * @param error
     */
    reject(error: any) {
        if (this.finished)
            return;

        this.finished = true;
        this.error = error;
        this._reject(error);
    }
}


const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export {Completer, delay}
