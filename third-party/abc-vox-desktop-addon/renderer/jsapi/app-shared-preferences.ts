/**
 * 用于读取应用配置
 */

import {electron} from "./jsaipienv";
const {remote}  = electron;

class AppSharedPreferences {

    constructor() {
    }

    /**
     * 读取客户端配置文件key-value
     * @param key {string} 格式: xx.xx
     * e.g: electron.appConfig.get('proxy.proxyCarrier')
     * @return {string|boolean}
     */
    get(key: string): any {
        const appAny = remote.app as any;
        return appAny.getSharedPreference(key);
    }


    set(key: string, value: any): void {
        const appAny = remote.app as any;
        return appAny.setSharedPreference(key, value);
    }
}

electron.appSharedPreferences = new AppSharedPreferences();
