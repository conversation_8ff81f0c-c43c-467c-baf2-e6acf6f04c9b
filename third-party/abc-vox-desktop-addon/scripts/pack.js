const fs = require("fs");
const AdmZip = require("adm-zip");
const path = require("path");

async function main() {
    const fs = require('fs');
    const outputDir = "./dist_for_zip";
    const srcPath = `./dist`;


    const configJSONString = JSON.stringify({
        name: "abc-vox-desktop-addon",
        buildTime: process.env.BUILD_TIME || new Date().getTime(),
        repoTag: process.env.BUILD_TAG,
        type: "embed",
        entry: "index.js"
    }, null, 2);
    fs.writeFileSync(`${srcPath}/conf.json`, configJSONString, {
        encoding: "utf8"
    });
    const targetZipName = `abc-vox-desktop-addon.zip`;
    const destZip = `${outputDir}/${targetZipName}`;


    if (fs.existsSync(outputDir)) {
        fs.rmdirSync(outputDir, {recursive: true});
    }

    fs.mkdirSync(outputDir);

    console.log(`正在生成:${destZip} from ${srcPath}`);

    const AdmZip = require('adm-zip');
    const zip = new AdmZip();

    // add local file
    zip.addLocalFolder(srcPath);
    // or write everything to disk
    zip.writeZip(destZip);
}


main().then();
