const path = require('path');
const {VueLoaderPlugin} = require('vue-loader');
const CopyPlugin = require('copy-webpack-plugin');
const packageJson = require('../../../package.json');

const common_config = {
    module: {
        rules: [
            {
                test: /\.mjs$/,
                include: /node_modules/,
                type: 'javascript/auto'
            },
            {
                test: /\.vue$/,
                loader: 'vue-loader',
            },
            {
                test: /\.scss$/,
                use: [
                    'style-loader',//https://github.com/vuejs/vue-style-loader/issues/42
                    'css-loader',
                ]
            },
            {
                test: /\.css$/i,
                use: ["style-loader", "css-loader"],
            },
            {
                test: /\.(woff|woff2|eot|ttf|svg)$/,
                use: [
                    {
                        loader: 'url-loader',
                        options: {
                            limit: 10000,
                            name: './font/[hash].[ext]',
                            publicPath: 'dist'
                        }
                    }
                ]
            },
            {
                test: /\.(png|jpg|gif)$/i,
                use: [
                    {
                        loader: 'url-loader',
                        options: {
                            limit: 8192,
                        },
                    },
                ],
            },

            {
                test: /\.tsx?$/,
                use: [{
                    loader: 'ts-loader',
                    options: {
                        configFile: "tsconfig-render.json"
                    }
                }],
                exclude: function (modulePath) {
                    return /node_modules/.test(modulePath) || /native-lib/.test(modulePath);
                },
            },
            {
                test: /node_modules[\/\\](iconv-lite)[\/\\].+/,
                resolve: {
                    aliasFields: ['main']
                }
            }
        ]
    },
    plugins: [
        new VueLoaderPlugin(),
        new CopyPlugin([
            {from: 'renderer/src/html', to: path.resolve(__dirname, '../dist/renderer')},
        ]),
    ],
    resolve: {
        extensions: ['.js', '.jsx', '.json', '.ts', '.tsx'],
        modules: [path.resolve(__dirname, '../node_modules')],
        alias: {
            '@': path.resolve('./renderer/src')
        }
    },
    externals: [
        (function () {
            const IGNORES = [
                ...Object.keys(packageJson.dependencies),
                'electron',
                'path',
                'fs',
                'child_process'
            ];
            return function (context, request, callback) {
                if (IGNORES.indexOf(request) >= 0) {
                    return callback(null, "require('" + request + "')");
                }
                return callback();
            };
        })(),
    ],

    node: {
        __dirname: false
    },

    target: "web",
    output: {
        filename: '[name].js',
        path: path.resolve('./dist/renderer'),
    },
    entry: {
        "render_index": ["./renderer/src/vue-index.ts"],
    },
}

module.exports = common_config;
