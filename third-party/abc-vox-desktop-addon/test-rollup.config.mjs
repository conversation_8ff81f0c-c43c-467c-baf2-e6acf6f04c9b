import livereload from "rollup-plugin-livereload";
import resolve from "rollup-plugin-node-resolve";
import commonjs from "rollup-plugin-commonjs";
import serve from "rollup-plugin-serve";
import json from '@rollup/plugin-json'
import {babel} from "@rollup/plugin-babel";

const isDevelopment = process.env.NODE_ENV !== 'production';

export default {
    input: 'test/index.ts',
    output: [
        {
            file: 'dist/test/index.cjs.js',
            format: 'cjs',
        },
        {
            file: 'dist/test/index.esm.js',
            format: 'es',
        },
        {
            name: 'AbcFeEngine',
            file: 'dist/test/index.umd.js',
            format: 'umd',
        },
    ],
    plugins: [
        babel({
            include: ['src/**/*', "test/**/*"],
            exclude: 'node_modules/**',
            extensions: ['.js', '.ts'],
        }),
        json(),
        resolve({
            extensions: ['.js', '.ts'],
            browser: true
        }),
        commonjs(),
        // typescript({
        //     exclude: 'node_modules/**',
        // }),
        isDevelopment ? serve({
            contentBase: 'dist',
            headers: {
                'Access-Control-Allow-Origin': '*',
            },
        }) : null,
        isDevelopment ? livereload('dist') : null,
    ]
}
