'use strict';

const bsdiff = require('./build/Release/bsdiff.node');

function promisify (fnName) {
  const fn = bsdiff[fnName];
  bsdiff[fnName] = function () {
    var args = Array.from(arguments);
    return new Promise(function (resolve, reject) {
      args.push(function (result) {
        resolve(result)
      });
      fn.apply(bsdiff, args);
    });
  };
}

promisify('diff');
promisify('patch');

module.exports = bsdiff;
