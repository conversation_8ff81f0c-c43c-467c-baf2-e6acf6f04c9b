{"_from": "bsdiff-node@^2.5.0", "_id": "bsdiff-node@2.5.0", "_inBundle": false, "_integrity": "sha512-efTHSHFd/t65Ui7HiGOGr5dMg7X35zHOHWPVngpkEbsr4UtwkHi/BZ5AFFIeVo6Tp4yNX3T62uW2E00ptJrifg==", "_location": "/bsdiff-node", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "bsdiff-node@^2.5.0", "name": "bsdiff-node", "escapedName": "bsdiff-node", "rawSpec": "^2.5.0", "saveSpec": null, "fetchSpec": "^2.5.0"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/bsdiff-node/-/bsdiff-node-2.5.0.tgz", "_shasum": "a8c8fec2fa822f6fab2e4a786de17a9df587c78c", "_spec": "bsdiff-node@^2.5.0", "_where": "D:\\workspace\\abc\\abcyun_clinic_desktop", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "binary": {"module_name": "bsdiff", "module_path": "./build/Release/", "host": "https://github.com/Brouilles/bsdiff-node/releases/download/", "remote_path": "{version}"}, "bugs": {"url": "https://github.com/Brouilles/bsdiff-node/issues"}, "bundleDependencies": false, "dependencies": {"node-addon-api": "^1.7.1"}, "deprecated": false, "description": "An binary diff and patch library based on bsdiff algorithm for NodeJS (Windows, Mac, Linux).", "gypfile": true, "homepage": "https://github.com/Brouilles/bsdiff-node", "keywords": ["bsdiff", "bspatch", "binary diff", "binary patch"], "license": "MIT", "main": "index.js", "name": "bsdiff-node", "repository": {"type": "git", "url": "git+https://github.com/Brouilles/bsdiff-node.git"}, "scripts": {"install": "node-gyp rebuild", "install-verbose": "node-gyp rebuild --verbose", "test": "mocha"}, "version": "2.5.0"}