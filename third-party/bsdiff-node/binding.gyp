{
  "targets": [
    {
      "target_name": "bsdiff",
      "sources": [
        "src/Main.cpp",
        "src/Util.cpp",
        "src/nodejs_utils.cc",
        "src/c/bsdiff/bsdiff.c",
        "src/c/bspatch/bspatch.c",
        "src/c/bzip2/bzlib.c",
        "src/c/bzip2/compress.c",
        "src/c/bzip2/crctable.c",
        "src/c/bzip2/randtable.c",
        "src/c/bzip2/blocksort.c",
        "src/c/bzip2/huffman.c",
        "src/c/bzip2/decompress.c"
      ],
      "defines": [ "NAPI_DISABLE_CPP_EXCEPTIONS=1" ],
      "include_dirs": [
        "include", "./src/c/bzip2",
        "<!@(node -p \"require('node-addon-api').include\")",
      ]
    }
  ]
}
