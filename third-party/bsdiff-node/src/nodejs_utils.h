#pragma once
#include <string>
#include <functional>
#include <napi.h>
#include <uv.h>



namespace abc {
	namespace base {
		typedef std::function<void(void)> VOID_FUNCTION;
		/*
		* 用于保存v8 funciton函数,延长其生命周期
		*/
		class NodeJsCallback {
		public:
			NodeJsCallback(const Napi::Function& fun);
			~NodeJsCallback();

			Napi::FunctionReference& Callback();

			Napi::Env Env();

		private:
			Napi::Env env_;
			Napi::FunctionReference callback_;
		};

		/*
		* 互斥锁
		*/
		class MutexLock {
		public:
			MutexLock();
			~MutexLock();
			void Lock();
			void UnLock();

		private:
			uv_mutex_t lock_;
		};

		/*
		* 在一个代码块里自动加锁和解锁
		*/
		class AutoLock {
		public:
			AutoLock(MutexLock& lock);
			~AutoLock();

		private:
			MutexLock& lock_;
		};

		/*
		* 信号量
		*/
		class Semphore {
		public:
			Semphore();
			~Semphore();

			void Wait();
			void Signal();

		private:
			uv_sem_t sem_;
		};

		/**
		* 线程任务队列,创建一个线程,所有post到这个线程队列的任务都在同个线程里执行
		*/
		class ThreadHandler {
		public:
			ThreadHandler();
			~ThreadHandler();

			void Start();
			void Stop(bool sync = false);
			void PostTask(const VOID_FUNCTION& fun);

		private:
			static void ThreadHandlerMainFunc(void* thread_handler);
			void RunLoop();
			uv_thread_t thread_;
			volatile bool exit_;
			std::vector<VOID_FUNCTION>  tasks_;

			MutexLock task_lock_;
			Semphore task_list_semphore_;
		};

		class NodeJSUtils{
		public:
			/**
			* 在Node JS主环境中执行
			*/
			static void RunInNodeMainThread(const VOID_FUNCTION& fun);

			/*
			*异步线程执行,主线程里回调
			*/
			static void RunAsync(const VOID_FUNCTION& worker, const VOID_FUNCTION& callback_in_main_thread);
		};
	}
}
