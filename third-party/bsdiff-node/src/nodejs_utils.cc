#include "nodejs_utils.h"

namespace abc {
	namespace base {

		NodeJsCallback::NodeJsCallback(const Napi::Function& fun):env_(fun.Env()){
			callback_ = Napi::Persistent(fun );
		}

		NodeJsCallback::~NodeJsCallback() {

		}

		Napi::FunctionReference& NodeJsCallback::Callback() {
			return callback_;
		}

		Napi::Env NodeJsCallback::Env() {
			return env_;
		}

		MutexLock::MutexLock() {
			uv_mutex_init(&lock_);
		}

		MutexLock::~MutexLock() {
			uv_mutex_destroy(&lock_);
		}

		void MutexLock::Lock() {
			uv_mutex_lock(&lock_);
		}

		void MutexLock::UnLock() {
			uv_mutex_unlock(&lock_);
		}

		AutoLock::AutoLock(MutexLock& lock) : lock_(lock) {
			lock_.Lock();
		}

		AutoLock::~AutoLock() {
			lock_.UnLock();
		}

		Semphore::Semphore() {
			uv_sem_init(&sem_, 1);
		}

		Semphore::~Semphore() {
			uv_sem_destroy(&sem_);
		}

		void Semphore::Wait() {
			uv_sem_wait(&sem_);
		}
		void Semphore::Signal() {
			uv_sem_post(&sem_);
		}

		void ThreadHandler::ThreadHandlerMainFunc(void* thread_handler)
		{
			((ThreadHandler*)(thread_handler))->RunLoop();
		}

		ThreadHandler::ThreadHandler(): exit_(false){
		}

		ThreadHandler::~ThreadHandler() {
			Stop();
		}

		void ThreadHandler::Start() {
			uv_thread_create(&thread_, ThreadHandlerMainFunc, this);
		}

		void ThreadHandler::Stop(bool sync) {
			exit_ = true;
			task_list_semphore_.Signal();
			if (sync) {
				uv_thread_join(&thread_);
			}

			thread_ = 0;
		}

		void ThreadHandler::PostTask(const VOID_FUNCTION& fun) {
			AutoLock lock(task_lock_);
			tasks_.push_back(fun);

			task_list_semphore_.Signal();
		}

		void ThreadHandler::RunLoop() {
			while (!exit_) {
				task_list_semphore_.Wait();
				std::function<void(void)> task;
				{
					AutoLock lock(task_lock_);
					if (tasks_.size()) {
						task = tasks_[0];
						tasks_.erase(tasks_.begin());
					}
				}

				if (task) {
					task();
				}
			}
		}

		class WorkT {
		public:
			VOID_FUNCTION wroker_in_async_thread;
			VOID_FUNCTION callback_in_main_thread;
		};

		void RunInAsyncThread(uv_work_t* req) {
			WorkT* inner_work = (WorkT*)(req->data);
			if (inner_work->wroker_in_async_thread) {
				inner_work->wroker_in_async_thread();
			}
		}
		void RunInMainThread(uv_work_t* req, int status) {
			(void)status;
			WorkT* inner_work = (WorkT*)(req->data);
			if (inner_work->callback_in_main_thread) {
				inner_work->callback_in_main_thread();
			}

			delete inner_work;
			delete req;
		}

		void NodeJSUtils::RunInNodeMainThread(const VOID_FUNCTION& fun) {
			RunAsync(nullptr, fun);
		}

		void NodeJSUtils::RunAsync(const VOID_FUNCTION& worker, const VOID_FUNCTION& callback_in_main_thread) {
			uv_work_t* req = new uv_work_t();
			WorkT* inner_work = new WorkT();
			inner_work->wroker_in_async_thread = worker;
			inner_work->callback_in_main_thread = callback_in_main_thread;
			req->data = inner_work;
			uv_queue_work(uv_default_loop(), req, RunInAsyncThread, RunInMainThread);
		}
	}
}
