#include <napi.h>
#include "Util.hpp"
#include "nodejs_utils.h"

extern "C" {
  #include "c/bsdiff/bsdiff.h"
  #include "c/bspatch/bspatch.h"
}

namespace bsdpNode {
  using namespace abc::base;

  void diff(const Napi::CallbackInfo& args)
  {
    auto env = args.Env();
    if(args.Length() < 4 || !args[0].IsString() || !args[1].IsString() || !args[2].IsString())
    {
        Napi::Error::New(env, "Invalid arguments.").ThrowAsJavaScriptException();
        return;
    }

    auto callback = std::make_shared<NodeJsCallback>(args[3].As<Napi::Function>());

    std::shared_ptr< std::string> oldfile = std::make_shared<std::string>(Utf8ToAnsi(args[0].As<Napi::String>().Utf8Value()));
    std::shared_ptr<std::string> newfile = std::make_shared<std::string>(Utf8ToAnsi(args[1].As<Napi::String>().Utf8Value()));
    std::shared_ptr<std::string> patchfile = std::make_shared<std::string>(Utf8ToAnsi(args[2].As<Napi::String>().Utf8Value()));

	std::shared_ptr<char> error(new char[1024] {0}, std::default_delete<char[]>());
    std::shared_ptr<int> errorCode = std::make_shared<int>(0);

	NodeJSUtils::RunAsync([=] {
        *errorCode = bsdiff(error.get(), Utf8ToAnsi(*oldfile).c_str(), Utf8ToAnsi(*newfile).c_str(), Utf8ToAnsi(*patchfile).c_str(), nullptr, nullptr);
		},
		[=] {
            Napi::Env v8Env = callback->Callback().Env();
			Napi::Object obj = Napi::Object::New(v8Env);
			obj.Set("errorCode", *errorCode);
			obj.Set("error", Napi::String::New(v8Env, error.get()));
			callback->Callback().Call({ obj });
		});
  }

  
  void patch(const Napi::CallbackInfo& args)
  {
      auto env = args.Env();
    if(args.Length() < 4 || !args[0].IsString() || !args[1].IsString() || !args[2].IsString())
    {
        Napi::Error::New(env, "Invalid arguments.").ThrowAsJavaScriptException();
      return;
    }

    auto callback = std::make_shared<NodeJsCallback>(args[3].As<Napi::Function>());


	std::shared_ptr< std::string> oldfile = std::make_shared<std::string>(Utf8ToAnsi(args[0].As<Napi::String>().Utf8Value()));
	std::shared_ptr<std::string> newfile = std::make_shared<std::string>(Utf8ToAnsi(args[1].As<Napi::String>().Utf8Value()));
	std::shared_ptr<std::string> patchfile = std::make_shared<std::string>(Utf8ToAnsi(args[2].As<Napi::String>().Utf8Value()));

    std::shared_ptr<char> error(new char[1024]{ 0 }, std::default_delete<char[]>());
    std::shared_ptr<int> errorCode = std::make_shared<int>(0);

	NodeJSUtils::RunAsync([=] {
        *errorCode = bspatch(error.get(), oldfile->c_str(), newfile->c_str(), patchfile->c_str(), nullptr, nullptr);
		},
		[=] {
			Napi::Env v8Env = callback->Callback().Env();
			Napi::HandleScope scope(v8Env);
			Napi::Object obj = Napi::Object::New(v8Env);
			obj.Set("errorCode", *errorCode);
            obj.Set("error", Napi::String::New(v8Env, error.get()));
			callback->Callback().Call({ obj });
  });
  }


  static Napi::Object Init(Napi::Env env, Napi::Object exports) {
      exports.Set(Napi::String::New(env, "diff"), Napi::Function::New(env, diff));
      exports.Set(Napi::String::New(env, "patch"), Napi::Function::New(env, patch));

      return exports;
  }

  NODE_API_MODULE(NODE_GYP_MODULE_NAME, Init);
}

