<?xml version="1.0" encoding="UTF-8"?>
<!-- Copyright (c) .NET Foundation and contributors. All rights reserved. Licensed under the Microsoft Reciprocal License. See LICENSE.TXT file in the project root for full license information. -->
<Wix xmlns="http://wixtoolset.org/schemas/v4/wxs">
  <Fragment>
    <WixVariable Id="WixUISupportPerUser" Value="1" Overridable="yes"/>
    <WixVariable Id="WixUISupportPerMachine" Value="1" Overridable="yes"/>

    <PropertyRef Id="ApplicationFolderName"/>

    <UI Id="WixUI_Assisted">
      <Dialog Id="InstallScopeDialog" Width="370" Height="270" Title="!(loc.InstallScopeDlg_Title)" KeepModeless="yes">
        <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="370" Height="44" TabSkip="no" Text="!(loc.InstallScopeDlgBannerBitmap)"/>
        <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="370" Height="0"/>
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="370" Height="0"/>
        <Control Id="Description" Type="Text" X="25" Y="23" Width="280" Height="20" Transparent="yes" NoPrefix="yes" Text="!(loc.InstallScopeDlgDescription)"/>
        <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes" Text="!(loc.InstallScopeDlgTitle)"/>

        <Control Id="BothScopes" Type="RadioButtonGroup" X="20" Y="55" Width="330" Height="120" Property="WixAppFolder" Hidden="yes">
          <RadioButtonGroup Property="WixAppFolder">
            <RadioButton Value="WixPerUserFolder" X="0" Y="0" Width="295" Height="16" Text="!(loc.InstallScopeDlgPerUser)"/>
            <RadioButton Value="WixPerMachineFolder" X="0" Y="60" Width="295" Height="16" Text="!(loc.InstallScopeDlgPerMachine)"/>
          </RadioButtonGroup>
          <Condition Action="show">Privileged AND (!(wix.WixUISupportPerUser) AND !(wix.WixUISupportPerMachine))</Condition>
        </Control>
        <Control Id="PerUserDescription" Type="Text" X="33" Y="70" Width="300" Height="36" Hidden="yes" NoPrefix="yes" Text="!(loc.InstallScopeDlgPerUserDescription)">
          <Condition Action="show">!(wix.WixUISupportPerUser)</Condition>
        </Control>
        <Control Id="NoPerUserDescription" Type="Text" X="33" Y="70" Width="300" Height="36" Hidden="yes" NoPrefix="yes" Text="!(loc.InstallScopeDlgNoPerUserDescription)">
          <Condition Action="show">NOT !(wix.WixUISupportPerUser)</Condition>
        </Control>
        <Control Id="PerMachineDescription" Type="Text" X="33" Y="131" Width="300" Height="36" Hidden="yes" NoPrefix="yes" Text="!(loc.InstallScopeDlgPerMachineDescription)">
          <Condition Action="show">Privileged</Condition>
        </Control>

        <Control Id="Install" Type="PushButton" ElevationShield="yes" X="212" Y="243" Width="80" Height="17" Default="yes" Text="!(loc.AdvancedWelcomeEulaDlgInstall)" Hidden="yes">
          <Condition Action="show">WixAppFolder = "WixPerMachineFolder"</Condition>
          <Condition Action="hide">WixAppFolder = "WixPerUserFolder"</Condition>

          <!-- F*** WiX - it doesn't work. Installed per-user in any case. -->
          <Publish Property="MSIINSTALLPERUSER">1</Publish>

          <Publish Event="SpawnWaitDialog" Value="WaitForCostingDlg">!(wix.WixUICostingPopupOptOut) OR CostingComplete = 1</Publish>
          <Publish Event="EndDialog" Value="Return"><![CDATA[OutOfDiskSpace <> 1]]></Publish>
          <Publish Event="SpawnDialog" Value="OutOfRbDiskDlg">OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 0 AND (PROMPTROLLBACKCOST="P" OR NOT PROMPTROLLBACKCOST)</Publish>
          <Publish Event="EndDialog" Value="Return">OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 0 AND PROMPTROLLBACKCOST="D"</Publish>
          <Publish Event="EnableRollback" Value="False">OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 0 AND PROMPTROLLBACKCOST="D"</Publish>
          <Publish Event="SpawnDialog" Value="OutOfDiskDlg">(OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 1) OR (OutOfDiskSpace = 1 AND PROMPTROLLBACKCOST="F")</Publish>
        </Control>
        <Control Id="InstallNoShield" Type="PushButton" ElevationShield="no" X="212" Y="243" Width="80" Height="17" Default="yes" Text="!(loc.AdvancedWelcomeEulaDlgInstall)" Hidden="yes">
          <Condition Action="show">WixAppFolder = "WixPerUserFolder"</Condition>
          <Condition Action="hide">WixAppFolder = "WixPerMachineFolder"</Condition>

          <!--<Publish Property="MSIINSTALLPERUSER" Value="1" Order="0">WixAppFolder = "WixPerUserFolder"</Publish>-->

          <Publish Event="SpawnWaitDialog" Value="WaitForCostingDlg">!(wix.WixUICostingPopupOptOut) OR CostingComplete = 1</Publish>
          <Publish Event="EndDialog" Value="Return"><![CDATA[OutOfDiskSpace <> 1]]></Publish>
          <Publish Event="SpawnDialog" Value="OutOfRbDiskDlg">OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 0 AND (PROMPTROLLBACKCOST="P" OR NOT PROMPTROLLBACKCOST)</Publish>
          <Publish Event="EndDialog" Value="Return">OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 0 AND PROMPTROLLBACKCOST="D"</Publish>
          <Publish Event="EnableRollback" Value="False">OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 0 AND PROMPTROLLBACKCOST="D"</Publish>
          <Publish Event="SpawnDialog" Value="OutOfDiskDlg">(OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 1) OR (OutOfDiskSpace = 1 AND PROMPTROLLBACKCOST="F")</Publish>
        </Control>

        <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes" Text="!(loc.WixUICancel)">
          <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
        </Control>
      </Dialog>

      <!--<Publish Dialog="InstallScopeDialog" Control="InstallNoShield" Property="MSIINSTALLPERUSER" Value="1" Order="3">WixAppFolder = "WixPerUserFolder"</Publish>-->
      <!--<Publish Dialog="InstallScopeDialog" Control="Install" Property="MSIINSTALLPERUSER" Value="{}" Order="4">WixAppFolder = "WixPerMachineFolder"</Publish>-->

      <TextStyle Id="WixUI_Font_Normal" FaceName="!(loc.Advanced_Font_FaceName)" Size="!(loc.Advanced_Font_Normal_Size)"/>
      <TextStyle Id="WixUI_Font_Bigger" FaceName="!(loc.Advanced_Font_FaceName)" Size="!(loc.Advanced_Font_Bigger_Size)"/>
      <TextStyle Id="WixUI_Font_Title" FaceName="!(loc.Advanced_Font_FaceName)" Size="!(loc.Advanced_Font_Title_Size)" Bold="yes"/>
      <TextStyle Id="WixUI_Font_Emphasized" FaceName="!(loc.Advanced_Font_FaceName)" Size="!(loc.Advanced_Font_Emphasized_Size)" Bold="yes"/>

      <Property Id="DefaultUIFont" Value="WixUI_Font_Normal"/>

      <DialogRef Id="BrowseDlg"/>
      <DialogRef Id="DiskCostDlg"/>
      <DialogRef Id="ErrorDlg"/>
      <DialogRef Id="FatalError"/>
      <DialogRef Id="FilesInUse"/>
      <DialogRef Id="MsiRMFilesInUse"/>
      <DialogRef Id="PrepareDlg"/>
      <DialogRef Id="ProgressDlg"/>
      <DialogRef Id="ResumeDlg"/>
      <DialogRef Id="UserExit"/>

      <Publish Dialog="ExitDialog" Control="Finish" Event="EndDialog" Value="Return" Order="999">1</Publish>
      <Publish Dialog="ExitDialog" Control="Finish" Event="DoAction" Value="runAfterFinish">WIXUI_EXITDIALOGOPTIONALCHECKBOX = 1 and NOT Installed</Publish>

      <!--<Publish Dialog="BrowseDlg" Control="OK" Event="DoAction" Value="WixUIValidatePath" Order="1">1</Publish>-->
      <!--<Publish Dialog="BrowseDlg" Control="OK" Event="SpawnDialog" Value="InvalidDirDlg" Order="2"><![CDATA[WIXUI_INSTALLDIR_VALID<>"1"]]></Publish>-->

      <!--<Publish Dialog="InstallScopeDialog" Control="Next" Property="WixAppFolder" Value="WixPerUserFolder" Order="1">!(wix.WixUISupportPerUser) AND NOT Privileged</Publish>-->
      <!--<Publish Dialog="InstallScopeDialog" Control="Next" Property="MSIINSTALLPERUSER" Value="1" Order="2">!(wix.WixUISupportPerUser) AND NOT Privileged</Publish>-->

      <!--<Publish Dialog="InstallScopeDialog" Control="Next" Property="MSIINSTALLPERUSER" Value="1" Order="3">WixAppFolder = "WixPerUserFolder"</Publish>-->
      <!--<Publish Dialog="InstallScopeDialog" Control="Next" Property="MSIINSTALLPERUSER" Value="{}" Order="4">WixAppFolder = "WixPerMachineFolder"</Publish>-->

      <!-- do not allow choosing installation dir if per-user -->
      <!--<Publish Dialog="InstallScopeDialog" Control="Next" Event="NewDialog" Value="FeaturesDlg" Order="6">WixAppFolder = "WixPerUserFolder"</Publish>-->
      <!--<Publish Dialog="InstallScopeDialog" Control="Next" Event="NewDialog" Value="InstallDirDlg" Order="7">WixAppFolder = "WixPerMachineFolder"</Publish>-->

      <!--<Publish Dialog="InstallDirDlg" Control="Back" Event="NewDialog" Value="InstallScopeDialog">!(wix.WixUISupportPerUser)</Publish>-->
      <!--<Publish Dialog="InstallDirDlg" Control="Back" Event="NewDialog" Value="AdvancedWelcomeEulaDlg">NOT !(wix.WixUISupportPerUser)</Publish>-->
      <!--<Publish Dialog="InstallDirDlg" Control="Next" Event="SetTargetPath" Value="[WIXUI_INSTALLDIR]" Order="1">1</Publish>-->
      <!--<Publish Dialog="InstallDirDlg" Control="Next" Event="DoAction" Value="WixUIValidatePath" Order="2">NOT WIXUI_DONTVALIDATEPATH</Publish>-->
      <!--<Publish Dialog="InstallDirDlg" Control="Next" Event="SpawnDialog" Value="InvalidDirDlg" Order="3"><![CDATA[NOT WIXUI_DONTVALIDATEPATH AND WIXUI_INSTALLDIR_VALID<>"1"]]></Publish>-->
      <!--<Publish Dialog="InstallDirDlg" Control="Next" Event="NewDialog" Value="ExitDlg" Order="4">WIXUI_DONTVALIDATEPATH OR WIXUI_INSTALLDIR_VALID="1"</Publish>-->
      <!--<Publish Dialog="InstallDirDlg" Control="ChangeFolder" Property="_BrowseProperty" Value="[WIXUI_INSTALLDIR]" Order="1">1</Publish>-->
      <!--<Publish Dialog="InstallDirDlg" Control="ChangeFolder" Event="SpawnDialog" Value="BrowseDlg" Order="2">1</Publish>-->

      <!--<Publish Dialog="MaintenanceWelcomeDlg" Control="Next" Event="NewDialog" Value="MaintenanceTypeDlg">1</Publish>-->

      <!--<Publish Dialog="MaintenanceTypeDlg" Control="ChangeButton" Event="NewDialog" Value="FeaturesDlg">1</Publish>-->
      <!--<Publish Dialog="MaintenanceTypeDlg" Control="RepairButton" Event="NewDialog" Value="VerifyReadyDlg">1</Publish>-->
      <!--<Publish Dialog="MaintenanceTypeDlg" Control="RemoveButton" Event="NewDialog" Value="VerifyReadyDlg">1</Publish>-->
      <!--<Publish Dialog="MaintenanceTypeDlg" Control="Back" Event="NewDialog" Value="MaintenanceWelcomeDlg">1</Publish>-->

      <!--<Publish Dialog="VerifyReadyDlg" Control="Back" Event="NewDialog" Value="MaintenanceTypeDlg" Order="2">Installed AND NOT PATCH</Publish>-->
      <!--<Publish Dialog="VerifyReadyDlg" Control="Back" Event="NewDialog" Value="WelcomeDlg" Order="3">Installed AND PATCH</Publish>-->

      <!--<Publish Dialog="WelcomeDlg" Control="Next" Event="NewDialog" Value="VerifyReadyDlg">Installed AND PATCH</Publish>-->

      <InstallUISequence>
        <Show Dialog="InstallScopeDialog" Sequence="1">NOT Installed</Show>
      </InstallUISequence>
    </UI>

    <Property Id="WIXUI_INSTALLDIR" Value="APPLICATIONFOLDER"/>
    <UIRef Id="WixUI_Common"/>
  </Fragment>
</Wix>