{"version": 3, "sources": ["../../src/asar/asarFileChecker.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AACO,eAAe,kBAAf,CAAkC,QAAlC,EAAoD,YAApD,EAA0E,aAA1E,EAA+F;AACpG,WAAS,KAAT,CAAe,IAAf,EAA2B;AACzB,WAAO,IAAI,KAAJ,CAAU,GAAG,aAAa,KAAK,YAAY,aAAa,QAAQ,KAAK,IAAI,EAAzE,CAAP;AACD;;AAED,MAAI,EAAJ;;AACA,MAAI;AACF,IAAA,EAAE,GAAG,MAAM,sBAAS,QAAT,CAAX;AACD,GAFD,CAGA,OAAO,CAAP,EAAU;AACR,UAAM,KAAK,CAAC,iBAAiB,CAAC,EAAnB,CAAX;AACD;;AAED,MAAI,IAAJ;;AACA,MAAI;AACF,IAAA,IAAI,GAAG,EAAE,CAAC,OAAH,CAAW,YAAX,CAAP;AACD,GAFD,CAGA,OAAO,CAAP,EAAU;AACR,UAAM,QAAQ,GAAG,MAAM,sBAAW,QAAX,CAAvB;;AACA,QAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,YAAM,KAAK,CAAC,mDAAD,CAAX;AACD,KAJO,CAMR;;;AACA,IAAA,IAAI,GAAG,IAAP;AACD;;AAED,MAAI,IAAI,IAAI,IAAZ,EAAkB;AAChB,UAAM,KAAK,CAAC,mDAAD,CAAX;AACD;;AACD,MAAI,IAAI,CAAC,IAAL,KAAc,CAAlB,EAAqB;AACnB,UAAM,KAAK,CAAC,sBAAD,CAAX;AACD;AACF,C", "sourcesContent": ["import { statOrNull } from \"builder-util/out/fs\"\nimport { Node, readAsar } from \"./asar\"\n\n/** @internal */\nexport async function checkFileInArchive(asarFile: string, relativeFile: string, messagePrefix: string) {\n  function error(text: string) {\n    return new Error(`${messagePrefix} \"${relativeFile}\" in the \"${asarFile}\" ${text}`)\n  }\n\n  let fs\n  try {\n    fs = await readAsar(asarFile)\n  }\n  catch (e) {\n    throw error(`is corrupted: ${e}`)\n  }\n\n  let stat: Node | null\n  try {\n    stat = fs.getFile(relativeFile)\n  }\n  catch (e) {\n    const fileStat = await statOrNull(asarFile)\n    if (fileStat == null) {\n      throw error(`does not exist. Seems like a wrong configuration.`)\n    }\n\n    // asar throws error on access to undefined object (info.link)\n    stat = null\n  }\n\n  if (stat == null) {\n    throw error(`does not exist. Seems like a wrong configuration.`)\n  }\n  if (stat.size === 0) {\n    throw error(`is corrupted: size 0`)\n  }\n}\n"], "sourceRoot": ""}