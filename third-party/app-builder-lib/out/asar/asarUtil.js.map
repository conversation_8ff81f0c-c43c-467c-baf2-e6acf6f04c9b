{"version": 3, "sources": ["../../src/asar/asarUtil.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AAIA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;AAEA;AACA,MAAM,MAAM,GAAG,OAAO,CAAC,oBAAD,CAAtB;AAEA;;;AACM,MAAO,YAAP,CAAmB;AAKvB,EAAA,WAAA,CAA6B,GAA7B,EAA2D,WAA3D,EAAiG,OAAjG,EAAwI,aAAxI,EAAoK;AAAvI,SAAA,GAAA,GAAA,GAAA;AAA8B,SAAA,WAAA,GAAA,WAAA;AAAsC,SAAA,OAAA,GAAA,OAAA;AAAuC,SAAA,aAAA,GAAA,aAAA;AAJvH,SAAA,EAAA,GAAK,KAAI,sBAAJ,EAAmB,KAAK,GAAxB,CAAL;AAKf,SAAK,OAAL,GAAe,IAAI,CAAC,IAAL,CAAU,WAAV,EAAuB,UAAvB,CAAf;AACA,SAAK,YAAL,GAAoB,GAAG,KAAK,OAAO,WAAnC;AACD,GARsB,CAUvB;;;AACA,QAAM,IAAN,CAAW,QAAX,EAA6C,QAA7C,EAA4E;AAC1E,QAAI,KAAK,OAAL,CAAa,QAAb,IAAyB,IAA7B,EAAmC;AACjC;AACA,YAAM,KAAK,CAAC,QAAQ,CAAC,CAAD,CAAR,CAAY,KAAb,EAAoB,KAAK,OAAL,CAAa,QAAjC,EAA2C,QAAQ,CAAC,CAAD,CAAR,CAAY,GAAvD,CAAX;AACD;;AACD,UAAM,0BAAU,IAAI,CAAC,OAAL,CAAa,KAAK,OAAlB,CAAV,CAAN;AACA,UAAM,oBAAoB,GAAG,IAAI,GAAJ,EAA7B;;AACA,SAAK,MAAM,OAAX,IAAsB,QAAtB,EAAgC;AAC9B,MAAA,oBAAoB,CAAC,GAArB,CAAyB,OAAzB,EAAkC,MAAM,KAAK,sBAAL,CAA4B,OAA5B,EAAqC,QAAQ,CAAC,IAA9C,CAAxC;AACD;;AACD,UAAM,KAAK,aAAL,CAAmB,QAAnB,EAA6B,oBAA7B,CAAN;AACD;;AAEO,QAAM,sBAAN,CAA6B,OAA7B,EAAuD,QAAvD,EAAyE;AAC/E,UAAM,QAAQ,GAAG,OAAO,CAAC,QAAzB,CAD+E,CAE/E;;AACA,UAAM,YAAY,GAAG,IAAI,GAAJ,EAArB;AACA,UAAM,0BAA0B,GAAG,IAAI,CAAC,IAAL,CAAU,KAAK,WAAf,EAA4B,KAA5B,CAAnC;;AAEA,QAAI,KAAK,OAAL,CAAa,WAAb,KAA6B,KAAjC,EAAwC;AACtC,YAAM,0CAAmB,OAAnB,EAA4B,YAA5B,EAA0C,KAAK,YAA/C,EAA6D,0BAA7D,CAAN;AACD;;AAED,UAAM,2BAA2B,GAAG,IAAI,GAAJ,CAAgB,YAAhB,CAApC;;AAEA,UAAM,0BAA0B,GAAG,OAAO,iBAAP,EAAkC,OAAlC,KAAmD;AACpF,WAAK,MAAM,GAAX,IAAkB,YAAlB,EAAgC;AAC9B,YAAI,iBAAiB,CAAC,MAAlB,GAA4B,GAAG,CAAC,MAAJ,GAAa,CAAzC,IAA+C,iBAAiB,CAAC,GAAG,CAAC,MAAL,CAAjB,KAAkC,IAAI,CAAC,GAAtF,IAA6F,iBAAiB,CAAC,UAAlB,CAA6B,GAA7B,CAAjG,EAAoI;AAClI,UAAA,OAAO,CAAC,QAAR,GAAmB,IAAnB;AACA,UAAA,YAAY,CAAC,GAAb,CAAiB,iBAAjB,EAFkI,CAGlI;AACA;;AACA,gBAAM,0BAAU,IAAI,CAAC,IAAL,CAAU,KAAK,YAAf,EAA6B,iBAA7B,CAAV,CAAN;AACA;AACD;AACF;AACF,KAXD;;AAaA,UAAM,gBAAgB,GAAG,OAAO,CAAC,gBAAjC;AACA,UAAM,WAAW,GAAG,KAAI,+BAAJ,EAAqB,QAAQ,CAAC,iBAA9B,CAApB;AACA,UAAM,UAAU,GAAG,KAAI,gBAAJ,GAAnB;AAEA,QAAI,cAAc,GAAgB,IAAlC;AACA,QAAI,cAAc,GAAkB,IAApC;AAEA,UAAM,oBAAoB,GAAG,IAAI,GAAJ,EAA7B;;AAEA,SAAK,IAAI,CAAC,GAAG,CAAR,EAAW,CAAC,GAAG,OAAO,CAAC,KAAR,CAAc,MAAlC,EAA0C,CAAC,GAAG,CAA9C,EAAiD,CAAC,EAAlD,EAAsD;AACpD,YAAM,IAAI,GAAG,OAAO,CAAC,KAAR,CAAc,CAAd,CAAb;AACA,YAAM,IAAI,GAAG,QAAQ,CAAC,GAAT,CAAa,IAAb,CAAb;;AACA,UAAI,IAAI,IAAI,IAAZ,EAAkB;AAChB;AACD;;AAED,YAAM,aAAa,GAAG,IAAI,CAAC,QAAL,CAAc,0BAAd,EAA0C,yCAAmB,IAAnB,EAAyB,OAAzB,CAA1C,CAAtB;;AAEA,UAAI,IAAI,CAAC,cAAL,EAAJ,EAA2B;AACzB,cAAM,CAAC,GAAI,IAAX;AACA,aAAK,EAAL,CAAQ,eAAR,CAAwB,aAAxB,EAAuC,IAAvC,GAA8C,CAAC,CAAC,YAAhD;AACA,QAAA,CAAC,CAAC,aAAF,GAAkB,aAAlB;AACA,QAAA,oBAAoB,CAAC,GAArB,CAAyB,CAAzB;AACA;AACD;;AAED,UAAI,UAAU,GAAG,IAAI,CAAC,OAAL,CAAa,aAAb,CAAjB;;AACA,UAAI,UAAU,KAAK,GAAnB,EAAwB;AACtB,QAAA,UAAU,GAAG,EAAb;AACD;;AAED,UAAI,cAAc,KAAK,UAAvB,EAAmC;AACjC,YAAI,UAAU,CAAC,UAAX,CAAsB,IAAtB,CAAJ,EAAiC;AAC/B,gBAAM,IAAI,KAAJ,CAAU,kDAAkD,UAAU,EAAtE,CAAN;AACD;;AAED,QAAA,cAAc,GAAG,UAAjB;AACA,QAAA,cAAc,GAAG,KAAK,EAAL,CAAQ,eAAR,CAAwB,UAAxB,CAAjB,CANiC,CAOjC;;AACA,YAAI,UAAU,KAAK,EAAf,IAAqB,CAAC,cAAc,CAAC,QAAzC,EAAmD;AACjD,cAAI,YAAY,CAAC,GAAb,CAAiB,UAAjB,CAAJ,EAAkC;AAChC,YAAA,cAAc,CAAC,QAAf,GAA0B,IAA1B;AACD,WAFD,MAGK;AACH,kBAAM,0BAA0B,CAAC,UAAD,EAAa,cAAb,CAAhC;AACD;AACF;AACF;;AAED,YAAM,OAAO,GAAG,cAAhB;AACA,YAAM,OAAO,GAAG,gBAAgB,IAAI,IAApB,GAA2B,IAA3B,GAAkC,gBAAgB,CAAC,GAAjB,CAAqB,CAArB,CAAlD;AACA,YAAM,UAAU,GAAG,OAAO,CAAC,QAAR,IAAqB,KAAK,aAAL,IAAsB,IAAtB,IAA8B,KAAK,aAAL,CAAmB,IAAnB,EAAyB,IAAzB,CAAtE;AACA,WAAK,EAAL,CAAQ,WAAR,CAAoB,IAApB,EAA0B,OAA1B,EAAmC,OAAO,IAAI,IAAX,GAAkB,IAAI,CAAC,IAAvB,GAA8B,MAAM,CAAC,UAAP,CAAkB,OAAlB,CAAjE,EAA6F,UAA7F,EAAyG,IAAzG;;AACA,UAAI,UAAJ,EAAgB;AACd,YAAI,CAAC,OAAO,CAAC,QAAT,IAAqB,CAAC,2BAA2B,CAAC,GAA5B,CAAgC,UAAhC,CAA1B,EAAuE;AACrE,UAAA,2BAA2B,CAAC,GAA5B,CAAgC,UAAhC;AACA,gBAAM,0BAAU,IAAI,CAAC,IAAL,CAAU,KAAK,YAAf,EAA6B,UAA7B,CAAV,CAAN;AACD;;AAED,cAAM,YAAY,GAAG,IAAI,CAAC,IAAL,CAAU,KAAK,YAAf,EAA6B,aAA7B,CAArB;AACA,QAAA,WAAW,CAAC,OAAZ,CAAoB,cAAc,CAAC,UAAD,EAAa,OAAb,EAAsB,IAAtB,EAA4B,YAA5B,EAA0C,IAA1C,CAAlC;;AACA,YAAI,WAAW,CAAC,KAAZ,CAAkB,MAAlB,GAA2B,uBAA/B,EAAkD;AAChD,gBAAM,WAAW,CAAC,UAAZ,EAAN;AACD;;AAED,QAAA,oBAAoB,CAAC,GAArB,CAAyB,CAAzB;AACD;AACF;;AAED,QAAI,WAAW,CAAC,KAAZ,CAAkB,MAAlB,GAA2B,CAA/B,EAAkC;AAChC,YAAM,WAAW,CAAC,UAAZ,EAAN;AACD;;AAED,WAAO,oBAAP;AACD;;AAEO,EAAA,aAAa,CAAC,QAAD,EAAmC,oBAAnC,EAA0F;AAC7G,WAAO,IAAI,OAAJ,CAAY,CAAC,OAAD,EAAU,MAAV,KAAoB;AACrC,YAAM,YAAY,GAAG,MAAM,CAAC,WAAP,EAArB;AACA,MAAA,YAAY,CAAC,WAAb,CAAyB,IAAI,CAAC,SAAL,CAAe,KAAK,EAAL,CAAQ,MAAvB,CAAzB;AACA,YAAM,SAAS,GAAG,YAAY,CAAC,QAAb,EAAlB;AAEA,YAAM,UAAU,GAAG,MAAM,CAAC,WAAP,EAAnB;AACA,MAAA,UAAU,CAAC,WAAX,CAAuB,SAAS,CAAC,MAAjC;AAEA,YAAM,OAAO,GAAG,UAAU,CAAC,QAAX,EAAhB;AACA,YAAM,WAAW,GAAG,kCAAkB,KAAK,OAAvB,CAApB;AACA,MAAA,WAAW,CAAC,EAAZ,CAAe,OAAf,EAAwB,MAAxB;AACA,MAAA,WAAW,CAAC,EAAZ,CAAe,OAAf,EAAwB,OAAxB;AACA,MAAA,WAAW,CAAC,KAAZ,CAAkB,OAAlB;AAEA,UAAI,YAAY,GAAG,CAAnB;AAEA,UAAI,KAAK,GAAG,QAAQ,CAAC,CAAD,CAAR,CAAY,KAAxB;AACA,UAAI,QAAQ,GAAG,QAAQ,CAAC,CAAD,CAAR,CAAY,QAA3B;AACA,UAAI,gBAAgB,GAAG,QAAQ,CAAC,CAAD,CAAR,CAAY,gBAAnC;AACA,UAAI,oBAAoB,GAAG,oBAAoB,CAAC,GAArB,CAAyB,QAAQ,CAAC,CAAD,CAAjC,CAA3B;;AACA,YAAM,CAAC,GAAI,KAAD,IAAkB;AAC1B,eAAO,IAAP,EAAa;AACX,cAAI,KAAK,IAAI,KAAK,CAAC,MAAnB,EAA2B;AACzB,gBAAI,EAAE,YAAF,IAAkB,QAAQ,CAAC,MAA/B,EAAuC;AACrC,cAAA,WAAW,CAAC,GAAZ;AACA;AACD,aAHD,MAIK;AACH,cAAA,KAAK,GAAG,QAAQ,CAAC,YAAD,CAAR,CAAuB,KAA/B;AACA,cAAA,QAAQ,GAAG,QAAQ,CAAC,YAAD,CAAR,CAAuB,QAAlC;AACA,cAAA,gBAAgB,GAAG,QAAQ,CAAC,YAAD,CAAR,CAAuB,gBAA1C;AACA,cAAA,oBAAoB,GAAG,oBAAoB,CAAC,GAArB,CAAyB,QAAQ,CAAC,YAAD,CAAjC,CAAvB;AACA,cAAA,KAAK,GAAG,CAAR;AACD;AACF;;AAED,cAAI,CAAC,oBAAoB,CAAC,GAArB,CAAyB,KAAzB,CAAL,EAAsC;AACpC;AACD,WAFD,MAGK;AACH,kBAAM,IAAI,GAAG,QAAQ,CAAC,GAAT,CAAa,KAAK,CAAC,KAAD,CAAlB,CAAb;;AACA,gBAAI,IAAI,IAAI,IAAR,IAAgB,IAAI,CAAC,cAAL,EAApB,EAA2C;AACzC,gCAAS,IAAY,CAAC,kBAAtB,EAA0C,IAAI,CAAC,IAAL,CAAU,KAAK,YAAf,EAA8B,IAAY,CAAC,aAA3C,CAA1C,EAAqG,MAAM,CAAC,CAAC,KAAK,GAAG,CAAT,CAA5G;AACA;AACD;AACF;;AACD,UAAA,KAAK;AACN;;AAED,cAAM,IAAI,GAAG,gBAAgB,IAAI,IAApB,GAA2B,IAA3B,GAAkC,gBAAgB,CAAC,GAAjB,CAAqB,KAArB,CAA/C;AACA,cAAM,IAAI,GAAG,KAAK,CAAC,KAAD,CAAlB;;AACA,YAAI,IAAI,KAAK,IAAT,IAAiB,IAAI,KAAK,SAA9B,EAAyC;AACvC,UAAA,WAAW,CAAC,KAAZ,CAAkB,IAAlB,EAAwB,MAAM,CAAC,CAAC,KAAK,GAAG,CAAT,CAA/B;AACA;AACD,SAlCyB,CAoC1B;;;AACA,cAAM,IAAI,GAAG,QAAQ,CAAC,GAAT,CAAa,IAAb,CAAb;;AACA,YAAI,IAAI,IAAI,IAAR,IAAgB,IAAI,CAAC,IAAL,GAAa,IAAI,IAAJ,GAAW,IAA5C,EAAmD;AACjD,mCAAS,IAAT,EACG,IADH,CACQ,EAAE,IAAG;AACT,YAAA,WAAW,CAAC,KAAZ,CAAkB,EAAlB,EAAsB,MAAM,CAAC,CAAC,KAAK,GAAG,CAAT,CAA7B;AACD,WAHH,EAIG,KAJH,CAIS,CAAC,IAAI,MAAM,CAAC,oBAAoB,IAAI,KAAK,CAAC,CAAC,KAAF,IAAW,CAAC,EAA1C,CAJpB;AAKD,SAND,MAOK;AACH,gBAAM,UAAU,GAAG,iCAAiB,IAAjB,CAAnB;AACA,UAAA,UAAU,CAAC,EAAX,CAAc,OAAd,EAAuB,MAAvB;AACA,UAAA,UAAU,CAAC,IAAX,CAAgB,KAAhB,EAAuB,MAAM,CAAC,CAAC,KAAK,GAAG,CAAT,CAA9B;AACA,UAAA,UAAU,CAAC,IAAX,CAAgB,WAAhB,EAA6B;AAC3B,YAAA,GAAG,EAAE;AADsB,WAA7B;AAGD;AACF,OArDD;;AAuDA,MAAA,WAAW,CAAC,KAAZ,CAAkB,SAAlB,EAA6B,MAAM,CAAC,CAAC,CAAD,CAApC;AACD,KA5EM,CAAP;AA6ED;;AA3MsB;;;;AA8MzB,eAAe,KAAf,CAAqB,SAArB,EAA+C,YAA/C,EAAqE,GAArE,EAAgF;AAC9E,QAAM,aAAa,GAAG,CAAC,MAAM,yBAAS,YAAT,EAAuB,MAAvB,CAAP,EAAuC,KAAvC,CAA6C,IAA7C,EAAmD,GAAnD,CAAuD,IAAI,IAAG;AAClF,QAAI,IAAI,CAAC,OAAL,CAAa,GAAb,MAAsB,CAAC,CAA3B,EAA8B;AAC5B,MAAA,IAAI,GAAG,IAAI,CAAC,KAAL,CAAW,GAAX,EAAgB,GAAhB,EAAP;AACD;;AACD,IAAA,IAAI,GAAG,IAAI,CAAC,IAAL,EAAP;;AACA,QAAI,IAAI,CAAC,CAAD,CAAJ,KAAY,GAAhB,EAAqB;AACnB,MAAA,IAAI,GAAG,IAAI,CAAC,KAAL,CAAW,CAAX,CAAP;AACD;;AACD,WAAO,IAAP;AACD,GATqB,CAAtB;AAWA,QAAM,QAAQ,GAAkB,EAAhC;;AACA,OAAK,MAAM,IAAX,IAAmB,aAAnB,EAAkC;AAChC,UAAM,cAAc,GAAG,IAAI,CAAC,KAAL,CAAW,IAAI,CAAC,GAAhB,CAAvB;;AACA,SAAK,MAAM,aAAX,IAA4B,cAA5B,EAA4C;AAC1C,MAAA,QAAQ,CAAC,IAAT,CAAc,IAAI,CAAC,IAAL,CAAU,GAAV,EAAe,aAAf,CAAd;AACD;AACF;;AAED,QAAM,WAAW,GAAkB,EAAnC;AACA,MAAI,OAAO,GAAG,CAAd;AACA,QAAM,KAAK,GAAG,SAAS,CAAC,MAAxB;;AACA,OAAK,MAAM,IAAX,IAAmB,QAAnB,EAA6B;AAC3B,QAAI,CAAC,WAAW,CAAC,QAAZ,CAAqB,IAArB,CAAD,IAA+B,SAAS,CAAC,QAAV,CAAmB,IAAnB,CAAnC,EAA6D;AAC3D,MAAA,WAAW,CAAC,IAAZ,CAAiB,IAAjB;AACD;AACF;;AACD,OAAK,MAAM,IAAX,IAAmB,SAAnB,EAA8B;AAC5B,QAAI,CAAC,WAAW,CAAC,QAAZ,CAAqB,IAArB,CAAL,EAAiC;AAC/B,MAAA,WAAW,CAAC,IAAZ,CAAiB,IAAjB;AACA,MAAA,OAAO,IAAI,CAAX;AACD;AACF;;AACD,qBAAI,IAAJ,CAAS;AAAC,IAAA,QAAQ,EAAG,CAAC,KAAK,GAAG,OAAT,IAAoB,KAApB,GAA4B;AAAxC,GAAT,EAAwD,gCAAxD;;AACA,SAAO,WAAP;AACD;;AAED,SAAS,cAAT,CAAwB,UAAxB,EAAgD,IAAhD,EAA0F,MAA1F,EAA0G,WAA1G,EAA+H,KAA/H,EAA2I;AACzI,MAAI,IAAI,IAAI,IAAZ,EAAkB;AAChB,WAAO,UAAU,CAAC,IAAX,CAAgB,MAAhB,EAAwB,WAAxB,EAAqC,KAArC,CAAP;AACD,GAFD,MAGK;AACH,WAAO,0BAAU,WAAV,EAAuB,IAAvB,CAAP;AACD;AACF,C", "sourcesContent": ["import { AsyncTaskManager, log } from \"builder-util\"\nimport { FileCopier, Filter, MAX_FILE_REQUESTS } from \"builder-util/out/fs\"\nimport { symlink } from \"fs\"\nimport { createReadStream, createWriteStream, ensureDir, readFile, Stats, writeFile } from \"fs-extra\"\nimport * as path from \"path\"\nimport { AsarOptions } from \"..\"\nimport { Packager } from \"../packager\"\nimport { PlatformPackager } from \"../platformPackager\"\nimport { getDestinationPath, ResolvedFileSet } from \"../util/appFileCopier\"\nimport { AsarFilesystem, Node } from \"./asar\"\nimport { detectUnpackedDirs } from \"./unpackDetector\"\n\n// eslint-disable-next-line @typescript-eslint/no-var-requires\nconst pickle = require(\"chromium-pickle-js\")\n\n/** @internal */\nexport class AsarPackager {\n  private readonly fs = new AsarFilesystem(this.src)\n  private readonly outFile: string\n  private readonly unpackedDest: string\n\n  constructor(private readonly src: string, private readonly destination: string, private readonly options: AsarOptions, private readonly unpackPattern: Filter | null) {\n    this.outFile = path.join(destination, \"app.asar\")\n    this.unpackedDest = `${this.outFile}.unpacked`\n  }\n\n  // sort files to minimize file change (i.e. asar file is not changed dramatically on small change)\n  async pack(fileSets: Array<ResolvedFileSet>, packager: PlatformPackager<any>) {\n    if (this.options.ordering != null) {\n      // ordering doesn't support transformed files, but ordering is not used functionality - wait user report to fix it\n      await order(fileSets[0].files, this.options.ordering, fileSets[0].src)\n    }\n    await ensureDir(path.dirname(this.outFile))\n    const unpackedFileIndexMap = new Map<ResolvedFileSet, Set<number>>()\n    for (const fileSet of fileSets) {\n      unpackedFileIndexMap.set(fileSet, await this.createPackageFromFiles(fileSet, packager.info))\n    }\n    await this.writeAsarFile(fileSets, unpackedFileIndexMap)\n  }\n\n  private async createPackageFromFiles(fileSet: ResolvedFileSet, packager: Packager) {\n    const metadata = fileSet.metadata\n    // search auto unpacked dir\n    const unpackedDirs = new Set<string>()\n    const rootForAppFilesWithoutAsar = path.join(this.destination, \"app\")\n\n    if (this.options.smartUnpack !== false) {\n      await detectUnpackedDirs(fileSet, unpackedDirs, this.unpackedDest, rootForAppFilesWithoutAsar)\n    }\n\n    const dirToCreateForUnpackedFiles = new Set<string>(unpackedDirs)\n\n    const correctDirNodeUnpackedFlag = async (filePathInArchive: string, dirNode: Node) => {\n      for (const dir of unpackedDirs) {\n        if (filePathInArchive.length > (dir.length + 2) && filePathInArchive[dir.length] === path.sep && filePathInArchive.startsWith(dir)) {\n          dirNode.unpacked = true\n          unpackedDirs.add(filePathInArchive)\n          // not all dirs marked as unpacked after first iteration - because node module dir can be marked as unpacked after processing node module dir content\n          // e.g. node-notifier/example/advanced.js processed, but only on process vendor/terminal-notifier.app module will be marked as unpacked\n          await ensureDir(path.join(this.unpackedDest, filePathInArchive))\n          break\n        }\n      }\n    }\n\n    const transformedFiles = fileSet.transformedFiles\n    const taskManager = new AsyncTaskManager(packager.cancellationToken)\n    const fileCopier = new FileCopier()\n\n    let currentDirNode: Node | null = null\n    let currentDirPath: string | null = null\n\n    const unpackedFileIndexSet = new Set<number>()\n\n    for (let i = 0, n = fileSet.files.length; i < n; i++) {\n      const file = fileSet.files[i]\n      const stat = metadata.get(file)\n      if (stat == null) {\n        continue\n      }\n\n      const pathInArchive = path.relative(rootForAppFilesWithoutAsar, getDestinationPath(file, fileSet))\n\n      if (stat.isSymbolicLink()) {\n        const s = (stat as any)\n        this.fs.getOrCreateNode(pathInArchive).link = s.relativeLink\n        s.pathInArchive = pathInArchive\n        unpackedFileIndexSet.add(i)\n        continue\n      }\n\n      let fileParent = path.dirname(pathInArchive)\n      if (fileParent === \".\") {\n        fileParent = \"\"\n      }\n\n      if (currentDirPath !== fileParent) {\n        if (fileParent.startsWith(\"..\")) {\n          throw new Error(`Internal error: path must not start with \"..\": ${fileParent}`)\n        }\n\n        currentDirPath = fileParent\n        currentDirNode = this.fs.getOrCreateNode(fileParent)\n        // do not check for root\n        if (fileParent !== \"\" && !currentDirNode.unpacked) {\n          if (unpackedDirs.has(fileParent)) {\n            currentDirNode.unpacked = true\n          }\n          else {\n            await correctDirNodeUnpackedFlag(fileParent, currentDirNode)\n          }\n        }\n      }\n\n      const dirNode = currentDirNode!\n      const newData = transformedFiles == null ? null : transformedFiles.get(i)\n      const isUnpacked = dirNode.unpacked || (this.unpackPattern != null && this.unpackPattern(file, stat))\n      this.fs.addFileNode(file, dirNode, newData == null ? stat.size : Buffer.byteLength(newData), isUnpacked, stat)\n      if (isUnpacked) {\n        if (!dirNode.unpacked && !dirToCreateForUnpackedFiles.has(fileParent)) {\n          dirToCreateForUnpackedFiles.add(fileParent)\n          await ensureDir(path.join(this.unpackedDest, fileParent))\n        }\n\n        const unpackedFile = path.join(this.unpackedDest, pathInArchive)\n        taskManager.addTask(copyFileOrData(fileCopier, newData, file, unpackedFile, stat))\n        if (taskManager.tasks.length > MAX_FILE_REQUESTS) {\n          await taskManager.awaitTasks()\n        }\n\n        unpackedFileIndexSet.add(i)\n      }\n    }\n\n    if (taskManager.tasks.length > 0) {\n      await taskManager.awaitTasks()\n    }\n\n    return unpackedFileIndexSet\n  }\n\n  private writeAsarFile(fileSets: Array<ResolvedFileSet>, unpackedFileIndexMap: Map<ResolvedFileSet, Set<number>>): Promise<any> {\n    return new Promise((resolve, reject) => {\n      const headerPickle = pickle.createEmpty()\n      headerPickle.writeString(JSON.stringify(this.fs.header))\n      const headerBuf = headerPickle.toBuffer()\n\n      const sizePickle = pickle.createEmpty()\n      sizePickle.writeUInt32(headerBuf.length)\n\n      const sizeBuf = sizePickle.toBuffer()\n      const writeStream = createWriteStream(this.outFile)\n      writeStream.on(\"error\", reject)\n      writeStream.on(\"close\", resolve)\n      writeStream.write(sizeBuf)\n\n      let fileSetIndex = 0\n\n      let files = fileSets[0].files\n      let metadata = fileSets[0].metadata\n      let transformedFiles = fileSets[0].transformedFiles\n      let unpackedFileIndexSet = unpackedFileIndexMap.get(fileSets[0])!!\n      const w = (index: number) => {\n        while (true) {\n          if (index >= files.length) {\n            if (++fileSetIndex >= fileSets.length) {\n              writeStream.end()\n              return\n            }\n            else {\n              files = fileSets[fileSetIndex].files\n              metadata = fileSets[fileSetIndex].metadata\n              transformedFiles = fileSets[fileSetIndex].transformedFiles\n              unpackedFileIndexSet = unpackedFileIndexMap.get(fileSets[fileSetIndex])!!\n              index = 0\n            }\n          }\n\n          if (!unpackedFileIndexSet.has(index)) {\n            break\n          }\n          else {\n            const stat = metadata.get(files[index])\n            if (stat != null && stat.isSymbolicLink()) {\n              symlink((stat as any).linkRelativeToFile, path.join(this.unpackedDest, (stat as any).pathInArchive), () => w(index + 1))\n              return\n            }\n          }\n          index++\n        }\n\n        const data = transformedFiles == null ? null : transformedFiles.get(index)\n        const file = files[index]\n        if (data !== null && data !== undefined) {\n          writeStream.write(data, () => w(index + 1))\n          return\n        }\n\n        // https://github.com/yarnpkg/yarn/pull/3539\n        const stat = metadata.get(file)\n        if (stat != null && stat.size < (2 * 1024 * 1024)) {\n          readFile(file)\n            .then(it => {\n              writeStream.write(it, () => w(index + 1))\n            })\n            .catch(e => reject(`Cannot read file ${file}: ${e.stack || e}`))\n        }\n        else {\n          const readStream = createReadStream(file)\n          readStream.on(\"error\", reject)\n          readStream.once(\"end\", () => w(index + 1))\n          readStream.pipe(writeStream, {\n            end: false\n          })\n        }\n      }\n\n      writeStream.write(headerBuf, () => w(0))\n    })\n  }\n}\n\nasync function order(filenames: Array<string>, orderingFile: string, src: string) {\n  const orderingFiles = (await readFile(orderingFile, \"utf8\")).split(\"\\n\").map(line => {\n    if (line.indexOf(\":\") !== -1) {\n      line = line.split(\":\").pop()!\n    }\n    line = line.trim()\n    if (line[0] === \"/\") {\n      line = line.slice(1)\n    }\n    return line\n  })\n\n  const ordering: Array<string> = []\n  for (const file of orderingFiles) {\n    const pathComponents = file.split(path.sep)\n    for (const pathComponent of pathComponents) {\n      ordering.push(path.join(src, pathComponent))\n    }\n  }\n\n  const sortedFiles: Array<string> = []\n  let missing = 0\n  const total = filenames.length\n  for (const file of ordering) {\n    if (!sortedFiles.includes(file) && filenames.includes(file)) {\n      sortedFiles.push(file)\n    }\n  }\n  for (const file of filenames) {\n    if (!sortedFiles.includes(file)) {\n      sortedFiles.push(file)\n      missing += 1\n    }\n  }\n  log.info({coverage: ((total - missing) / total * 100)}, \"ordering files in ASAR archive\")\n  return sortedFiles\n}\n\nfunction copyFileOrData(fileCopier: FileCopier, data: string | Buffer | undefined | null, source: string, destination: string, stats: Stats) {\n  if (data == null) {\n    return fileCopier.copy(source, destination, stats)\n  }\n  else {\n    return writeFile(destination, data)\n  }\n}"], "sourceRoot": ""}