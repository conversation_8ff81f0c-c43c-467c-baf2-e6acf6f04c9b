{"version": 3, "sources": ["../../src/asar/asar.ts"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;;;;;AAEA;AACM,MAAO,IAAP,CAAW;AAejB;;;;;AACM,MAAO,cAAP,CAAqB;AAGzB,EAAA,WAAA,CAAqB,GAArB,EAA2C,MAAA,GAAS,IAAI,IAAJ,EAApD,EAAyE,UAAA,GAAqB,CAAC,CAA/F,EAAgG;AAA3E,SAAA,GAAA,GAAA,GAAA;AAAsB,SAAA,MAAA,GAAA,MAAA;AAA8B,SAAA,UAAA,GAAA,UAAA;AAFjE,SAAA,MAAA,GAAS,CAAT;;AAGN,QAAI,KAAK,MAAL,CAAY,KAAZ,IAAqB,IAAzB,EAA+B;AAC7B,WAAK,MAAL,CAAY,KAAZ,GAAoB,EAApB;AACD;AACF;;AAED,EAAA,uBAAuB,CAAC,CAAD,EAAY,QAAZ,EAA6B;AAClD,QAAI,IAAI,GAAG,KAAK,MAAhB;;AACA,SAAK,MAAM,GAAX,IAAkB,CAAC,CAAC,KAAF,CAAQ,IAAI,CAAC,GAAb,CAAlB,EAAqC;AACnC,UAAI,GAAG,KAAK,GAAZ,EAAiB;AACf,YAAI,KAAK,GAAG,IAAI,CAAC,KAAL,CAAY,GAAZ,CAAZ;;AACA,YAAI,KAAK,IAAI,IAAb,EAAmB;AACjB,cAAI,CAAC,QAAL,EAAe;AACb,mBAAO,IAAP;AACD;;AACD,UAAA,KAAK,GAAG,IAAI,IAAJ,EAAR;AACA,UAAA,KAAK,CAAC,KAAN,GAAc,EAAd;AACA,UAAA,IAAI,CAAC,KAAL,CAAY,GAAZ,IAAmB,KAAnB;AACD;;AACD,QAAA,IAAI,GAAG,KAAP;AACD;AACF;;AACD,WAAO,IAAP;AACD;;AAED,EAAA,eAAe,CAAC,CAAD,EAAU;AACvB,QAAI,CAAC,IAAI,IAAL,IAAa,CAAC,CAAC,MAAF,KAAa,CAA9B,EAAiC;AAC/B,aAAO,KAAK,MAAZ;AACD;;AAED,UAAM,IAAI,GAAG,IAAI,CAAC,QAAL,CAAc,CAAd,CAAb;AACA,UAAM,OAAO,GAAG,KAAK,uBAAL,CAA6B,IAAI,CAAC,OAAL,CAAa,CAAb,CAA7B,EAA8C,IAA9C,CAAhB;;AACA,QAAI,OAAO,CAAC,KAAR,IAAiB,IAArB,EAA2B;AACzB,MAAA,OAAO,CAAC,KAAR,GAAgB,EAAhB;AACD;;AAED,QAAI,MAAM,GAAG,OAAO,CAAC,KAAR,CAAc,IAAd,CAAb;;AACA,QAAI,MAAM,IAAI,IAAd,EAAoB;AAClB,MAAA,MAAM,GAAG,IAAI,IAAJ,EAAT;AACA,MAAA,OAAO,CAAC,KAAR,CAAc,IAAd,IAAsB,MAAtB;AACD;;AACD,WAAO,MAAP;AACD;;AAED,EAAA,WAAW,CAAC,IAAD,EAAe,OAAf,EAA8B,IAA9B,EAA4C,QAA5C,EAA+D,IAA/D,EAA0E;AACnF,QAAI,IAAI,GAAG,UAAX,EAAuB;AACrB,YAAM,IAAI,KAAJ,CAAU,GAAG,IAAI,yCAAjB,CAAN;AACD;;AAED,UAAM,IAAI,GAAG,IAAI,IAAJ,EAAb;AACA,IAAA,IAAI,CAAC,IAAL,GAAY,IAAZ;;AACA,QAAI,QAAJ,EAAc;AACZ,MAAA,IAAI,CAAC,QAAL,GAAgB,IAAhB;AACD,KAFD,MAGK;AACH;AACA,MAAA,IAAI,CAAC,MAAL,GAAc,KAAK,MAAL,CAAY,QAAZ,EAAd;;AACA,UAAI,OAAO,CAAC,QAAR,KAAqB,OAArB,IAAiC,IAAI,CAAC,IAAL,GAAY,KAAjD,EAAyD;AACvD,QAAA,IAAI,CAAC,UAAL,GAAkB,IAAlB;AACD;;AACD,WAAK,MAAL,IAAe,IAAI,CAAC,IAApB;AACD;;AAED,QAAI,QAAQ,GAAG,OAAO,CAAC,KAAvB;;AACA,QAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,MAAA,QAAQ,GAAG,EAAX;AACA,MAAA,OAAO,CAAC,KAAR,GAAgB,QAAhB;AACD;;AACD,IAAA,QAAQ,CAAC,IAAI,CAAC,QAAL,CAAc,IAAd,CAAD,CAAR,GAAgC,IAAhC;AAEA,WAAO,IAAP;AACD;;AAED,EAAA,OAAO,CAAC,CAAD,EAAU;AACf,UAAM,IAAI,GAAG,KAAK,uBAAL,CAA6B,IAAI,CAAC,OAAL,CAAa,CAAb,CAA7B,EAA8C,KAA9C,CAAb;AACA,WAAO,IAAI,CAAC,KAAL,CAAa,IAAI,CAAC,QAAL,CAAc,CAAd,CAAb,CAAP;AACD;;AAED,EAAA,OAAO,CAAC,CAAD,EAAY,WAAA,GAAuB,IAAnC,EAAuC;AAC5C,UAAM,IAAI,GAAG,KAAK,OAAL,CAAa,CAAb,CAAb,CAD4C,CAE5C;;AACA,WAAO,WAAW,IAAI,IAAI,CAAC,IAAL,IAAa,IAA5B,GAAmC,KAAK,OAAL,CAAa,IAAI,CAAC,IAAlB,CAAnC,GAA6D,IAApE;AACD;;AAED,QAAM,QAAN,CAAe,IAAf,EAA2B;AACzB,WAAO,IAAI,CAAC,KAAL,CAAW,CAAC,MAAM,KAAK,QAAL,CAAc,IAAd,CAAP,EAA4B,QAA5B,EAAX,CAAP;AACD;;AAED,EAAA,QAAQ,CAAC,IAAD,EAAa;AACnB,WAAO,gBAAgB,CAAC,IAAD,EAAO,IAAP,EAAa,KAAK,OAAL,CAAa,IAAb,CAAb,CAAvB;AACD;;AA7FwB;;;;AAgGpB,eAAe,QAAf,CAAwB,OAAxB,EAAuC;AAC5C,QAAM,EAAE,GAAG,MAAM,qBAAK,OAAL,EAAc,GAAd,CAAjB;AACA,MAAI,IAAJ;AACA,MAAI,SAAJ;;AACA,MAAI;AACF,UAAM,OAAO,GAAG,MAAM,CAAC,WAAP,CAAmB,CAAnB,CAAhB;;AACA,QAAI,CAAC,MAAM,qBAAK,EAAL,EAAS,OAAT,EAAkB,CAAlB,EAAqB,CAArB,EAAwB,IAAxB,CAAP,EAA6C,SAA7C,KAA2D,CAA/D,EAAkE;AAChE,YAAM,IAAI,KAAJ,CAAU,4BAAV,CAAN;AACD;;AAED,UAAM,UAAU,GAAG,0CAAiB,OAAjB,CAAnB;AACA,IAAA,IAAI,GAAG,UAAU,CAAC,cAAX,GAA4B,UAA5B,EAAP;AACA,IAAA,SAAS,GAAG,MAAM,CAAC,WAAP,CAAmB,IAAnB,CAAZ;;AACA,QAAI,CAAC,MAAM,qBAAK,EAAL,EAAS,SAAT,EAAoB,CAApB,EAAuB,IAAvB,EAA6B,IAA7B,CAAP,EAAkD,SAAlD,KAAgE,IAApE,EAA0E;AACxE,YAAM,IAAI,KAAJ,CAAU,uBAAV,CAAN;AACD;AACF,GAZD,SAaQ;AACN,UAAM,sBAAM,EAAN,CAAN;AACD;;AAED,QAAM,YAAY,GAAG,0CAAiB,SAAjB,CAArB;AACA,QAAM,MAAM,GAAG,YAAY,CAAC,cAAb,GAA8B,UAA9B,EAAf;AACA,SAAO,IAAI,cAAJ,CAAmB,OAAnB,EAA4B,IAAI,CAAC,KAAL,CAAW,MAAX,CAA5B,EAAgD,IAAhD,CAAP;AACD;;AAEM,eAAe,YAAf,CAA4B,OAA5B,EAA6C,IAA7C,EAAyD;AAC9D,QAAM,EAAE,GAAG,MAAM,QAAQ,CAAC,OAAD,CAAzB;AACA,SAAO,MAAM,EAAE,CAAC,QAAH,CAAY,IAAZ,CAAb;AACD;;AAED,eAAe,gBAAf,CAAgC,UAAhC,EAA4D,QAA5D,EAA8E,IAA9E,EAAwF;AACtF,QAAM,IAAI,GAAG,IAAI,CAAC,IAAlB;AACA,QAAM,MAAM,GAAG,MAAM,CAAC,WAAP,CAAmB,IAAnB,CAAf;;AACA,MAAI,IAAI,IAAI,CAAZ,EAAe;AACb,WAAO,MAAP;AACD;;AAED,MAAI,IAAI,CAAC,QAAT,EAAmB;AACjB,WAAO,MAAM,yBAAS,IAAI,CAAC,IAAL,CAAU,GAAG,UAAU,CAAC,GAAG,WAA3B,EAAwC,QAAxC,CAAT,CAAb;AACD;;AAED,QAAM,EAAE,GAAG,MAAM,qBAAK,UAAU,CAAC,GAAhB,EAAqB,GAArB,CAAjB;;AACA,MAAI;AACF,UAAM,MAAM,GAAG,IAAI,UAAU,CAAC,UAAf,GAA4B,QAAQ,CAAC,IAAI,CAAC,MAAN,EAAgB,EAAhB,CAAnD;AACA,UAAM,qBAAK,EAAL,EAAS,MAAT,EAAiB,CAAjB,EAAoB,IAApB,EAA0B,MAA1B,CAAN;AACD,GAHD,SAIQ;AACN,UAAM,sBAAM,EAAN,CAAN;AACD;;AACD,SAAO,MAAP;AACD,C", "sourcesContent": ["import { create<PERSON><PERSON><PERSON><PERSON><PERSON> } from \"chromium-pickle-js\"\nimport { close, open, read, readFile, Stats } from \"fs-extra\"\nimport * as path from \"path\"\n\n/** @internal */\nexport class Node {\n  // we don't use Map because later it will be stringified\n  files?: { [key: string]: Node }\n\n  unpacked?: boolean\n\n  size?: number\n  // electron expects string\n  offset?: string\n\n  executable?: boolean\n\n  link?: string\n}\n\n/** @internal */\nexport class AsarFilesystem {\n  private offset = 0\n\n  constructor(readonly src: string, readonly header = new Node(), readonly headerSize: number = -1) {\n    if (this.header.files == null) {\n      this.header.files = {}\n    }\n  }\n\n  searchNodeFromDirectory(p: string, isCreate: boolean): Node | null {\n    let node = this.header\n    for (const dir of p.split(path.sep)) {\n      if (dir !== \".\") {\n        let child = node.files![dir]\n        if (child == null) {\n          if (!isCreate) {\n            return null\n          }\n          child = new Node()\n          child.files = {}\n          node.files![dir] = child\n        }\n        node = child\n      }\n    }\n    return node\n  }\n\n  getOrCreateNode(p: string): Node {\n    if (p == null || p.length === 0) {\n      return this.header\n    }\n\n    const name = path.basename(p)\n    const dirNode = this.searchNodeFromDirectory(path.dirname(p), true)!\n    if (dirNode.files == null) {\n      dirNode.files = {}\n    }\n\n    let result = dirNode.files[name]\n    if (result == null) {\n      result = new Node()\n      dirNode.files[name] = result\n    }\n    return result\n  }\n\n  addFileNode(file: string, dirNode: Node, size: number, unpacked: boolean, stat: Stats): Node {\n    if (size > 4294967295) {\n      throw new Error(`${file}: file size cannot be larger than 4.2GB`)\n    }\n\n    const node = new Node()\n    node.size = size\n    if (unpacked) {\n      node.unpacked = true\n    }\n    else {\n      // electron expects string\n      node.offset = this.offset.toString()\n      if (process.platform !== \"win32\" && (stat.mode & 0o100)) {\n        node.executable = true\n      }\n      this.offset += node.size\n    }\n\n    let children = dirNode.files\n    if (children == null) {\n      children = {}\n      dirNode.files = children\n    }\n    children[path.basename(file)] = node\n\n    return node\n  }\n\n  getNode(p: string): Node | null {\n    const node = this.searchNodeFromDirectory(path.dirname(p), false)!!\n    return node.files!![path.basename(p)]\n  }\n\n  getFile(p: string, followLinks: boolean = true): Node {\n    const info = this.getNode(p)!\n    // if followLinks is false we don't resolve symlinks\n    return followLinks && info.link != null ? this.getFile(info.link) : info\n  }\n\n  async readJson(file: string): Promise<any> {\n    return JSON.parse((await this.readFile(file)).toString())\n  }\n\n  readFile(file: string): Promise<Buffer> {\n    return readFileFromAsar(this, file, this.getFile(file))\n  }\n}\n\nexport async function readAsar(archive: string): Promise<AsarFilesystem> {\n  const fd = await open(archive, \"r\")\n  let size: number\n  let headerBuf\n  try {\n    const sizeBuf = Buffer.allocUnsafe(8)\n    if ((await read(fd, sizeBuf, 0, 8, null as any)).bytesRead !== 8) {\n      throw new Error(\"Unable to read header size\")\n    }\n\n    const sizePickle = createFromBuffer(sizeBuf)\n    size = sizePickle.createIterator().readUInt32()\n    headerBuf = Buffer.allocUnsafe(size)\n    if ((await read(fd, headerBuf, 0, size, null as any)).bytesRead !== size) {\n      throw new Error(\"Unable to read header\")\n    }\n  }\n  finally {\n    await close(fd)\n  }\n\n  const headerPickle = createFromBuffer(headerBuf!)\n  const header = headerPickle.createIterator().readString()\n  return new AsarFilesystem(archive, JSON.parse(header), size)\n}\n\nexport async function readAsarJson(archive: string, file: string): Promise<any> {\n  const fs = await readAsar(archive)\n  return await fs.readJson(file)\n}\n\nasync function readFileFromAsar(filesystem: AsarFilesystem, filename: string, info: Node): Promise<Buffer> {\n  const size = info.size!!\n  const buffer = Buffer.allocUnsafe(size)\n  if (size <= 0) {\n    return buffer\n  }\n\n  if (info.unpacked) {\n    return await readFile(path.join(`${filesystem.src}.unpacked`, filename))\n  }\n\n  const fd = await open(filesystem.src, \"r\")\n  try {\n    const offset = 8 + filesystem.headerSize + parseInt(info.offset!!, 10)\n    await read(fd, buffer, 0, size, offset)\n  }\n  finally {\n    await close(fd)\n  }\n  return buffer\n}\n"], "sourceRoot": ""}