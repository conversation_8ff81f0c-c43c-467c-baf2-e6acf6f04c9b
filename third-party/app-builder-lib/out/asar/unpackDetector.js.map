{"version": 3, "sources": ["../../src/asar/unpackDetector.ts"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;;;AAEA,SAAS,QAAT,CAAkB,GAAlB,EAAmD,GAAnD,EAAgE,KAAhE,EAA6E;AAC3E,MAAI,IAAI,GAAG,GAAG,CAAC,GAAJ,CAAQ,GAAR,CAAX;;AACA,MAAI,IAAI,IAAI,IAAZ,EAAkB;AAChB,IAAA,IAAI,GAAG,CAAC,KAAD,CAAP;AACA,IAAA,GAAG,CAAC,GAAJ,CAAQ,GAAR,EAAa,IAAb;AACD,GAHD,MAIK;AACH,IAAA,IAAI,CAAC,IAAL,CAAU,KAAV;AACD;AACF;;AAEK,SAAU,UAAV,CAAqB,IAArB,EAAiC;AACrC,SAAO,IAAI,CAAC,QAAL,CAAc,MAAd,KAAyB,IAAI,CAAC,QAAL,CAAc,MAAd,CAAzB,IAAkD,IAAI,CAAC,QAAL,CAAc,QAAd,CAAlD,IAA6E,IAAI,CAAC,QAAL,CAAc,KAAd,CAApF;AACD;AAED;;;AACO,eAAe,kBAAf,CAAkC,OAAlC,EAA4D,cAA5D,EAAyF,YAAzF,EAA+G,0BAA/G,EAAiJ;AACtJ,QAAM,WAAW,GAAG,IAAI,GAAJ,EAApB;AACA,QAAM,QAAQ,GAAG,OAAO,CAAC,QAAzB;;AAEA,WAAS,UAAT,CAAoB,KAApB,EAAmC,IAAnC,EAA+C;AAC7C,IAAA,KAAK,GAAG,IAAI,CAAC,OAAL,CAAa,KAAb,CAAR;;AACA,QAAI,cAAc,CAAC,GAAf,CAAmB,KAAnB,CAAJ,EAA+B;AAC7B;AACD;;AAED,OAAG;AACD,MAAA,cAAc,CAAC,GAAf,CAAmB,KAAnB;AACA,YAAM,CAAC,GAAG,IAAI,CAAC,OAAL,CAAa,KAAb,CAAV,CAFC,CAGD;;AACA,MAAA,QAAQ,CAAC,WAAD,EAAc,CAAd,EAAiB,IAAI,CAAC,QAAL,CAAc,KAAd,CAAjB,CAAR;;AAEA,UAAI,KAAK,KAAK,IAAV,IAAkB,CAAC,KAAK,IAAxB,IAAgC,cAAc,CAAC,GAAf,CAAmB,CAAnB,CAApC,EAA2D;AACzD;AACD;;AACD,MAAA,KAAK,GAAG,CAAR;AACD,KAVD,QAWO,IAXP;;AAaA,IAAA,cAAc,CAAC,GAAf,CAAmB,IAAnB;AACD;;AAED,OAAK,IAAI,CAAC,GAAG,CAAR,EAAW,CAAC,GAAG,OAAO,CAAC,KAAR,CAAc,MAAlC,EAA0C,CAAC,GAAG,CAA9C,EAAiD,CAAC,EAAlD,EAAsD;AACpD,UAAM,IAAI,GAAG,OAAO,CAAC,KAAR,CAAc,CAAd,CAAb;AACA,UAAM,KAAK,GAAG,IAAI,CAAC,WAAL,CAAiB,uCAAjB,CAAd;;AACA,QAAI,KAAK,GAAG,CAAZ,EAAe;AACb;AACD;;AAED,QAAI,cAAc,GAAG,IAAI,CAAC,OAAL,CAAa,IAAI,CAAC,GAAlB,EAAuB,KAAK,GAAG,wCAAqB,MAA7B,GAAsC,CAA7D,CAArB;;AACA,QAAI,cAAc,GAAG,CAArB,EAAwB;AACtB;AACD;;AAED,QAAI,IAAI,CAAC,KAAK,GAAG,wCAAqB,MAA9B,CAAJ,KAA8C,GAAlD,EAAuD;AACrD,MAAA,cAAc,GAAG,IAAI,CAAC,OAAL,CAAa,IAAI,CAAC,GAAlB,EAAuB,cAAc,GAAG,CAAxC,CAAjB;AACD;;AAED,QAAI,CAAC,QAAQ,CAAC,GAAT,CAAa,IAAb,EAAoB,MAApB,EAAL,EAAmC;AACjC;AACD;;AAED,UAAM,UAAU,GAAG,IAAI,CAAC,SAAL,CAAe,CAAf,EAAkB,cAAlB,CAAnB;AACA,UAAM,uBAAuB,GAAG,IAAI,CAAC,QAAL,CAAc,0BAAd,EAA0C,yCAAmB,UAAnB,EAA+B,OAA/B,CAA1C,CAAhC;AACA,UAAM,aAAa,GAAG,IAAI,CAAC,QAAL,CAAc,0BAAd,EAA0C,yCAAmB,IAAnB,EAAyB,OAAzB,CAA1C,CAAtB;;AACA,QAAI,cAAc,CAAC,GAAf,CAAmB,uBAAnB,CAAJ,EAAiD;AAC/C;AACA,MAAA,UAAU,CAAC,aAAD,EAAgB,uBAAhB,CAAV;AACA;AACD,KA3BmD,CA6BpD;;;AACA,QAAI,YAAY,GAAG,KAAnB,CA9BoD,CA+BpD;;AACA,UAAM,UAAU,GAAG,IAAI,CAAC,QAAL,CAAc,UAAd,CAAnB;;AACA,QAAI,UAAU,KAAM,gBAAhB,IAAoC,UAAU,KAAK,eAAnD,IAAsE,UAAU,CAAC,IAAD,CAApF,EAA4F;AAC1F,MAAA,YAAY,GAAG,IAAf;AACD,KAFD,MAGK,IAAI,CAAC,IAAI,CAAC,QAAL,CAAc,GAAd,EAAmB,cAAnB,CAAD,IAAuC,IAAI,CAAC,OAAL,CAAa,IAAb,MAAuB,EAAlE,EAAsE;AACzE,MAAA,YAAY,GAAG,MAAM,kCAAa,IAAb,CAArB;AACD;;AAED,QAAI,CAAC,YAAL,EAAmB;AACjB;AACD;;AAED,QAAI,mBAAI,cAAR,EAAwB;AACtB,yBAAI,KAAJ,CAAU;AAAC,QAAA,IAAI,EAAE,aAAP;AAAsB,QAAA,MAAM,EAAE;AAA9B,OAAV,EAAqE,8BAArE;AACD;;AAED,IAAA,UAAU,CAAC,aAAD,EAAgB,uBAAhB,CAAV;AACD;;AAED,MAAI,WAAW,CAAC,IAAZ,GAAmB,CAAvB,EAA0B;AACxB,UAAM,0BAAU,YAAY,GAAG,IAAI,CAAC,GAApB,GAA0B,cAApC,CAAN,CADwB,CAExB;;AACA,UAAM,uBAAgB,GAAhB,CAAoB,WAAW,CAAC,IAAZ,EAApB,EAAwC,MAAM,SAAN,IAAkB;AAC9D,YAAM,IAAI,GAAG,YAAY,GAAG,IAAI,CAAC,GAApB,GAA0B,SAAvC;AACA,YAAM,0BAAU,IAAV,CAAN;AACA,YAAM,uBAAgB,IAAhB,CAAqB,WAAW,CAAC,GAAZ,CAAgB,SAAhB,CAArB,EAAmD,EAAD,IAAY;AAClE,YAAI,WAAW,CAAC,GAAZ,CAAgB,SAAS,GAAG,IAAI,CAAC,GAAjB,GAAuB,EAAvC,CAAJ,EAAgD;AAC9C;AACA,iBAAO,IAAP;AACD,SAHD,MAIK;AACH,iBAAO,0BAAU,IAAI,GAAG,IAAI,CAAC,GAAZ,GAAkB,EAA5B,CAAP;AACD;AACF,OARK,CAAN;AASD,KAZK,EAYH,iBAZG,CAAN;AAaD;AACF,C", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { log } from \"builder-util\"\nimport { CONCURRENCY } from \"builder-util/out/fs\"\nimport { ensureDir } from \"fs-extra\"\nimport { isBinaryFile } from \"isbinaryfile\"\nimport * as path from \"path\"\nimport { NODE_MODULES_PATTERN } from \"../fileTransformer\"\nimport { getDestinationPath, ResolvedFileSet } from \"../util/appFileCopier\"\n\nfunction addValue(map: Map<string, Array<string>>, key: string, value: string) {\n  let list = map.get(key)\n  if (list == null) {\n    list = [value]\n    map.set(key, list)\n  }\n  else {\n    list.push(value)\n  }\n}\n\nexport function isLibOrExe(file: string): boolean {\n  return file.endsWith(\".dll\") || file.endsWith(\".exe\") || file.endsWith(\".dylib\") || file.endsWith(\".so\")\n}\n\n/** @internal */\nexport async function detectUnpackedDirs(fileSet: ResolvedFileSet, autoUnpackDirs: Set<string>, unpackedDest: string, rootForAppFilesWithoutAsar: string) {\n  const dirToCreate = new Map<string, Array<string>>()\n  const metadata = fileSet.metadata\n\n  function addParents(child: string, root: string) {\n    child = path.dirname(child)\n    if (autoUnpackDirs.has(child)) {\n      return\n    }\n\n    do {\n      autoUnpackDirs.add(child)\n      const p = path.dirname(child)\n      // create parent dir to be able to copy file later without directory existence check\n      addValue(dirToCreate, p, path.basename(child))\n\n      if (child === root || p === root || autoUnpackDirs.has(p)) {\n        break\n      }\n      child = p\n    }\n    while (true)\n\n    autoUnpackDirs.add(root)\n  }\n\n  for (let i = 0, n = fileSet.files.length; i < n; i++) {\n    const file = fileSet.files[i]\n    const index = file.lastIndexOf(NODE_MODULES_PATTERN)\n    if (index < 0) {\n      continue\n    }\n\n    let nextSlashIndex = file.indexOf(path.sep, index + NODE_MODULES_PATTERN.length + 1)\n    if (nextSlashIndex < 0) {\n      continue\n    }\n\n    if (file[index + NODE_MODULES_PATTERN.length] === \"@\") {\n      nextSlashIndex = file.indexOf(path.sep, nextSlashIndex + 1)\n    }\n\n    if (!metadata.get(file)!.isFile()) {\n      continue\n    }\n\n    const packageDir = file.substring(0, nextSlashIndex)\n    const packageDirPathInArchive = path.relative(rootForAppFilesWithoutAsar, getDestinationPath(packageDir, fileSet))\n    const pathInArchive = path.relative(rootForAppFilesWithoutAsar, getDestinationPath(file, fileSet))\n    if (autoUnpackDirs.has(packageDirPathInArchive)) {\n      // if package dir is unpacked, any file also unpacked\n      addParents(pathInArchive, packageDirPathInArchive)\n      continue\n    }\n\n    // https://github.com/electron-userland/electron-builder/issues/2679\n    let shouldUnpack = false\n    // ffprobe-static and ffmpeg-static are known packages to always unpack\n    const moduleName = path.basename(packageDir)\n    if (moduleName ===  \"ffprobe-static\" || moduleName === \"ffmpeg-static\" || isLibOrExe(file)) {\n      shouldUnpack = true\n    }\n    else if (!file.includes(\".\", nextSlashIndex) && path.extname(file) === \"\") {\n      shouldUnpack = await isBinaryFile(file)\n    }\n\n    if (!shouldUnpack) {\n      continue\n    }\n\n    if (log.isDebugEnabled) {\n      log.debug({file: pathInArchive, reason: \"contains executable code\"}, \"not packed into asar archive\")\n    }\n\n    addParents(pathInArchive, packageDirPathInArchive)\n  }\n\n  if (dirToCreate.size > 0) {\n    await ensureDir(unpackedDest + path.sep + \"node_modules\")\n    // child directories should be not created asynchronously - parent directories should be created first\n    await BluebirdPromise.map(dirToCreate.keys(), async parentDir => {\n      const base = unpackedDest + path.sep + parentDir\n      await ensureDir(base)\n      await BluebirdPromise.each(dirToCreate.get(parentDir)!, (it): any => {\n        if (dirToCreate.has(parentDir + path.sep + it)) {\n          // already created\n          return null\n        }\n        else {\n          return ensureDir(base + path.sep + it)\n        }\n      })\n    }, CONCURRENCY)\n  }\n}"], "sourceRoot": ""}