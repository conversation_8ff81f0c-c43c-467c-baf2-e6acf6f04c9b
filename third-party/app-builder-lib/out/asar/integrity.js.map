{"version": 3, "sources": ["../../src/asar/integrity.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;;;;;;;AAeO,eAAe,WAAf,CAA2B,aAA3B,EAAkD,OAAlD,EAAuF;AAC5F;AACA,QAAM,KAAK,GAAG,CAAC,MAAM,wBAAQ,aAAR,CAAP,EAA+B,MAA/B,CAAsC,EAAE,IAAI,EAAE,CAAC,QAAH,CAAY,OAAZ,CAA5C,EAAkE,IAAlE,EAAd;AACA,QAAM,SAAS,GAAG,MAAM,uBAAgB,GAAhB,CAAoB,KAApB,EAA2B,EAAE,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAL,CAAU,aAAV,EAAyB,EAAzB,CAAD,CAAzC,CAAxB;AAEA,QAAM,MAAM,GAA8B,EAA1C;;AACA,OAAK,IAAI,CAAC,GAAG,CAAb,EAAgB,CAAC,GAAG,KAAK,CAAC,MAA1B,EAAkC,CAAC,EAAnC,EAAuC;AACrC,IAAA,MAAM,CAAC,KAAK,CAAC,CAAD,CAAN,CAAN,GAAmB,SAAS,CAAC,CAAD,CAA5B;AACD;;AACD,SAAO;AAAC,IAAA,SAAS,EAAE,MAAZ;AAAoB,OAAG;AAAvB,GAAP;AACD;;AAED,SAAS,QAAT,CAAkB,IAAlB,EAAgC,SAAA,GAAoB,QAApD,EAA8D,QAAA,GAAwC,QAAtG,EAA8G;AAC5G,SAAO,IAAI,OAAJ,CAAoB,CAAC,OAAD,EAAU,MAAV,KAAoB;AAC7C,UAAM,IAAI,GAAG,0BAAW,SAAX,CAAb;AACA,IAAA,IAAI,CACD,EADH,CACM,OADN,EACe,MADf,EAEG,WAFH,CAEe,QAFf;AAIA,8BAAiB,IAAjB,EACG,EADH,CACM,OADN,EACe,MADf,EAEG,EAFH,CAEM,KAFN,EAEa,MAAK;AACd,MAAA,IAAI,CAAC,GAAL;AACA,MAAA,OAAO,CAAC,IAAI,CAAC,IAAL,EAAD,CAAP;AACD,KALH,EAMG,IANH,CAMQ,IANR,EAMc;AAAC,MAAA,GAAG,EAAE;AAAN,KANd;AAOD,GAbM,CAAP;AAcD,C", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { createHash } from \"crypto\"\nimport { createReadStream } from \"fs\"\nimport { readdir } from \"fs-extra\"\nimport * as path from \"path\"\n\nexport interface AsarIntegrityOptions {\n  /**\n   * Allows external asar files.\n   *\n   * @default false\n   */\n  readonly externalAllowed?: boolean\n}\n\nexport interface AsarIntegrity extends AsarIntegrityOptions {\n  checksums: { [key: string]: string }\n}\n\nexport async function computeData(resourcesPath: string, options?: AsarIntegrityOptions | null): Promise<AsarIntegrity> {\n  // sort to produce constant result\n  const names = (await readdir(resourcesPath)).filter(it => it.endsWith(\".asar\")).sort()\n  const checksums = await BluebirdPromise.map(names, it => hashFile(path.join(resourcesPath, it)))\n\n  const result: { [key: string]: string } = {}\n  for (let i = 0; i < names.length; i++) {\n    result[names[i]] = checksums[i]\n  }\n  return {checksums: result, ...options}\n}\n\nfunction hashFile(file: string, algorithm: string = \"sha512\", encoding: \"hex\" | \"base64\" | \"latin1\" = \"base64\") {\n  return new Promise<string>((resolve, reject) => {\n    const hash = createHash(algorithm)\n    hash\n      .on(\"error\", reject)\n      .setEncoding(encoding)\n\n    createReadStream(file)\n      .on(\"error\", reject)\n      .on(\"end\", () => {\n        hash.end()\n        resolve(hash.read() as string)\n      })\n      .pipe(hash, {end: false})\n  })\n}"], "sourceRoot": ""}