{"version": 3, "sources": ["../../src/codeSign/macCodeSign.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;;;AAEO,MAAM,wBAAwB,GAAG,CAAC,2BAAD,EAA8B,yBAA9B,EAAyD,sCAAzD,EAAiG,oCAAjG,CAAjC;;;AAQD,SAAU,aAAV,CAAwB,WAAW,GAAG,IAAtC,EAA0C;AAC9C,MAAI,OAAO,CAAC,QAAR,KAAqB,QAAzB,EAAmC;AACjC,QAAI,WAAJ,EAAiB;AACf,kBAAI,IAAJ,CAAS;AAAC,QAAA,MAAM,EAAE;AAAT,OAAT,EAA8C,wCAA9C;AACD;;AACD,WAAO,KAAP;AACD;;AAED,QAAM,iBAAiB,GAAG,qKACxB,6LADF;;AAGA,MAAI,4BAAJ,EAAqB;AACnB,QAAI,uBAAU,OAAO,CAAC,GAAR,CAAY,oBAAtB,CAAJ,EAAiD;AAC/C,UAAI,WAAJ,EAAiB;AACf,oBAAI,IAAJ,CAAS,iBAAT;AACD;AACF,KAJD,MAKK;AACH,UAAI,WAAJ,EAAiB;AACf;AACA,oBAAI,IAAJ,CAAS,2EACP,+DADO,GAEP,KAAK,iBAAiB,EAFxB;AAGD;;AACD,aAAO,KAAP;AACD;AACF;;AACD,SAAO,IAAP;AACD;;AAEM,eAAe,WAAf,CAA2B,KAA3B,EAA2C,eAA3C,EAAsE,SAAtE,EAA4G,YAA5G,EAAqJ,kBAArJ,EAAgL;AACrL,QAAM,SAAS,GAAW,EAA1B;;AACA,MAAI,SAAS,IAAI,IAAjB,EAAuB;AACrB,IAAA,SAAS,CAAC,MAAV,GAAmB,EAAnB;;AACA,QAAI,+CAAJ,EAAuC;AACrC,MAAA,SAAS,CAAC,MAAV,IAAoB,sBAAsB,eAAe,aAAc,KAAK,GAAG,EAAH,GAAQ,+CAAgD,EAApI;AACD;;AACD,IAAA,SAAS,CAAC,MAAV,IAAoB,2CAApB;;AACA,QAAI,CAAC,+CAAL,EAAwC;AACtC,MAAA,SAAS,CAAC,2BAAV,GAAwC,KAAxC;AACD;AACF,GATD,MAUK;AACH,IAAA,SAAS,CAAC,MAAV,GAAmB,kFAAnB;AACA,IAAA,SAAS,CAAC,QAAV,GAAqB,SAArB;AACD;;AAED,QAAM,IAAI,GAAG,CAAC,eAAD,CAAb;;AACA,MAAI,YAAY,IAAI,IAApB,EAA0B;AACxB,IAAA,IAAI,CAAC,IAAL,CAAU,YAAV;AACD;;AAED,MAAI,SAAS,IAAI,IAAb,IAAqB,+CAAzB,EAA4D;AAC1D,IAAA,SAAS,CAAC,aAAV,GAA0B,CAAC,MAAM,kBAAK,UAAL,EAAiB,IAAjB,CAAP,EACvB,IADuB,GAEvB,KAFuB,CAEjB,IAFiB,EAGvB,MAHuB,CAGhB,EAAE,IAAI,EAAE,EAAE,CAAC,QAAH,CAAY,qBAAZ,KAAsC,EAAE,CAAC,QAAH,CAAY,qBAAZ,CAAxC,CAHU,EAIvB,IAJuB,CAIlB,IAJkB,CAA1B;AAKD;;AAED,MAAI,KAAK,IAAI,kBAAb,EAAiC;AAC/B,UAAM,IAAI,KAAJ,CAAU,cAAO,aAAP,CAAqB,wCAArB,EAA+D,SAA/D,EAA0E,OAA1E,EAAmF,EAAE,IAAI,EAAzF,CAAV,CAAN;AACD,GAFD,MAGK;AACH,gBAAI,IAAJ,CAAS,SAAT,EAAoB,wCAApB;AACD;AACF,C,CAED;AACA;AACA;AACA;;;AACA,MAAM,wBAAwB,GAAG,KAAI,eAAJ,EAAe,YAAW;AACzD;AACA,QAAM,QAAQ,GAAG,iBAAiB,EAAlC;AACA,QAAM,eAAe,GAAG,IAAI,CAAC,IAAL,CAAU,QAAV,EAAoB,6BAAY,6BAAZ,CAApB,CAAxB;AACA,QAAM,YAAY,GAAG,IAAI,CAAC,IAAL,CAAU,QAAV,EAAoB,sCAApB,CAArB;AACA,QAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAR,CAAiB,CACrC,iBAAiB,EADoB,EAErC,oBAAS,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,IAArB,EAA2B,IAA3B,EAAiC,OAAjC,EAA0C,qBAA1C,CAAT,EAA2E,eAA3E,EACG,IADH,CACQ,MAAM,uBAAO,eAAP,EAAwB,YAAxB,CADd,CAFqC,CAAjB,CAAtB;AAKA,QAAM,IAAI,GAAG,OAAO,CAAC,CAAD,CAApB;;AACA,MAAI,CAAC,IAAI,CAAC,QAAL,CAAc,YAAd,CAAL,EAAkC;AAChC,UAAM,kBAAK,UAAL,EAAiB,CAAC,gBAAD,EAAmB,IAAnB,EAAyB,MAAzB,EAAiC,IAAjC,EAAuC,YAAvC,EAAqD,MAArD,CAA4D,IAA5D,CAAjB,CAAN;AACD;AACF,CAdgC,CAAjC;;AAgBA,SAAS,iBAAT,GAA0B;AACxB,QAAM,GAAG,GAAG,OAAO,CAAC,GAAR,CAAY,sBAAxB;AACA,SAAO,6BAAgB,GAAhB,IAAuB,IAAI,CAAC,IAAL,CAAU,oBAAV,EAAqB,SAArB,EAAgC,QAAhC,EAA0C,kBAA1C,CAAvB,GAAuF,IAAI,CAAC,OAAL,CAAa,GAAb,CAA9F;AACD;;AAED,SAAS,iBAAT,GAA0B;AACxB,SAAO,kBAAK,UAAL,EAAiB,CAAC,gBAAD,EAAmB,IAAnB,EAAyB,MAAzB,CAAjB,EACJ,IADI,CACC,EAAE,IAAI,EAAE,CACX,KADS,CACH,IADG,EAET,GAFS,CAEL,EAAE,IAAG;AACR,UAAM,CAAC,GAAG,EAAE,CAAC,IAAH,EAAV;AACA,WAAO,CAAC,CAAC,SAAF,CAAY,CAAZ,EAAe,CAAC,CAAC,MAAF,GAAW,CAA1B,CAAP;AACD,GALS,EAMT,MANS,CAMF,EAAE,IAAI,EAAE,CAAC,MAAH,GAAY,CANhB,CADP,CAAP;AAQD;;AAWK,SAAU,cAAV,CAAyB,YAAzB,EAA+C,SAAS,GAAG,IAA3D,EAA+D;AACnE,SAAO,kBAAK,UAAL,EAAiB,CAAC,iBAAD,EAAoB,YAApB,CAAjB,EACJ,KADI,CACE,CAAC,IAAG;AACT,QAAI,SAAJ,EAAe;AACb,kBAAI,IAAJ,CAAS;AAAC,QAAA,IAAI,EAAE,YAAP;AAAqB,QAAA,KAAK,EAAE,CAAC,CAAC,KAAF,IAAW;AAAvC,OAAT,EAAoD,wBAApD;AACD;;AACD,WAAO,0BAAe,YAAf,CAAP;AACD,GANI,CAAP;AAOD;;AAEM,eAAe,cAAf,CAA8B;AAAC,EAAA,MAAD;AAAS,EAAA,OAAT;AAAkB,EAAA,cAAlB;AAAkC,EAAA,QAAlC;AAA4C,EAAA,eAA5C;AAA6D,EAAA;AAA7D,CAA9B,EAA6H;AAClI;AACA,MAAI,OAAO,CAAC,GAAR,CAAY,MAAZ,KAAuB,MAA3B,EAAmC;AACjC,UAAM,wBAAwB,CAAC,KAA/B;AACD,GAJiI,CAMlI;AACA;;;AACA,QAAM,YAAY,GAAG,IAAI,CAAC,IAAL,CAAU,OAAO,CAAC,GAAR,CAAY,mBAAZ,IAAmC,mBAA7C,EAAuD,GAAG,0BAAW,QAAX,EAAqB,MAArB,CAA4B,UAA5B,EAAwC,MAAxC,CAA+C,aAA/C,EAA8D,MAA9D,CAAqE,KAArE,CAA2E,WAArI,CAArB,CARkI,CASlI;AACA;;AACA,QAAM,cAAc,CAAC,YAAD,EAAe,KAAf,CAAd,CAAoC,KAApC,CAA0C,CAAC,IAAG,CAAc,CAA5D,CAAN;AAEA,QAAM,SAAS,GAAG,CAAC,OAAD,CAAlB;;AACA,MAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,IAAA,SAAS,CAAC,IAAV,CAAe,QAAf;AACD;;AAED,QAAM,SAAS,GAAG,IAAI,KAAJ,CAAU,SAAS,CAAC,MAApB,CAAlB;AACA,QAAM,gBAAgB,GAAG,2BAAY,EAAZ,EAAgB,QAAhB,CAAyB,QAAzB,CAAzB;AACA,QAAM,gBAAgB,GAAG,CACvB,CAAC,iBAAD,EAAoB,IAApB,EAA0B,gBAA1B,EAA4C,YAA5C,CADuB,EAEvB,CAAC,iBAAD,EAAoB,IAApB,EAA0B,gBAA1B,EAA4C,YAA5C,CAFuB,EAGvB,CAAC,uBAAD,EAA0B,YAA1B,CAHuB,CAAzB,CApBkI,CA0BlI;AACA;;AACA,QAAM,IAAI,GAAG,MAAM,iBAAiB,EAApC;;AACA,MAAI,CAAC,IAAI,CAAC,QAAL,CAAc,YAAd,CAAL,EAAkC;AAChC,IAAA,gBAAgB,CAAC,IAAjB,CAAsB,CAAC,gBAAD,EAAmB,IAAnB,EAAyB,MAAzB,EAAiC,IAAjC,EAAuC,YAAvC,EAAqD,MAArD,CAA4D,IAA5D,CAAtB;AACD;;AAED,QAAM,OAAO,CAAC,GAAR,CAAY,CAChB;AACA,yBAAgB,GAAhB,CAAoB,SAApB,EAA+B,CAAC,IAAD,EAAO,CAAP,KAAa,qCAAoB,IAApB,EAA0B,MAA1B,EAAkC,UAAlC,EAA8C,IAA9C,CAAmD,EAAE,IAAI,SAAS,CAAC,CAAD,CAAT,GAAe,EAAxE,CAA5C,CAFgB,EAGhB,uBAAgB,SAAhB,CAA0B,gBAA1B,EAA4C,EAAE,IAAI,kBAAK,UAAL,EAAiB,EAAjB,CAAlD,CAHgB,CAAZ,CAAN;AAKA,SAAO,MAAM,WAAW,CAAC,YAAD,EAAe,SAAf,EAA0B,CAAC,cAAD,EAAiB,eAAjB,EAAkC,MAAlC,CAAyC,EAAE,IAAI,EAAE,IAAI,IAArD,CAA1B,CAAxB;AACD;;AAED,eAAe,WAAf,CAA2B,YAA3B,EAAiD,KAAjD,EAAuE,YAAvE,EAAkG;AAChG,OAAK,IAAI,CAAC,GAAG,CAAb,EAAgB,CAAC,GAAG,KAAK,CAAC,MAA1B,EAAkC,CAAC,EAAnC,EAAuC;AACrC,UAAM,QAAQ,GAAG,YAAY,CAAC,CAAD,CAA7B;AACA,UAAM,kBAAK,UAAL,EAAiB,CAAC,QAAD,EAAW,KAAK,CAAC,CAAD,CAAhB,EAAqB,IAArB,EAA2B,YAA3B,EAAyC,IAAzC,EAA+C,mBAA/C,EAAoE,IAApE,EAA0E,uBAA1E,EAAmG,IAAnG,EAAyG,QAAzG,CAAjB,CAAN,CAFqC,CAIrC;AACA;;AACA,UAAM,kBAAK,UAAL,EAAiB,CAAC,wBAAD,EAA2B,IAA3B,EAAiC,oBAAjC,EAAuD,IAAvD,EAA6D,IAA7D,EAAmE,QAAnE,EAA6E,YAA7E,CAAjB,CAAN;AACD;;AAED,SAAO;AACL,IAAA;AADK,GAAP;AAGD;AAED;;;AACM,SAAU,IAAV,CAAe,IAAf,EAA6B,IAA7B,EAA2C,QAA3C,EAA2D;AAC/D,QAAM,IAAI,GAAG,CAAC,QAAD,EAAW,SAAX,EAAsB,QAAtB,EAAgC,IAAhC,EAAsC,IAAtC,CAAb;;AACA,MAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,IAAA,IAAI,CAAC,IAAL,CAAU,YAAV,EAAwB,QAAxB;AACD;;AACD,SAAO,kBAAK,UAAL,EAAiB,IAAjB,CAAP;AACD;;AAEM,IAAI,qBAAqB,GAAkC,IAA3D;;;AAEP,eAAe,kBAAf,CAAkC,QAAlC,EAA0D;AACxD,WAAS,WAAT,CAAqB,IAArB,EAAwC;AACtC,QAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,MAAA,IAAI,CAAC,IAAL,CAAU,QAAV;AACD;;AACD,WAAO,IAAP;AACD;;AAED,MAAI,MAAM,GAAG,qBAAb;;AACA,MAAI,MAAM,IAAI,IAAV,IAAkB,QAAQ,IAAI,IAAlC,EAAwC;AACtC;AACA;AACA,IAAA,MAAM,GAAG,OAAO,CAAC,GAAR,CAA2B,CAClC,kBAAK,UAAL,EAAiB,WAAW,CAAC,CAAC,eAAD,EAAkB,IAAlB,CAAD,CAA5B,EACG,IADH,CACQ,EAAE,IAAI,EAAE,CAAC,IAAH,GAAU,KAAV,CAAgB,IAAhB,EAAsB,MAAtB,CAA6B,EAAE,IAAG;AAC5C,WAAK,MAAM,MAAX,IAAqB,wBAArB,EAA+C;AAC7C,YAAI,EAAE,CAAC,QAAH,CAAY,MAAZ,CAAJ,EAAyB;AACvB,iBAAO,IAAP;AACD;AACF;;AACD,aAAO,KAAP;AACD,KAPW,CADd,CADkC,EAUlC,kBAAK,UAAL,EAAiB,WAAW,CAAC,CAAC,eAAD,EAAkB,IAAlB,EAAwB,IAAxB,EAA8B,aAA9B,CAAD,CAA5B,EACG,IADH,CACQ,EAAE,IAAI,EAAE,CAAC,IAAH,GAAU,KAAV,CAAiB,IAAjB,CADd,CAVkC,CAA3B,EAaN,IAbM,CAaD,EAAE,IAAG;AACT,YAAM,KAAK,GAAG,EAAE,CAAC,CAAD,CAAF,CAAM,MAAN,CAAa,EAAE,CAAC,CAAD,CAAf,EACX,MADW,CACJ,EAAE,IAAI,CAAC,EAAE,CAAC,QAAH,CAAY,8BAAZ,CAAD,IAAgD,CAAC,EAAE,CAAC,QAAH,CAAY,wBAAZ,CAAjD,IAA0F,CAAC,EAAE,CAAC,QAAH,CAAY,SAAZ,CAA3F,IAAqH,CAAC,EAAE,CAAC,QAAH,CAAY,6BAAZ,CADxH,EAEZ;AAFY,OAGX,GAHW,CAGP,EAAE,IAAI,EAAE,CAAC,SAAH,CAAa,EAAE,CAAC,OAAH,CAAW,GAAX,IAAkB,CAA/B,EAAkC,IAAlC,EAHC,CAAd;AAIA,aAAO,KAAK,CAAC,IAAN,CAAW,IAAI,GAAJ,CAAQ,KAAR,CAAX,CAAP;AACD,KAnBM,CAAT;;AAqBA,QAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,sCAAA,qBAAqB,GAAG,MAAxB;AACD;AACF;;AACD,SAAO,MAAP;AACD;;AAED,eAAe,aAAf,CAA6B,IAA7B,EAA6C,SAA7C,EAAwE,QAAxE,EAAgG;AAC9F;AACA;AACA,QAAM,KAAK,GAAG,MAAM,kBAAkB,CAAC,QAAD,CAAtC;AACA,QAAM,UAAU,GAAG,GAAG,IAAI,GAA1B;;AACA,OAAK,MAAM,IAAX,IAAmB,KAAnB,EAA0B;AACxB,QAAI,SAAS,IAAI,IAAb,IAAqB,CAAC,IAAI,CAAC,QAAL,CAAc,SAAd,CAA1B,EAAoD;AAClD;AACD;;AAED,QAAI,IAAI,CAAC,QAAL,CAAc,UAAd,CAAJ,EAA+B;AAC7B,aAAO,aAAa,CAAC,IAAD,CAApB;AACD;AACF;;AAED,MAAI,IAAI,KAAK,0BAAb,EAAyC;AACvC;AACA;AACA,IAAA,CAAC,EAAE,KAAK,MAAM,IAAX,IAAmB,KAAnB,EAA0B;AAC3B,UAAI,SAAS,IAAI,IAAb,IAAqB,CAAC,IAAI,CAAC,QAAL,CAAc,SAAd,CAA1B,EAAoD;AAClD;AACD;;AAED,UAAI,IAAI,CAAC,QAAL,CAAc,gBAAd,CAAJ,EAAqC;AACnC;AACD;;AAED,WAAK,MAAM,MAAX,IAAqB,wBAArB,EAA+C;AAC7C,YAAI,IAAI,CAAC,QAAL,CAAc,MAAd,CAAJ,EAA2B;AACzB,mBAAS,CAAT;AACD;AACF;;AAED,aAAO,aAAa,CAAC,IAAD,CAApB;AACD;AACF;;AACD,SAAO,IAAP;AACD;;AASD,MAAM,SAAS,GAAG,OAAO,CAAC,yCAAD,CAAP,CAAmD,QAArE;;AAEA,SAAS,aAAT,CAAuB,IAAvB,EAAmC;AACjC,QAAM,eAAe,GAAG,IAAI,CAAC,OAAL,CAAa,GAAb,CAAxB;AACA,QAAM,IAAI,GAAG,IAAI,CAAC,SAAL,CAAe,eAAe,GAAG,CAAjC,EAAoC,IAAI,CAAC,WAAL,CAAiB,GAAjB,CAApC,CAAb;AACA,QAAM,IAAI,GAAG,IAAI,CAAC,SAAL,CAAe,CAAf,EAAkB,eAAe,GAAG,CAApC,CAAb;AACA,SAAO,IAAI,SAAJ,CAAc,IAAd,EAAoB,IAApB,CAAP;AACD;;AAEK,SAAU,YAAV,CAAuB,QAAvB,EAA2C,SAA3C,EAAsE,QAAtE,EAA8F;AAClG,MAAI,QAAQ,GAAG,SAAS,IAAI,OAAO,CAAC,GAAR,CAAY,QAAxC;;AACA,MAAI,6BAAgB,QAAhB,CAAJ,EAA+B;AAC7B,QAAI,+CAAJ,EAAuC;AACrC,aAAO,aAAa,CAAC,QAAD,EAAW,IAAX,EAAiB,QAAjB,CAApB;AACD,KAFD,MAGK;AACH,aAAO,OAAO,CAAC,OAAR,CAAgB,IAAhB,CAAP;AACD;AACF,GAPD,MAQK;AACH,IAAA,QAAQ,GAAG,QAAS,CAAC,IAAV,EAAX;;AACA,SAAK,MAAM,MAAX,IAAqB,wBAArB,EAA+C;AAC7C,MAAA,WAAW,CAAC,QAAD,EAAW,MAAX,CAAX;AACD;;AACD,WAAO,aAAa,CAAC,QAAD,EAAW,QAAX,EAAqB,QAArB,CAApB;AACD;AACF;;AAED,SAAS,WAAT,CAAqB,IAArB,EAAmC,MAAnC,EAAiD;AAC/C,MAAI,IAAI,CAAC,UAAL,CAAgB,MAAhB,CAAJ,EAA6B;AAC3B,UAAM,KAAI,iCAAJ,EAA8B,yBAAyB,MAAM,kFAA7D,CAAN;AACD;AACF,C", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { exec, InvalidConfigurationError, isEmptyOrSpaces, isEnvTrue, isPullRequest, log, TmpDir } from \"builder-util/out/util\"\nimport { copyFile, unlinkIfExists } from \"builder-util/out/fs\"\nimport { Fields, Logger } from \"builder-util/out/log\"\nimport { randomBytes, createHash } from \"crypto\"\nimport { rename } from \"fs-extra\"\nimport { Lazy } from \"lazy-val\"\nimport { homedir, tmpdir } from \"os\"\nimport * as path from \"path\"\nimport { getTempName } from \"temp-file\"\nimport { isAutoDiscoveryCodeSignIdentity } from \"../util/flags\"\nimport { downloadCertificate } from \"./codesign\"\n\nexport const appleCertificatePrefixes = [\"Developer ID Application:\", \"Developer ID Installer:\", \"3rd Party Mac Developer Application:\", \"3rd Party Mac Developer Installer:\"]\n\nexport type CertType = \"Developer ID Application\" | \"Developer ID Installer\" | \"3rd Party Mac Developer Application\" | \"3rd Party Mac Developer Installer\" | \"Mac Developer\"\n\nexport interface CodeSigningInfo {\n  keychainFile?: string | null\n}\n\nexport function isSignAllowed(isPrintWarn = true): boolean {\n  if (process.platform !== \"darwin\") {\n    if (isPrintWarn) {\n      log.warn({reason: \"supported only on macOS\"}, \"skipped macOS application code signing\")\n    }\n    return false\n  }\n\n  const buildForPrWarning = \"There are serious security concerns with CSC_FOR_PULL_REQUEST=true (see the  CircleCI documentation (https://circleci.com/docs/1.0/fork-pr-builds/) for details)\" +\n    \"\\nIf you have SSH keys, sensitive env vars or AWS credentials stored in your project settings and untrusted forks can make pull requests against your repo, then this option isn't for you.\"\n\n  if (isPullRequest()) {\n    if (isEnvTrue(process.env.CSC_FOR_PULL_REQUEST)) {\n      if (isPrintWarn) {\n        log.warn(buildForPrWarning)\n      }\n    }\n    else {\n      if (isPrintWarn) {\n        // https://github.com/electron-userland/electron-builder/issues/1524\n        log.warn(\"Current build is a part of pull request, code signing will be skipped.\" +\n          \"\\nSet env CSC_FOR_PULL_REQUEST to true to force code signing.\" +\n          `\\n${buildForPrWarning}`)\n      }\n      return false\n    }\n  }\n  return true\n}\n\nexport async function reportError(isMas: boolean, certificateType: CertType, qualifier: string | null | undefined, keychainFile: string | null | undefined, isForceCodeSigning: boolean) {\n  const logFields: Fields = {}\n  if (qualifier == null) {\n    logFields.reason = \"\"\n    if (isAutoDiscoveryCodeSignIdentity()) {\n      logFields.reason += `cannot find valid \"${certificateType}\" identity${(isMas ? \"\" : ` or custom non-Apple code signing certificate`)}`\n    }\n    logFields.reason += \", see https://electron.build/code-signing\"\n    if (!isAutoDiscoveryCodeSignIdentity()) {\n      logFields.CSC_IDENTITY_AUTO_DISCOVERY = false\n    }\n  }\n  else {\n    logFields.reason = \"Identity name is specified, but no valid identity with this name in the keychain\"\n    logFields.identity = qualifier\n  }\n\n  const args = [\"find-identity\"]\n  if (keychainFile != null) {\n    args.push(keychainFile)\n  }\n\n  if (qualifier != null || isAutoDiscoveryCodeSignIdentity()) {\n    logFields.allIdentities = (await exec(\"security\", args))\n      .trim()\n      .split(\"\\n\")\n      .filter(it => !(it.includes(\"Policy: X.509 Basic\") || it.includes(\"Matching identities\")))\n      .join(\"\\n\")\n  }\n\n  if (isMas || isForceCodeSigning) {\n    throw new Error(Logger.createMessage(\"skipped macOS application code signing\", logFields, \"error\", it => it))\n  }\n  else {\n    log.warn(logFields, \"skipped macOS application code signing\")\n  }\n}\n\n// \"Note that filename will not be searched to resolve the signing identity's certificate chain unless it is also on the user's keychain search list.\"\n// but \"security list-keychains\" doesn't support add - we should 1) get current list 2) set new list - it is very bad http://stackoverflow.com/questions/10538942/add-a-keychain-to-search-list\n// \"overly complicated and introduces a race condition.\"\n// https://github.com/electron-userland/electron-builder/issues/398\nconst bundledCertKeychainAdded = new Lazy<void>(async () => {\n  // copy to temp and then atomic rename to final path\n  const cacheDir = getCacheDirectory()\n  const tmpKeychainPath = path.join(cacheDir, getTempName(\"electron-builder-root-certs\"))\n  const keychainPath = path.join(cacheDir, \"electron-builder-root-certs.keychain\")\n  const results = await Promise.all<any>([\n    listUserKeychains(),\n    copyFile(path.join(__dirname, \"..\", \"..\", \"certs\", \"root_certs.keychain\"), tmpKeychainPath)\n      .then(() => rename(tmpKeychainPath, keychainPath)),\n  ])\n  const list = results[0]\n  if (!list.includes(keychainPath)) {\n    await exec(\"security\", [\"list-keychains\", \"-d\", \"user\", \"-s\", keychainPath].concat(list))\n  }\n})\n\nfunction getCacheDirectory(): string {\n  const env = process.env.ELECTRON_BUILDER_CACHE\n  return isEmptyOrSpaces(env) ? path.join(homedir(), \"Library\", \"Caches\", \"electron-builder\") : path.resolve(env!!)\n}\n\nfunction listUserKeychains(): Promise<Array<string>> {\n  return exec(\"security\", [\"list-keychains\", \"-d\", \"user\"])\n    .then(it => it\n      .split(\"\\n\")\n      .map(it => {\n        const r = it.trim()\n        return r.substring(1, r.length - 1)\n      })\n      .filter(it => it.length > 0))\n}\n\nexport interface CreateKeychainOptions {\n  tmpDir: TmpDir\n  cscLink: string\n  cscKeyPassword: string\n  cscILink?: string | null\n  cscIKeyPassword?: string | null\n  currentDir: string\n}\n\nexport function removeKeychain(keychainFile: string, printWarn = true): Promise<any> {\n  return exec(\"security\", [\"delete-keychain\", keychainFile])\n    .catch(e => {\n      if (printWarn) {\n        log.warn({file: keychainFile, error: e.stack || e}, \"cannot delete keychain\")\n      }\n      return unlinkIfExists(keychainFile)\n    })\n}\n\nexport async function createKeychain({tmpDir, cscLink, cscKeyPassword, cscILink, cscIKeyPassword, currentDir}: CreateKeychainOptions): Promise<CodeSigningInfo> {\n  // travis has correct AppleWWDRCA cert\n  if (process.env.TRAVIS !== \"true\") {\n    await bundledCertKeychainAdded.value\n  }\n\n  // https://github.com/electron-userland/electron-builder/issues/3685\n  // use constant file\n  const keychainFile = path.join(process.env.APP_BUILDER_TMP_DIR || tmpdir(), `${createHash(\"sha256\").update(currentDir).update(\"app-builder\").digest(\"hex\")}.keychain`)\n  // noinspection JSUnusedLocalSymbols\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  await removeKeychain(keychainFile, false).catch(_ => {/* ignore*/})\n\n  const certLinks = [cscLink]\n  if (cscILink != null) {\n    certLinks.push(cscILink)\n  }\n\n  const certPaths = new Array(certLinks.length)\n  const keychainPassword = randomBytes(32).toString(\"base64\")\n  const securityCommands = [\n    [\"create-keychain\", \"-p\", keychainPassword, keychainFile],\n    [\"unlock-keychain\", \"-p\", keychainPassword, keychainFile],\n    [\"set-keychain-settings\", keychainFile]\n  ]\n\n  // https://stackoverflow.com/questions/42484678/codesign-keychain-gets-ignored\n  // https://github.com/electron-userland/electron-builder/issues/1457\n  const list = await listUserKeychains()\n  if (!list.includes(keychainFile)) {\n    securityCommands.push([\"list-keychains\", \"-d\", \"user\", \"-s\", keychainFile].concat(list))\n  }\n\n  await Promise.all([\n    // we do not clear downloaded files - will be removed on tmpDir cleanup automatically. not a security issue since in any case data is available as env variables and protected by password.\n    BluebirdPromise.map(certLinks, (link, i) => downloadCertificate(link, tmpDir, currentDir).then(it => certPaths[i] = it)),\n    BluebirdPromise.mapSeries(securityCommands, it => exec(\"security\", it))\n  ])\n  return await importCerts(keychainFile, certPaths, [cscKeyPassword, cscIKeyPassword].filter(it => it != null) as Array<string>)\n}\n\nasync function importCerts(keychainFile: string, paths: Array<string>, keyPasswords: Array<string>): Promise<CodeSigningInfo> {\n  for (let i = 0; i < paths.length; i++) {\n    const password = keyPasswords[i]\n    await exec(\"security\", [\"import\", paths[i], \"-k\", keychainFile, \"-T\", \"/usr/bin/codesign\", \"-T\", \"/usr/bin/productbuild\", \"-P\", password])\n\n    // https://stackoverflow.com/questions/39868578/security-codesign-in-sierra-keychain-ignores-access-control-settings-and-ui-p\n    // https://github.com/electron-userland/electron-packager/issues/701#issuecomment-322315996\n    await exec(\"security\", [\"set-key-partition-list\", \"-S\", \"apple-tool:,apple:\", \"-s\", \"-k\", password, keychainFile])\n  }\n\n  return {\n    keychainFile,\n  }\n}\n\n/** @private */\nexport function sign(path: string, name: string, keychain: string): Promise<any> {\n  const args = [\"--deep\", \"--force\", \"--sign\", name, path]\n  if (keychain != null) {\n    args.push(\"--keychain\", keychain)\n  }\n  return exec(\"codesign\", args)\n}\n\nexport let findIdentityRawResult: Promise<Array<string>> | null = null\n\nasync function getValidIdentities(keychain?: string | null): Promise<Array<string>> {\n  function addKeychain(args: Array<string>) {\n    if (keychain != null) {\n      args.push(keychain)\n    }\n    return args\n  }\n\n  let result = findIdentityRawResult\n  if (result == null || keychain != null) {\n    // https://github.com/electron-userland/electron-builder/issues/481\n    // https://github.com/electron-userland/electron-builder/issues/535\n    result = Promise.all<Array<string>>([\n      exec(\"security\", addKeychain([\"find-identity\", \"-v\"]))\n        .then(it => it.trim().split(\"\\n\").filter(it => {\n          for (const prefix of appleCertificatePrefixes) {\n            if (it.includes(prefix)) {\n              return true\n            }\n          }\n          return false\n        })),\n      exec(\"security\", addKeychain([\"find-identity\", \"-v\", \"-p\", \"codesigning\"]))\n        .then(it => it.trim().split((\"\\n\"))),\n    ])\n      .then(it => {\n        const array = it[0].concat(it[1])\n          .filter(it => !it.includes(\"(Missing required extension)\") && !it.includes(\"valid identities found\") && !it.includes(\"iPhone \") && !it.includes(\"com.apple.idms.appleid.prd.\"))\n          // remove 1)\n          .map(it => it.substring(it.indexOf(\")\") + 1).trim())\n        return Array.from(new Set(array))\n      })\n\n    if (keychain == null) {\n      findIdentityRawResult = result\n    }\n  }\n  return result\n}\n\nasync function _findIdentity(type: CertType, qualifier?: string | null, keychain?: string | null): Promise<Identity | null> {\n  // https://github.com/electron-userland/electron-builder/issues/484\n  //noinspection SpellCheckingInspection\n  const lines = await getValidIdentities(keychain)\n  const namePrefix = `${type}:`\n  for (const line of lines) {\n    if (qualifier != null && !line.includes(qualifier)) {\n      continue\n    }\n\n    if (line.includes(namePrefix)) {\n      return parseIdentity(line)\n    }\n  }\n\n  if (type === \"Developer ID Application\") {\n    // find non-Apple certificate\n    // https://github.com/electron-userland/electron-builder/issues/458\n    l: for (const line of lines) {\n      if (qualifier != null && !line.includes(qualifier)) {\n        continue\n      }\n\n      if (line.includes(\"Mac Developer:\")) {\n        continue\n      }\n\n      for (const prefix of appleCertificatePrefixes) {\n        if (line.includes(prefix)) {\n          continue l\n        }\n      }\n\n      return parseIdentity(line)\n    }\n  }\n  return null\n}\n\nexport declare class Identity {\n  readonly name: string\n  readonly hash: string\n\n  constructor(name: string, hash: string)\n}\n\nconst _Identity = require(\"../../electron-osx-sign/util-identities\").Identity\n\nfunction parseIdentity(line: string): Identity {\n  const firstQuoteIndex = line.indexOf('\"')\n  const name = line.substring(firstQuoteIndex + 1, line.lastIndexOf('\"'))\n  const hash = line.substring(0, firstQuoteIndex - 1)\n  return new _Identity(name, hash)\n}\n\nexport function findIdentity(certType: CertType, qualifier?: string | null, keychain?: string | null): Promise<Identity | null> {\n  let identity = qualifier || process.env.CSC_NAME\n  if (isEmptyOrSpaces(identity)) {\n    if (isAutoDiscoveryCodeSignIdentity()) {\n      return _findIdentity(certType, null, keychain)\n    }\n    else {\n      return Promise.resolve(null)\n    }\n  }\n  else {\n    identity = identity!.trim()\n    for (const prefix of appleCertificatePrefixes) {\n      checkPrefix(identity, prefix)\n    }\n    return _findIdentity(certType, identity, keychain)\n  }\n}\n\nfunction checkPrefix(name: string, prefix: string) {\n  if (name.startsWith(prefix)) {\n    throw new InvalidConfigurationError(`Please remove prefix \"${prefix}\" from the specified name — appropriate certificate will be chosen automatically`)\n  }\n}"], "sourceRoot": ""}