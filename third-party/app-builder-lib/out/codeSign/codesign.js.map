{"version": 3, "sources": ["../../src/codeSign/codesign.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;AAEA;AACO,eAAe,mBAAf,CAAmC,WAAnC,EAAwD,MAAxD,EAAwE,UAAxE,EAA0F;AAC/F,EAAA,WAAW,GAAG,WAAW,CAAC,IAAZ,EAAd;AAEA,MAAI,IAAI,GAAkB,IAA1B;;AACA,MAAK,WAAW,CAAC,MAAZ,GAAqB,CAArB,IAA0B,WAAW,CAAC,CAAD,CAAX,KAAmB,GAA9C,IAAsD,WAAW,CAAC,UAAZ,CAAuB,GAAvB,CAAtD,IAAqF,WAAW,CAAC,UAAZ,CAAuB,GAAvB,CAAzF,EAAsH;AACpH,IAAA,IAAI,GAAG,WAAP;AACD,GAFD,MAGK,IAAI,WAAW,CAAC,UAAZ,CAAuB,SAAvB,CAAJ,EAAuC;AAC1C,IAAA,IAAI,GAAG,WAAW,CAAC,SAAZ,CAAsB,UAAU,MAAhC,CAAP;AACD,GAFI,MAGA,IAAI,WAAW,CAAC,UAAZ,CAAuB,IAAvB,CAAJ,EAAkC;AACrC,IAAA,IAAI,GAAG,IAAI,CAAC,IAAL,CAAU,oBAAV,EAAqB,WAAW,CAAC,SAAZ,CAAsB,KAAK,MAA3B,CAArB,CAAP;AACD,GAFI,MAGA;AACH,UAAM,KAAK,GAAG,WAAW,CAAC,UAAZ,CAAuB,UAAvB,CAAd;;AACA,QAAI,KAAK,IAAI,WAAW,CAAC,MAAZ,GAAqB,IAA9B,IAAsC,WAAW,CAAC,QAAZ,CAAqB,GAArB,CAA1C,EAAqE;AACnE,YAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,WAAP,CAAmB;AAAC,QAAA,MAAM,EAAE;AAAT,OAAnB,CAAvB;;AACA,UAAI,KAAJ,EAAW;AACT,cAAM,6BAAS,WAAT,EAAsB,QAAtB,CAAN;AACD,OAFD,MAGK;AACH,cAAM,2BAAW,QAAX,EAAqB,MAAM,CAAC,IAAP,CAAY,WAAZ,EAAyB,QAAzB,CAArB,CAAN;AACD;;AACD,aAAO,QAAP;AACD,KATD,MAUK;AACH,MAAA,IAAI,GAAG,WAAP;AACD;AACF;;AAED,EAAA,IAAI,GAAG,IAAI,CAAC,OAAL,CAAa,UAAb,EAAyB,IAAzB,CAAP;AACA,QAAM,IAAI,GAAG,MAAM,sBAAW,IAAX,CAAnB;;AACA,MAAI,IAAI,IAAI,IAAZ,EAAkB;AAChB,UAAM,KAAI,wCAAJ,EAA8B,GAAG,IAAI,gBAArC,CAAN;AACD,GAFD,MAGK,IAAI,CAAC,IAAI,CAAC,MAAL,EAAL,EAAoB;AACvB,UAAM,KAAI,wCAAJ,EAA8B,GAAG,IAAI,aAArC,CAAN;AACD,GAFI,MAGA;AACH,WAAO,IAAP;AACD;AACF,C", "sourcesContent": ["import { outputFile } from \"fs-extra\"\nimport { homedir } from \"os\"\nimport * as path from \"path\"\nimport { TmpDir } from \"temp-file\"\nimport { InvalidConfigurationError } from \"builder-util\"\nimport { statOrNull } from \"builder-util/out/fs\"\nimport { download } from \"../binDownload\"\n\n/** @private */\nexport async function downloadCertificate(urlOrBase64: string, tmpDir: TmpDir, currentDir: string): Promise<string> {\n  urlOrBase64 = urlOrBase64.trim()\n\n  let file: string | null = null\n  if ((urlOrBase64.length > 3 && urlOrBase64[1] === \":\") || urlOrBase64.startsWith(\"/\") || urlOrBase64.startsWith(\".\")) {\n    file = urlOrBase64\n  }\n  else if (urlOrBase64.startsWith(\"file://\")) {\n    file = urlOrBase64.substring(\"file://\".length)\n  }\n  else if (urlOrBase64.startsWith(\"~/\")) {\n    file = path.join(homedir(), urlOrBase64.substring(\"~/\".length))\n  }\n  else {\n    const isUrl = urlOrBase64.startsWith(\"https://\")\n    if (isUrl || urlOrBase64.length > 2048 || urlOrBase64.endsWith(\"=\")) {\n      const tempFile = await tmpDir.getTempFile({suffix: \".p12\"})\n      if (isUrl) {\n        await download(urlOrBase64, tempFile)\n      }\n      else {\n        await outputFile(tempFile, Buffer.from(urlOrBase64, \"base64\"))\n      }\n      return tempFile\n    }\n    else {\n      file = urlOrBase64\n    }\n  }\n\n  file = path.resolve(currentDir, file)\n  const stat = await statOrNull(file)\n  if (stat == null) {\n    throw new InvalidConfigurationError(`${file} doesn't exist`)\n  }\n  else if (!stat.isFile()) {\n    throw new InvalidConfigurationError(`${file} not a file`)\n  }\n  else {\n    return file\n  }\n}"], "sourceRoot": ""}