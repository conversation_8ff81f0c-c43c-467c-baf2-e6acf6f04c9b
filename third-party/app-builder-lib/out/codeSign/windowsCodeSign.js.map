{"version": 3, "sources": ["../../src/codeSign/windowsCodeSign.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;AAGM,SAAU,iBAAV,GAA2B;AAC/B,SAAO,2BAAO,aAAP,CAAP;AACD;;AA0BM,eAAe,IAAf,CAAoB,OAApB,EAAiD,QAAjD,EAAsE;AAC3E,MAAI,MAAM,GAAG,OAAO,CAAC,OAAR,CAAgB,qBAA7B,CAD2E,CAE3E;;AACA,MAAI,OAAO,CAAC,IAAR,CAAa,QAAb,CAAsB,MAAtB,CAAJ,EAAmC;AACjC,IAAA,MAAM,GAAG,CAAC,MAAM,IAAI,IAAV,IAAkB,CAAC,MAAM,CAAC,QAAP,CAAgB,MAAhB,CAAnB,GAA6C,QAA7C,GAAwD,MAAzD,CAAT;AACD,GAFD,MAGK,IAAI,OAAO,CAAC,IAAR,CAAa,QAAb,CAAsB,OAAtB,CAAJ,EAAoC;AACvC,IAAA,MAAM,GAAG,CAAC,QAAD,CAAT;AACD,GAFI,MAGA,IAAI,MAAM,IAAI,IAAd,EAAoB;AACvB,IAAA,MAAM,GAAG,CAAC,MAAD,EAAS,QAAT,CAAT;AACD,GAFI,MAGA;AACH,IAAA,MAAM,GAAG,KAAK,CAAC,OAAN,CAAc,MAAd,IAAwB,MAAxB,GAAiC,CAAC,MAAD,CAA1C;AACD;;AAED,QAAM,QAAQ,GAAG,yCAAgB,OAAO,CAAC,OAAR,CAAgB,IAAhC,EAAsC,MAAtC,KAAiD,MAAlE;AACA,MAAI,MAAM,GAAG,KAAb;;AACA,OAAK,MAAM,IAAX,IAAmB,MAAnB,EAA2B;AACzB,UAAM,iBAAiB,GAAiC,EAAC,GAAG,OAAJ;AAAa,MAAA,IAAb;AAAmB,MAAA;AAAnB,KAAxD;AACA,UAAM,QAAQ,CAAC,EACb,GAAG,iBADU;AAEb,MAAA,mBAAmB,EAAE,KAAK,IAAI,mBAAmB,CAAC,iBAAD,EAAoB,KAApB;AAFpC,KAAD,EAGX,QAHW,CAAd;AAIA,IAAA,MAAM,GAAG,IAAT;;AACA,QAAI,iBAAiB,CAAC,gBAAlB,IAAsC,IAA1C,EAAgD;AAC9C,YAAM,uBAAO,iBAAiB,CAAC,gBAAzB,EAA2C,OAAO,CAAC,IAAnD,CAAN;AACD;AACF;AACF;;AAOM,eAAe,WAAf,CAA2B,IAA3B,EAAyC,QAAzC,EAAyD;AAC9D,MAAI,MAAM,GAAQ,IAAlB;AACA,QAAM,kBAAkB,GAAG,4GAA3B;;AACA,MAAI;AACF,IAAA,MAAM,GAAG,MAAM,2CAA6B,CAAC,kBAAD,EAAqB,SAArB,EAAgC,IAAhC,EAAsC,YAAtC,EAAoD,QAApD,CAA7B,CAAf;AACD,GAFD,CAGA,OAAO,CAAP,EAAU;AACR,UAAM,IAAI,KAAJ,CAAU,GAAG,kBAAkB,GAAG,CAAC,CAAC,KAAF,IAAW,CAAC,EAA9C,CAAN;AACD;;AAED,MAAI,MAAM,CAAC,KAAP,IAAgB,IAApB,EAA0B;AACxB;AACA,UAAM,KAAI,iCAAJ,EAA8B,GAAG,kBAAkB,GAAG,MAAM,CAAC,KAAK,EAAlE,CAAN;AACD;;AACD,SAAO,MAAP;AACD;;AAcM,eAAe,2BAAf,CAA2C,OAA3C,EAA0E,EAA1E,EAAuF;AAC5F,QAAM,sBAAsB,GAAG,OAAO,CAAC,sBAAvC;AACA,QAAM,eAAe,GAAG,OAAO,CAAC,eAAR,GAA0B,OAAO,CAAC,eAAR,CAAwB,WAAxB,EAA1B,GAAkE,OAAO,CAAC,eAAlG,CAF4F,CAG5F;AACA;;AACA,QAAM,SAAS,GAAG,MAAM,EAAE,CAAC,IAAH,CAAQ,gBAAR,EAA0B,CAAC,oIAAD,CAA1B,CAAxB;AACA,QAAM,QAAQ,GAAG,SAAS,CAAC,MAAV,KAAqB,CAArB,GAAyB,EAAzB,GAA8B,qBAAkB,IAAI,CAAC,KAAL,CAAW,SAAX,CAAlB,CAA/C;;AACA,OAAK,MAAM,QAAX,IAAuB,QAAvB,EAAiC;AAC/B,QAAK,sBAAsB,IAAI,IAA1B,IAAkC,CAAC,QAAQ,CAAC,OAAT,CAAiB,QAAjB,CAA0B,sBAA1B,CAApC,IACG,eAAe,IAAI,IAAnB,IAA2B,QAAQ,CAAC,UAAT,CAAoB,WAApB,OAAsC,eADxE,EACyF;AACvF;AACD;;AAED,UAAM,UAAU,GAAG,QAAQ,CAAC,YAA5B;AACA,UAAM,KAAK,GAAG,UAAU,CAAC,SAAX,CAAqB,UAAU,CAAC,WAAX,CAAuB,IAAvB,IAA+B,CAApD,CAAd;;AACA,gBAAI,KAAJ,CAAU;AAAC,MAAA,KAAD;AAAQ,MAAA,YAAY,EAAE;AAAtB,KAAV,EAA6C,+BAA7C,EAR+B,CAS/B;;;AACA,UAAM,mBAAmB,GAAI,UAAU,CAAC,QAAX,CAAoB,2BAApB,CAA7B;;AACA,gBAAI,KAAJ,CAAU,IAAV,EAAgB,yCAAhB;;AACA,WAAO;AACL,MAAA,UAAU,EAAE,QAAQ,CAAC,UADhB;AAEL,MAAA,OAAO,EAAE,QAAQ,CAAC,OAFb;AAGL,MAAA,KAHK;AAIL,MAAA;AAJK,KAAP;AAMD;;AAED,QAAM,IAAI,KAAJ,CAAU,2BAA2B,sBAAsB,IAAI,eAAe,gBAAgB,SAAS,EAAvG,CAAN;AACD;;AAEM,eAAe,MAAf,CAAsB,aAAtB,EAAyE,QAAzE,EAA8F;AACnG;AACA,QAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAR,CAAY,gBAAb,EAAsC,EAAtC,CAAR,IAAqD,KAAK,EAAL,GAAU,IAA/E;AAEA,MAAI,IAAJ;AACA,MAAI,IAAJ;AACA,MAAI,GAAG,GAAG,OAAO,CAAC,GAAlB;AACA,MAAI,EAAJ;;AACA,MAAI,aAAa,CAAC,IAAd,CAAmB,QAAnB,CAA4B,OAA5B,KAAwC,EAAE,UAAU,aAAa,CAAC,OAA1B;AAAqC;AAAjF,IAAsI;AACpI,MAAA,EAAE,GAAG,MAAM,QAAQ,CAAC,EAAT,CAAY,KAAvB;AACA,MAAA,IAAI,GAAG,cAAc,CAAC,MAAM,iBAAiB,EAAxB,CAArB;AACA,MAAA,IAAI,GAAG,mBAAmB,CAAC,aAAD,EAAgB,IAAhB,EAAsB,EAAtB,CAA1B;AACD,KAJD,MAKK;AACH,IAAA,EAAE,GAAG,KAAI,eAAJ,GAAL;AACA,UAAM,QAAQ,GAAG,MAAM,WAAW,EAAlC;AACA,IAAA,IAAI,GAAG,QAAQ,CAAC,IAAhB;AACA,IAAA,IAAI,GAAG,aAAa,CAAC,mBAAd,CAAkC,OAAO,CAAC,QAAR,KAAqB,OAAvD,CAAP;;AACA,QAAI,QAAQ,CAAC,GAAT,IAAgB,IAApB,EAA0B;AACxB,MAAA,GAAG,GAAG,QAAQ,CAAC,GAAf;AACD;AACF;;AAED,MAAI;AACF,UAAM,EAAE,CAAC,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB;AAAC,MAAA,OAAD;AAAU,MAAA;AAAV,KAApB,CAAN;AACD,GAFD,CAGA,OAAO,CAAP,EAAU;AACR,QAAI,CAAC,CAAC,OAAF,CAAU,QAAV,CAAmB,2CAAnB,KAAmE,CAAC,CAAC,OAAF,CAAU,QAAV,CAAmB,4DAAnB,CAAvE,EAAyJ;AACvJ,kBAAI,IAAJ,CAAS,kFAAkF,CAAC,CAAC,OAAO,EAApG;;AACA,YAAM,IAAI,OAAJ,CAAY,CAAC,OAAD,EAAU,MAAV,KAAoB;AACpC,QAAA,UAAU,CAAC,MAAK;AACd,UAAA,EAAE,CAAC,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB;AAAC,YAAA,OAAD;AAAU,YAAA;AAAV,WAApB,EACG,IADH,CACQ,OADR,EAEG,KAFH,CAES,MAFT;AAGD,SAJS,EAIP,KAJO,CAAV;AAKD,OANK,CAAN;AAOD;;AACD,UAAM,CAAN;AACD;AACF,C,CAQD;;;AACA,SAAS,mBAAT,CAA6B,OAA7B,EAAoE,KAApE,EAAoF,EAAA,GAAgB,KAAI,eAAJ,GAApG,EAAmH;AACjH,QAAM,SAAS,GAAG,EAAE,CAAC,QAAH,CAAY,OAAO,CAAC,IAApB,CAAlB;AACA,QAAM,UAAU,GAAG,KAAK,GAAG,SAAH,GAAe,aAAa,CAAC,SAAD,EAAY,OAAO,CAAC,IAApB,CAApD;;AACA,MAAI,CAAC,KAAL,EAAY;AACV,IAAA,OAAO,CAAC,gBAAR,GAA2B,UAA3B;AACD;;AAED,QAAM,IAAI,GAAG,KAAK,GAAG,CAAC,MAAD,CAAH,GAAc,CAAC,KAAD,EAAQ,SAAR,EAAmB,MAAnB,EAA2B,UAA3B,CAAhC;;AAEA,MAAI,OAAO,CAAC,GAAR,CAAY,wBAAZ,KAAyC,MAA7C,EAAqD;AACnD,UAAM,sBAAsB,GAAG,OAAO,CAAC,OAAR,CAAgB,eAAhB,IAAmC,+BAAlE;;AACA,QAAI,KAAJ,EAAW;AACT,MAAA,IAAI,CAAC,IAAL,CAAU,OAAO,CAAC,MAAR,IAAkB,OAAO,CAAC,IAAR,KAAiB,QAAnC,GAA8C,KAA9C,GAAsD,IAAhE,EAAsE,OAAO,CAAC,MAAR,IAAkB,OAAO,CAAC,IAAR,KAAiB,QAAnC,GAA+C,OAAO,CAAC,OAAR,CAAgB,sBAAhB,IAA0C,+BAAzF,GAA4H,sBAAlM;AACD,KAFD,MAGK;AACH,MAAA,IAAI,CAAC,IAAL,CAAU,IAAV,EAAgB,sBAAhB;AACD;AACF;;AAED,QAAM,eAAe,GAAI,OAAO,CAAC,OAAR,CAAwC,IAAjE;;AACA,MAAI,eAAe,IAAI,IAAvB,EAA6B;AAC3B,UAAM,OAAO,GAAI,OAAO,CAAC,OAAzB;AACA,UAAM,WAAW,GAAG,OAAO,CAAC,UAA5B;;AACA,QAAI,CAAC,KAAL,EAAY;AACV,YAAM,IAAI,KAAJ,CAAU,GAAG,WAAW,IAAI,IAAf,GAAsB,iBAAtB,GAA0C,wBAAwB,4BAA/E,CAAN;AACD;;AAED,IAAA,IAAI,CAAC,IAAL,CAAU,OAAV,EAAmB,OAAO,CAAC,UAA3B;AACA,IAAA,IAAI,CAAC,IAAL,CAAU,IAAV,EAAgB,OAAO,CAAC,KAAxB;;AACA,QAAI,OAAO,CAAC,mBAAZ,EAAiC;AAC/B,MAAA,IAAI,CAAC,IAAL,CAAU,KAAV;AACD;AACF,GAZD,MAaK;AACH,UAAM,aAAa,GAAG,IAAI,CAAC,OAAL,CAAa,eAAb,CAAtB;;AACA,QAAI,aAAa,KAAK,MAAlB,IAA4B,aAAa,KAAK,MAAlD,EAA0D;AACxD,MAAA,IAAI,CAAC,IAAL,CAAU,KAAK,GAAG,IAAH,GAAU,SAAzB,EAAoC,EAAE,CAAC,QAAH,CAAY,eAAZ,CAApC;AACD,KAFD,MAGK;AACH,YAAM,IAAI,KAAJ,CAAU,2CAA2C,eAAe,iBAApE,CAAN;AACD;AACF;;AAED,MAAI,CAAC,KAAD,IAAU,OAAO,CAAC,IAAR,KAAiB,MAA/B,EAAuC;AACrC,IAAA,IAAI,CAAC,IAAL,CAAU,KAAK,GAAG,KAAH,GAAW,IAA1B,EAAgC,OAAO,CAAC,IAAxC;;AACA,QAAI,KAAK,IAAI,OAAO,CAAC,GAAR,CAAY,wBAAZ,KAAyC,MAAtD,EAA8D;AAC5D,MAAA,IAAI,CAAC,IAAL,CAAU,KAAV,EAAiB,QAAjB;AACD;AACF;;AAED,MAAI,OAAO,CAAC,IAAZ,EAAkB;AAChB,IAAA,IAAI,CAAC,IAAL,CAAU,KAAK,GAAG,IAAH,GAAU,IAAzB,EAA+B,OAAO,CAAC,IAAvC;AACD;;AAED,MAAI,OAAO,CAAC,IAAZ,EAAkB;AAChB,IAAA,IAAI,CAAC,IAAL,CAAU,KAAK,GAAG,KAAH,GAAW,IAA1B,EAAgC,OAAO,CAAC,IAAxC;AACD,GAxDgH,CA0DjH;;;AACA,MAAI,OAAO,CAAC,MAAZ,EAAoB;AAClB,IAAA,IAAI,CAAC,IAAL,CAAU,KAAK,GAAG,KAAH,GAAW,OAA1B;AACD;;AAED,QAAM,QAAQ,GAAG,OAAO,CAAC,OAAR,IAAmB,IAAnB,GAA0B,IAA1B,GAAkC,OAAO,CAAC,OAAR,CAAwC,QAA3F;;AACA,MAAI,QAAJ,EAAc;AACZ,IAAA,IAAI,CAAC,IAAL,CAAU,KAAK,GAAG,IAAH,GAAU,OAAzB,EAAkC,QAAlC;AACD;;AAED,MAAI,OAAO,CAAC,OAAR,CAAgB,yBAApB,EAA+C;AAC7C,IAAA,IAAI,CAAC,IAAL,CAAU,KAAK,GAAG,KAAH,GAAW,KAA1B,EAAiC,EAAE,CAAC,QAAH,CAAY,OAAO,CAAC,OAAR,CAAgB,yBAA5B,CAAjC;AACD;;AAED,QAAM,iBAAiB,GAAG,OAAO,CAAC,GAAR,CAAY,WAAtC;;AACA,MAAI,CAAC,KAAD,IAAU,iBAAiB,IAAI,IAA/B,IAAuC,iBAAiB,CAAC,MAA7D,EAAqE;AACnE,IAAA,IAAI,CAAC,IAAL,CAAU,IAAV,EAAgB,iBAAhB;AACD;;AAED,MAAI,KAAJ,EAAW;AACT;AACA,IAAA,IAAI,CAAC,IAAL,CAAU,QAAV,EAFS,CAGT;;AACA,IAAA,IAAI,CAAC,IAAL,CAAU,SAAV;AACD;;AAED,SAAO,IAAP;AACD;;AAED,SAAS,aAAT,CAAuB,SAAvB,EAA0C,IAA1C,EAAsD;AACpD,QAAM,SAAS,GAAG,IAAI,CAAC,OAAL,CAAa,SAAb,CAAlB;AACA,SAAO,IAAI,CAAC,IAAL,CAAU,IAAI,CAAC,OAAL,CAAa,SAAb,CAAV,EAAmC,GAAG,IAAI,CAAC,QAAL,CAAc,SAAd,EAAyB,SAAzB,CAAmC,WAAW,IAAI,GAAG,SAAS,EAApG,CAAP;AACD;AAED;;;AACM,SAAU,SAAV,GAAmB;AACvB,QAAM,UAAU,GAAG,EAAE,GAAC,OAAH,EAAnB;AACA,SAAO,UAAU,CAAC,UAAX,CAAsB,IAAtB,KAA+B,CAAC,UAAU,CAAC,UAAX,CAAsB,KAAtB,CAAvC;AACD;;AAED,SAAS,cAAT,CAAwB,UAAxB,EAA0C;AACxC;AACA,MAAI,SAAS,EAAb,EAAiB;AACf,WAAO,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,WAAtB,EAAmC,cAAnC,CAAP;AACD,GAFD,MAGK;AACH,WAAO,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,YAAtB,EAAoC,OAAO,CAAC,IAA5C,EAAkD,cAAlD,CAAP;AACD;AACF;;AAED,eAAe,WAAf,GAA0B;AACxB,MAAI,mCAAJ,EAA2B;AACzB,WAAO;AAAC,MAAA,IAAI,EAAE;AAAP,KAAP;AACD;;AAED,QAAM,MAAM,GAAG,OAAO,CAAC,GAAR,CAAY,aAA3B;;AACA,MAAI,MAAJ,EAAY;AACV,WAAO;AAAC,MAAA,IAAI,EAAE;AAAP,KAAP;AACD;;AAED,QAAM,UAAU,GAAG,MAAM,iBAAiB,EAA1C;;AACA,MAAI,OAAO,CAAC,QAAR,KAAqB,OAAzB,EAAkC;AAChC;AACA,WAAO;AAAC,MAAA,IAAI,EAAE,cAAc,CAAC,UAAD;AAArB,KAAP;AACD,GAHD,MAIK,IAAI,OAAO,CAAC,QAAR,KAAqB,QAAzB,EAAmC;AACtC,UAAM,WAAW,GAAG,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,OAAO,CAAC,QAA9B,EAAwC,OAAxC,CAApB;AACA,WAAO;AACL,MAAA,IAAI,EAAE,IAAI,CAAC,IAAL,CAAU,WAAV,EAAuB,cAAvB,CADD;AAEL,MAAA,GAAG,EAAE,mCAAe,CAAC,IAAI,CAAC,IAAL,CAAU,WAAV,EAAuB,KAAvB,CAAD,CAAf;AAFA,KAAP;AAID,GANI,MAOA;AACH,WAAO;AAAC,MAAA,IAAI,EAAE,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,OAAO,CAAC,QAA9B,EAAwC,cAAxC;AAAP,KAAP;AACD;AACF,C", "sourcesContent": ["import { InvalidConfigurationError, asArray, log } from \"builder-util/out/util\"\nimport { getBin } from \"../binDownload\"\nimport { executeAppBuilderAsJson } from \"../util/appBuilder\"\nimport { computeToolEnv, ToolInfo } from \"../util/bundledTool\"\nimport { rename } from \"fs-extra\"\nimport * as os from \"os\"\nimport * as path from \"path\"\nimport { WindowsConfiguration } from \"..\"\nimport { resolveFunction } from \"../platformPackager\"\nimport { isUseSystemSigncode } from \"../util/flags\"\nimport { VmManager } from \"../vm/vm\"\nimport { WinPackager } from \"../winPackager\"\n\nexport function getSignVendorPath() {\n  return getBin(\"winCodeSign\")\n}\n\nexport type CustomWindowsSign = (configuration: CustomWindowsSignTaskConfiguration, packager?: WinPackager) => Promise<any>\n\nexport interface WindowsSignOptions {\n  readonly path: string\n\n  readonly name?: string | null\n  readonly cscInfo?: FileCodeSigningInfo | CertificateFromStoreInfo | null\n  readonly site?: string | null\n\n  readonly options: WindowsConfiguration\n}\n\nexport interface WindowsSignTaskConfiguration extends WindowsSignOptions {\n  // set if output path differs from input (e.g. osslsigncode cannot sign file in-place)\n  resultOutputPath?: string\n\n  hash: string\n  isNest: boolean\n}\n\nexport interface CustomWindowsSignTaskConfiguration extends WindowsSignTaskConfiguration {\n  computeSignToolArgs(isWin: boolean): Array<string>\n}\n\nexport async function sign(options: WindowsSignOptions, packager: WinPackager) {\n  let hashes = options.options.signingHashAlgorithms\n  // msi does not support dual-signing\n  if (options.path.endsWith(\".msi\")) {\n    hashes = [hashes != null && !hashes.includes(\"sha1\") ? \"sha256\" : \"sha1\"]\n  }\n  else if (options.path.endsWith(\".appx\")) {\n    hashes = [\"sha256\"]\n  }\n  else if (hashes == null) {\n    hashes = [\"sha1\", \"sha256\"]\n  }\n  else {\n    hashes = Array.isArray(hashes) ? hashes : [hashes]\n  }\n\n  const executor = resolveFunction(options.options.sign, \"sign\") || doSign\n  let isNest = false\n  for (const hash of hashes) {\n    const taskConfiguration: WindowsSignTaskConfiguration = {...options, hash, isNest}\n    await executor({\n      ...taskConfiguration,\n      computeSignToolArgs: isWin => computeSignToolArgs(taskConfiguration, isWin)\n    }, packager)\n    isNest = true\n    if (taskConfiguration.resultOutputPath != null) {\n      await rename(taskConfiguration.resultOutputPath, options.path)\n    }\n  }\n}\n\nexport interface FileCodeSigningInfo {\n  readonly file: string\n  readonly password: string | null\n}\n\nexport async function getCertInfo(file: string, password: string): Promise<CertificateInfo> {\n  let result: any = null\n  const errorMessagePrefix = \"Cannot extract publisher name from code signing certificate. As workaround, set win.publisherName. Error: \"\n  try {\n    result = await executeAppBuilderAsJson<any>([\"certificate-info\", \"--input\", file, \"--password\", password])\n  }\n  catch (e) {\n    throw new Error(`${errorMessagePrefix}${e.stack || e}`)\n  }\n\n  if (result.error != null) {\n    // noinspection ExceptionCaughtLocallyJS\n    throw new InvalidConfigurationError(`${errorMessagePrefix}${result.error}`)\n  }\n  return result\n}\n\nexport interface CertificateInfo {\n  readonly commonName: string\n  readonly bloodyMicrosoftSubjectDn: string\n}\n\nexport interface CertificateFromStoreInfo {\n  thumbprint: string\n  subject: string\n  store: string\n  isLocalMachineStore: boolean\n}\n\nexport async function getCertificateFromStoreInfo(options: WindowsConfiguration, vm: VmManager): Promise<CertificateFromStoreInfo> {\n  const certificateSubjectName = options.certificateSubjectName\n  const certificateSha1 = options.certificateSha1 ? options.certificateSha1.toUpperCase() : options.certificateSha1\n  // ExcludeProperty doesn't work, so, we cannot exclude RawData, it is ok\n  // powershell can return object if the only item\n  const rawResult = await vm.exec(\"powershell.exe\", [\"Get-ChildItem -Recurse Cert: -CodeSigningCert | Select-Object -Property Subject,PSParentPath,Thumbprint | ConvertTo-Json -Compress\"])\n  const certList = rawResult.length === 0 ? [] : asArray<CertInfo>(JSON.parse(rawResult))\n  for (const certInfo of certList) {\n    if ((certificateSubjectName != null && !certInfo.Subject.includes(certificateSubjectName))\n        || certificateSha1 != null && certInfo.Thumbprint.toUpperCase() !== certificateSha1) {\n      continue\n    }\n\n    const parentPath = certInfo.PSParentPath\n    const store = parentPath.substring(parentPath.lastIndexOf(\"\\\\\") + 1)\n    log.debug({store, PSParentPath: parentPath}, \"auto-detect certificate store\")\n    // https://github.com/electron-userland/electron-builder/issues/1717\n    const isLocalMachineStore = (parentPath.includes(\"Certificate::LocalMachine\"))\n    log.debug(null, \"auto-detect using of LocalMachine store\")\n    return {\n      thumbprint: certInfo.Thumbprint,\n      subject: certInfo.Subject,\n      store,\n      isLocalMachineStore\n    }\n  }\n\n  throw new Error(`Cannot find certificate ${certificateSubjectName || certificateSha1}, all certs: ${rawResult}`)\n}\n\nexport async function doSign(configuration: CustomWindowsSignTaskConfiguration, packager: WinPackager) {\n  // https://github.com/electron-userland/electron-builder/pull/1944\n  const timeout = parseInt(process.env.SIGNTOOL_TIMEOUT as any, 10) || 10 * 60 * 1000\n\n  let tool: string\n  let args: Array<string>\n  let env = process.env\n  let vm: VmManager\n  if (configuration.path.endsWith(\".appx\") || !(\"file\" in configuration.cscInfo!!) /* certificateSubjectName and other such options */) {\n    vm = await packager.vm.value\n    tool = getWinSignTool(await getSignVendorPath())\n    args = computeSignToolArgs(configuration, true, vm)\n  }\n  else {\n    vm = new VmManager()\n    const toolInfo = await getToolPath()\n    tool = toolInfo.path\n    args = configuration.computeSignToolArgs(process.platform === \"win32\")\n    if (toolInfo.env != null) {\n      env = toolInfo.env\n    }\n  }\n\n  try {\n    await vm.exec(tool, args, {timeout, env})\n  }\n  catch (e) {\n    if (e.message.includes(\"The file is being used by another process\") || e.message.includes(\"The specified timestamp server either could not be reached\")) {\n      log.warn(`First attempt to code sign failed, another attempt will be made in 15 seconds: ${e.message}`)\n      await new Promise((resolve, reject) => {\n        setTimeout(() => {\n          vm.exec(tool, args, {timeout, env})\n            .then(resolve)\n            .catch(reject)\n        }, 15000)\n      })\n    }\n    throw e\n  }\n}\n\ninterface CertInfo {\n  Subject: string\n  Thumbprint: string\n  PSParentPath: string\n}\n\n// on windows be aware of http://stackoverflow.com/a/32640183/1910191\nfunction computeSignToolArgs(options: WindowsSignTaskConfiguration, isWin: boolean, vm: VmManager = new VmManager()): Array<string> {\n  const inputFile = vm.toVmFile(options.path)\n  const outputPath = isWin ? inputFile : getOutputPath(inputFile, options.hash)\n  if (!isWin) {\n    options.resultOutputPath = outputPath\n  }\n\n  const args = isWin ? [\"sign\"] : [\"-in\", inputFile, \"-out\", outputPath]\n\n  if (process.env.ELECTRON_BUILDER_OFFLINE !== \"true\") {\n    const timestampingServiceUrl = options.options.timeStampServer || \"http://timestamp.digicert.com\"\n    if (isWin) {\n      args.push(options.isNest || options.hash === \"sha256\" ? \"/tr\" : \"/t\", options.isNest || options.hash === \"sha256\" ? (options.options.rfc3161TimeStampServer || \"http://timestamp.digicert.com\") : timestampingServiceUrl)\n    }\n    else {\n      args.push(\"-t\", timestampingServiceUrl)\n    }\n  }\n\n  const certificateFile = (options.cscInfo as FileCodeSigningInfo).file\n  if (certificateFile == null) {\n    const cscInfo = (options.cscInfo as CertificateFromStoreInfo)\n    const subjectName = cscInfo.thumbprint\n    if (!isWin) {\n      throw new Error(`${subjectName == null ? \"certificateSha1\" : \"certificateSubjectName\"} supported only on Windows`)\n    }\n\n    args.push(\"/sha1\", cscInfo.thumbprint)\n    args.push(\"/s\", cscInfo.store)\n    if (cscInfo.isLocalMachineStore) {\n      args.push(\"/sm\")\n    }\n  }\n  else {\n    const certExtension = path.extname(certificateFile)\n    if (certExtension === \".p12\" || certExtension === \".pfx\") {\n      args.push(isWin ? \"/f\" : \"-pkcs12\", vm.toVmFile(certificateFile))\n    }\n    else {\n      throw new Error(`Please specify pkcs12 (.p12/.pfx) file, ${certificateFile} is not correct`)\n    }\n  }\n\n  if (!isWin || options.hash !== \"sha1\") {\n    args.push(isWin ? \"/fd\" : \"-h\", options.hash)\n    if (isWin && process.env.ELECTRON_BUILDER_OFFLINE !== \"true\") {\n      args.push(\"/td\", \"sha256\")\n    }\n  }\n\n  if (options.name) {\n    args.push(isWin ? \"/d\" : \"-n\", options.name)\n  }\n\n  if (options.site) {\n    args.push(isWin ? \"/du\" : \"-i\", options.site)\n  }\n\n  // msi does not support dual-signing\n  if (options.isNest) {\n    args.push(isWin ? \"/as\" : \"-nest\")\n  }\n\n  const password = options.cscInfo == null ? null : (options.cscInfo as FileCodeSigningInfo).password\n  if (password) {\n    args.push(isWin ? \"/p\" : \"-pass\", password)\n  }\n\n  if (options.options.additionalCertificateFile) {\n    args.push(isWin ? \"/ac\" : \"-ac\", vm.toVmFile(options.options.additionalCertificateFile))\n  }\n\n  const httpsProxyFromEnv = process.env.HTTPS_PROXY\n  if (!isWin && httpsProxyFromEnv != null && httpsProxyFromEnv.length) {\n    args.push(\"-p\", httpsProxyFromEnv)\n  }\n\n  if (isWin) {\n    // https://github.com/electron-userland/electron-builder/issues/2875#issuecomment-387233610\n    args.push(\"/debug\")\n    // must be last argument\n    args.push(inputFile)\n  }\n\n  return args\n}\n\nfunction getOutputPath(inputPath: string, hash: string) {\n  const extension = path.extname(inputPath)\n  return path.join(path.dirname(inputPath), `${path.basename(inputPath, extension)}-signed-${hash}${extension}`)\n}\n\n/** @internal */\nexport function isOldWin6() {\n  const winVersion = os.release()\n  return winVersion.startsWith(\"6.\") && !winVersion.startsWith(\"6.3\")\n}\n\nfunction getWinSignTool(vendorPath: string): string {\n  // use modern signtool on Windows Server 2012 R2 to be able to sign AppX\n  if (isOldWin6()) {\n    return path.join(vendorPath, \"windows-6\", \"signtool.exe\")\n  }\n  else {\n    return path.join(vendorPath, \"windows-10\", process.arch, \"signtool.exe\")\n  }\n}\n\nasync function getToolPath(): Promise<ToolInfo> {\n  if (isUseSystemSigncode()) {\n    return {path: \"osslsigncode\"}\n  }\n\n  const result = process.env.SIGNTOOL_PATH\n  if (result) {\n    return {path: result}\n  }\n\n  const vendorPath = await getSignVendorPath()\n  if (process.platform === \"win32\") {\n    // use modern signtool on Windows Server 2012 R2 to be able to sign AppX\n    return {path: getWinSignTool(vendorPath)}\n  }\n  else if (process.platform === \"darwin\") {\n    const toolDirPath = path.join(vendorPath, process.platform, \"10.12\")\n    return {\n      path: path.join(toolDirPath, \"osslsigncode\"),\n      env: computeToolEnv([path.join(toolDirPath, \"lib\")]),\n    }\n  }\n  else {\n    return {path: path.join(vendorPath, process.platform, \"osslsigncode\")}\n  }\n}\n"], "sourceRoot": ""}