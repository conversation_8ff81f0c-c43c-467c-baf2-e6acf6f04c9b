{"version": 3, "sources": ["../../src/frameworks/LibUiFramework.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAIA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;AAEM,MAAO,cAAP,CAAqB;AAazB,EAAA,WAAA,CAAqB,OAArB,EAA+C,gBAA/C,EAA4F,aAA5F,EAAkH;AAA7F,SAAA,OAAA,GAAA,OAAA;AAA0B,SAAA,gBAAA,GAAA,gBAAA;AAA6C,SAAA,aAAA,GAAA,aAAA;AAZnF,SAAA,IAAA,GAAe,OAAf,CAYyG,CAXlH;;AACS,SAAA,mBAAA,GAAsB,CAAC,KAAD,CAAtB;AAEA,SAAA,kBAAA,GAA6B,YAA7B,CAQyG,CANlH;;AACS,SAAA,mBAAA,GAAsB,KAAtB,CAKyG,CAHlH;;AACS,SAAA,oBAAA,GAAuB,KAAvB;AAGR;;AAED,QAAM,gCAAN,CAAuC,OAAvC,EAAuF;AACrF,UAAM,yBAAS,OAAO,CAAC,SAAjB,CAAN;AAEA,UAAM,QAAQ,GAAG,OAAO,CAAC,QAAzB;AACA,UAAM,QAAQ,GAAG,QAAQ,CAAC,QAA1B;;AAEA,QAAI,KAAK,wBAAL,CAA8B,QAA9B,CAAJ,EAA6C;AAC3C,YAAM,SAAS,GAAG,OAAO,CAAC,SAA1B;AACA,YAAM,sCAAkB,CAAC,eAAD,EACtB,gBADsB,EACJ,KAAK,OADD,EAEtB,iBAFsB,EAGtB,YAHsB,EAGR,QAAQ,CAAC,QAHD,EAItB,QAJsB,EAIZ,OAAO,CAAC,IAJI,EAKtB,SALsB,EAKX,SALW,EAMtB,cANsB,EAMN,GAAG,QAAQ,CAAC,OAAT,CAAiB,eAAe,GAAG,QAAQ,KAAK,iBAAS,OAAtB,GAAgC,MAAhC,GAAyC,EAAE,EAN3E,CAAlB,CAAN;AAQA;AACD;;AAED,QAAI,QAAQ,KAAK,iBAAS,GAA1B,EAA+B;AAC7B,YAAM,KAAK,qCAAL,CAA2C,QAA3C,EAAoE,OAApE,CAAN;AACD,KAFD,MAGK,IAAI,QAAQ,KAAK,iBAAS,KAA1B,EAAiC;AACpC,YAAM,KAAK,qCAAL,CAA2C,OAA3C,CAAN;AACD;AACF;;AAEO,QAAM,qCAAN,CAA4C,QAA5C,EAAmE,OAAnE,EAAmH;AACzH,UAAM,cAAc,GAAG,IAAI,CAAC,IAAL,CAAU,OAAO,CAAC,SAAlB,EAA6B,KAAK,gBAAlC,EAAoD,UAApD,CAAvB;AACA,UAAM,0BAAU,IAAI,CAAC,IAAL,CAAU,cAAV,EAA0B,WAA1B,CAAV,CAAN;AACA,UAAM,0BAAU,IAAI,CAAC,IAAL,CAAU,cAAV,EAA0B,OAA1B,CAAV,CAAN;AACA,UAAM,sCAAkB,CAAC,eAAD,EAAkB,gBAAlB,EAAoC,KAAK,OAAzC,EAAkD,YAAlD,EAAgE,QAAhE,EAA0E,SAA1E,EAAqF,IAAI,CAAC,IAAL,CAAU,cAAV,EAA0B,OAA1B,CAArF,CAAlB,CAAN;AAEA,UAAM,QAAQ,GAAQ;AACpB;AACA,MAAA,uBAAuB,EAAE;AAFL,KAAtB;AAIA,UAAM,QAAQ,CAAC,eAAT,CAAyB,QAAzB,EAAmC,cAAnC,CAAN;AACA,UAAM,OAAO,CAAC,GAAR,CAAY,CAChB,iDAA8B,CAAC,cAAD,CAA9B,EAAgD;AAAC,OAAC,IAAI,CAAC,IAAL,CAAU,cAAV,EAA0B,YAA1B,CAAD,GAA2C;AAA5C,KAAhD,CADgB,EAEhB,mBAAmB,CAAC,IAAI,CAAC,IAAL,CAAU,cAAV,EAA0B,OAA1B,EAAmC,QAAQ,CAAC,kBAA5C,CAAD,EAAkE;;uCAEpD,OAAO,CAAC,QAAR,CAAiB,IAAjB,CAAsB,QAAtB,CAA+B,IAA/B,IAAuC,UAAU;GAF/D,CAFH,CAAZ,CAAN;AAOD;;AAEO,QAAM,qCAAN,CAA4C,OAA5C,EAA4F;AAClG,UAAM,SAAS,GAAG,OAAO,CAAC,SAA1B;AACA,UAAM,sCAAkB,CAAC,eAAD,EAAkB,gBAAlB,EAAoC,KAAK,OAAzC,EAAkD,YAAlD,EAAgE,OAAhE,EAAyE,QAAzE,EAAmF,OAAO,CAAC,IAA3F,EAAiG,SAAjG,EAA4G,SAA5G,CAAlB,CAAN;AACA,UAAM,QAAQ,GAAG,IAAI,CAAC,IAAL,CAAU,SAAV,EAAsB,OAAO,CAAC,QAAR,CAAmC,cAAzD,CAAjB;AACA,UAAM,mBAAmB,CAAC,QAAD,EAAW;;0BAEd,OAAO,CAAC,QAAR,CAAiB,IAAjB,CAAsB,QAAtB,CAA+B,IAA/B,IAAuC,UAAU;GAF9C,CAAzB;AAID;;AAED,QAAM,SAAN,CAAgB,OAAhB,EAAyC;AACvC,UAAM,QAAQ,GAAG,OAAO,CAAC,QAAzB;;AACA,QAAI,CAAC,KAAK,wBAAL,CAA8B,QAAQ,CAAC,QAAvC,CAAL,EAAuD;AACrD;AACD,KAJsC,CAMvC;;;AACA,UAAM,QAAQ,GAAG,QAAQ,CAAC,IAAT,CAAc,QAAd,CAAuB,IAAvB,IAA+B,UAAhD;;AACA,QAAI,QAAQ,KAAK,SAAjB,EAA4B;AAC1B;AACD;;AAED,UAAM,uBAAO,IAAI,CAAC,IAAL,CAAU,OAAO,CAAC,SAAlB,EAA6B,KAA7B,EAAoC,QAApC,CAAP,EAAsD,IAAI,CAAC,IAAL,CAAU,OAAO,CAAC,SAAlB,EAA6B,KAA7B,EAAoC,SAApC,CAAtD,CAAN;AACD;;AAED,EAAA,WAAW,CAAC,QAAD,EAAmB;AAC5B,WAAO,KAAK,wBAAL,CAA8B,QAA9B,IAA0C,SAA1C,GAAsD,IAA7D;AACD;;AAEO,EAAA,wBAAwB,CAAC,QAAD,EAAmB;AACjD,WAAO,QAAQ,KAAK,iBAAS,OAAtB,IAAkC,KAAK,aAAL,IAAsB,QAAQ,KAAK,iBAAS,KAArF;AACD;;AAED,EAAA,uBAAuB,CAAC,QAAD,EAAmB;AACxC;AACA,WAAO,KAAK,wBAAL,CAA8B,QAA9B,IAA0C,CAAC,YAAD,CAA1C,GAA2D,IAAlE;AACD;;AAnGwB;;;;AAsG3B,eAAe,mBAAf,CAAmC,IAAnC,EAAiD,OAAjD,EAAgE;AAC9D,QAAM,0BAAU,IAAV,EAAgB,OAAhB,EAAyB;AAAC,IAAA,IAAI,EAAE;AAAP,GAAzB,CAAN;AACA,QAAM,sBAAM,IAAN,EAAY,KAAZ,CAAN;AACD,C", "sourcesContent": ["import { chmod, emptyDir, ensureDir, rename, writeFile } from \"fs-extra\"\nimport * as path from \"path\"\nimport { executeAppBuilder } from \"builder-util\"\nimport { AfterPackContext } from \"../configuration\"\nimport { Platform } from \"../core\"\nimport { Framework, PrepareApplicationStageDirectoryOptions } from \"../Framework\"\nimport { LinuxPackager } from \"../linuxPackager\"\nimport MacPackager from \"../macPackager\"\nimport { executeAppBuilderAndWriteJson } from \"../util/appBuilder\"\n\nexport class LibUiFramework implements Framework {\n  readonly name: string = \"libui\"\n  // noinspection JSUnusedGlobalSymbols\n  readonly macOsDefaultTargets = [\"dmg\"]\n\n  readonly defaultAppIdPrefix: string = \"com.libui.\"\n\n  // noinspection JSUnusedGlobalSymbols\n  readonly isCopyElevateHelper = false\n\n  // noinspection JSUnusedGlobalSymbols\n  readonly isNpmRebuildRequired = false\n\n  constructor(readonly version: string, readonly distMacOsAppName: string, protected readonly isUseLaunchUi: boolean) {\n  }\n\n  async prepareApplicationStageDirectory(options: PrepareApplicationStageDirectoryOptions) {\n    await emptyDir(options.appOutDir)\n\n    const packager = options.packager\n    const platform = packager.platform\n\n    if (this.isUseLaunchUiForPlatform(platform)) {\n      const appOutDir = options.appOutDir\n      await executeAppBuilder([\"proton-native\",\n        \"--node-version\", this.version,\n        \"--use-launch-ui\",\n        \"--platform\", platform.nodeName,\n        \"--arch\", options.arch,\n        \"--stage\", appOutDir,\n        \"--executable\", `${packager.appInfo.productFilename}${platform === Platform.WINDOWS ? \".exe\" : \"\"}`,\n      ])\n      return\n    }\n\n    if (platform === Platform.MAC) {\n      await this.prepareMacosApplicationStageDirectory(packager as MacPackager, options)\n    }\n    else if (platform === Platform.LINUX) {\n      await this.prepareLinuxApplicationStageDirectory(options)\n    }\n  }\n\n  private async prepareMacosApplicationStageDirectory(packager: MacPackager, options: PrepareApplicationStageDirectoryOptions) {\n    const appContentsDir = path.join(options.appOutDir, this.distMacOsAppName, \"Contents\")\n    await ensureDir(path.join(appContentsDir, \"Resources\"))\n    await ensureDir(path.join(appContentsDir, \"MacOS\"))\n    await executeAppBuilder([\"proton-native\", \"--node-version\", this.version, \"--platform\", \"darwin\", \"--stage\", path.join(appContentsDir, \"MacOS\")])\n\n    const appPlist: any = {\n      // https://github.com/albe-rosado/create-proton-app/issues/13\n      NSHighResolutionCapable: true,\n    }\n    await packager.applyCommonInfo(appPlist, appContentsDir)\n    await Promise.all([\n      executeAppBuilderAndWriteJson([\"encode-plist\"], {[path.join(appContentsDir, \"Info.plist\")]: appPlist}),\n      writeExecutableMain(path.join(appContentsDir, \"MacOS\", appPlist.CFBundleExecutable), `#!/bin/sh\n  DIR=$(dirname \"$0\")\n  \"$DIR/node\" \"$DIR/../Resources/app/${options.packager.info.metadata.main || \"index.js\"}\"\n  `),\n    ])\n  }\n\n  private async prepareLinuxApplicationStageDirectory(options: PrepareApplicationStageDirectoryOptions) {\n    const appOutDir = options.appOutDir\n    await executeAppBuilder([\"proton-native\", \"--node-version\", this.version, \"--platform\", \"linux\", \"--arch\", options.arch, \"--stage\", appOutDir])\n    const mainPath = path.join(appOutDir, (options.packager as LinuxPackager).executableName)\n    await writeExecutableMain(mainPath, `#!/bin/sh\n  DIR=$(dirname \"$0\")\n  \"$DIR/node\" \"$DIR/app/${options.packager.info.metadata.main || \"index.js\"}\"\n  `)\n  }\n\n  async afterPack(context: AfterPackContext) {\n    const packager = context.packager\n    if (!this.isUseLaunchUiForPlatform(packager.platform)) {\n      return\n    }\n\n    // LaunchUI requires main.js, rename if need\n    const userMain = packager.info.metadata.main || \"index.js\"\n    if (userMain === \"main.js\") {\n      return\n    }\n\n    await rename(path.join(context.appOutDir, \"app\", userMain), path.join(context.appOutDir, \"app\", \"main.js\"))\n  }\n\n  getMainFile(platform: Platform): string | null {\n    return this.isUseLaunchUiForPlatform(platform) ? \"main.js\" : null\n  }\n\n  private isUseLaunchUiForPlatform(platform: Platform) {\n    return platform === Platform.WINDOWS || (this.isUseLaunchUi && platform === Platform.LINUX)\n  }\n\n  getExcludedDependencies(platform: Platform): Array<string> | null {\n    // part of launchui\n    return this.isUseLaunchUiForPlatform(platform) ? [\"libui-node\"] : null\n  }\n}\n\nasync function writeExecutableMain(file: string, content: string) {\n  await writeFile(file, content, {mode: 0o755})\n  await chmod(file, 0o755)\n}"], "sourceRoot": ""}