{"version": 3, "sources": ["../../src/publish/PublishManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAGA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;;;AAEA,MAAM,mBAAmB,GAAG,yKAC1B,6LADF;AAGA,MAAM,KAAK,GAAG,qBAAO,0BAAP,CAAd;;AAEA,SAAS,YAAT,CAAsB,aAAtB,EAAwC;AACtC,MAAI,aAAa,IAAI,IAAjB,IAAyB,aAAa,KAAK,OAA3C,IAAsD,aAAa,KAAK,cAAxE,IAA0F,aAAa,KAAK,QAA5G,IAAwH,aAAa,KAAK,OAA9I,EAAuJ;AACrJ,QAAI,OAAO,aAAP,KAAyB,QAA7B,EAAuC;AACrC,YAAM,KAAI,wCAAJ,EAA8B,uEAAuE,IAAI,CAAC,SAAL,CAAe,aAAf,CAA6B,8EAAlI,CAAN;AACD;AACF;AACF;;AAEK,MAAO,cAAP,CAAqB;AAWzB,EAAA,WAAA,CAA6B,QAA7B,EAAkE,cAAlE,EAA2G,iBAAA,GAAuC,QAAQ,CAAC,iBAA3J,EAA4K;AAA/I,SAAA,QAAA,GAAA,QAAA;AAAqC,SAAA,cAAA,GAAA,cAAA;AAAyC,SAAA,iBAAA,GAAA,iBAAA;AAV1F,SAAA,eAAA,GAAkB,IAAI,GAAJ,EAAlB;AAIR,SAAA,SAAA,GAAqB,KAArB;AAEA,SAAA,QAAA,GAAY,OAAO,CAAC,MAAR,CAAkC,KAAlC,GAA0C,KAAI,8BAAJ,GAA1C,GAAgE,IAA5E;AAEQ,SAAA,mBAAA,GAAiD,EAAjD;AAGf,IAAA,YAAY,CAAC,cAAc,CAAC,OAAhB,CAAZ;AAEA,SAAK,WAAL,GAAmB,KAAI,+BAAJ,EAAqB,iBAArB,CAAnB;AAEA,UAAM,iBAAiB,GAAG,OAAO,CAAC,GAAR,CAAY,wBAAZ,KAAyC,MAAnE;;AACA,QAAI,CAAC,mCAAD,IAAoB,iBAAxB,EAA2C;AACzC,UAAI,cAAc,CAAC,OAAf,KAA2B,SAA/B,EAA0C;AACxC,YAAI,OAAO,CAAC,GAAR,CAAY,mBAAZ,KAAoC,SAAxC,EAAmD;AACjD,UAAA,cAAc,CAAC,OAAf,GAAyB,QAAzB;AACD,SAFD,MAGK;AACH,gBAAM,GAAG,GAAG,kCAAZ;;AACA,cAAI,GAAG,IAAI,IAAX,EAAiB;AACf,+BAAI,IAAJ,CAAS;AAAC,cAAA,MAAM,EAAE,gBAAT;AAA2B,cAAA;AAA3B,aAAT,EAA0C,6BAA1C;;AACA,YAAA,cAAc,CAAC,OAAf,GAAyB,OAAzB;AACD,WAHD,MAIK,IAAI,eAAJ,EAAU;AACb,+BAAI,IAAJ,CAAS;AAAC,cAAA,MAAM,EAAE;AAAT,aAAT,EAAkC,qDAAlC;;AACA,YAAA,cAAc,CAAC,OAAf,GAAyB,cAAzB;AACD;AACF;AACF;;AAED,YAAM,aAAa,GAAG,cAAc,CAAC,OAArC;AACA,WAAK,SAAL,GAAiB,aAAa,IAAI,IAAjB,IAAyB,cAAc,CAAC,OAAf,KAA2B,OAApD,KAAgE,aAAa,KAAK,OAAlB,IAA6B,sCAAc,IAA3G,CAAjB;;AACA,UAAI,KAAK,SAAL,IAAkB,iBAAtB,EAAyC;AACvC,2BAAI,IAAJ,CAAS,mBAAT;AACD;AACF,KAvBD,MAwBK,IAAI,cAAc,CAAC,OAAf,KAA2B,OAA/B,EAAwC;AAC3C,yBAAI,IAAJ,CAAS;AACP,QAAA,MAAM,EAAE,yCADD;AAEP,QAAA,QAAQ,EAAE,mEAAmE,mBAAmB;AAFzF,OAAT,EAGG,4BAHH;AAID;;AAED,IAAA,QAAQ,CAAC,mBAAT,CAA6B,MAAM,KAAN,IAAc;AACzC,YAAM,QAAQ,GAAG,KAAK,CAAC,QAAvB;;AACA,UAAI,KAAK,CAAC,oBAAN,KAA+B,QAAnC,EAA6C;AAC3C,YAAI,CAAC,KAAK,CAAC,OAAN,CAAc,IAAd,CAAmB,EAAE,IAAI,EAAE,CAAC,IAAH,KAAY,KAAZ,IAAqB,EAAE,CAAC,IAAH,KAAY,KAA1D,CAAL,EAAuE;AACrE;AACD;AACF,OAJD,MAKK,IAAI,QAAQ,CAAC,QAAT,KAAsB,kBAAS,OAAnC,EAA4C;AAC/C,YAAI,CAAC,KAAK,CAAC,OAAN,CAAc,IAAd,CAAmB,EAAE,IAAI,uBAAuB,CAAC,EAAD,CAAhD,CAAL,EAA4D;AAC1D;AACD;AACF,OAJI,MAKA;AACH;AACA;AACD;;AAED,YAAM,aAAa,GAAG,MAAM,gCAAgC,CAAC,QAAD,EAAW,KAAK,CAAC,IAAjB,EAAuB,KAAK,SAA5B,CAA5D;;AACA,UAAI,aAAa,IAAI,IAArB,EAA2B;AACzB,cAAM,0BAAU,IAAI,CAAC,IAAL,CAAU,QAAQ,CAAC,eAAT,CAAyB,KAAK,CAAC,SAA/B,CAAV,EAAqD,gBAArD,CAAV,EAAkF,oCAAgB,aAAhB,CAAlF,CAAN;AACD;AACF,KArBD;AAuBA,IAAA,QAAQ,CAAC,eAAT,CAAyB,KAAK,IAAG;AAC/B,YAAM,oBAAoB,GAAG,KAAK,CAAC,aAAnC;;AACA,UAAI,oBAAoB,IAAI,IAA5B,EAAkC;AAChC,aAAK,WAAL,CAAiB,OAAjB,CAAyB,KAAK,2CAAL,CAAiD,KAAjD,CAAzB;AACD,OAFD,MAGK,IAAI,KAAK,SAAT,EAAoB;AACvB,YAAI,KAAK,CAAC,OAAV,EAAmB;AACjB,UAAA,KAAK,CAAC,+BAA+B,KAAK,SAAS,MAAM,sCAAkB,KAAlB,EAAyB,IAAI,GAAJ,CAAQ,CAAC,UAAD,CAAR,CAAzB,CAA+C,uBAAuB,sCAAkB,oBAAlB,CAAuC,EAAjK,CAAL;AACD;;AACD,aAAK,cAAL,CAAoB,oBAApB,EAA0C,KAA1C,EAAiD,KAAK,UAAL,CAAgB,KAAK,CAAC,QAAtB,CAAjD;AACD;AACF,KAXD;AAYD;;AAEO,EAAA,UAAU,CAAC,gBAAD,EAA+C;AAC/D,WAAO,gBAAgB,IAAI,IAApB,GAA2B,KAAK,QAAL,CAAc,OAAzC,GAAmD,gBAAgB,CAAC,OAA3E;AACD;;AAED,QAAM,8BAAN,GAAoC;AAClC,UAAM,UAAU,GAAG,KAAK,QAAL,CAAc,MAAd,CAAqB,OAAxC;AACA,WAAO,MAAM,4BAA4B,CAAC,UAAD,EAAa,IAAb,EAAmB,KAAK,QAAxB,EAAkC,IAAlC,EAAwC,IAAxC,CAAzC;AACD;AAED;;;AACA,EAAA,cAAc,CAAC,aAAD,EAAsC,KAAtC,EAAyD,OAAzD,EAAyE;AACrF,QAAI,aAAa,CAAC,QAAd,KAA2B,SAA/B,EAA0C;AACxC;AACD;;AAED,UAAM,SAAS,GAAG,KAAK,oBAAL,CAA0B,aAA1B,EAAyC,OAAzC,CAAlB;;AACA,QAAI,SAAS,IAAI,IAAjB,EAAuB;AACrB,yBAAI,KAAJ,CAAU;AACR,QAAA,IAAI,EAAE,KAAK,CAAC,IADJ;AAER,QAAA,MAAM,EAAE,mBAFA;AAGR,QAAA,aAAa,EAAE,sCAAkB,aAAlB;AAHP,OAAV,EAIG,eAJH;;AAKA;AACD;;AAED,UAAM,YAAY,GAAG,SAAS,CAAC,YAA/B;;AACA,QAAI,KAAK,cAAL,CAAoB,OAApB,KAAgC,cAAhC,IAAkD,sCAAc,IAAhE,IAAwE,EAAE,YAAY,KAAK,QAAjB,IAA6B,YAAY,KAAK,SAAhD,CAA5E,EAAwI;AACtI,yBAAI,IAAJ,CAAS;AAAC,QAAA,IAAI,EAAE,KAAK,CAAC,IAAb;AAAmB,QAAA,MAAM,EAAE,oCAA3B;AAAiE,QAAA,aAAa,EAAE;AAAhF,OAAT,EAA0G,oBAAoB,YAAY,EAA1I;;AACA;AACD;;AAED,SAAK,WAAL,CAAiB,OAAjB,CAAyB,SAAS,CAAC,MAAV,CAAiB,KAAjB,CAAzB;AACD;;AAEO,QAAM,2CAAN,CAAkD,KAAlD,EAAwE;AAC9E,UAAM,gBAAgB,GAAG,KAAK,CAAC,QAA/B;AACA,UAAM,MAAM,GAAG,KAAK,CAAC,MAArB;AACA,UAAM,cAAc,GAAG,MAAM,iBAAiB,CAAC,gBAAD,EAAmB,MAAM,IAAI,IAAV,GAAiB,IAAjB,GAAwB,MAAM,CAAC,OAAlD,EAA2D,KAAK,CAAC,IAAjE,EAAuE,KAAK,SAA5E,CAA9C;;AAEA,QAAI,KAAK,CAAC,OAAV,EAAmB;AACjB,MAAA,KAAK,CAAC,+BAA+B,KAAK,SAAS,MAAM,sCAAkB,KAAlB,EAAyB,IAAI,GAAJ,CAAQ,CAAC,UAAD,CAAR,CAAzB,CAA+C,wBAAwB,sCAAkB,cAAlB,CAAiC,EAA5J,CAAL;AACD;;AAED,UAAM,SAAS,GAAG,KAAK,CAAC,IAAxB;;AACA,QAAI,cAAc,IAAI,IAAtB,EAA4B;AAC1B,UAAI,KAAK,SAAT,EAAoB;AAClB,2BAAI,KAAJ,CAAU;AAAC,UAAA,IAAI,EAAE,SAAP;AAAkB,UAAA,MAAM,EAAE;AAA1B,SAAV,EAA2D,eAA3D;AACD;;AACD;AACD;;AAED,QAAI,KAAK,SAAT,EAAoB;AAClB,WAAK,MAAM,aAAX,IAA4B,cAA5B,EAA4C;AAC1C,YAAI,KAAK,iBAAL,CAAuB,SAA3B,EAAsC;AACpC,6BAAI,KAAJ,CAAU;AAAC,YAAA,IAAI,EAAE,KAAK,CAAC,IAAb;AAAmB,YAAA,MAAM,EAAE;AAA3B,WAAV,EAAmD,eAAnD;;AACA;AACD;;AAED,aAAK,cAAL,CAAoB,aAApB,EAAmC,KAAnC,EAA0C,KAAK,UAAL,CAAgB,gBAAhB,CAA1C;AACD;AACF;;AAED,QAAI,KAAK,CAAC,iBAAN,IAA2B,MAAM,IAAI,IAArC,IAA6C,SAAS,IAAI,IAA1D,IACF,CAAC,KAAK,iBAAL,CAAuB,SADtB,KAED,gBAAgB,CAAC,QAAjB,KAA8B,kBAAS,OAAvC,IAAkD,uBAAuB,CAAC,MAAD,CAFxE,CAAJ,EAEuF;AACrF,WAAK,WAAL,CAAiB,OAAjB,CAAyB,gDAAsB,KAAtB,EAA6B,cAA7B,EAA6C,IAA7C,CAAkD,EAAE,IAAI,KAAK,mBAAL,CAAyB,IAAzB,CAA8B,GAAG,EAAjC,CAAxD,CAAzB;AACD;AACF;;AAEO,EAAA,oBAAoB,CAAC,aAAD,EAAsC,OAAtC,EAAsD;AAChF;AACA,UAAM,gBAAgB,GAAG,sCAAkB,aAAlB,CAAzB;AACA,QAAI,SAAS,GAAG,KAAK,eAAL,CAAqB,GAArB,CAAyB,gBAAzB,CAAhB;;AACA,QAAI,SAAS,IAAI,IAAjB,EAAuB;AACrB,MAAA,SAAS,GAAG,eAAe,CAAC,IAAD,EAAO,OAAO,CAAC,OAAf,EAAwB,aAAxB,EAAuC,KAAK,cAA5C,EAA4D,KAAK,QAAjE,CAA3B;AACA,WAAK,eAAL,CAAqB,GAArB,CAAyB,gBAAzB,EAA2C,SAA3C;;AACA,yBAAI,IAAJ,CAAS;AAAC,QAAA,SAAS,EAAE,SAAW,CAAC,QAAZ;AAAZ,OAAT,EAA8C,YAA9C;AACD;;AACD,WAAO,SAAP;AACD,GApKwB,CAsKzB;;;AACA,EAAA,WAAW,GAAA;AACT,SAAK,WAAL,CAAiB,WAAjB;AACA,SAAK,eAAL,CAAqB,KAArB;AACD;;AAED,QAAM,UAAN,GAAgB;AACd,UAAM,KAAK,WAAL,CAAiB,UAAjB,EAAN;AAEA,UAAM,mBAAmB,GAAG,KAAK,mBAAjC;;AACA,QAAI,KAAK,iBAAL,CAAuB,SAAvB,IAAoC,mBAAmB,CAAC,MAApB,KAA+B,CAAvE,EAA0E;AACxE;AACD;;AAED,UAAM,+CAAqB,mBAArB,EAA0C,KAAK,QAA/C,CAAN;AACA,UAAM,KAAK,WAAL,CAAiB,UAAjB,EAAN;AACD;;AAtLwB;;;;AAyLpB,eAAe,gCAAf,CAAgD,QAAhD,EAAiF,IAAjF,EAA6F,aAA7F,EAAmH;AACxH,QAAM,cAAc,GAAG,MAAM,8BAA8B,CAAC,QAAD,EAAW,MAAM,iBAAiB,CAAC,QAAD,EAAW,IAAX,EAAiB,IAAjB,EAAuB,aAAvB,CAAlC,EAAyE,IAAzE,CAA3D;;AACA,MAAI,cAAc,IAAI,IAAlB,IAA0B,cAAc,CAAC,MAAf,KAA0B,CAAxD,EAA2D;AACzD,WAAO,IAAP;AACD;;AAED,QAAM,aAAa,GAAG,EACpB,GAAG,cAAc,CAAC,CAAD,CADG;AAEpB,IAAA,mBAAmB,EAAE,QAAQ,CAAC,OAAT,CAAiB;AAFlB,GAAtB;;AAKA,MAAI,QAAQ,CAAC,QAAT,KAAsB,kBAAS,OAA/B,IAA0C,aAAa,CAAC,aAAd,IAA+B,IAA7E,EAAmF;AACjF,UAAM,WAAW,GAAG,QAApB;AACA,UAAM,aAAa,GAAG,WAAW,CAAC,8BAAZ,GAA6C,MAAM,WAAW,CAAC,qBAAZ,CAAkC,KAArF,GAA6F,SAAnH;;AACA,QAAI,aAAa,IAAI,IAArB,EAA2B;AACzB,MAAA,aAAa,CAAC,aAAd,GAA8B,aAA9B;AACD;AACF;;AACD,SAAO,aAAP;AACD;;AAEM,eAAe,8BAAf,CAA8C,QAA9C,EAA+E,cAA/E,EAAmI,IAAnI,EAAoJ;AACzJ,MAAI,cAAc,KAAK,IAAvB,EAA6B;AAC3B,WAAO,IAAP;AACD;;AAED,MAAI,cAAc,CAAC,MAAf,KAA0B,CAA9B,EAAiC;AAC/B,uBAAI,KAAJ,CAAU,IAAV,EAAgB,iFAAhB,EAD+B,CAE/B;AACA;;;AACA,UAAM,cAAc,GAAG,MAAM,QAAQ,CAAC,IAAT,CAAc,cAA3C;AACA,IAAA,KAAK,CAAC,mCAAmC,sCAAkB,cAAlB,CAAiC,EAArE,CAAL;;AACA,QAAI,cAAc,IAAI,IAAlB,IAA0B,cAAc,CAAC,IAAf,KAAwB,QAAtD,EAAgE;AAC9D,YAAM,qBAAqB,GAAG,MAAM,wBAAwB,CAAC,QAAD,EAAW,QAAQ,CAAC,IAApB,EAA0B;AAAC,QAAA,QAAQ,EAAE,cAAc,CAAC;AAA1B,OAA1B,EAA2D,IAA3D,EAAiE,KAAjE,CAA5D;;AACA,UAAI,qBAAqB,IAAI,IAA7B,EAAmC;AACjC,QAAA,KAAK,CAAC,6DAA6D,sCAAkB,qBAAlB,CAAwC,EAAtG,CAAL;AACA,eAAO,CAAC,qBAAD,CAAP;AACD;AACF;AACF;;AACD,SAAO,cAAP;AACD;;AAEK,SAAU,eAAV,CAA0B,OAA1B,EAAmD,OAAnD,EAAoE,aAApE,EAAyG,OAAzG,EAAkI,QAAlI,EAAoJ;AACxJ,MAAI,KAAK,CAAC,OAAV,EAAmB;AACjB,IAAA,KAAK,CAAC,qBAAqB,sCAAkB,aAAlB,CAAgC,EAAtD,CAAL;AACD;;AAED,QAAM,QAAQ,GAAG,aAAa,CAAC,QAA/B;;AACA,UAAQ,QAAR;AACE,SAAK,QAAL;AACE,aAAO,KAAI,kCAAJ,EAAoB,OAApB,EAA6B,aAA7B,EAA6D,OAA7D,EAAsE,OAAtE,CAAP;;AAEF,SAAK,SAAL;AACE,aAAO,KAAI,oCAAJ,EAAqB,OAArB,EAA8B,aAA9B,EAA+D,OAA/D,EAAwE,OAAxE,CAAP;;AAEF,SAAK,SAAL;AACE,aAAO,IAAP;;AAEF,SAAK,WAAL;AACE,aAAO,KAAI,wCAAJ,EAAuB,OAAvB,EAAgC,aAAhC,CAAP;;AAEF;AAAS;AACP,cAAM,KAAK,GAAG,oBAAoB,CAAC,QAAD,EAAW,QAAX,CAAlC;AACA,eAAO,KAAK,IAAI,IAAT,GAAgB,IAAhB,GAAuB,IAAI,KAAJ,CAAU,OAAV,EAAmB,aAAnB,CAA9B;AACD;AAhBH;AAkBD;;AAED,SAAS,oBAAT,CAA8B,QAA9B,EAAgD,QAAhD,EAAkE;AAChE,UAAQ,QAAR;AACE,SAAK,QAAL;AACE,aAAO,kCAAP;;AAEF,SAAK,SAAL;AACE,aAAO,oCAAP;;AAEF,SAAK,SAAL;AACE,aAAO,IAAP;;AAEF,SAAK,IAAL;AACE,aAAO,sBAAP;;AAEF,SAAK,WAAL;AACE,aAAO,wCAAP;;AAEF,SAAK,QAAL;AACE,aAAO,0BAAP;;AAEF;AAAS;AACP,cAAM,IAAI,GAAG,sBAAsB,QAAQ,EAA3C;AACA,YAAI,MAAM,GAAQ,IAAlB;;AACA,YAAI;AACF,UAAA,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,IAAL,CAAU,QAAQ,CAAC,iBAAnB,EAAsC,IAAI,GAAG,KAA7C,CAAD,CAAhB;AACD,SAFD,CAGA,OAAO,OAAP,EAAgB;AACd,UAAA,OAAO,CAAC,GAAR,CAAY,OAAZ;AACD;;AAED,YAAI,MAAM,IAAI,IAAd,EAAoB;AAClB,UAAA,MAAM,GAAG,OAAO,CAAC,IAAD,CAAhB;AACD;;AACD,eAAO,MAAM,CAAC,OAAP,IAAkB,MAAzB;AACD;AAjCH;AAmCD;;AAEK,SAAU,kBAAV,CAA6B,oBAA7B,EAAyE,QAAzE,EAAkG,QAAlG,EAAiI;AACrI,MAAI,oBAAoB,CAAC,QAArB,KAAkC,SAAtC,EAAiD;AAC/C,UAAM,aAAa,GAAI,oBAA6C,CAAC,GAArE;;AACA,QAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,aAAO,aAAP;AACD;;AAED,UAAM,OAAO,GAAG,GAAG,GAAC,KAAJ,CAAU,aAAV,CAAhB;AACA,WAAO,GAAG,GAAC,MAAJ,CAAW,EAAC,GAAG,OAAJ;AAA8B,MAAA,QAAQ,EAAE,IAAI,CAAC,KAAL,CAAW,OAAX,CAAmB,OAAO,CAAC,QAAR,IAAoB,GAAvC,EAA4C,SAAS,CAAC,QAAD,CAArD;AAAxC,KAAX,CAAP;AACD;;AAED,MAAI,OAAJ;;AACA,MAAI,oBAAoB,CAAC,QAArB,KAAkC,QAAtC,EAAgD;AAC9C,UAAM,EAAE,GAAG,oBAAX;AACA,IAAA,OAAO,GAAG,GAAG,qCAAU,EAAV,CAAa,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,IAAI,sBAAsB,EAAE,CAAC,gBAAH,KAAwB,KAAxB,GAAgC,EAAhC,GAAqC,GAAG,GAAG,QAAQ,CAAC,OAAT,CAAiB,OAAO,EAA1I;AACD,GAHD,MAIK;AACH,IAAA,OAAO,GAAG,oDAAyB,oBAAzB,CAAV;AACD;;AAED,MAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,WAAO,OAAP;AACD;;AACD,SAAO,GAAG,OAAO,IAAI,SAAS,CAAC,QAAD,CAAU,EAAxC;AACD;;AAEM,eAAe,iBAAf,CAAiC,gBAAjC,EAA0E,qBAA1E,EAAkJ,IAAlJ,EAAqK,aAArK,EAA2L;AAChM,MAAI,UAAJ,CADgM,CAGhM;;AACA,MAAI,qBAAqB,IAAI,IAA7B,EAAmC;AACjC,IAAA,UAAU,GAAG,qBAAqB,CAAC,OAAnC,CADiC,CAEjC;;AACA,QAAI,UAAU,KAAK,IAAnB,EAAyB;AACvB,aAAO,IAAP;AACD;AACF,GAV+L,CAYhM;;;AACA,MAAI,UAAU,IAAI,IAAlB,EAAwB;AACtB,IAAA,UAAU,GAAG,gBAAgB,CAAC,4BAAjB,CAA8C,OAA3D;;AACA,QAAI,UAAU,KAAK,IAAnB,EAAyB;AACvB,aAAO,IAAP;AACD;AACF;;AAED,MAAI,UAAU,IAAI,IAAlB,EAAwB;AACtB,IAAA,UAAU,GAAG,gBAAgB,CAAC,MAAjB,CAAwB,OAArC;;AACA,QAAI,UAAU,KAAK,IAAnB,EAAyB;AACvB,aAAO,IAAP;AACD;AACF;;AACD,SAAO,MAAM,4BAA4B,CAAC,UAAD,EAAa,gBAAb,EAA+B,gBAAgB,CAAC,IAAhD,EAAsD,IAAtD,EAA4D,aAA5D,CAAzC;AACD;;AAED,eAAe,4BAAf,CAA4C,UAA5C,EAA6D,gBAA7D,EAA6G,QAA7G,EAAiI,IAAjI,EAAoJ,aAApJ,EAA0K;AACxK,MAAI,UAAU,IAAI,IAAlB,EAAwB;AACtB,QAAI,WAAW,GAA2B,IAA1C;;AACA,QAAI,CAAC,oCAAgB,OAAO,CAAC,GAAR,CAAY,QAA5B,CAAD,IAA0C,CAAC,oCAAgB,OAAO,CAAC,GAAR,CAAY,YAA5B,CAA/C,EAA0F;AACxF,MAAA,WAAW,GAAG,QAAd;AACD,KAFD,MAGK,IAAI,CAAC,oCAAgB,OAAO,CAAC,GAAR,CAAY,QAA5B,CAAL,EAA4C;AAC/C,MAAA,WAAW,GAAG,SAAd;AACD;;AAED,QAAI,WAAW,IAAI,IAAnB,EAAyB;AACvB,yBAAI,KAAJ,CAAU,IAAV,EAAgB,UAAU,WAAW,sBAArC;;AACA,aAAO,CAAE,MAAM,wBAAwB,CAAC,gBAAD,EAAmB,QAAnB,EAA6B;AAAC,QAAA,QAAQ,EAAE;AAAX,OAA7B,EAAsD,IAAtD,EAA4D,aAA5D,CAAhC,CAAP;AACD;AACF;;AAED,MAAI,UAAU,IAAI,IAAlB,EAAwB;AACtB,WAAO,EAAP;AACD;;AAED,EAAA,KAAK,CAAC,8BAA8B,sCAAkB,UAAlB,CAA6B,EAA5D,CAAL;AACA,SAAO,MAAO,uBAAgB,GAAhB,CAAoB,4BAAQ,UAAR,CAApB,EAAyC,EAAE,IAAI,wBAAwB,CAAC,gBAAD,EAAmB,QAAnB,EAA6B,OAAO,EAAP,KAAc,QAAd,GAAyB;AAAC,IAAA,QAAQ,EAAE;AAAX,GAAzB,GAA0C,EAAvE,EAA2E,IAA3E,EAAiF,aAAjF,CAAvE,CAAd;AACD;;AAED,SAAS,uBAAT,CAAiC,MAAjC,EAA+C;AAC7C,MAAI,MAAM,CAAC,IAAP,KAAgB,MAAhB,IAA0B,MAAM,CAAC,OAAP,IAAkB,IAA5C,IAAqD,MAAM,CAAC,OAAP,CAAuB,oBAAhF,EAAsG;AACpG,WAAO,IAAP;AACD;;AACD,SAAO,MAAM,CAAC,IAAP,KAAgB,MAAhB,IAA0B,MAAM,CAAC,IAAP,CAAY,UAAZ,CAAuB,OAAvB,CAAjC;AACD;;AAED,SAAS,mBAAT,CAA6B,OAA7B,EAA2C,gBAA3C,EAA2F,QAA3F,EAA+G,IAA/G,EAAgI;AAC9H,OAAK,MAAM,IAAX,IAAmB,MAAM,CAAC,IAAP,CAAY,OAAZ,CAAnB,EAAyC;AACvC,UAAM,KAAK,GAAG,OAAO,CAAC,IAAD,CAArB;;AACA,QAAI,OAAO,KAAP,KAAiB,QAArB,EAA+B;AAC7B,YAAM,SAAS,GAAG,IAAI,IAAI,IAAR,GAAe,IAAf,GAAsB,oBAAK,IAAL,CAAxC;AACA,YAAM,QAAQ,GAAG,gBAAgB,IAAI,IAApB,GAA2B,kCAAY,KAAZ,EAAmB,SAAnB,EAA8B,QAAQ,CAAC,OAAvC,CAA3B,GAA6E,gBAAgB,CAAC,WAAjB,CAA6B,KAA7B,EAAoC,SAApC,CAA9F;;AACA,UAAI,QAAQ,KAAK,KAAjB,EAAwB;AACtB,QAAA,OAAO,CAAC,IAAD,CAAP,GAAgB,QAAhB;AACD;AACF;AACF;AACF;;AAED,SAAS,qBAAT,CAA+B,6BAA/B,EAAmG,aAAnG,EAA+H;AAC7H,QAAM,KAAK,GAAG,6BAA6B,IAAI,IAAjC,GAAwC,IAAxC,GAA+C,6BAA6B,CAAC,mBAA3F;AACA,SAAO,KAAK,IAAI,IAAT,GAAgB,aAAa,CAAC,mBAAd,KAAsC,KAAtD,GAA8D,KAArE;AACD;;AAED,eAAe,wBAAf,CAAwC,gBAAxC,EAAwF,QAAxF,EAA4G,OAA5G,EAA2I,IAA3I,EAA8J,aAA9J,EAAoL;AAClL,EAAA,OAAO,GAAG,EAAC,GAAG;AAAJ,GAAV;AACA,EAAA,mBAAmB,CAAC,OAAD,EAAU,gBAAV,EAA4B,QAA5B,EAAsC,IAAtC,CAAnB;AAEA,MAAI,qBAAqB,GAAkB,IAA3C;;AACA,MAAK,OAAgC,CAAC,OAAjC,IAA4C,IAA5C,IAAoD,qBAAqB,CAAC,gBAAgB,IAAI,IAApB,GAA2B,IAA3B,GAAkC,gBAAgB,CAAC,4BAApD,EAAkF,QAAQ,CAAC,MAA3F,CAA9E,EAAkL;AAChL,IAAA,qBAAqB,GAAG,QAAQ,CAAC,OAAT,CAAiB,OAAzC;AACD;;AAED,QAAM,QAAQ,GAAG,OAAO,CAAC,QAAzB;;AACA,MAAI,QAAQ,KAAK,SAAjB,EAA4B;AAC1B,UAAM,CAAC,GAAG,OAAV;;AACA,QAAI,CAAC,CAAC,GAAF,IAAS,IAAb,EAAmB;AACjB,YAAM,KAAI,wCAAJ,EAA8B,kDAA9B,CAAN;AACD;;AAED,QAAI,qBAAqB,IAAI,IAA7B,EAAmC;AAChC,MAAA,CAAS,CAAC,OAAV,GAAoB,qBAApB;AACF;;AACD,WAAO,OAAP;AACD;;AAED,QAAM,aAAa,GAAG,oBAAoB,CAAC,OAAO,CAAC,QAAT,EAAmB,QAAnB,CAA1C;;AACA,MAAI,aAAa,IAAI,IAAjB,IAAyB,aAAa,CAAC,sBAAd,IAAwC,IAArE,EAA2E;AACzE,UAAM,aAAa,CAAC,sBAAd,CAAqC,OAArC,EAA8C,qBAA9C,EAAqE,aAArE,CAAN;AACA,WAAO,OAAP;AACD;;AAED,QAAM,QAAQ,GAAG,QAAQ,KAAK,QAA9B;;AACA,MAAI,CAAC,QAAD,IAAa,QAAQ,KAAK,SAA9B,EAAyC;AACvC,WAAO,OAAP;AACD;;AAED,MAAI,KAAK,GAAG,QAAQ,GAAI,OAAyB,CAAC,KAA9B,GAAuC,OAA0B,CAAC,KAAtF;AACA,MAAI,OAAO,GAAG,QAAQ,GAAI,OAAyB,CAAC,IAA9B,GAAsC,OAA0B,CAAC,OAAvF;;AAEA,MAAI,QAAQ,IAAI,KAAK,IAAI,IAArB,IAA6B,OAAO,IAAI,IAA5C,EAAkD;AAChD,UAAM,KAAK,GAAG,OAAO,CAAC,OAAR,CAAgB,GAAhB,CAAd;;AACA,QAAI,KAAK,GAAG,CAAZ,EAAe;AACb,YAAM,IAAI,GAAG,OAAb;AACA,MAAA,OAAO,GAAG,IAAI,CAAC,SAAL,CAAe,CAAf,EAAkB,KAAlB,CAAV;AACA,MAAA,KAAK,GAAG,IAAI,CAAC,SAAL,CAAe,KAAK,GAAG,CAAvB,CAAR;AACD;AACF;;AAED,iBAAe,OAAf,GAAsB;AACpB,UAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,cAA5B;;AACA,QAAI,IAAI,IAAI,IAAZ,EAAkB;AAChB,aAAO,IAAP;AACD;;AAED,UAAM,OAAO,GAAG,2MAAhB;;AACA,QAAI,aAAJ,EAAmB;AACjB,YAAM,IAAI,KAAJ,CAAU,OAAV,CAAN;AACD,KAFD,MAGK;AACH,yBAAI,IAAJ,CAAS,OAAT;;AACA,aAAO,IAAP;AACD;AACF;;AAED,MAAI,CAAC,KAAD,IAAU,CAAC,OAAf,EAAwB;AACtB,uBAAI,KAAJ,CAAU;AAAC,MAAA,MAAM,EAAE,8CAAT;AAAyD,MAAA,QAAzD;AAAmE,MAAA,KAAnE;AAA0E,MAAA;AAA1E,KAAV,EAA8F,iBAA9F;;AACA,UAAM,IAAI,GAAG,MAAM,OAAO,EAA1B;;AACA,QAAI,IAAI,IAAI,IAAZ,EAAkB;AAChB,aAAO,IAAP;AACD;;AAED,QAAI,CAAC,KAAL,EAAY;AACV,MAAA,KAAK,GAAG,IAAI,CAAC,IAAb;AACD;;AACD,QAAI,CAAC,OAAL,EAAc;AACZ,MAAA,OAAO,GAAG,IAAI,CAAC,OAAf;AACD;AACF;;AAED,MAAI,QAAJ,EAAc;AACZ,QAAK,OAAyB,CAAC,KAA1B,IAAmC,IAAnC,IAA2C,CAAE,OAAyB,CAAC,OAA5E,EAAqF;AACnF,yBAAI,IAAJ,CAAS,yJAAT;AACD,KAHW,CAIZ;;;AACA,WAAO;AAAC,MAAA,KAAD;AAAQ,MAAA,IAAI,EAAE,OAAd;AAAuB,SAAG;AAA1B,KAAP;AACD,GAND,MAOK;AACH;AACA,WAAO;AAAC,MAAA,KAAD;AAAQ,MAAA,OAAO,EAAE,OAAjB;AAA0B,SAAG;AAA7B,KAAP;AACD;AACF,C", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { Arch, as<PERSON><PERSON>y, AsyncTaskManager, InvalidConfigurationError, isEmptyOrSpaces, isPullRequest, log, safeStringifyJson, serializeToYaml } from \"builder-util\"\nimport { BintrayOptions, CancellationToken, GenericServerOptions, getS3LikeProviderBaseUrl, GithubOptions, githubUrl, PublishConfiguration, PublishProvider } from \"builder-util-runtime\"\nimport _debug from \"debug\"\nimport { getCiTag, PublishContext, Publisher, PublishOptions, UploadTask } from \"electron-publish\"\nimport { BintrayPublisher } from \"./BintrayPublisher\"\nimport { GitHubPublisher } from \"electron-publish/out/gitHubPublisher\"\nimport { MultiProgress } from \"electron-publish/out/multiProgress\"\nimport S3Publisher from \"./s3/s3Publisher\"\nimport SpacesPublisher from \"./s3/spacesPublisher\"\nimport { writeFile } from \"fs-extra\"\nimport isCi from \"is-ci\"\nimport * as path from \"path\"\nimport { WriteStream as TtyWriteStream } from \"tty\"\nimport * as url from \"url\"\nimport { AppInfo, ArtifactCreated, Configuration, Platform, PlatformSpecificBuildOptions, Target } from \"../index\"\nimport { Packager } from \"../packager\"\nimport { PlatformPackager } from \"../platformPackager\"\nimport { expandMacro } from \"../util/macroExpander\"\nimport { WinPackager } from \"../winPackager\"\nimport { SnapStoreOptions, SnapStorePublisher } from \"./SnapStorePublisher\"\nimport { createUpdateInfoTasks, UpdateInfoFileTask, writeUpdateInfoFiles } from \"./updateInfoBuilder\"\n\nconst publishForPrWarning = \"There are serious security concerns with PUBLISH_FOR_PULL_REQUEST=true (see the  CircleCI documentation (https://circleci.com/docs/1.0/fork-pr-builds/) for details)\" +\n  \"\\nIf you have SSH keys, sensitive env vars or AWS credentials stored in your project settings and untrusted forks can make pull requests against your repo, then this option isn't for you.\"\n\nconst debug = _debug(\"electron-builder:publish\")\n\nfunction checkOptions(publishPolicy: any) {\n  if (publishPolicy != null && publishPolicy !== \"onTag\" && publishPolicy !== \"onTagOrDraft\" && publishPolicy !== \"always\" && publishPolicy !== \"never\") {\n    if (typeof publishPolicy === \"string\") {\n      throw new InvalidConfigurationError(`Expected one of \"onTag\", \"onTagOrDraft\", \"always\", \"never\", but got ${JSON.stringify(publishPolicy)}.\\nPlease note that publish configuration should be specified under \"config\"`)\n    }\n  }\n}\n\nexport class PublishManager implements PublishContext {\n  private readonly nameToPublisher = new Map<string, Publisher | null>()\n\n  private readonly taskManager: AsyncTaskManager\n\n  readonly isPublish: boolean = false\n\n  readonly progress = (process.stdout as TtyWriteStream).isTTY ? new MultiProgress() : null\n\n  private readonly updateFileWriteTask: Array<UpdateInfoFileTask> = []\n\n  constructor(private readonly packager: Packager, private readonly publishOptions: PublishOptions, readonly cancellationToken: CancellationToken = packager.cancellationToken) {\n    checkOptions(publishOptions.publish)\n\n    this.taskManager = new AsyncTaskManager(cancellationToken)\n\n    const forcePublishForPr = process.env.PUBLISH_FOR_PULL_REQUEST === \"true\"\n    if (!isPullRequest() || forcePublishForPr) {\n      if (publishOptions.publish === undefined) {\n        if (process.env.npm_lifecycle_event === \"release\") {\n          publishOptions.publish = \"always\"\n        }\n        else {\n          const tag = getCiTag()\n          if (tag != null) {\n            log.info({reason: \"tag is defined\", tag}, \"artifacts will be published\")\n            publishOptions.publish = \"onTag\"\n          }\n          else if (isCi) {\n            log.info({reason: \"CI detected\"}, \"artifacts will be published if draft release exists\")\n            publishOptions.publish = \"onTagOrDraft\"\n          }\n        }\n      }\n\n      const publishPolicy = publishOptions.publish\n      this.isPublish = publishPolicy != null && publishOptions.publish !== \"never\" && (publishPolicy !== \"onTag\" || getCiTag() != null)\n      if (this.isPublish && forcePublishForPr) {\n        log.warn(publishForPrWarning)\n      }\n    }\n    else if (publishOptions.publish !== \"never\") {\n      log.info({\n        reason: \"current build is a part of pull request\",\n        solution: `set env PUBLISH_FOR_PULL_REQUEST to true to force code signing\\n${publishForPrWarning}`,\n      }, \"publishing will be skipped\")\n    }\n\n    packager.addAfterPackHandler(async event => {\n      const packager = event.packager\n      if (event.electronPlatformName === \"darwin\") {\n        if (!event.targets.some(it => it.name === \"dmg\" || it.name === \"zip\")) {\n          return\n        }\n      }\n      else if (packager.platform === Platform.WINDOWS) {\n        if (!event.targets.some(it => isSuitableWindowsTarget(it))) {\n          return\n        }\n      }\n      else {\n        // AppImage writes data to AppImage stage dir, not to linux-unpacked\n        return\n      }\n\n      const publishConfig = await getAppUpdatePublishConfiguration(packager, event.arch, this.isPublish)\n      if (publishConfig != null) {\n        await writeFile(path.join(packager.getResourcesDir(event.appOutDir), \"app-update.yml\"), serializeToYaml(publishConfig))\n      }\n    })\n\n    packager.artifactCreated(event => {\n      const publishConfiguration = event.publishConfig\n      if (publishConfiguration == null) {\n        this.taskManager.addTask(this.artifactCreatedWithoutExplicitPublishConfig(event))\n      }\n      else if (this.isPublish) {\n        if (debug.enabled) {\n          debug(`artifactCreated (isPublish: ${this.isPublish}): ${safeStringifyJson(event, new Set([\"packager\"]))},\\n  publishConfig: ${safeStringifyJson(publishConfiguration)}`)\n        }\n        this.scheduleUpload(publishConfiguration, event, this.getAppInfo(event.packager))\n      }\n    })\n  }\n\n  private getAppInfo(platformPackager: PlatformPackager<any> | null) {\n    return platformPackager == null ? this.packager.appInfo : platformPackager.appInfo\n  }\n\n  async getGlobalPublishConfigurations(): Promise<Array<PublishConfiguration> | null> {\n    const publishers = this.packager.config.publish\n    return await resolvePublishConfigurations(publishers, null, this.packager, null, true)\n  }\n\n  /** @internal */\n  scheduleUpload(publishConfig: PublishConfiguration, event: UploadTask, appInfo: AppInfo): void {\n    if (publishConfig.provider === \"generic\") {\n      return\n    }\n\n    const publisher = this.getOrCreatePublisher(publishConfig, appInfo)\n    if (publisher == null) {\n      log.debug({\n        file: event.file,\n        reason: \"publisher is null\",\n        publishConfig: safeStringifyJson(publishConfig),\n      }, \"not published\")\n      return\n    }\n\n    const providerName = publisher.providerName\n    if (this.publishOptions.publish === \"onTagOrDraft\" && getCiTag() == null && !(providerName === \"GitHub\" || providerName === \"Bintray\")) {\n      log.info({file: event.file, reason: \"current build is not for a git tag\", publishPolicy: \"onTagOrDraft\"}, `not published to ${providerName}`)\n      return\n    }\n\n    this.taskManager.addTask(publisher.upload(event))\n  }\n\n  private async artifactCreatedWithoutExplicitPublishConfig(event: ArtifactCreated) {\n    const platformPackager = event.packager\n    const target = event.target\n    const publishConfigs = await getPublishConfigs(platformPackager, target == null ? null : target.options, event.arch, this.isPublish)\n\n    if (debug.enabled) {\n      debug(`artifactCreated (isPublish: ${this.isPublish}): ${safeStringifyJson(event, new Set([\"packager\"]))},\\n  publishConfigs: ${safeStringifyJson(publishConfigs)}`)\n    }\n\n    const eventFile = event.file\n    if (publishConfigs == null) {\n      if (this.isPublish) {\n        log.debug({file: eventFile, reason: \"no publish configs\"}, \"not published\")\n      }\n      return\n    }\n\n    if (this.isPublish) {\n      for (const publishConfig of publishConfigs) {\n        if (this.cancellationToken.cancelled) {\n          log.debug({file: event.file, reason: \"cancelled\"}, \"not published\")\n          break\n        }\n\n        this.scheduleUpload(publishConfig, event, this.getAppInfo(platformPackager))\n      }\n    }\n\n    if (event.isWriteUpdateInfo && target != null && eventFile != null &&\n      !this.cancellationToken.cancelled &&\n      (platformPackager.platform !== Platform.WINDOWS || isSuitableWindowsTarget(target))) {\n      this.taskManager.addTask(createUpdateInfoTasks(event, publishConfigs).then(it => this.updateFileWriteTask.push(...it)))\n    }\n  }\n\n  private getOrCreatePublisher(publishConfig: PublishConfiguration, appInfo: AppInfo): Publisher | null {\n    // to not include token into cache key\n    const providerCacheKey = safeStringifyJson(publishConfig)\n    let publisher = this.nameToPublisher.get(providerCacheKey)\n    if (publisher == null) {\n      publisher = createPublisher(this, appInfo.version, publishConfig, this.publishOptions, this.packager)\n      this.nameToPublisher.set(providerCacheKey, publisher)\n      log.info({publisher: publisher!!.toString()}, \"publishing\")\n    }\n    return publisher\n  }\n\n  // noinspection JSUnusedGlobalSymbols\n  cancelTasks() {\n    this.taskManager.cancelTasks()\n    this.nameToPublisher.clear()\n  }\n\n  async awaitTasks(): Promise<void> {\n    await this.taskManager.awaitTasks()\n\n    const updateInfoFileTasks = this.updateFileWriteTask\n    if (this.cancellationToken.cancelled || updateInfoFileTasks.length === 0) {\n      return\n    }\n\n    await writeUpdateInfoFiles(updateInfoFileTasks, this.packager)\n    await this.taskManager.awaitTasks()\n  }\n}\n\nexport async function getAppUpdatePublishConfiguration(packager: PlatformPackager<any>, arch: Arch, errorIfCannot: boolean) {\n  const publishConfigs = await getPublishConfigsForUpdateInfo(packager, await getPublishConfigs(packager, null, arch, errorIfCannot), arch)\n  if (publishConfigs == null || publishConfigs.length === 0) {\n    return null\n  }\n\n  const publishConfig = {\n    ...publishConfigs[0],\n    updaterCacheDirName: packager.appInfo.updaterCacheDirName,\n  }\n\n  if (packager.platform === Platform.WINDOWS && publishConfig.publisherName == null) {\n    const winPackager = packager as WinPackager\n    const publisherName = winPackager.isForceCodeSigningVerification ? await winPackager.computedPublisherName.value : undefined\n    if (publisherName != null) {\n      publishConfig.publisherName = publisherName\n    }\n  }\n  return publishConfig\n}\n\nexport async function getPublishConfigsForUpdateInfo(packager: PlatformPackager<any>, publishConfigs: Array<PublishConfiguration> | null, arch: Arch | null): Promise<Array<PublishConfiguration> | null> {\n  if (publishConfigs === null) {\n    return null\n  }\n\n  if (publishConfigs.length === 0) {\n    log.debug(null, \"getPublishConfigsForUpdateInfo: no publishConfigs, detect using repository info\")\n    // https://github.com/electron-userland/electron-builder/issues/925#issuecomment-261732378\n    // default publish config is github, file should be generated regardless of publish state (user can test installer locally or manage the release process manually)\n    const repositoryInfo = await packager.info.repositoryInfo\n    debug(`getPublishConfigsForUpdateInfo: ${safeStringifyJson(repositoryInfo)}`)\n    if (repositoryInfo != null && repositoryInfo.type === \"github\") {\n      const resolvedPublishConfig = await getResolvedPublishConfig(packager, packager.info, {provider: repositoryInfo.type}, arch, false)\n      if (resolvedPublishConfig != null) {\n        debug(`getPublishConfigsForUpdateInfo: resolve to publish config ${safeStringifyJson(resolvedPublishConfig)}`)\n        return [resolvedPublishConfig]\n      }\n    }\n  }\n  return publishConfigs\n}\n\nexport function createPublisher(context: PublishContext, version: string, publishConfig: PublishConfiguration, options: PublishOptions, packager: Packager): Publisher | null {\n  if (debug.enabled) {\n    debug(`Create publisher: ${safeStringifyJson(publishConfig)}`)\n  }\n\n  const provider = publishConfig.provider\n  switch (provider) {\n    case \"github\":\n      return new GitHubPublisher(context, publishConfig as GithubOptions, version, options)\n\n    case \"bintray\":\n      return new BintrayPublisher(context, publishConfig as BintrayOptions, version, options)\n\n    case \"generic\":\n      return null\n\n    case \"snapStore\":\n      return new SnapStorePublisher(context, publishConfig as SnapStoreOptions)\n\n    default: {\n      const clazz = requireProviderClass(provider, packager)\n      return clazz == null ? null : new clazz(context, publishConfig)\n    }\n  }\n}\n\nfunction requireProviderClass(provider: string, packager: Packager): any | null {\n  switch (provider) {\n    case \"github\":\n      return GitHubPublisher\n\n    case \"bintray\":\n      return BintrayPublisher\n\n    case \"generic\":\n      return null\n\n    case \"s3\":\n      return S3Publisher\n\n    case \"snapStore\":\n      return SnapStorePublisher\n\n    case \"spaces\":\n      return SpacesPublisher\n\n    default: {\n      const name = `electron-publisher-${provider}`\n      let module: any = null\n      try {\n        module = require(path.join(packager.buildResourcesDir, name + \".js\"))\n      }\n      catch (ignored) {\n        console.log(ignored)\n      }\n\n      if (module == null) {\n        module = require(name)\n      }\n      return module.default || module\n    }\n  }\n}\n\nexport function computeDownloadUrl(publishConfiguration: PublishConfiguration, fileName: string | null, packager: PlatformPackager<any>) {\n  if (publishConfiguration.provider === \"generic\") {\n    const baseUrlString = (publishConfiguration as GenericServerOptions).url\n    if (fileName == null) {\n      return baseUrlString\n    }\n\n    const baseUrl = url.parse(baseUrlString)\n    return url.format({...baseUrl as url.UrlObject, pathname: path.posix.resolve(baseUrl.pathname || \"/\", encodeURI(fileName))})\n  }\n\n  let baseUrl\n  if (publishConfiguration.provider === \"github\") {\n    const gh = publishConfiguration as GithubOptions\n    baseUrl = `${githubUrl(gh)}/${gh.owner}/${gh.repo}/releases/download/${gh.vPrefixedTagName === false ? \"\" : \"v\"}${packager.appInfo.version}`\n  }\n  else {\n    baseUrl = getS3LikeProviderBaseUrl(publishConfiguration)\n  }\n\n  if (fileName == null) {\n    return baseUrl\n  }\n  return `${baseUrl}/${encodeURI(fileName)}`\n}\n\nexport async function getPublishConfigs(platformPackager: PlatformPackager<any>, targetSpecificOptions: PlatformSpecificBuildOptions | null | undefined, arch: Arch | null, errorIfCannot: boolean): Promise<Array<PublishConfiguration> | null> {\n  let publishers\n\n  // check build.nsis (target)\n  if (targetSpecificOptions != null) {\n    publishers = targetSpecificOptions.publish\n    // if explicitly set to null - do not publish\n    if (publishers === null) {\n      return null\n    }\n  }\n\n  // check build.win (platform)\n  if (publishers == null) {\n    publishers = platformPackager.platformSpecificBuildOptions.publish\n    if (publishers === null) {\n      return null\n    }\n  }\n\n  if (publishers == null) {\n    publishers = platformPackager.config.publish\n    if (publishers === null) {\n      return null\n    }\n  }\n  return await resolvePublishConfigurations(publishers, platformPackager, platformPackager.info, arch, errorIfCannot)\n}\n\nasync function resolvePublishConfigurations(publishers: any, platformPackager: PlatformPackager<any> | null, packager: Packager, arch: Arch | null, errorIfCannot: boolean): Promise<Array<PublishConfiguration> | null> {\n  if (publishers == null) {\n    let serviceName: PublishProvider | null = null\n    if (!isEmptyOrSpaces(process.env.GH_TOKEN) || !isEmptyOrSpaces(process.env.GITHUB_TOKEN)) {\n      serviceName = \"github\"\n    }\n    else if (!isEmptyOrSpaces(process.env.BT_TOKEN)) {\n      serviceName = \"bintray\"\n    }\n\n    if (serviceName != null) {\n      log.debug(null, `detect ${serviceName} as publish provider`)\n      return [(await getResolvedPublishConfig(platformPackager, packager, {provider: serviceName}, arch, errorIfCannot))!]\n    }\n  }\n\n  if (publishers == null) {\n    return []\n  }\n\n  debug(`Explicit publish provider: ${safeStringifyJson(publishers)}`)\n  return await (BluebirdPromise.map(asArray(publishers), it => getResolvedPublishConfig(platformPackager, packager, typeof it === \"string\" ? {provider: it} : it, arch, errorIfCannot)) as Promise<Array<PublishConfiguration>>)\n}\n\nfunction isSuitableWindowsTarget(target: Target) {\n  if (target.name === \"appx\" && target.options != null && (target.options as any).electronUpdaterAware) {\n    return true\n  }\n  return target.name === \"nsis\" || target.name.startsWith(\"nsis-\")\n}\n\nfunction expandPublishConfig(options: any, platformPackager: PlatformPackager<any> | null, packager: Packager, arch: Arch | null): void {\n  for (const name of Object.keys(options)) {\n    const value = options[name]\n    if (typeof value === \"string\") {\n      const archValue = arch == null ? null : Arch[arch]\n      const expanded = platformPackager == null ? expandMacro(value, archValue, packager.appInfo) : platformPackager.expandMacro(value, archValue)\n      if (expanded !== value) {\n        options[name] = expanded\n      }\n    }\n  }\n}\n\nfunction isDetectUpdateChannel(platformSpecificConfiguration: PlatformSpecificBuildOptions | null, configuration: Configuration) {\n  const value = platformSpecificConfiguration == null ? null : platformSpecificConfiguration.detectUpdateChannel\n  return value == null ? configuration.detectUpdateChannel !== false : value\n}\n\nasync function getResolvedPublishConfig(platformPackager: PlatformPackager<any> | null, packager: Packager, options: PublishConfiguration, arch: Arch | null, errorIfCannot: boolean): Promise<PublishConfiguration | GithubOptions | BintrayOptions | null> {\n  options = {...options}\n  expandPublishConfig(options, platformPackager, packager, arch)\n\n  let channelFromAppVersion: string | null = null\n  if ((options as GenericServerOptions).channel == null && isDetectUpdateChannel(platformPackager == null ? null : platformPackager.platformSpecificBuildOptions, packager.config)) {\n    channelFromAppVersion = packager.appInfo.channel\n  }\n\n  const provider = options.provider\n  if (provider === \"generic\") {\n    const o = options as GenericServerOptions\n    if (o.url == null) {\n      throw new InvalidConfigurationError(`Please specify \"url\" for \"generic\" update server`)\n    }\n\n    if (channelFromAppVersion != null) {\n      (o as any).channel = channelFromAppVersion\n    }\n    return options\n  }\n\n  const providerClass = requireProviderClass(options.provider, packager)\n  if (providerClass != null && providerClass.checkAndResolveOptions != null) {\n    await providerClass.checkAndResolveOptions(options, channelFromAppVersion, errorIfCannot)\n    return options\n  }\n\n  const isGithub = provider === \"github\"\n  if (!isGithub && provider !== \"bintray\") {\n    return options\n  }\n\n  let owner = isGithub ? (options as GithubOptions).owner : (options as BintrayOptions).owner\n  let project = isGithub ? (options as GithubOptions).repo : (options as BintrayOptions).package\n\n  if (isGithub && owner == null && project != null) {\n    const index = project.indexOf(\"/\")\n    if (index > 0) {\n      const repo = project\n      project = repo.substring(0, index)\n      owner = repo.substring(index + 1)\n    }\n  }\n\n  async function getInfo() {\n    const info = await packager.repositoryInfo\n    if (info != null) {\n      return info\n    }\n\n    const message = `Cannot detect repository by .git/config. Please specify \"repository\" in the package.json (https://docs.npmjs.com/files/package.json#repository).\\nPlease see https://electron.build/configuration/publish`\n    if (errorIfCannot) {\n      throw new Error(message)\n    }\n    else {\n      log.warn(message)\n      return null\n    }\n  }\n\n  if (!owner || !project) {\n    log.debug({reason: \"owner or project is not specified explicitly\", provider, owner, project}, \"calling getInfo\")\n    const info = await getInfo()\n    if (info == null) {\n      return null\n    }\n\n    if (!owner) {\n      owner = info.user\n    }\n    if (!project) {\n      project = info.project\n    }\n  }\n\n  if (isGithub) {\n    if ((options as GithubOptions).token != null && !(options as GithubOptions).private) {\n      log.warn('\"token\" specified in the github publish options. It should be used only for [setFeedURL](module:electron-updater/out/AppUpdater.AppUpdater+setFeedURL).')\n    }\n    //tslint:disable-next-line:no-object-literal-type-assertion\n    return {owner, repo: project, ...options} as GithubOptions\n  }\n  else {\n    //tslint:disable-next-line:no-object-literal-type-assertion\n    return {owner, package: project, ...options} as BintrayOptions\n  }\n}"], "sourceRoot": ""}