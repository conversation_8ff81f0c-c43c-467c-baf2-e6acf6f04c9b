{"version": 3, "sources": ["../../src/publish/updateInfoBuilder.ts"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAIA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;;;AAEA,eAAe,cAAf,CAA8B,QAA9B,EAA6D;AAC3D,QAAM,WAAW,GAAgB,EAAC,IAAI,QAAQ,CAAC,4BAAT,CAAsC,WAAtC,IAAqD,QAAQ,CAAC,MAAT,CAAgB,WAAzE;AAAD,GAAjC;;AACA,MAAI,WAAW,CAAC,YAAZ,IAA4B,IAAhC,EAAsC;AACpC,UAAM,gBAAgB,GAAG,MAAM,QAAQ,CAAC,WAAT,CAAqB,WAAW,CAAC,gBAAjC,EAAmD,iBAAiB,QAAQ,CAAC,QAAT,CAAkB,qBAAqB,KAA3G,EAAkH,iBAAiB,QAAQ,CAAC,QAAT,CAAkB,IAAI,KAAzJ,EAAgK,iBAAiB,QAAQ,CAAC,QAAT,CAAkB,QAAQ,KAA3M,EAAkN,kBAAlN,CAA/B;AACA,UAAM,YAAY,GAAG,gBAAgB,IAAI,IAApB,GAA2B,IAA3B,GAAkC,MAAM,yBAAS,gBAAT,EAA2B,OAA3B,CAA7D,CAFoC,CAGpC;;AACA,QAAI,YAAY,IAAI,IAApB,EAA0B;AACxB,MAAA,WAAW,CAAC,YAAZ,GAA2B,YAA3B;AACD;AACF;;AACD,SAAO,WAAW,CAAC,gBAAnB;AACA,SAAO,WAAP;AACD;;AAED,SAAS,oCAAT,CAA8C,QAA9C,EAA6E;AAC3E,QAAM,KAAK,GAAG,QAAQ,CAAC,4BAAT,CAAsC,kCAApD;AACA,SAAO,KAAK,IAAI,IAAT,GAAgB,QAAQ,CAAC,MAAT,CAAgB,kCAAhC,GAAqE,KAA5E;AACD;AAED;;;;;;;AAKA,SAAS,mBAAT,CAA6B,QAA7B,EAA8D,aAA9D,EAAiG;AAC/F,QAAM,cAAc,GAAY,aAAsC,CAAC,OAAvC,IAAkD,QAAlF,CAD+F,CAE/F;;AACA,MAAI,cAAc,KAAK,OAAnB,IAA8B,aAAa,CAAC,QAAd,KAA2B,QAAzD,IAAqE,CAAC,oCAAoC,CAAC,QAAD,CAA9G,EAA0H;AACxH,WAAO,CAAC,cAAD,CAAP;AACD;;AAED,UAAQ,cAAR;AACE,SAAK,MAAL;AACE,aAAO,CAAC,cAAD,EAAiB,OAAjB,CAAP;;AAEF,SAAK,QAAL;AACE,aAAO,CAAC,cAAD,EAAiB,OAAjB,EAA0B,MAA1B,CAAP;;AAEF;AACE,aAAO,CAAC,cAAD,CAAP;AARJ;AAUD;;AAED,SAAS,qBAAT,CAA+B,OAA/B,EAAgD,QAAhD,EAAiF,IAAjF,EAAkG;AAChG,QAAM,QAAQ,GAAG,QAAQ,CAAC,QAAT,KAAsB,iBAAS,OAA/B,GAAyC,EAAzC,GAA8C,IAAI,QAAQ,CAAC,QAAT,CAAkB,qBAAqB,EAA1G;AACA,SAAO,GAAG,OAAO,GAAG,QAAQ,GAAG,0BAA0B,CAAC,IAAD,EAAO,QAAP,CAAgB,MAAzE;AACD;;AAED,SAAS,0BAAT,CAAoC,IAApC,EAAuD,QAAvD,EAAsF;AACpF,MAAI,IAAI,IAAI,IAAR,IAAgB,IAAI,KAAK,oBAAK,GAA9B,IAAqC,QAAQ,CAAC,QAAT,KAAsB,iBAAS,KAAxE,EAA+E;AAC7E,WAAO,EAAP;AACD;;AACD,SAAO,IAAI,KAAK,oBAAK,MAAd,GAAuB,MAAvB,GAAgC,IAAI,oBAAK,IAAL,CAAU,EAArD;AACD;;AAUD,SAAS,yCAAT,CAAmD,oBAAnD,EAAwF,oBAAxF,EAAoI,QAApI,EAAsJ;AACpJ,MAAI,oBAAoB,IAAI,IAA5B,EAAkC;AAChC,WAAO,MAAM,GAAC,SAAP,CAAiB,OAAjB,EAA0B,oBAA1B,CAAP;AACD,GAHmJ,CAKpJ;;;AACA,MAAI,oBAAoB,CAAC,QAArB,KAAkC,QAAtC,EAAgD;AAC9C,WAAO,KAAP;AACD;;AAED,QAAM,cAAc,GAAG,QAAQ,CAAC,QAAT,CAAkB,YAAlB,IAAkC,IAAlC,GAAyC,IAAzC,GAAgD,QAAQ,CAAC,QAAT,CAAkB,YAAlB,CAA+B,kBAA/B,CAAvE;AACA,SAAO,cAAc,IAAI,IAAlB,IAA0B,MAAM,GAAC,EAAP,CAAU,cAAV,EAA0B,OAA1B,CAAjC;AACD;AAED;;;AACO,eAAe,qBAAf,CAAqC,KAArC,EAA6D,eAA7D,EAAyG;AAC9G,QAAM,QAAQ,GAAG,KAAK,CAAC,QAAvB;AACA,QAAM,cAAc,GAAG,MAAM,sDAA+B,QAA/B,EAAyC,eAAzC,EAA0D,KAAK,CAAC,IAAhE,CAA7B;;AACA,MAAI,cAAc,IAAI,IAAlB,IAA0B,cAAc,CAAC,MAAf,KAA0B,CAAxD,EAA2D;AACzD,WAAO,EAAP;AACD;;AAED,QAAM,MAAM,GAAG,KAAK,CAAC,MAAN,CAAc,MAA7B;AACA,QAAM,OAAO,GAAG,QAAQ,CAAC,OAAT,CAAiB,OAAjC;AACA,QAAM,IAAI,GAAG,KAAI,eAAJ,EAAiB,MAAM,sBAAS,KAAK,CAAC,IAAf,EAAsB,QAAtB,EAAgC,KAAhC,CAAvB,CAAb;;AACA,QAAM,KAAK,GAAG,QAAQ,CAAC,QAAT,KAAsB,iBAAS,GAA7C;;AACA,QAAM,YAAY,GAAG,IAAI,GAAJ,EAArB;AACA,QAAM,UAAU,GAAG,MAAM,gBAAgB,CAAC,OAAD,EAAU,KAAV,EAAiB,MAAM,cAAc,CAAC,QAAD,CAArC,CAAzC;AACA,QAAM,KAAK,GAA8B,EAAzC;AACA,QAAM,4BAA4B,GAAG,QAAQ,CAAC,4BAAT,CAAsC,4BAAtC,IAAsE,QAAQ,CAAC,MAAT,CAAgB,4BAAtF,IAAsH,QAA3J;;AACA,OAAK,MAAM,oBAAX,IAAmC,cAAnC,EAAmD;AACjD,UAAM,SAAS,GAAG,oBAAoB,CAAC,QAArB,KAAkC,SAApD;AACA,QAAI,GAAG,GAAG,MAAV,CAFiD,CAGjD;;AACA,QAAI,SAAS,IAAK,cAAc,CAAC,MAAf,GAAwB,CAAxB,IAA6B,oBAAoB,KAAK,cAAc,CAAC,CAAD,CAAtF,EAA4F;AAC1F,MAAA,GAAG,GAAG,IAAI,CAAC,IAAL,CAAU,MAAV,EAAkB,oBAAoB,CAAC,QAAvC,CAAN;AACD;;AAED,QAAI,gCAAgC,GAAG,yCAAyC,CAAC,4BAAD,EAA+B,oBAA/B,EAAqD,QAAQ,CAAC,IAA9D,CAAhF;AAEA,QAAI,IAAI,GAAG,UAAX,CAViD,CAWjD;;AACA,QAAI,gCAAgC,IAAI,QAAQ,CAAC,QAAT,KAAsB,iBAAS,OAAvE,EAAgF;AAC9E,MAAA,IAAI,GAAG,EACL,GAAG;AADE,OAAP,CAD8E,CAI9E;;AACC,MAAA,IAA0B,CAAC,IAA3B,GAAkC,MAAM,IAAI,CAAC,KAA7C;AACF;;AAED,QAAI,KAAK,CAAC,gBAAN,IAA0B,IAA1B,IAAkC,oBAAoB,CAAC,QAArB,KAAkC,QAAxE,EAAkF;AAChF,YAAM,QAAQ,GAAG,IAAI,CAAC,KAAL,CAAW,KAAX,EAAjB;AACA,MAAA,QAAQ,CAAC,CAAD,CAAR,CAAY,GAAZ,GAAkB,KAAK,CAAC,gBAAxB;AACA,MAAA,IAAI,GAAG,EACL,GAAG,IADE;AAEL,QAAA,KAAK,EAAE,QAFF;AAGL,QAAA,IAAI,EAAE,KAAK,CAAC;AAHP,OAAP;AAKD;;AAED,SAAK,MAAM,OAAX,IAAsB,mBAAmB,CAAC,QAAD,EAAW,oBAAX,CAAzC,EAA2E;AACzE,UAAI,KAAK,IAAI,gCAAT,IAA6C,KAAK,CAAC,IAAN,CAAW,QAAX,CAAoB,MAApB,CAAjD,EAA8E;AAC5E;AACA,QAAA,gCAAgC,GAAG,KAAnC;AACA,cAAM,eAAe,CAAC,oBAAD,EAAuB,MAAvB,EAA+B,GAA/B,EAAoC,OAApC,EAA6C,YAA7C,EAA2D,OAA3D,EAAoE,QAApE,CAArB;AACD;;AAED,YAAM,cAAc,GAAG,IAAI,CAAC,IAAL,CAAU,GAAV,EAAe,CAAC,SAAS,GAAG,GAAG,OAAO,GAAb,GAAmB,EAA7B,IAAmC,qBAAqB,CAAC,OAAD,EAAU,QAAV,EAAoB,KAAK,CAAC,IAA1B,CAAvE,CAAvB;;AACA,UAAI,YAAY,CAAC,GAAb,CAAiB,cAAjB,CAAJ,EAAsC;AACpC;AACD;;AAED,MAAA,YAAY,CAAC,GAAb,CAAiB,cAAjB,EAZyE,CAczE;;AACA,MAAA,KAAK,CAAC,IAAN,CAAW;AACT,QAAA,IAAI,EAAE,cADG;AAET,QAAA,IAFS;AAGT,QAAA,oBAHS;AAIT,QAAA;AAJS,OAAX;AAMD;AACF;;AACD,SAAO,KAAP;AACD;;AAED,eAAe,gBAAf,CAAgC,OAAhC,EAAiD,KAAjD,EAAyE,WAAzE,EAAiG;AAC/F,QAAM,gBAAgB,GAAG,KAAK,CAAC,UAA/B;AACA,QAAM,GAAG,GAAG,IAAI,CAAC,QAAL,CAAc,KAAK,CAAC,IAApB,CAAZ;AACA,QAAM,MAAM,GAAG,CAAC,gBAAgB,IAAI,IAApB,GAA2B,IAA3B,GAAkC,gBAAgB,CAAC,MAApD,MAA+D,MAAM,sBAAS,KAAK,CAAC,IAAf,CAArE,CAAf;AACA,QAAM,KAAK,GAAG,CAAC;AAAC,IAAA,GAAD;AAAM,IAAA;AAAN,GAAD,CAAd;AACA,QAAM,MAAM,GAAe;AACzB;AACA,IAAA,OAFyB;AAGzB;AACA,IAAA,KAJyB;AAKzB;AACA,IAAA,IAAI,EAAE;AAAI;AANe;AAOzB;AACA,IAAA;AAAO;AARkB;AASzB,OAAG;AATsB,GAA3B;;AAYA,MAAI,gBAAgB,IAAI,IAAxB,EAA8B;AAC5B;AACA,IAAA,MAAM,CAAC,MAAP,CAAc,YAAY,gBAAZ,GAA+B,KAAK,CAAC,CAAD,CAApC,GAA0C,MAAxD,EAAgE,gBAAhE;AACD;;AACD,SAAO,MAAP;AACD;;AAEM,eAAe,oBAAf,CAAoC,mBAApC,EAAoF,QAApF,EAAsG;AAC3G;AACA,EAAA,mBAAmB,CAAC,IAApB,CAAyB,CAAC,CAAD,EAAI,CAAJ,KAAU,CAAC,CAAC,CAAC,IAAF,CAAO,KAAP,CAAa,CAAb,EAAgB,GAAhB,CAAoB,QAApB,CAA6B,MAA7B,IAAuC,CAAvC,GAA2C,GAA5C,KAAoD,CAAC,CAAC,IAAF,CAAO,KAAP,CAAa,CAAb,EAAgB,GAAhB,CAAoB,QAApB,CAA6B,MAA7B,IAAuC,CAAvC,GAA2C,GAA/F,CAAnC;AAEA,QAAM,uBAAuB,GAAG,IAAI,GAAJ,EAAhC;;AACA,OAAK,MAAM,IAAX,IAAmB,mBAAnB,EAAwC;AACtC;AACA,UAAM,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,IAAI,sCAAkB,IAAI,CAAC,oBAAvB,EAA6C,IAAI,GAAJ,CAAQ,CAAC,aAAD,CAAR,CAA7C,CAAsE,EAAlG;AACA,UAAM,YAAY,GAAG,uBAAuB,CAAC,GAAxB,CAA4B,GAA5B,CAArB;;AACA,QAAI,YAAY,IAAI,IAApB,EAA0B;AACxB,MAAA,uBAAuB,CAAC,GAAxB,CAA4B,GAA5B,EAAiC,IAAjC;AACA;AACD;;AAED,IAAA,YAAY,CAAC,IAAb,CAAkB,KAAlB,CAAwB,IAAxB,CAA6B,GAAG,IAAI,CAAC,IAAL,CAAU,KAA1C;AACD;;AAED,QAAM,WAAW,GAAG,IAAI,IAAJ,GAAW,WAAX,EAApB;AACA,QAAM,uBAAgB,GAAhB,CAAoB,uBAAuB,CAAC,MAAxB,EAApB,EAAsD,MAAM,IAAN,IAAa;AACvE,UAAM,aAAa,GAAG,IAAI,CAAC,oBAA3B;;AACA,QAAI,aAAa,CAAC,iBAAd,KAAoC,KAAxC,EAA+C;AAC7C,yBAAI,KAAJ,CAAU;AACR,QAAA,QAAQ,EAAE,aAAa,CAAC,QADhB;AAER,QAAA,MAAM,EAAE;AAFA,OAAV,EAGG,yCAHH;;AAIA;AACD;;AAED,QAAI,IAAI,CAAC,IAAL,CAAU,WAAV,IAAyB,IAA7B,EAAmC;AACjC,MAAA,IAAI,CAAC,IAAL,CAAU,WAAV,GAAwB,WAAxB;AACD;;AAED,UAAM,WAAW,GAAG,MAAM,CAAC,IAAP,CAAY,oCAAgB,IAAI,CAAC,IAArB,EAA2B,KAA3B,EAAkC,IAAlC,CAAZ,CAApB;AACA,UAAM,2BAAW,IAAI,CAAC,IAAhB,EAAsB,WAAtB,CAAN;AACA,IAAA,QAAQ,CAAC,uBAAT,CAAiC;AAC/B,MAAA,IAAI,EAAE,IAAI,CAAC,IADoB;AAE/B,MAAA,WAF+B;AAG/B,MAAA,IAAI,EAAE,IAHyB;AAI/B,MAAA,QAAQ,EAAE,IAAI,CAAC,QAJgB;AAK/B,MAAA,MAAM,EAAE,IALuB;AAM/B,MAAA;AAN+B,KAAjC;AAQD,GAxBK,EAwBH;AAAC,IAAA,WAAW,EAAE;AAAd,GAxBG,CAAN;AAyBD,C,CAED;;;AACA,eAAe,eAAf,CAA+B,aAA/B,EAAoE,MAApE,EAAoF,GAApF,EAAiG,OAAjG,EAAkH,YAAlH,EAA6I,OAA7I,EAA8J,QAA9J,EAA6L;AAC3L,QAAM,QAAQ,GAAG,aAAa,CAAC,QAAd,KAA2B,QAA5C;AACA,QAAM,cAAc,GAAI,QAAQ,IAAI,MAAM,KAAK,GAAxB,GAA+B,IAAI,CAAC,IAAL,CAAU,GAAV,EAAe,QAAf,EAAyB,GAAG,OAAO,WAAnC,CAA/B,GAAiF,IAAI,CAAC,IAAL,CAAU,GAAV,EAAe,GAAG,OAAO,WAAzB,CAAxG;;AACA,MAAI,CAAC,YAAY,CAAC,GAAb,CAAiB,cAAjB,CAAL,EAAuC;AACrC,IAAA,YAAY,CAAC,GAAb,CAAiB,cAAjB;AACA,UAAM,2BAAW,cAAX,EAA2B;AAC/B,MAAA,OAD+B;AAE/B,MAAA,WAAW,EAAE,IAAI,IAAJ,GAAW,WAAX,EAFkB;AAG/B,MAAA,GAAG,EAAE,0CAAmB,aAAnB,EAAkC,QAAQ,CAAC,aAAT,CAAuB,KAAvB,EAA8B,KAA9B,EAAqC,QAArC,CAAlC,EAAkF,QAAlF;AAH0B,KAA3B,EAIH;AAAC,MAAA,MAAM,EAAE;AAAT,KAJG,CAAN;AAMA,IAAA,QAAQ,CAAC,IAAT,CAAc,uBAAd,CAAsC;AACpC,MAAA,IAAI,EAAE,cAD8B;AAEpC,MAAA,IAAI,EAAE,IAF8B;AAGpC,MAAA,QAHoC;AAIpC,MAAA,MAAM,EAAE,IAJ4B;AAKpC,MAAA;AALoC,KAAtC;AAOD;AACF,C", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { Arch, log, safeStringify<PERSON>son, serializeToYaml } from \"builder-util\"\nimport { GenericServerOptions, PublishConfiguration, UpdateInfo, WindowsUpdateInfo } from \"builder-util-runtime\"\nimport { outputFile, outputJson, readFile } from \"fs-extra\"\nimport { Lazy } from \"lazy-val\"\nimport * as path from \"path\"\nimport * as semver from \"semver\"\nimport { ReleaseInfo } from \"..\"\nimport { Platform } from \"../core\"\nimport { Packager } from \"../packager\"\nimport { ArtifactCreated } from \"../packagerApi\"\nimport { PlatformPackager } from \"../platformPackager\"\nimport { hashFile } from \"../util/hash\"\nimport { computeDownloadUrl, getPublishConfigsForUpdateInfo } from \"./PublishManager\"\n\nasync function getReleaseInfo(packager: PlatformPackager<any>) {\n  const releaseInfo: ReleaseInfo = {...(packager.platformSpecificBuildOptions.releaseInfo || packager.config.releaseInfo)}\n  if (releaseInfo.releaseNotes == null) {\n    const releaseNotesFile = await packager.getResource(releaseInfo.releaseNotesFile, `release-notes-${packager.platform.buildConfigurationKey}.md`, `release-notes-${packager.platform.name}.md`, `release-notes-${packager.platform.nodeName}.md`, \"release-notes.md\")\n    const releaseNotes = releaseNotesFile == null ? null : await readFile(releaseNotesFile, \"utf-8\")\n    // to avoid undefined in the file, check for null\n    if (releaseNotes != null) {\n      releaseInfo.releaseNotes = releaseNotes\n    }\n  }\n  delete releaseInfo.releaseNotesFile\n  return releaseInfo\n}\n\nfunction isGenerateUpdatesFilesForAllChannels(packager: PlatformPackager<any>) {\n  const value = packager.platformSpecificBuildOptions.generateUpdatesFilesForAllChannels\n  return value == null ? packager.config.generateUpdatesFilesForAllChannels : value\n}\n\n/**\n if this is an \"alpha\" version, we need to generate only the \"alpha\" .yml file\n if this is a \"beta\" version, we need to generate both the \"alpha\" and \"beta\" .yml file\n if this is a \"stable\" version, we need to generate all the \"alpha\", \"beta\" and \"stable\" .yml file\n */\nfunction computeChannelNames(packager: PlatformPackager<any>, publishConfig: PublishConfiguration): Array<string> {\n  const currentChannel: string = (publishConfig as GenericServerOptions).channel || \"latest\"\n  // for GitHub should be pre-release way be used\n  if (currentChannel === \"alpha\" || publishConfig.provider === \"github\" || !isGenerateUpdatesFilesForAllChannels(packager)) {\n    return [currentChannel]\n  }\n\n  switch (currentChannel) {\n    case \"beta\":\n      return [currentChannel, \"alpha\"]\n\n    case \"latest\":\n      return [currentChannel, \"alpha\", \"beta\"]\n\n    default:\n      return [currentChannel]\n  }\n}\n\nfunction getUpdateInfoFileName(channel: string, packager: PlatformPackager<any>, arch: Arch | null): string {\n  const osSuffix = packager.platform === Platform.WINDOWS ? \"\" : `-${packager.platform.buildConfigurationKey}`\n  return `${channel}${osSuffix}${getArchPrefixForUpdateFile(arch, packager)}.yml`\n}\n\nfunction getArchPrefixForUpdateFile(arch: Arch | null, packager: PlatformPackager<any>) {\n  if (arch == null || arch === Arch.x64 || packager.platform !== Platform.LINUX) {\n    return \"\"\n  }\n  return arch === Arch.armv7l ? \"-arm\" : `-${Arch[arch]}`\n}\n\nexport interface UpdateInfoFileTask {\n  readonly file: string\n  readonly info: UpdateInfo\n  readonly publishConfiguration: PublishConfiguration\n\n  readonly packager: PlatformPackager<any>\n}\n\nfunction computeIsisElectronUpdater1xCompatibility(updaterCompatibility: string | null, publishConfiguration: PublishConfiguration, packager: Packager) {\n  if (updaterCompatibility != null) {\n    return semver.satisfies(\"1.0.0\", updaterCompatibility)\n  }\n\n  // spaces is a new publish provider, no need to keep backward compatibility\n  if (publishConfiguration.provider === \"spaces\") {\n    return false\n  }\n\n  const updaterVersion = packager.metadata.dependencies == null ? null : packager.metadata.dependencies[\"electron-updater\"]\n  return updaterVersion == null || semver.lt(updaterVersion, \"4.0.0\")\n}\n\n/** @internal */\nexport async function createUpdateInfoTasks(event: ArtifactCreated, _publishConfigs: Array<PublishConfiguration>): Promise<Array<UpdateInfoFileTask>> {\n  const packager = event.packager\n  const publishConfigs = await getPublishConfigsForUpdateInfo(packager, _publishConfigs, event.arch)\n  if (publishConfigs == null || publishConfigs.length === 0) {\n    return []\n  }\n\n  const outDir = event.target!.outDir\n  const version = packager.appInfo.version\n  const sha2 = new Lazy<string>(() => hashFile(event.file!, \"sha256\", \"hex\"))\n  const isMac = packager.platform === Platform.MAC\n  const createdFiles = new Set<string>()\n  const sharedInfo = await createUpdateInfo(version, event, await getReleaseInfo(packager))\n  const tasks: Array<UpdateInfoFileTask> = []\n  const electronUpdaterCompatibility = packager.platformSpecificBuildOptions.electronUpdaterCompatibility || packager.config.electronUpdaterCompatibility || \">=2.15\"\n  for (const publishConfiguration of publishConfigs) {\n    const isBintray = publishConfiguration.provider === \"bintray\"\n    let dir = outDir\n    // Bintray uses different variant of channel file info, better to generate it to a separate dir by always\n    if (isBintray || (publishConfigs.length > 1 && publishConfiguration !== publishConfigs[0])) {\n      dir = path.join(outDir, publishConfiguration.provider)\n    }\n\n    let isElectronUpdater1xCompatibility = computeIsisElectronUpdater1xCompatibility(electronUpdaterCompatibility, publishConfiguration, packager.info)\n\n    let info = sharedInfo\n    // noinspection JSDeprecatedSymbols\n    if (isElectronUpdater1xCompatibility && packager.platform === Platform.WINDOWS) {\n      info = {\n        ...info,\n      };\n      // noinspection JSDeprecatedSymbols\n      (info as WindowsUpdateInfo).sha2 = await sha2.value\n    }\n\n    if (event.safeArtifactName != null && publishConfiguration.provider === \"github\") {\n      const newFiles = info.files.slice()\n      newFiles[0].url = event.safeArtifactName\n      info = {\n        ...info,\n        files: newFiles,\n        path: event.safeArtifactName,\n      }\n    }\n\n    for (const channel of computeChannelNames(packager, publishConfiguration)) {\n      if (isMac && isElectronUpdater1xCompatibility && event.file.endsWith(\".zip\")) {\n        // write only for first channel (generateUpdatesFilesForAllChannels is a new functionality, no need to generate old mac update info file)\n        isElectronUpdater1xCompatibility = false\n        await writeOldMacInfo(publishConfiguration, outDir, dir, channel, createdFiles, version, packager)\n      }\n\n      const updateInfoFile = path.join(dir, (isBintray ? `${version}_` : \"\") + getUpdateInfoFileName(channel, packager, event.arch))\n      if (createdFiles.has(updateInfoFile)) {\n        continue\n      }\n\n      createdFiles.add(updateInfoFile)\n\n      // artifact should be uploaded only to designated publish provider\n      tasks.push({\n        file: updateInfoFile,\n        info,\n        publishConfiguration,\n        packager,\n      })\n    }\n  }\n  return tasks\n}\n\nasync function createUpdateInfo(version: string, event: ArtifactCreated, releaseInfo: ReleaseInfo): Promise<UpdateInfo> {\n  const customUpdateInfo = event.updateInfo\n  const url = path.basename(event.file!)\n  const sha512 = (customUpdateInfo == null ? null : customUpdateInfo.sha512) || await hashFile(event.file!)\n  const files = [{url, sha512}]\n  const result: UpdateInfo = {\n    // @ts-ignore\n    version,\n    // @ts-ignore\n    files,\n    // @ts-ignore\n    path: url /* backward compatibility, electron-updater 1.x - electron-updater 2.15.0 */,\n    // @ts-ignore\n    sha512 /* backward compatibility, electron-updater 1.x - electron-updater 2.15.0 */,\n    ...releaseInfo as UpdateInfo,\n  }\n\n  if (customUpdateInfo != null) {\n    // file info or nsis web installer packages info\n    Object.assign(\"sha512\" in customUpdateInfo ? files[0] : result, customUpdateInfo)\n  }\n  return result\n}\n\nexport async function writeUpdateInfoFiles(updateInfoFileTasks: Array<UpdateInfoFileTask>, packager: Packager) {\n  // zip must be first and zip info must be used for old path/sha512 properties in the update info\n  updateInfoFileTasks.sort((a, b) => (a.info.files[0].url.endsWith(\".zip\") ? 0 : 100) - (b.info.files[0].url.endsWith(\".zip\") ? 0 : 100))\n\n  const updateChannelFileToInfo = new Map<string, UpdateInfoFileTask>()\n  for (const task of updateInfoFileTasks) {\n    // https://github.com/electron-userland/electron-builder/pull/2994\n    const key = `${task.file}@${safeStringifyJson(task.publishConfiguration, new Set([\"releaseType\"]))}`\n    const existingTask = updateChannelFileToInfo.get(key)\n    if (existingTask == null) {\n      updateChannelFileToInfo.set(key, task)\n      continue\n    }\n\n    existingTask.info.files.push(...task.info.files)\n  }\n\n  const releaseDate = new Date().toISOString()\n  await BluebirdPromise.map(updateChannelFileToInfo.values(), async task => {\n    const publishConfig = task.publishConfiguration\n    if (publishConfig.publishAutoUpdate === false) {\n      log.debug({\n        provider: publishConfig.provider,\n        reason: \"publishAutoUpdate is set to false\"\n      }, \"auto update metadata file not published\")\n      return\n    }\n\n    if (task.info.releaseDate == null) {\n      task.info.releaseDate = releaseDate\n    }\n\n    const fileContent = Buffer.from(serializeToYaml(task.info, false, true))\n    await outputFile(task.file, fileContent)\n    packager.dispatchArtifactCreated({\n      file: task.file,\n      fileContent,\n      arch: null,\n      packager: task.packager,\n      target: null,\n      publishConfig,\n    })\n  }, {concurrency: 4})\n}\n\n// backward compatibility - write json file\nasync function writeOldMacInfo(publishConfig: PublishConfiguration, outDir: string, dir: string, channel: string, createdFiles: Set<string>, version: string, packager: PlatformPackager<any>) {\n  const isGitHub = publishConfig.provider === \"github\"\n  const updateInfoFile = (isGitHub && outDir === dir) ? path.join(dir, \"github\", `${channel}-mac.json`) : path.join(dir, `${channel}-mac.json`)\n  if (!createdFiles.has(updateInfoFile)) {\n    createdFiles.add(updateInfoFile)\n    await outputJson(updateInfoFile, {\n      version,\n      releaseDate: new Date().toISOString(),\n      url: computeDownloadUrl(publishConfig, packager.generateName2(\"zip\", \"mac\", isGitHub), packager),\n    }, {spaces: 2})\n\n    packager.info.dispatchArtifactCreated({\n      file: updateInfoFile,\n      arch: null,\n      packager,\n      target: null,\n      publishConfig,\n    })\n  }\n}\n"], "sourceRoot": ""}