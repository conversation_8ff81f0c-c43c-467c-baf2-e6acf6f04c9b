{"version": 3, "sources": ["../../src/publish/BintrayPublisher.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEM,MAAO,gBAAP,SAAgC,gCAAhC,CAA6C;AAOjD,EAAA,WAAA,CAAY,OAAZ,EAAqC,IAArC,EAA4E,OAA5E,EAA8G,OAAA,GAA0B,EAAxI,EAA0I;AACxI,UAAM,OAAN;AAD0E,SAAA,OAAA,GAAA,OAAA;AAAkC,SAAA,OAAA,GAAA,OAAA;AAN7F,SAAA,eAAA,GAAkB,KAAI,eAAJ,EAAS,MAAM,KAAK,IAAL,EAAf,CAAlB;AAIR,SAAA,YAAA,GAAe,SAAf;AAKP,QAAI,KAAK,GAAG,IAAI,CAAC,KAAjB;;AACA,QAAI,oCAAgB,KAAhB,CAAJ,EAA4B;AAC1B,MAAA,KAAK,GAAG,OAAO,CAAC,GAAR,CAAY,QAApB;;AACA,UAAI,oCAAgB,KAAhB,CAAJ,EAA4B;AAC1B,cAAM,KAAI,wCAAJ,EAA8B,oJAA9B,CAAN;AACD;;AAED,MAAA,KAAK,GAAG,KAAK,CAAC,IAAN,EAAR;;AAEA,UAAI,CAAC,qCAAiB,KAAjB,CAAL,EAA8B;AAC5B,cAAM,KAAI,wCAAJ,EAA8B,kBAAkB,IAAI,CAAC,SAAL,CAAe,KAAf,CAAqB,4DAArE,CAAN;AACD;AACF;;AAED,SAAK,MAAL,GAAc,KAAI,wBAAJ,EAAkB,IAAlB,EAAwB,gCAAxB,EAAsC,KAAK,OAAL,CAAa,iBAAnD,EAAsE,KAAtE,CAAd;AACD;;AAEO,QAAM,IAAN,GAAU;AAChB,QAAI;AACF,aAAO,MAAM,KAAK,MAAL,CAAY,UAAZ,CAAuB,KAAK,OAA5B,CAAb;AACD,KAFD,CAGA,OAAO,CAAP,EAAU;AACR,UAAI,CAAC,YAAY,+BAAb,IAA0B,CAAC,CAAC,UAAF,KAAiB,GAA/C,EAAoD;AAClD,YAAI,KAAK,OAAL,CAAa,OAAb,KAAyB,cAA7B,EAA6C;AAC3C,6BAAI,IAAJ,CAAS;AAAC,YAAA,OAAO,EAAE,KAAK;AAAf,WAAT,EAAkC,qCAAlC;;AACA,iBAAO,MAAM,KAAK,MAAL,CAAY,aAAZ,CAA0B,KAAK,OAA/B,CAAb;AACD,SAHD,MAIK;AACH,6BAAI,IAAJ,CAAS;AAAC,YAAA,MAAM,EAAE,uBAAT;AAAkC,YAAA,OAAO,EAAE,KAAK;AAAhD,WAAT,EAAmE,oBAAnE;AACD;AACF;;AAED,YAAM,CAAN;AACD;AACF;;AAES,QAAM,QAAN,CAAe,QAAf,EAAiC,IAAjC,EAA6C,UAA7C,EAAiE,gBAAjE,EAAmJ;AAC3J,UAAM,OAAO,GAAG,MAAM,KAAK,eAAL,CAAqB,KAA3C;;AACA,QAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,yBAAI,IAAJ,CAAS;AAAC,QAAA,IAAI,EAAE,QAAP;AAAiB,QAAA,MAAM,EAAE,0CAAzB;AAAqE,QAAA,OAAO,EAAE,KAAK;AAAnF,OAAT,EAAsG,oBAAtG;;AACA;AACD;;AAED,UAAM,OAAO,GAAmB;AAC9B,MAAA,QAAQ,EAAE,iBADoB;AAE9B,MAAA,IAAI,EAAE,YAAY,KAAK,MAAL,CAAY,KAAK,IAAI,KAAK,MAAL,CAAY,IAAI,IAAI,KAAK,MAAL,CAAY,WAAW,IAAI,SAAS,CAAC,GAAG,OAAO,CAAC,IAAI,IAAI,QAAQ,EAA5B,CAA+B,EAFhG;AAG9B,MAAA,MAAM,EAAE,KAHsB;AAI9B,MAAA,OAAO,EAAE;AACP,0BAAkB,UADX;AAEP,8BAAsB,GAFf;AAGP,6BAAqB,GAHd;AAIP,yCAAiC,sCAAkB,IAAlB,EAAwB,KAAxB;AAJ1B;AAJqB,KAAhC;;AAYA,QAAI,KAAK,MAAL,CAAY,YAAZ,IAA4B,IAAhC,EAAsC;AACpC,MAAA,OAAO,CAAC,OAAR,CAAkB,+BAAlB,IAAqD,KAAK,MAAL,CAAY,YAAjE;AACD;;AAED,QAAI,KAAK,MAAL,CAAY,SAAZ,IAAyB,IAA7B,EAAmC;AACjC,MAAA,OAAO,CAAC,OAAR,CAAkB,4BAAlB,IAAkD,KAAK,MAAL,CAAY,SAA9D;AACD;;AAED,SAAK,IAAI,aAAa,GAAG,CAAzB,GAA8B,aAAa,EAA3C,EAA+C;AAC7C,UAAI;AACF,eAAO,MAAM,iCAAa,YAAb,CAA0B,mDAAwB,OAAxB,EAAiC,KAAK,MAAL,CAAY,IAA7C,CAA1B,EAA8E,KAAK,OAAL,CAAa,iBAA3F,EAA8G,gBAA9G,CAAb;AACD,OAFD,CAGA,OAAO,CAAP,EAAU;AACR,YAAI,aAAa,GAAG,CAAhB,KAAuB,CAAC,YAAY,+BAAb,IAA0B,CAAC,CAAC,UAAF,KAAiB,GAA5C,IAAoD,CAAC,CAAC,IAAF,KAAW,OAArF,CAAJ,EAAmG;AACjG;AACD;;AAED,cAAM,CAAN;AACD;AACF;AACF,GArFgD,CAuFjD;;;AACA,QAAM,aAAN,CAAoB,OAAO,GAAG,KAA9B,EAAmC;AACjC,QAAI,CAAC,OAAD,IAAY,CAAC,KAAK,eAAL,CAAqB,QAAtC,EAAgD;AAC9C;AACD;;AAED,UAAM,OAAO,GAAI,MAAM,KAAK,eAAL,CAAqB,KAA5C;;AACA,QAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,YAAM,KAAK,MAAL,CAAY,aAAZ,CAA0B,OAAO,CAAC,IAAlC,CAAN;AACD;AACF;;AAED,EAAA,QAAQ,GAAA;AACN,WAAO,kBAAkB,KAAK,MAAL,CAAY,IAAZ,IAAoB,KAAK,MAAL,CAAY,KAAK,YAAY,KAAK,MAAL,CAAY,KAAK,eAAe,KAAK,MAAL,CAAY,WAAW,iBAAiB,KAAK,MAAL,CAAY,IAAI,cAAc,KAAK,OAAO,GAA5L;AACD;;AArGgD,C", "sourcesContent": ["import { Arch, InvalidConfigurationError, isEmptyOrSpaces, isTokenCharValid, log, toLinuxArchString } from \"builder-util\"\nimport { BintrayOptions, configureRequestOptions, HttpError } from \"builder-util-runtime\"\nimport { BintrayClient, Version } from \"builder-util-runtime/out/bintray\"\nimport { httpExecutor } from \"builder-util/out/nodeHttpExecutor\"\nimport { ClientRequest, RequestOptions } from \"http\"\nimport { Lazy } from \"lazy-val\"\nimport { HttpPublisher, PublishContext, PublishOptions } from \"electron-publish\"\n\nexport class BintrayPublisher extends HttpPublisher {\n  private readonly _versionPromise = new Lazy(() => this.init())\n\n  private readonly client: BintrayClient\n\n  readonly providerName = \"Bintray\"\n\n  constructor(context: PublishContext, info: BintrayOptions, private readonly version: string, private readonly options: PublishOptions = {}) {\n    super(context)\n\n    let token = info.token\n    if (isEmptyOrSpaces(token)) {\n      token = process.env.BT_TOKEN\n      if (isEmptyOrSpaces(token)) {\n        throw new InvalidConfigurationError(`Bintray token is not set, neither programmatically, nor using env \"BT_TOKEN\" (see https://www.electron.build/configuration/publish#bintrayoptions)`)\n      }\n\n      token = token.trim()\n\n      if (!isTokenCharValid(token)) {\n        throw new InvalidConfigurationError(`Bintray token (${JSON.stringify(token)}) contains invalid characters, please check env \"BT_TOKEN\"`)\n      }\n    }\n\n    this.client = new BintrayClient(info, httpExecutor, this.context.cancellationToken, token)\n  }\n\n  private async init(): Promise<Version | null> {\n    try {\n      return await this.client.getVersion(this.version)\n    }\n    catch (e) {\n      if (e instanceof HttpError && e.statusCode === 404) {\n        if (this.options.publish !== \"onTagOrDraft\") {\n          log.info({version: this.version}, \"version doesn't exist, creating one\")\n          return await this.client.createVersion(this.version)\n        }\n        else {\n          log.warn({reason: \"version doesn't exist\", version: this.version}, \"skipped publishing\")\n        }\n      }\n\n      throw e\n    }\n  }\n\n  protected async doUpload(fileName: string, arch: Arch, dataLength: number, requestProcessor: (request: ClientRequest, reject: (error: Error) => void) => void) {\n    const version = await this._versionPromise.value\n    if (version == null) {\n      log.warn({file: fileName, reason: \"version doesn't exist and is not created\", version: this.version}, \"skipped publishing\")\n      return\n    }\n\n    const options: RequestOptions = {\n      hostname: \"api.bintray.com\",\n      path: `/content/${this.client.owner}/${this.client.repo}/${this.client.packageName}/${encodeURI(`${version.name}/${fileName}`)}`,\n      method: \"PUT\",\n      headers: {\n        \"Content-Length\": dataLength,\n        \"X-Bintray-Override\": \"1\",\n        \"X-Bintray-Publish\": \"1\",\n        \"X-Bintray-Debian-Architecture\": toLinuxArchString(arch, \"deb\")\n      }\n    }\n\n    if (this.client.distribution != null) {\n      options.headers!![\"X-Bintray-Debian-Distribution\"] = this.client.distribution\n    }\n\n    if (this.client.component != null) {\n      options.headers!![\"X-Bintray-Debian-Component\"] = this.client.component\n    }\n\n    for (let attemptNumber = 0; ; attemptNumber++) {\n      try {\n        return await httpExecutor.doApiRequest(configureRequestOptions(options, this.client.auth), this.context.cancellationToken, requestProcessor)\n      }\n      catch (e) {\n        if (attemptNumber < 3 && ((e instanceof HttpError && e.statusCode === 502) || e.code === \"EPIPE\")) {\n          continue\n        }\n\n        throw e\n      }\n    }\n  }\n\n  //noinspection JSUnusedGlobalSymbols\n  async deleteRelease(isForce = false): Promise<void> {\n    if (!isForce && !this._versionPromise.hasValue) {\n      return\n    }\n\n    const version = (await this._versionPromise.value)\n    if (version != null) {\n      await this.client.deleteVersion(version.name)\n    }\n  }\n\n  toString() {\n    return `Bintray (user: ${this.client.user || this.client.owner}, owner: ${this.client.owner},  package: ${this.client.packageName}, repository: ${this.client.repo}, version: ${this.version})`\n  }\n}"], "sourceRoot": ""}