{"version": 3, "sources": ["../../../src/publish/s3/spacesPublisher.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAGA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEc,MAAO,eAAP,SAA+B,kCAA/B,CAA8C;AAG1D,EAAA,WAAA,CAAY,OAAZ,EAAsD,IAAtD,EAAyE;AACvE,UAAM,OAAN,EAAe,IAAf;AADoD,SAAA,IAAA,GAAA,IAAA;AAF7C,SAAA,YAAA,GAAe,QAAf;AAIR,GALyD,CAO1D;;;AACA,eAAa,sBAAb,CAAoC,OAApC,EAA4D,qBAA5D,EAAkG,aAAlG,EAAwH;AACtH,QAAI,OAAO,CAAC,IAAR,IAAgB,IAApB,EAA0B;AACxB,YAAM,KAAI,wCAAJ,EAA8B,0HAA9B,CAAN;AACD;;AACD,QAAI,OAAO,CAAC,MAAR,IAAkB,IAAtB,EAA4B;AAC1B,YAAM,KAAI,wCAAJ,EAA8B,4HAA9B,CAAN;AACD;;AAED,QAAI,OAAO,CAAC,OAAR,IAAmB,IAAnB,IAA2B,qBAAqB,IAAI,IAAxD,EAA8D;AAC5D,MAAA,OAAO,CAAC,OAAR,GAAkB,qBAAlB;AACD;AACF;;AAES,EAAA,aAAa,GAAA;AACrB,WAAO,KAAK,IAAL,CAAU,IAAjB;AACD;;AAES,EAAA,kBAAkB,CAAC,IAAD,EAAoB;AAC9C,UAAM,kBAAN,CAAyB,IAAzB;AAEA,IAAA,IAAI,CAAC,IAAL,CAAU,YAAV,EAAwB,GAAG,KAAK,IAAL,CAAU,MAAM,yBAA3C;AACA,IAAA,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,KAAK,IAAL,CAAU,MAAhC;AAEA,UAAM,SAAS,GAAG,OAAO,CAAC,GAAR,CAAY,SAA9B;AACA,UAAM,SAAS,GAAG,OAAO,CAAC,GAAR,CAAY,aAA9B;;AACA,QAAI,oCAAgB,SAAhB,CAAJ,EAAgC;AAC9B,YAAM,KAAI,wCAAJ,EAA8B,+FAA9B,CAAN;AACD;;AACD,QAAI,oCAAgB,SAAhB,CAAJ,EAAgC;AAC9B,YAAM,KAAI,wCAAJ,EAA8B,mGAA9B,CAAN;AACD;;AACD,IAAA,IAAI,CAAC,IAAL,CAAU,aAAV,EAAyB,SAAzB;AACA,IAAA,IAAI,CAAC,IAAL,CAAU,aAAV,EAAyB,SAAzB;AACD;;AAzCyD,C", "sourcesContent": ["import { InvalidConfigurationError, isEmptyOrSpaces } from \"builder-util\"\nimport { SpacesOptions } from \"builder-util-runtime\"\nimport { PublishContext } from \"electron-publish\"\nimport { BaseS3Publisher } from \"./BaseS3Publisher\"\n\nexport default class SpacesPublisher extends BaseS3Publisher {\n  readonly providerName = \"Spaces\"\n\n  constructor(context: PublishContext, private readonly info: SpacesOptions) {\n    super(context, info)\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  static async checkAndResolveOptions(options: SpacesOptions, channelFromAppVersion: string | null, errorIfCannot: boolean) {\n    if (options.name == null) {\n      throw new InvalidConfigurationError(`Please specify \"name\" for \"spaces\" publish provider (see https://www.electron.build/configuration/publish#spacesoptions)`)\n    }\n    if (options.region == null) {\n      throw new InvalidConfigurationError(`Please specify \"region\" for \"spaces\" publish provider (see https://www.electron.build/configuration/publish#spacesoptions)`)\n    }\n\n    if (options.channel == null && channelFromAppVersion != null) {\n      options.channel = channelFromAppVersion\n    }\n  }\n\n  protected getBucketName(): string {\n    return this.info.name\n  }\n\n  protected configureS3Options(args: Array<string>): void {\n    super.configureS3Options(args)\n\n    args.push(\"--endpoint\", `${this.info.region}.digitaloceanspaces.com`)\n    args.push(\"--region\", this.info.region)\n\n    const accessKey = process.env.DO_KEY_ID\n    const secretKey = process.env.DO_SECRET_KEY\n    if (isEmptyOrSpaces(accessKey)) {\n      throw new InvalidConfigurationError(\"Please set env DO_KEY_ID (see https://www.electron.build/configuration/publish#spacesoptions)\")\n    }\n    if (isEmptyOrSpaces(secretKey)) {\n      throw new InvalidConfigurationError(\"Please set env DO_SECRET_KEY (see https://www.electron.build/configuration/publish#spacesoptions)\")\n    }\n    args.push(\"--accessKey\", accessKey)\n    args.push(\"--secretKey\", secretKey)\n  }\n}"], "sourceRoot": ""}