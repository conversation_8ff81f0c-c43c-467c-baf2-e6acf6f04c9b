{"version": 3, "sources": ["../../../src/publish/s3/BaseS3Publisher.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;;;;;AAEM,MAAgB,eAAhB,SAAwC,4BAAxC,CAAiD;AACrD,EAAA,WAAA,CAAsB,OAAtB,EAAuD,OAAvD,EAA6E;AAC3E,UAAM,OAAN;AADqD,SAAA,OAAA,GAAA,OAAA;AAEtD;;AAIS,EAAA,kBAAkB,CAAC,IAAD,EAAoB;AAC9C;AACA,QAAI,KAAK,OAAL,CAAa,GAAb,KAAqB,IAAzB,EAA+B;AAC7B,MAAA,IAAI,CAAC,IAAL,CAAU,OAAV,EAAmB,KAAK,OAAL,CAAa,GAAb,IAAoB,aAAvC;AACD;AACF,GAZoD,CAcrD;;;AACA,QAAM,MAAN,CAAa,IAAb,EAA6B;AAC3B,UAAM,QAAQ,GAAG,IAAI,CAAC,QAAL,CAAc,IAAI,CAAC,IAAnB,CAAjB;AACA,UAAM,iBAAiB,GAAG,KAAK,OAAL,CAAa,iBAAvC;AAEA,UAAM,MAAM,GAAG,CAAC,KAAK,OAAL,CAAa,IAAb,IAAqB,IAArB,GAA4B,EAA5B,GAAiC,GAAG,KAAK,OAAL,CAAa,IAAI,GAAtD,IAA6D,QAA5E;AAEA,UAAM,IAAI,GAAG,CAAC,YAAD,EAAe,UAAf,EAA2B,KAAK,aAAL,EAA3B,EAAiD,OAAjD,EAA0D,MAA1D,EAAkE,QAAlE,EAA4E,IAAI,CAAC,IAAjF,CAAb;AACA,SAAK,kBAAL,CAAwB,IAAxB;;AAEA,QAAI,OAAO,CAAC,GAAR,CAAY,qBAAZ,IAAqC,IAAzC,EAA+C;AAC7C,YAAM,QAAQ,GAAG,IAAI,CAAC,IAAL,CAAU,OAAO,CAAC,GAAR,CAAY,qBAAtB,EAA8C,MAA9C,CAAjB;AACA,YAAM,0BAAU,IAAI,CAAC,OAAL,CAAa,QAAb,CAAV,CAAN;AACA,YAAM,wBAAQ,IAAI,CAAC,IAAb,EAAmB,QAAnB,CAAN;AACA;AACD,KAd0B,CAgB3B;;;AACA,SAAK,iBAAL,CAAuB,QAAvB,EAAiC,CAAC,CAAlC,EAjB2B,CAkB3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,WAAO,MAAM,iBAAiB,CAAC,aAAlB,CAAgC,CAAC,OAAD,EAAU,MAAV,EAAkB,QAAlB,KAA8B;AACzE,4CAAkB,IAAlB,EAAwB,OAAO,IAAG;AAChC,QAAA,QAAQ,CAAC,MAAK;AACZ,UAAA,OAAO,CAAC,IAAR,CAAa,QAAb;AACD,SAFO,CAAR;AAGD,OAJD,EAKG,IALH,CAKQ,MAAK;AACT,YAAI;AACF,6BAAI,KAAJ,CAAU;AAAC,YAAA,QAAQ,EAAE,KAAK,YAAhB;AAA8B,YAAA,IAAI,EAAE,QAApC;AAA8C,YAAA,MAAM,EAAE,KAAK,aAAL;AAAtD,WAAV,EAAuF,UAAvF;AACD,SAFD,SAGQ;AACN,UAAA,OAAO,CAAC,SAAD,CAAP;AACD;AACF,OAZH,EAaG,KAbH,CAaS,MAbT;AAcD,KAfY,CAAb;AAgBD;;AAED,EAAA,QAAQ,GAAA;AACN,WAAO,GAAG,KAAK,YAAY,aAAa,KAAK,aAAL,EAAoB,GAA5D;AACD;;AA9DoD,C", "sourcesContent": ["import { log, executeAppBuilder } from \"builder-util\"\nimport { BaseS3Options } from \"builder-util-runtime\"\nimport { PublishContext, Publisher, UploadTask } from \"electron-publish\"\nimport { ensureDir, symlink } from \"fs-extra\"\nimport * as path from \"path\"\n\nexport abstract class BaseS3Publisher extends Publisher {\n  protected constructor(context: PublishContext, private options: BaseS3Options) {\n    super(context)\n  }\n\n  protected abstract getBucketName(): string\n\n  protected configureS3Options(args: Array<string>) {\n    // if explicitly set to null, do not add\n    if (this.options.acl !== null) {\n      args.push(\"--acl\", this.options.acl || \"public-read\")\n    }\n  }\n\n  // http://docs.aws.amazon.com/sdk-for-javascript/v2/developer-guide/s3-example-creating-buckets.html\n  async upload(task: UploadTask): Promise<any> {\n    const fileName = path.basename(task.file)\n    const cancellationToken = this.context.cancellationToken\n\n    const target = (this.options.path == null ? \"\" : `${this.options.path}/`) + fileName\n\n    const args = [\"publish-s3\", \"--bucket\", this.getBucketName(), \"--key\", target, \"--file\", task.file]\n    this.configureS3Options(args)\n\n    if (process.env.__TEST_S3_PUBLISHER__ != null) {\n      const testFile = path.join(process.env.__TEST_S3_PUBLISHER__!, target)\n      await ensureDir(path.dirname(testFile))\n      await symlink(task.file, testFile)\n      return\n    }\n\n    // https://github.com/aws/aws-sdk-go/issues/279\n    this.createProgressBar(fileName, -1)\n    // if (progressBar != null) {\n    //   const callback = new ProgressCallback(progressBar)\n    //   uploader.on(\"progress\", () => {\n    //     if (!cancellationToken.cancelled) {\n    //       callback.update(uploader.loaded, uploader.contentLength)\n    //     }\n    //   })\n    // }\n\n    return await cancellationToken.createPromise((resolve, reject, onCancel) => {\n      executeAppBuilder(args, process => {\n        onCancel(() => {\n          process.kill(\"SIGINT\")\n        })\n      })\n        .then(() => {\n          try {\n            log.debug({provider: this.providerName, file: fileName, bucket: this.getBucketName()}, \"uploaded\")\n          }\n          finally {\n            resolve(undefined)\n          }\n        })\n        .catch(reject)\n    })\n  }\n\n  toString() {\n    return `${this.providerName} (bucket: ${this.getBucketName()})`\n  }\n}\n"], "sourceRoot": ""}