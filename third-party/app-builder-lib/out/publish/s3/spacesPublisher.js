"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;

function _builderUtil() {
  const data = require("builder-util");

  _builderUtil = function () {
    return data;
  };

  return data;
}

function _BaseS3Publisher() {
  const data = require("./BaseS3Publisher");

  _BaseS3Publisher = function () {
    return data;
  };

  return data;
}

class SpacesPublisher extends _BaseS3Publisher().BaseS3Publisher {
  constructor(context, info) {
    super(context, info);
    this.info = info;
    this.providerName = "Spaces";
  } // eslint-disable-next-line @typescript-eslint/no-unused-vars


  static async checkAndResolveOptions(options, channelFromAppVersion, errorIfCannot) {
    if (options.name == null) {
      throw new (_builderUtil().InvalidConfigurationError)(`Please specify "name" for "spaces" publish provider (see https://www.electron.build/configuration/publish#spacesoptions)`);
    }

    if (options.region == null) {
      throw new (_builderUtil().InvalidConfigurationError)(`Please specify "region" for "spaces" publish provider (see https://www.electron.build/configuration/publish#spacesoptions)`);
    }

    if (options.channel == null && channelFromAppVersion != null) {
      options.channel = channelFromAppVersion;
    }
  }

  getBucketName() {
    return this.info.name;
  }

  configureS3Options(args) {
    super.configureS3Options(args);
    args.push("--endpoint", `${this.info.region}.digitaloceanspaces.com`);
    args.push("--region", this.info.region);
    const accessKey = process.env.DO_KEY_ID;
    const secretKey = process.env.DO_SECRET_KEY;

    if ((0, _builderUtil().isEmptyOrSpaces)(accessKey)) {
      throw new (_builderUtil().InvalidConfigurationError)("Please set env DO_KEY_ID (see https://www.electron.build/configuration/publish#spacesoptions)");
    }

    if ((0, _builderUtil().isEmptyOrSpaces)(secretKey)) {
      throw new (_builderUtil().InvalidConfigurationError)("Please set env DO_SECRET_KEY (see https://www.electron.build/configuration/publish#spacesoptions)");
    }

    args.push("--accessKey", accessKey);
    args.push("--secretKey", secretKey);
  }

} exports.default = SpacesPublisher;
// __ts-babel@6.0.4
//# sourceMappingURL=spacesPublisher.js.map