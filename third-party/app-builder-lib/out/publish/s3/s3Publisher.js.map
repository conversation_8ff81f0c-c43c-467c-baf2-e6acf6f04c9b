{"version": 3, "sources": ["../../../src/publish/s3/s3Publisher.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAGA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEc,MAAO,WAAP,SAA2B,kCAA3B,CAA0C;AAGtD,EAAA,WAAA,CAAY,OAAZ,EAAsD,IAAtD,EAAqE;AACnE,UAAM,OAAN,EAAe,IAAf;AADoD,SAAA,IAAA,GAAA,IAAA;AAF7C,SAAA,YAAA,GAAe,IAAf;AAIR;;AAED,eAAa,sBAAb,CAAoC,OAApC,EAAwD,qBAAxD,EAA8F,aAA9F,EAAoH;AAClH,UAAM,MAAM,GAAG,OAAO,CAAC,MAAvB;;AACA,QAAI,MAAM,IAAI,IAAd,EAAoB;AAClB,YAAM,KAAI,wCAAJ,EAA8B,mDAA9B,CAAN;AACD;;AAED,QAAI,OAAO,CAAC,QAAR,IAAoB,IAApB,IAA6B,MAAM,CAAC,QAAP,CAAgB,GAAhB,KAAwB,OAAO,CAAC,MAAR,IAAkB,IAA3E,EAAkF;AAChF;AACA,UAAI;AACF,QAAA,OAAO,CAAC,MAAR,GAAiB,MAAM,sCAAkB,CAAC,qBAAD,EAAwB,UAAxB,EAAoC,MAApC,CAAlB,CAAvB;AACD,OAFD,CAGA,OAAO,CAAP,EAAU;AACR,YAAI,aAAJ,EAAmB;AACjB,gBAAM,CAAN;AACD,SAFD,MAGK;AACH,6BAAI,IAAJ,CAAS,yHAAyH,CAAC,EAAnI;AACD;AACF;AACF;;AAED,QAAI,OAAO,CAAC,OAAR,IAAmB,IAAnB,IAA2B,qBAAqB,IAAI,IAAxD,EAA8D;AAC5D,MAAA,OAAO,CAAC,OAAR,GAAkB,qBAAlB;AACD;;AAED,QAAI,OAAO,CAAC,QAAR,IAAoB,IAApB,IAA4B,OAAO,CAAC,QAAR,CAAiB,QAAjB,CAA0B,GAA1B,CAAhC,EAAgE;AAC7D,MAAA,OAAe,CAAC,QAAhB,GAA2B,OAAO,CAAC,QAAR,CAAiB,KAAjB,CAAuB,CAAvB,EAA0B,CAAC,CAA3B,CAA3B;AACF;AACF;;AAES,EAAA,aAAa,GAAA;AACrB,WAAO,KAAK,IAAL,CAAU,MAAjB;AACD;;AAES,EAAA,kBAAkB,CAAC,IAAD,EAAoB;AAC9C,UAAM,kBAAN,CAAyB,IAAzB;;AAEA,QAAI,KAAK,IAAL,CAAU,QAAV,IAAsB,IAA1B,EAAgC;AAC9B,MAAA,IAAI,CAAC,IAAL,CAAU,YAAV,EAAwB,KAAK,IAAL,CAAU,QAAlC;AACD;;AACD,QAAI,KAAK,IAAL,CAAU,MAAV,IAAoB,IAAxB,EAA8B;AAC5B,MAAA,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,KAAK,IAAL,CAAU,MAAhC;AACD;;AAED,QAAI,KAAK,IAAL,CAAU,YAAV,IAA0B,IAA9B,EAAoC;AAClC,MAAA,IAAI,CAAC,IAAL,CAAU,gBAAV,EAA4B,KAAK,IAAL,CAAU,YAAtC;AACD;;AACD,QAAI,KAAK,IAAL,CAAU,UAAV,IAAwB,IAA5B,EAAkC;AAChC,MAAA,IAAI,CAAC,IAAL,CAAU,cAAV,EAA0B,KAAK,IAAL,CAAU,UAApC;AACD;AACF;;AAED,EAAA,QAAQ,GAAA;AACN,UAAM,MAAM,GAAG,MAAM,QAAN,EAAf;AACA,UAAM,QAAQ,GAAG,KAAK,IAAL,CAAU,QAA3B;;AACA,QAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,aAAO,MAAM,CAAC,SAAP,CAAiB,CAAjB,EAAoB,MAAM,CAAC,MAAP,GAAgB,CAApC,IAAyC,eAAe,QAAQ,GAAvE;AACD;;AACD,WAAO,MAAP;AACD;;AAlEqD,C", "sourcesContent": ["import { executeApp<PERSON>uilder, InvalidConfigurationError, log } from \"builder-util\"\nimport { S3Options } from \"builder-util-runtime\"\nimport { PublishContext } from \"electron-publish\"\nimport { BaseS3Publisher } from \"./BaseS3Publisher\"\n\nexport default class S3Publisher extends BaseS3Publisher {\n  readonly providerName = \"S3\"\n\n  constructor(context: PublishContext, private readonly info: S3Options) {\n    super(context, info)\n  }\n\n  static async checkAndResolveOptions(options: S3Options, channelFromAppVersion: string | null, errorIfCannot: boolean) {\n    const bucket = options.bucket\n    if (bucket == null) {\n      throw new InvalidConfigurationError(`Please specify \"bucket\" for \"s3\" publish provider`)\n    }\n\n    if (options.endpoint == null && (bucket.includes(\".\") && options.region == null)) {\n      // on dotted bucket names, we need to use a path-based endpoint URL. Path-based endpoint URLs need to include the region.\n      try {\n        options.region = await executeAppBuilder([\"get-bucket-location\", \"--bucket\", bucket])\n      }\n      catch (e) {\n        if (errorIfCannot) {\n          throw e\n        }\n        else {\n          log.warn(`cannot compute region for bucket (required because on dotted bucket names, we need to use a path-based endpoint URL): ${e}`)\n        }\n      }\n    }\n\n    if (options.channel == null && channelFromAppVersion != null) {\n      options.channel = channelFromAppVersion\n    }\n\n    if (options.endpoint != null && options.endpoint.endsWith(\"/\")) {\n      (options as any).endpoint = options.endpoint.slice(0, -1)\n    }\n  }\n\n  protected getBucketName(): string {\n    return this.info.bucket!\n  }\n\n  protected configureS3Options(args: Array<string>): void {\n    super.configureS3Options(args)\n\n    if (this.info.endpoint != null) {\n      args.push(\"--endpoint\", this.info.endpoint)\n    }\n    if (this.info.region != null) {\n      args.push(\"--region\", this.info.region)\n    }\n\n    if (this.info.storageClass != null) {\n      args.push(\"--storageClass\", this.info.storageClass)\n    }\n    if (this.info.encryption != null) {\n      args.push(\"--encryption\", this.info.encryption)\n    }\n  }\n\n  toString() {\n    const result = super.toString()\n    const endpoint = this.info.endpoint\n    if (endpoint != null) {\n      return result.substring(0, result.length - 1) + `, endpoint: ${endpoint})`\n    }\n    return result\n  }\n}\n"], "sourceRoot": ""}