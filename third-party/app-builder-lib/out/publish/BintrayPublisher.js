"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.BintrayPublisher = void 0;

function _builderUtil() {
  const data = require("builder-util");

  _builderUtil = function () {
    return data;
  };

  return data;
}

function _builderUtilRuntime() {
  const data = require("builder-util-runtime");

  _builderUtilRuntime = function () {
    return data;
  };

  return data;
}

function _bintray() {
  const data = require("builder-util-runtime/out/bintray");

  _bintray = function () {
    return data;
  };

  return data;
}

function _nodeHttpExecutor() {
  const data = require("builder-util/out/nodeHttpExecutor");

  _nodeHttpExecutor = function () {
    return data;
  };

  return data;
}

function _lazyVal() {
  const data = require("lazy-val");

  _lazyVal = function () {
    return data;
  };

  return data;
}

function _electronPublish() {
  const data = require("electron-publish");

  _electronPublish = function () {
    return data;
  };

  return data;
}

class BintrayPublisher extends _electronPublish().HttpPublisher {
  constructor(context, info, version, options = {}) {
    super(context);
    this.version = version;
    this.options = options;
    this._versionPromise = new (_lazyVal().Lazy)(() => this.init());
    this.providerName = "Bintray";
    let token = info.token;

    if ((0, _builderUtil().isEmptyOrSpaces)(token)) {
      token = process.env.BT_TOKEN;

      if ((0, _builderUtil().isEmptyOrSpaces)(token)) {
        throw new (_builderUtil().InvalidConfigurationError)(`Bintray token is not set, neither programmatically, nor using env "BT_TOKEN" (see https://www.electron.build/configuration/publish#bintrayoptions)`);
      }

      token = token.trim();

      if (!(0, _builderUtil().isTokenCharValid)(token)) {
        throw new (_builderUtil().InvalidConfigurationError)(`Bintray token (${JSON.stringify(token)}) contains invalid characters, please check env "BT_TOKEN"`);
      }
    }

    this.client = new (_bintray().BintrayClient)(info, _nodeHttpExecutor().httpExecutor, this.context.cancellationToken, token);
  }

  async init() {
    try {
      return await this.client.getVersion(this.version);
    } catch (e) {
      if (e instanceof _builderUtilRuntime().HttpError && e.statusCode === 404) {
        if (this.options.publish !== "onTagOrDraft") {
          _builderUtil().log.info({
            version: this.version
          }, "version doesn't exist, creating one");

          return await this.client.createVersion(this.version);
        } else {
          _builderUtil().log.warn({
            reason: "version doesn't exist",
            version: this.version
          }, "skipped publishing");
        }
      }

      throw e;
    }
  }

  async doUpload(fileName, arch, dataLength, requestProcessor) {
    const version = await this._versionPromise.value;

    if (version == null) {
      _builderUtil().log.warn({
        file: fileName,
        reason: "version doesn't exist and is not created",
        version: this.version
      }, "skipped publishing");

      return;
    }

    const options = {
      hostname: "api.bintray.com",
      path: `/content/${this.client.owner}/${this.client.repo}/${this.client.packageName}/${encodeURI(`${version.name}/${fileName}`)}`,
      method: "PUT",
      headers: {
        "Content-Length": dataLength,
        "X-Bintray-Override": "1",
        "X-Bintray-Publish": "1",
        "X-Bintray-Debian-Architecture": (0, _builderUtil().toLinuxArchString)(arch, "deb")
      }
    };

    if (this.client.distribution != null) {
      options.headers["X-Bintray-Debian-Distribution"] = this.client.distribution;
    }

    if (this.client.component != null) {
      options.headers["X-Bintray-Debian-Component"] = this.client.component;
    }

    for (let attemptNumber = 0;; attemptNumber++) {
      try {
        return await _nodeHttpExecutor().httpExecutor.doApiRequest((0, _builderUtilRuntime().configureRequestOptions)(options, this.client.auth), this.context.cancellationToken, requestProcessor);
      } catch (e) {
        if (attemptNumber < 3 && (e instanceof _builderUtilRuntime().HttpError && e.statusCode === 502 || e.code === "EPIPE")) {
          continue;
        }

        throw e;
      }
    }
  } //noinspection JSUnusedGlobalSymbols


  async deleteRelease(isForce = false) {
    if (!isForce && !this._versionPromise.hasValue) {
      return;
    }

    const version = await this._versionPromise.value;

    if (version != null) {
      await this.client.deleteVersion(version.name);
    }
  }

  toString() {
    return `Bintray (user: ${this.client.user || this.client.owner}, owner: ${this.client.owner},  package: ${this.client.packageName}, repository: ${this.client.repo}, version: ${this.version})`;
  }

} exports.BintrayPublisher = BintrayPublisher;
// __ts-babel@6.0.4
//# sourceMappingURL=BintrayPublisher.js.map