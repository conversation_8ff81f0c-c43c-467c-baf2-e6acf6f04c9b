{"version": 3, "sources": ["../../src/publish/SnapStorePublisher.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;;;;;AAGM,MAAO,kBAAP,SAAkC,4BAAlC,CAA2C;AAG/C,EAAA,WAAA,CAAY,OAAZ,EAA6C,OAA7C,EAAsE;AACpE,UAAM,OAAN;AAD2C,SAAA,OAAA,GAAA,OAAA;AAFpC,SAAA,YAAA,GAAe,WAAf;AAIR;;AAED,EAAA,MAAM,CAAC,IAAD,EAAiB;AACrB,SAAK,iBAAL,CAAuB,IAAI,CAAC,QAAL,CAAc,IAAI,CAAC,IAAnB,CAAvB,EAAiD,CAAC,CAAlD;AAEA,UAAM,IAAI,GAAG,CAAC,cAAD,EAAiB,IAAjB,EAAuB,IAAI,CAAC,IAA5B,CAAb;AAEA,QAAI,QAAQ,GAAG,KAAK,OAAL,CAAa,QAA5B;;AACA,QAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,MAAA,QAAQ,GAAG,CAAC,MAAD,CAAX;AACD,KAFD,MAGK;AACH,UAAI,OAAO,QAAP,KAAoB,QAAxB,EAAkC;AAChC,QAAA,QAAQ,GAAG,QAAQ,CAAC,KAAT,CAAe,GAAf,CAAX;AACD;AACF;;AAED,SAAK,MAAM,OAAX,IAAsB,QAAtB,EAAgC;AAC9B,MAAA,IAAI,CAAC,IAAL,CAAU,IAAV,EAAgB,OAAhB;AACD;;AAED,WAAO,sCAAkB,IAAlB,CAAP;AACD;;AAED,EAAA,QAAQ,GAAA;AACN,WAAO,YAAP;AACD;;AA/B8C,C", "sourcesContent": ["import { Publisher, UploadTask, PublishContext } from \"electron-publish\"\nimport { executeAppBuilder } from \"builder-util\"\nimport * as path from \"path\"\nimport { PublishConfiguration } from \"builder-util-runtime\"\n\nexport class SnapStorePublisher extends Publisher {\n  readonly providerName = \"snapStore\"\n\n  constructor(context: PublishContext, private options: SnapStoreOptions) {\n    super(context)\n  }\n\n  upload(task: UploadTask): Promise<any> {\n    this.createProgressBar(path.basename(task.file), -1)\n\n    const args = [\"publish-snap\", \"-f\", task.file]\n\n    let channels = this.options.channels\n    if (channels == null) {\n      channels = [\"edge\"]\n    }\n    else {\n      if (typeof channels === \"string\") {\n        channels = channels.split(\",\")\n      }\n    }\n\n    for (const channel of channels) {\n      args.push(\"-c\", channel)\n    }\n\n    return executeAppBuilder(args)\n  }\n\n  toString(): string {\n    return \"Snap Store\"\n  }\n}\n\n/**\n * [Snap Store](https://snapcraft.io/) options.\n */\nexport interface SnapStoreOptions extends PublishConfiguration {\n  /**\n   * The list of channels the snap would be released.\n   * @default [\"edge\"]\n   */\n  readonly channels?: string | Array<string> | null\n}"], "sourceRoot": ""}