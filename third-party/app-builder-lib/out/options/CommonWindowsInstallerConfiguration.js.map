{"version": 3, "sources": ["../../src/options/CommonWindowsInstallerConfiguration.ts"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AAqDM,SAAU,mBAAV,CAA8B,OAA9B,EAA4E,QAA5E,EAAiG;AACrG,QAAM,OAAO,GAAG,QAAQ,CAAC,OAAzB;AAEA,MAAI,YAAY,GAAkB,IAAlC;;AACA,MAAI,OAAO,CAAC,YAAR,IAAwB,IAAxB,IAAgC,OAAO,CAAC,YAAR,KAAyB,KAA7D,EAAoE;AAClE,QAAI,OAAO,CAAC,YAAR,KAAyB,IAA7B,EAAmC;AACjC,YAAM,WAAW,GAAG,QAAQ,CAAC,OAAT,CAAiB,WAArC;;AACA,UAAI,WAAW,IAAI,IAAnB,EAAyB;AACvB,cAAM,KAAI,wCAAJ,EAA8B,iHAA9B,CAAN;AACD;;AACD,MAAA,YAAY,GAAG,iCAAiB,WAAjB,CAAf;AACD,KAND,MAOK;AACH,MAAA,YAAY,GAAI,OAAO,CAAC,YAAR,CAAgC,KAAhC,CAAsC,OAAtC,EAA+C,GAA/C,CAAmD,EAAE,IAAI,iCAAiB,EAAjB,CAAzD,EAA+E,IAA/E,CAAoF,IAApF,CAAhB;AACD;AACF;;AAED,SAAO;AACL,IAAA,YAAY,EAAE,OAAO,CAAC,UAAR,KAAuB,IADhC;AAEL,IAAA,UAAU,EAAE,OAAO,CAAC,QAAR,KAAqB,KAF5B;AAIL,IAAA,YAAY,EAAE,oCAAgB,OAAO,CAAC,YAAxB,IAAwC,OAAO,CAAC,eAAhD,GAAkE,QAAQ,CAAC,WAAT,CAAqB,OAAO,CAAC,YAA7B,CAJ3E;AAKL,IAAA,uBAAuB,EAAE,sCAAsC,CAAC,OAAO,CAAC,qBAAT,CAL1D;AAML,IAAA,yBAAyB,EAAE,OAAO,CAAC,uBAAR,KAAoC,KAN1D;AAOL,IAAA;AAPK,GAAP;AASD;;AAED,SAAS,sCAAT,CAAgD,KAAhD,EAAmF;AACjF,MAAI,KAAK,KAAK,KAAd,EAAqB;AACnB,WAAO,6BAA6B,CAAC,KAArC;AACD,GAFD,MAGK,IAAI,KAAK,KAAK,QAAd,EAAwB;AAC3B,WAAO,6BAA6B,CAAC,MAArC;AACD,GAFI,MAGA;AACH,WAAO,6BAA6B,CAAC,aAArC;AACD;AACF;;AAED,IAAY,6BAAZ;;;AAAA,CAAA,UAAY,6BAAZ,EAAyC;AACvC,EAAA,6BAAA,CAAA,6BAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAA;AAAe,EAAA,6BAAA,CAAA,6BAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAA;AAAQ,EAAA,6BAAA,CAAA,6BAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAA;AACxB,CAFD,EAAY,6BAA6B,6CAA7B,6BAA6B,GAAA,EAAA,CAAzC,E", "sourcesContent": ["import { InvalidConfigurationError, isEmptyOrSpaces } from \"builder-util\"\nimport sanitizeFileName from \"sanitize-filename\"\nimport { WinPackager } from \"../winPackager\"\n\nexport interface CommonWindowsInstallerConfiguration {\n  readonly oneClick?: boolean\n\n  /**\n   * Whether to install per all users (per-machine).\n   * @default false\n   */\n  readonly perMachine?: boolean\n\n  /**\n   * Whether to run the installed application after finish. For assisted installer corresponding checkbox will be removed.\n   * @default true\n   */\n  readonly runAfterFinish?: boolean\n\n  /**\n   * Whether to create desktop shortcut. Set to `always` if to recreate also on reinstall (even if removed by user).\n   * @default true\n   */\n  readonly createDesktopShortcut?: boolean | \"always\"\n\n  /**\n   * Whether to create start menu shortcut.\n   * @default true\n   */\n  readonly createStartMenuShortcut?: boolean\n\n  /**\n   * Whether to create submenu for start menu shortcut and program files directory. If `true`, company name will be used. Or string value.\n   * @default false\n   */\n  readonly menuCategory?: boolean | string\n\n  /**\n   * The name that will be used for all shortcuts. Defaults to the application name.\n   */\n  readonly shortcutName?: string | null\n}\n\nexport interface FinalCommonWindowsInstallerOptions {\n  isAssisted: boolean\n  isPerMachine: boolean\n\n  shortcutName: string\n  menuCategory: string | null\n\n  isCreateDesktopShortcut: DesktopShortcutCreationPolicy\n  isCreateStartMenuShortcut: boolean\n}\n\nexport function getEffectiveOptions(options: CommonWindowsInstallerConfiguration, packager: WinPackager): FinalCommonWindowsInstallerOptions {\n  const appInfo = packager.appInfo\n\n  let menuCategory: string | null = null\n  if (options.menuCategory != null && options.menuCategory !== false) {\n    if (options.menuCategory === true) {\n      const companyName = packager.appInfo.companyName\n      if (companyName == null) {\n        throw new InvalidConfigurationError(`Please specify \"author\" in the application package.json — it is required because \"menuCategory\" is set to true.`)\n      }\n      menuCategory = sanitizeFileName(companyName)\n    }\n    else {\n      menuCategory = (options.menuCategory as string).split(/[/\\\\]/).map(it => sanitizeFileName(it)).join(\"\\\\\")\n    }\n  }\n\n  return {\n    isPerMachine: options.perMachine === true,\n    isAssisted: options.oneClick === false,\n\n    shortcutName: isEmptyOrSpaces(options.shortcutName) ? appInfo.productFilename : packager.expandMacro(options.shortcutName!!),\n    isCreateDesktopShortcut: convertToDesktopShortcutCreationPolicy(options.createDesktopShortcut),\n    isCreateStartMenuShortcut: options.createStartMenuShortcut !== false,\n    menuCategory,\n  }\n}\n\nfunction convertToDesktopShortcutCreationPolicy(value: boolean | undefined | string): DesktopShortcutCreationPolicy {\n  if (value === false) {\n    return DesktopShortcutCreationPolicy.NEVER\n  }\n  else if (value === \"always\") {\n    return DesktopShortcutCreationPolicy.ALWAYS\n  }\n  else {\n    return DesktopShortcutCreationPolicy.FRESH_INSTALL\n  }\n}\n\nexport enum DesktopShortcutCreationPolicy {\n  FRESH_INSTALL, ALWAYS, NEVER\n}"], "sourceRoot": ""}