{"version": 3, "sources": ["../src/binDownload.ts"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA,MAAM,gBAAgB,GAAG,IAAI,GAAJ,EAAzB;;AAEM,SAAU,QAAV,CAAmB,GAAnB,EAAgC,MAAhC,EAAgD,QAAhD,EAAwE;AAC5E,QAAM,IAAI,GAAG,CAAC,UAAD,EAAa,OAAb,EAAsB,GAAtB,EAA2B,UAA3B,EAAuC,MAAvC,CAAb;;AACA,MAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,IAAA,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,QAAtB;AACD;;AACD,SAAO,sCAAkB,IAAlB,CAAP;AACD;;AAEK,SAAU,aAAV,CAAwB,IAAxB,EAAsC,OAAtC,EAAuD,QAAvD,EAAuE;AAC3E,QAAM,OAAO,GAAG,GAAG,IAAI,IAAI,OAAO,EAAlC;AACA,MAAI,GAAJ;;AACA,MAAI,OAAO,CAAC,GAAR,CAAY,+CAAhB,EAAiE;AAC/D,IAAA,GAAG,GAAG,OAAO,CAAC,GAAR,CAAY,+CAAZ,GAA8D,GAA9D,GAAoE,OAApE,GAA8E,KAApF;AACD,GAFD,MAGK;AAEH,UAAM,OAAO,GAAG,OAAO,CAAC,GAAR,CAAY,2CAAZ,IACd,OAAO,CAAC,GAAR,CAAY,2CADE,IAEd,OAAO,CAAC,GAAR,CAAY,mDAFE,IAGd,OAAO,CAAC,GAAR,CAAY,gCAHE,IAId,mFAJF;AAKA,UAAM,SAAS,GAAG,OAAO,CAAC,GAAR,CAAY,+CAAZ,IAChB,OAAO,CAAC,GAAR,CAAY,+CADI,IAEhB,OAAO,CAAC,GAAR,CAAY,uDAFI,IAGhB,OAAO,CAAC,GAAR,CAAY,oCAHI,IAIhB,OAJF;AAKA,UAAM,SAAS,GAAG,OAAO,GAAG,KAA5B;AACA,IAAA,GAAG,GAAG,GAAG,OAAO,GAAG,SAAS,IAAI,SAAS,EAAzC;AACD;;AAED,SAAO,MAAM,CAAC,OAAD,EAAU,GAAV,EAAe,QAAf,CAAb;AACD;;AAEK,SAAU,MAAV,CAAiB,IAAjB,EAA+B,GAA/B,EAAoD,QAApD,EAA4E;AAChF;AACA,QAAM,SAAS,GAAG,OAAO,CAAC,GAAR,CAAY,sBAAZ,GAAqC,IAAvD;AACA,MAAI,OAAO,GAAG,gBAAgB,CAAC,GAAjB,CAAqB,SAArB,CAAd,CAHgF,CAGlC;;AAE9C,MAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,WAAO,OAAP;AACD;;AAED,EAAA,OAAO,GAAG,QAAQ,CAAC,IAAD,EAAO,GAAP,EAAY,QAAZ,CAAlB;AACA,EAAA,gBAAgB,CAAC,GAAjB,CAAqB,SAArB,EAAgC,OAAhC;AACA,SAAO,OAAP;AACD;;AAED,SAAS,QAAT,CAAkB,IAAlB,EAAgC,GAAhC,EAAgE,QAAhE,EAAmG;AACjG,QAAM,IAAI,GAAG,CAAC,mBAAD,EAAsB,QAAtB,EAAgC,IAAhC,CAAb;;AACA,MAAI,GAAG,IAAI,IAAX,EAAiB;AACf,IAAA,IAAI,CAAC,IAAL,CAAU,OAAV,EAAmB,GAAnB;AACD;;AACD,MAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,IAAA,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,QAAtB;AACD;;AACD,SAAO,sCAAkB,IAAlB,CAAP;AACD,C", "sourcesContent": ["import { executeAppBuilder } from \"builder-util\"\n\nconst versionToPromise = new Map<string, Promise<string>>()\n\nexport function download(url: string, output: string, checksum?: string | null): Promise<void> {\n  const args = [\"download\", \"--url\", url, \"--output\", output]\n  if (checksum != null) {\n    args.push(\"--sha512\", checksum)\n  }\n  return executeAppBuilder(args) as Promise<any>\n}\n\nexport function getBinFromUrl(name: string, version: string, checksum: string): Promise<string> {\n  const dirName = `${name}-${version}`\n  let url: string\n  if (process.env.ELECTRON_BUILDER_BINARIES_DOWNLOAD_OVERRIDE_URL) {\n    url = process.env.ELECTRON_BUILDER_BINARIES_DOWNLOAD_OVERRIDE_URL + \"/\" + dirName + \".7z\"\n  }\n  else {\n\n    const baseUrl = process.env.NPM_CONFIG_ELECTRON_BUILDER_BINARIES_MIRROR ||\n      process.env.npm_config_electron_builder_binaries_mirror ||\n      process.env.npm_package_config_electron_builder_binaries_mirror ||\n      process.env.ELECTRON_BUILDER_BINARIES_MIRROR ||\n      \"https://github.com/electron-userland/electron-builder-binaries/releases/download/\"\n    const middleUrl = process.env.NPM_CONFIG_ELECTRON_BUILDER_BINARIES_CUSTOM_DIR ||\n      process.env.npm_config_electron_builder_binaries_custom_dir ||\n      process.env.npm_package_config_electron_builder_binaries_custom_dir ||\n      process.env.ELECTRON_BUILDER_BINARIES_CUSTOM_DIR ||\n      dirName\n    const urlSuffix = dirName + \".7z\"\n    url = `${baseUrl}${middleUrl}/${urlSuffix}`\n  }\n\n  return getBin(dirName, url, checksum)\n}\n\nexport function getBin(name: string, url?: string | null, checksum?: string | null): Promise<string> {\n  // Old cache is ignored if cache environment variable changes\n  const cacheName = process.env.ELECTRON_BUILDER_CACHE + name;\n  let promise = versionToPromise.get(cacheName);// if rejected, we will try to download again\n\n  if (promise != null) {\n    return promise\n  }\n\n  promise = doGetBin(name, url, checksum)\n  versionToPromise.set(cacheName, promise)\n  return promise\n}\n\nfunction doGetBin(name: string, url: string | undefined | null, checksum: string | null | undefined): Promise<string> {\n  const args = [\"download-artifact\", \"--name\", name]\n  if (url != null) {\n    args.push(\"--url\", url)\n  }\n  if (checksum != null) {\n    args.push(\"--sha512\", checksum)\n  }\n  return executeAppBuilder(args)\n}"], "sourceRoot": ""}