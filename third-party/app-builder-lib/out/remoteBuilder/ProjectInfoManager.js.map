{"version": 3, "sources": ["../../src/remoteBuilder/ProjectInfoManager.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;;;;;AAGM,MAAO,kBAAP,CAAyB;AAG7B,EAAA,WAAA,CAAqB,QAArB,EAAuC;AAAlB,SAAA,QAAA,GAAA,QAAA;AAFZ,SAAA,QAAA,GAAW,KAAI,eAAJ,EAAS,MAAM,KAAK,4BAAL,EAAf,CAAX;AAGR;;AAEO,QAAM,4BAAN,GAAkC;AACxC,UAAM,QAAQ,GAAG,KAAK,QAAtB;AACA,UAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,cAAT,CAAwB,aAAxB,CAAsC;AAAC,MAAA,MAAM,EAAE;AAAT,KAAtC,CAAtB,CAFwC,CAGxC;;AACA,UAAM,IAAI,GAAQ;AAChB,MAAA,QAAQ,EAAE,QAAQ,CAAC,QADH;AAEhB,MAAA,aAAa,EAAE,QAAQ,CAAC,MAFR;AAGhB,MAAA,cAAc,EAAE,MAAM,QAAQ,CAAC,cAHf;AAIhB,MAAA,oBAAoB,EAAE,IAAI,CAAC,QAAL,CAAc,QAAQ,CAAC,iBAAvB;AAJN,KAAlB;;AAMA,QAAI,QAAQ,CAAC,QAAT,KAAsB,QAAQ,CAAC,WAA/B,IAA8C,QAAQ,CAAC,WAAT,IAAwB,IAA1E,EAAgF;AAC9E,MAAA,IAAI,CAAC,WAAL,GAAmB,QAAQ,CAAC,WAA5B;AACD;;AACD,UAAM,IAAI,GAAG,IAAI,CAAC,IAAL,CAAU,OAAV,EAAmB,WAAnB,CAAb;AACA,UAAM,2BAAW,IAAX,EAAiB,IAAjB,CAAN;AACA,WAAO,IAAP;AACD;;AAtB4B,C", "sourcesContent": ["import { outputJson } from \"fs-extra\"\nimport { Lazy } from \"lazy-val\"\nimport * as path from \"path\"\nimport { Packager } from \"../packager\"\n\nexport class ProjectInfoManager {\n  readonly infoFile = new Lazy(() => this.saveConfigurationAndMetadata())\n\n  constructor(readonly packager: Packager) {\n  }\n\n  private async saveConfigurationAndMetadata() {\n    const packager = this.packager\n    const tempDir = await packager.tempDirManager.createTempDir({prefix: \"remote-build-metadata\"})\n    // we cannot use getTempFile because file name must be constant\n    const info: any = {\n      metadata: packager.metadata,\n      configuration: packager.config,\n      repositoryInfo: await packager.repositoryInfo,\n      buildResourceDirName: path.basename(packager.buildResourcesDir)\n    }\n    if (packager.metadata !== packager.devMetadata && packager.devMetadata != null) {\n      info.devMetadata = packager.devMetadata\n    }\n    const file = path.join(tempDir, \"info.json\")\n    await outputJson(file, info)\n    return file\n  }\n}"], "sourceRoot": ""}