{"version": 3, "sources": ["../../src/remoteBuilder/builder-cli.ts"], "names": [], "mappings": ";;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;AAGA,IAAI,OAAO,CAAC,GAAR,CAAY,kCAAZ,IAAkD,IAAtD,EAA4D;AAC1D,EAAA,OAAO,CAAC,GAAR,CAAY,kCAAZ,GAAiD,MAAjD;AACD;;AAED,eAAe,OAAf,CAAuB,IAAvB,EAAsC;AACpC,MAAI,OAAO,CAAC,GAAR,CAAY,mBAAZ,IAAmC,IAAvC,EAA6C;AAC3C,UAAM,KAAI,wCAAJ,EAA8B,yDAA9B,CAAN;AACD;;AAED,QAAM,UAAU,GAAG,OAAO,CAAC,GAAR,CAAY,WAA/B;;AACA,MAAI,UAAU,IAAI,IAAlB,EAAwB;AACtB,UAAM,KAAI,wCAAJ,EAA8B,iDAA9B,CAAN;AACD;;AAED,QAAM,OAAO,GAAG,IAAI,CAAC,OAArB;;AACA,MAAI,IAAI,CAAC,QAAL,IAAiB,IAArB,EAA2B;AACzB,UAAM,KAAI,wCAAJ,EAA8B,wBAA9B,CAAN;AACD;;AACD,MAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,UAAM,KAAI,wCAAJ,EAA8B,4BAA9B,CAAN;AACD;;AACD,MAAI,CAAC,KAAK,CAAC,OAAN,CAAc,OAAd,CAAL,EAA6B;AAC3B,UAAM,KAAI,wCAAJ,EAA8B,sCAA9B,CAAN;AACD;;AAED,QAAM,QAAQ,GAAG,UAAU,GAAG,IAAI,CAAC,GAAlB,GAAwB,WAAzC;AACA,QAAM,IAAI,GAAG,MAAM,yBAAS,QAAT,CAAnB;AAEA,QAAM,aAAa,GAAG,OAAO,CAAC,GAAR,CAAY,eAAlC;;AACA,MAAI,UAAU,IAAI,IAAlB,EAAwB;AACtB,UAAM,KAAI,wCAAJ,EAA8B,qDAA9B,CAAN;AACD,GA3BmC,CA6BpC;;;AACA,QAAM,WAAW,GAAG,UAAU,GAAG,IAAI,CAAC,GAAlB,GAAwB,OAAO,CAAC,CAAD,CAAP,CAAW,eAAvD,CA9BoC,CA+BpC;;AACA,QAAM,OAAO,GAAqC;AAChD,IAAA,WADgD;AAEhD,IAAA,UAFgD;AAGhD,KAAC,IAAI,CAAC,QAAN,GAAiB,OAAO,CAAC,GAAR,CAAY,EAAE,IAAI,EAAE,CAAC,IAAH,GAAU,GAAV,GAAgB,EAAE,CAAC,IAArC,CAH+B;AAIhD,IAAA,OAAO,EAAE;AAJuC,GAAlD;AAMA,QAAM,QAAQ,GAAG,KAAI,YAAJ,EAAa,OAAb,CAAjB;AAEA,QAAM,SAAS,GAAwB,EAAvC;AACA,QAAM,kBAAkB,GAAG,aAAe,CAAC,MAAhB,GAAyB,CAApD;AACA,EAAA,QAAQ,CAAC,eAAT,CAAyB,KAAK,IAAG;AAC/B,QAAI,KAAK,CAAC,IAAN,IAAc,IAAlB,EAAwB;AACtB;AACD;;AAED,IAAA,SAAS,CAAC,IAAV,CAAe;AACb,MAAA,IAAI,EAAE,KAAK,CAAC,IAAN,CAAW,SAAX,CAAqB,kBAArB,CADO;AAEb,MAAA,MAAM,EAAE,KAAK,CAAC,MAAN,IAAgB,IAAhB,GAAuB,IAAvB,GAA8B,KAAK,CAAC,MAAN,CAAa,IAFtC;AAGb,MAAA,IAAI,EAAE,KAAK,CAAC,IAHC;AAIb,MAAA,gBAAgB,EAAE,KAAK,CAAC,gBAJX;AAKb,MAAA,iBAAiB,EAAE,KAAK,CAAC,iBAAN,KAA4B,IALlC;AAMb,MAAA,UAAU,EAAE,KAAK,CAAC;AANL,KAAf;AAQD,GAbD;;AAeA,EAAA,QAAQ,CAAC,sBAAT,GAAkC,CAAC,MAAD,EAAS,QAAT,EAAmB,IAAnB,KAA2B;AAC3D;AACA,UAAM,SAAS,GAAG,MAAM,CAAC,IAAP,KAAgB,MAAhB,IAA0B,CAAE,MAAqB,CAAC,gBAAlD,GAAqE,aAArE,GAAqF,UAAvG;AACA,WAAO,SAAS,GAAG,IAAI,CAAC,GAAjB,GAAuB,KAAK,MAAM,CAAC,IAAI,IAAI,SAAK,IAAL,CAAU,EAA5D;AACD,GAJD,CAzDoC,CA+DpC;;;AACA,QAAM,QAAQ,CAAC,MAAT,CAAgB,EACpB,GAAG,IAAI,CAAC,aADY;AAEpB,IAAA,OAAO,EAAE,IAFW;AAGpB,IAAA,WAAW,EAAE,IAHO;AAIpB,IAAA,SAAS,EAAE,IAJS;AAKpB,IAAA,SAAS,EAAE,IALS;AAMpB,IAAA,qBAAqB,EAAE,IANH;AAOpB,IAAA,gBAAgB,EAAE,IAPE;AAQpB,IAAA,WAAW,EAAE;AACX,MAAA,MAAM,EAAE,aADG;AAEX,MAAA,cAAc,EAAE,UAAU,GAAG,IAAI,CAAC,GAAlB,GAAwB,IAAI,CAAC;AAFlC;AARO,GAAhB,EAYH,IAAI,CAAC,QAZF,EAYY,IAAI,CAAC,WAZjB,EAY8B,IAAI,CAAC,cAZnC,CAAN,CAhEoC,CA8EpC;;AACA,QAAM,0BAAU,IAAI,CAAC,IAAL,CAAU,OAAO,CAAC,GAAR,CAAY,mBAAtB,EAA6C,qBAA7C,CAAV,EAA+E,IAAI,CAAC,SAAL,CAAe,SAAf,CAA/E,CAAN;AACD;;AAED,OAAO,CAAC,IAAI,CAAC,KAAL,CAAW,OAAO,CAAC,IAAR,CAAa,CAAb,CAAX,CAAD,CAAP,CACG,KADH,CACS,KAAK,IAAG;AACb,EAAA,OAAO,CAAC,QAAR,GAAmB,CAAnB;AACA,SAAO,0BAAU,IAAI,CAAC,IAAL,CAAU,OAAO,CAAC,GAAR,CAAY,mBAAtB,EAA6C,qBAA7C,CAAV,EAA+E,CAAC,KAAK,CAAC,KAAN,IAAe,KAAhB,EAAuB,QAAvB,EAA/E,CAAP;AACD,CAJH,E", "sourcesContent": ["import { readJson, writeFile } from \"fs-extra\"\nimport * as path from \"path\"\nimport { UploadTask, Arch, Packager, PackagerOptions, PublishOptions } from \"..\"\nimport { InvalidConfigurationError } from \"builder-util\"\nimport SnapTarget from \"../targets/snap\"\n\nif (process.env.BUILDER_REMOVE_STAGE_EVEN_IF_DEBUG == null) {\n  process.env.BUILDER_REMOVE_STAGE_EVEN_IF_DEBUG = \"true\"\n}\n\nasync function doBuild(data: BuildTask): Promise<void> {\n  if (process.env.APP_BUILDER_TMP_DIR == null) {\n    throw new InvalidConfigurationError(\"Env APP_BUILDER_TMP_DIR must be set for builder process\")\n  }\n\n  const projectDir = process.env.PROJECT_DIR\n  if (projectDir == null) {\n    throw new InvalidConfigurationError(\"Env PROJECT_DIR must be set for builder process\")\n  }\n\n  const targets = data.targets\n  if (data.platform == null) {\n    throw new InvalidConfigurationError(\"platform not specified\")\n  }\n  if (targets == null) {\n    throw new InvalidConfigurationError(\"targets path not specified\")\n  }\n  if (!Array.isArray(targets)) {\n    throw new InvalidConfigurationError(\"targets must be array of target name\")\n  }\n\n  const infoFile = projectDir + path.sep + \"info.json\"\n  const info = await readJson(infoFile)\n\n  const projectOutDir = process.env.PROJECT_OUT_DIR\n  if (projectDir == null) {\n    throw new InvalidConfigurationError(\"Env PROJECT_OUT_DIR must be set for builder process\")\n  }\n\n  // yes, for now we expect the only target\n  const prepackaged = projectDir + path.sep + targets[0].unpackedDirName\n  // do not use build function because we don't need to publish artifacts\n  const options: PackagerOptions & PublishOptions = {\n    prepackaged,\n    projectDir,\n    [data.platform]: targets.map(it => it.name + \":\" + it.arch),\n    publish: \"never\",\n  }\n  const packager = new Packager(options)\n\n  const artifacts: Array<ArtifactInfo> = []\n  const relativePathOffset = projectOutDir!!.length + 1\n  packager.artifactCreated(event => {\n    if (event.file == null) {\n      return\n    }\n\n    artifacts.push({\n      file: event.file.substring(relativePathOffset),\n      target: event.target == null ? null : event.target.name,\n      arch: event.arch,\n      safeArtifactName: event.safeArtifactName,\n      isWriteUpdateInfo: event.isWriteUpdateInfo === true,\n      updateInfo: event.updateInfo,\n    })\n  })\n\n  packager.stageDirPathCustomizer = (target, packager, arch) => {\n    // snap creates a lot of files and so, we cannot use tmpfs to avoid out of memory error\n    const parentDir = target.name === \"snap\" && !(target as SnapTarget).isUseTemplateApp ? projectOutDir : projectDir\n    return parentDir + path.sep + `__${target.name}-${Arch[arch]}`\n  }\n\n  // _build method expects final effective configuration - packager.options.config is ignored\n  await packager._build({\n    ...info.configuration,\n    publish: null,\n    beforeBuild: null,\n    afterPack: null,\n    afterSign: null,\n    afterAllArtifactBuild: null,\n    onNodeModuleFile: null,\n    directories: {\n      output: projectOutDir,\n      buildResources: projectDir + path.sep + info.buildResourceDirName\n    },\n  }, info.metadata, info.devMetadata, info.repositoryInfo)\n\n  // writeJson must be not used because it adds unwanted \\n as last file symbol\n  await writeFile(path.join(process.env.APP_BUILDER_TMP_DIR!!, \"__build-result.json\"), JSON.stringify(artifacts))\n}\n\ndoBuild(JSON.parse(process.argv[2]))\n  .catch(error => {\n    process.exitCode = 0\n    return writeFile(path.join(process.env.APP_BUILDER_TMP_DIR!!, \"__build-result.json\"), (error.stack || error).toString())\n  })\n\ninterface TargetInfo {\n  name: string\n  arch: string\n  unpackedDirName: string\n}\n\ninterface ArtifactInfo extends UploadTask {\n  target: string | null\n\n  readonly isWriteUpdateInfo?: boolean\n  readonly updateInfo?: any\n}\n\ninterface BuildTask {\n  platform: string\n  targets: Array<TargetInfo>\n}"], "sourceRoot": ""}