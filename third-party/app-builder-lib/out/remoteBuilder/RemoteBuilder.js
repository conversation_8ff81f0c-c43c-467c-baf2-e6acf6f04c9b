"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.RemoteBuilder = void 0;

function _bluebirdLst() {
  const data = _interopRequireDefault(require("bluebird-lst"));

  _bluebirdLst = function () {
    return data;
  };

  return data;
}

function _builderUtil() {
  const data = require("builder-util");

  _builderUtil = function () {
    return data;
  };

  return data;
}

var path = _interopRequireWildcard(require("path"));

function _core() {
  const data = require("../core");

  _core = function () {
    return data;
  };

  return data;
}

function _platformPackager() {
  const data = require("../platformPackager");

  _platformPackager = function () {
    return data;
  };

  return data;
}

function _appBuilder() {
  const data = require("../util/appBuilder");

  _appBuilder = function () {
    return data;
  };

  return data;
}

function _ProjectInfoManager() {
  const data = require("./ProjectInfoManager");

  _ProjectInfoManager = function () {
    return data;
  };

  return data;
}

function _getRequireWildcardCache() { if (typeof WeakMap !== "function") return null; var cache = new WeakMap(); _getRequireWildcardCache = function () { return cache; }; return cache; }

function _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

class RemoteBuilder {
  constructor(packager) {
    this.packager = packager;
    this.toBuild = new Map();
    this.buildStarted = false;
  }

  scheduleBuild(target, arch, unpackedDirectory) {
    if (!(0, _builderUtil().isEnvTrue)(process.env._REMOTE_BUILD) && this.packager.config.remoteBuild === false) {
      throw new (_builderUtil().InvalidConfigurationError)("Target is not supported on your OS and using of Electron Build Service is disabled (\"remoteBuild\" option)");
    }

    let list = this.toBuild.get(arch);

    if (list == null) {
      list = [];
      this.toBuild.set(arch, list);
    }

    list.push({
      name: target.name,
      arch: _builderUtil().Arch[arch],
      unpackedDirectory,
      outDir: target.outDir
    });
  }

  build() {
    if (this.buildStarted) {
      return Promise.resolve();
    }

    this.buildStarted = true;
    return _bluebirdLst().default.mapSeries(Array.from(this.toBuild.keys()), arch => {
      return this._build(this.toBuild.get(arch), this.packager);
    });
  } // noinspection JSMethodCanBeStatic


  async _build(targets, packager) {
    if (_builderUtil().log.isDebugEnabled) {
      _builderUtil().log.debug({
        remoteTargets: JSON.stringify(targets, null, 2)
      }, "remote building");
    }

    const projectInfoManager = new (_ProjectInfoManager().ProjectInfoManager)(packager.info);
    const buildRequest = {
      targets: targets.map(it => {
        return {
          name: it.name,
          arch: it.arch,
          unpackedDirName: path.basename(it.unpackedDirectory)
        };
      }),
      platform: packager.platform.buildConfigurationKey
    };

    if ((0, _platformPackager().isSafeToUnpackElectronOnRemoteBuildServer)(packager)) {
      buildRequest.electronDownload = {
        version: packager.info.framework.version,
        platform: _core().Platform.LINUX.nodeName,
        arch: targets[0].arch
      };
      const linuxPackager = packager;
      buildRequest.executableName = linuxPackager.executableName;
    }

    const req = Buffer.from(JSON.stringify(buildRequest)).toString("base64");
    const outDir = targets[0].outDir;
    const args = ["remote-build", "--request", req, "--output", outDir];
    args.push("--file", targets[0].unpackedDirectory);
    args.push("--file", await projectInfoManager.infoFile.value);
    const buildResourcesDir = packager.buildResourcesDir;

    if (buildResourcesDir === packager.projectDir) {
      throw new (_builderUtil().InvalidConfigurationError)(`Build resources dir equals to project dir and so, not sent to remote build agent. It will lead to incorrect results.\nPlease set "directories.buildResources" to separate dir or leave default ("build" directory in the project root)`);
    }

    args.push("--build-resource-dir", buildResourcesDir);
    const result = await (0, _appBuilder().executeAppBuilderAsJson)(args);

    if (result.error != null) {
      throw new (_builderUtil().InvalidConfigurationError)(`Remote builder error (if you think that it is not your application misconfiguration issue, please file issue to https://github.com/electron-userland/electron-builder/issues):\n\n${result.error}`, "REMOTE_BUILDER_ERROR");
    } else if (result.files != null) {
      for (const artifact of result.files) {
        const localFile = path.join(outDir, artifact.file);
        const artifactCreatedEvent = this.artifactInfoToArtifactCreatedEvent(artifact, localFile, outDir); // PublishManager uses outDir and options, real (the same as for local build) values must be used

        await this.packager.info.callArtifactBuildCompleted(artifactCreatedEvent);
      }
    }
  }

  artifactInfoToArtifactCreatedEvent(artifact, localFile, outDir) {
    const target = artifact.target; // noinspection SpellCheckingInspection

    return { ...artifact,
      file: localFile,
      target: target == null ? null : new FakeTarget(target, outDir, this.packager.config[target]),
      packager: this.packager
    };
  }

}

exports.RemoteBuilder = RemoteBuilder;

class FakeTarget extends _core().Target {
  constructor(name, outDir, options) {
    super(name);
    this.outDir = outDir;
    this.options = options;
  } // eslint-disable-next-line @typescript-eslint/no-unused-vars


  async build(appOutDir, arch) {// no build
  }

} 
// __ts-babel@6.0.4
//# sourceMappingURL=RemoteBuilder.js.map