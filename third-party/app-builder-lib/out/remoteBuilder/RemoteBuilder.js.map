{"version": 3, "sources": ["../../src/remoteBuilder/RemoteBuilder.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAGA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;;;AASM,MAAO,aAAP,CAAoB;AAIxB,EAAA,WAAA,CAAqB,QAArB,EAAoD;AAA/B,SAAA,QAAA,GAAA,QAAA;AAHJ,SAAA,OAAA,GAAU,IAAI,GAAJ,EAAV;AACT,SAAA,YAAA,GAAe,KAAf;AAGP;;AAED,EAAA,aAAa,CAAC,MAAD,EAAiB,IAAjB,EAA6B,iBAA7B,EAAsD;AACjE,QAAI,CAAC,8BAAU,OAAO,CAAC,GAAR,CAAY,aAAtB,CAAD,IAAyC,KAAK,QAAL,CAAc,MAAd,CAAqB,WAArB,KAAqC,KAAlF,EAAyF;AACvF,YAAM,KAAI,wCAAJ,EAA8B,6GAA9B,CAAN;AACD;;AAED,QAAI,IAAI,GAAG,KAAK,OAAL,CAAa,GAAb,CAAiB,IAAjB,CAAX;;AACA,QAAI,IAAI,IAAI,IAAZ,EAAkB;AAChB,MAAA,IAAI,GAAG,EAAP;AACA,WAAK,OAAL,CAAa,GAAb,CAAiB,IAAjB,EAAuB,IAAvB;AACD;;AAED,IAAA,IAAI,CAAC,IAAL,CAAU;AACR,MAAA,IAAI,EAAE,MAAM,CAAC,IADL;AAER,MAAA,IAAI,EAAE,oBAAK,IAAL,CAFE;AAGR,MAAA,iBAHQ;AAIR,MAAA,MAAM,EAAE,MAAM,CAAC;AAJP,KAAV;AAMD;;AAED,EAAA,KAAK,GAAA;AACH,QAAI,KAAK,YAAT,EAAuB;AACrB,aAAO,OAAO,CAAC,OAAR,EAAP;AACD;;AAED,SAAK,YAAL,GAAoB,IAApB;AAEA,WAAO,uBAAgB,SAAhB,CAA0B,KAAK,CAAC,IAAN,CAAW,KAAK,OAAL,CAAa,IAAb,EAAX,CAA1B,EAA4D,IAAD,IAAe;AAC/E,aAAO,KAAK,MAAL,CAAY,KAAK,OAAL,CAAa,GAAb,CAAiB,IAAjB,CAAZ,EAAsC,KAAK,QAA3C,CAAP;AACD,KAFM,CAAP;AAGD,GApCuB,CAsCxB;;;AACQ,QAAM,MAAN,CAAa,OAAb,EAAyC,QAAzC,EAAwE;AAC9E,QAAI,mBAAI,cAAR,EAAwB;AACtB,yBAAI,KAAJ,CAAU;AAAC,QAAA,aAAa,EAAE,IAAI,CAAC,SAAL,CAAe,OAAf,EAAwB,IAAxB,EAA8B,CAA9B;AAAhB,OAAV,EAA6D,iBAA7D;AACD;;AAED,UAAM,kBAAkB,GAAG,KAAI,wCAAJ,EAAuB,QAAQ,CAAC,IAAhC,CAA3B;AAEA,UAAM,YAAY,GAAQ;AACxB,MAAA,OAAO,EAAE,OAAO,CAAC,GAAR,CAAY,EAAE,IAAG;AACxB,eAAO;AACL,UAAA,IAAI,EAAE,EAAE,CAAC,IADJ;AAEL,UAAA,IAAI,EAAE,EAAE,CAAC,IAFJ;AAGL,UAAA,eAAe,EAAE,IAAI,CAAC,QAAL,CAAc,EAAE,CAAC,iBAAjB;AAHZ,SAAP;AAKD,OANQ,CADe;AAQxB,MAAA,QAAQ,EAAE,QAAQ,CAAC,QAAT,CAAkB;AARJ,KAA1B;;AAWA,QAAI,mEAA0C,QAA1C,CAAJ,EAAyD;AACvD,MAAA,YAAY,CAAC,gBAAb,GAAgC;AAC9B,QAAA,OAAO,EAAE,QAAQ,CAAC,IAAT,CAAc,SAAd,CAAwB,OADH;AAE9B,QAAA,QAAQ,EAAE,iBAAS,KAAT,CAAe,QAFK;AAG9B,QAAA,IAAI,EAAE,OAAO,CAAC,CAAD,CAAP,CAAW;AAHa,OAAhC;AAMA,YAAM,aAAa,GAAI,QAAvB;AACA,MAAA,YAAY,CAAC,cAAb,GAA8B,aAAa,CAAC,cAA5C;AACD;;AAED,UAAM,GAAG,GAAG,MAAM,CAAC,IAAP,CAAY,IAAI,CAAC,SAAL,CAAe,YAAf,CAAZ,EAA0C,QAA1C,CAAmD,QAAnD,CAAZ;AACA,UAAM,MAAM,GAAG,OAAO,CAAC,CAAD,CAAP,CAAW,MAA1B;AACA,UAAM,IAAI,GAAG,CAAC,cAAD,EAAiB,WAAjB,EAA8B,GAA9B,EAAmC,UAAnC,EAA+C,MAA/C,CAAb;AAEA,IAAA,IAAI,CAAC,IAAL,CAAU,QAAV,EAAoB,OAAO,CAAC,CAAD,CAAP,CAAW,iBAA/B;AACA,IAAA,IAAI,CAAC,IAAL,CAAU,QAAV,EAAoB,MAAM,kBAAkB,CAAC,QAAnB,CAA4B,KAAtD;AAEA,UAAM,iBAAiB,GAAG,QAAQ,CAAC,iBAAnC;;AACA,QAAI,iBAAiB,KAAK,QAAQ,CAAC,UAAnC,EAA+C;AAC7C,YAAM,KAAI,wCAAJ,EAA8B,wOAA9B,CAAN;AACD;;AAED,IAAA,IAAI,CAAC,IAAL,CAAU,sBAAV,EAAkC,iBAAlC;AAEA,UAAM,MAAM,GAAQ,MAAM,2CAAwB,IAAxB,CAA1B;;AACA,QAAI,MAAM,CAAC,KAAP,IAAgB,IAApB,EAA0B;AACxB,YAAM,KAAI,wCAAJ,EAA8B,qLAAqL,MAAM,CAAC,KAAK,EAA/N,EAAmO,sBAAnO,CAAN;AACD,KAFD,MAGK,IAAI,MAAM,CAAC,KAAP,IAAgB,IAApB,EAA0B;AAC7B,WAAK,MAAM,QAAX,IAAuB,MAAM,CAAC,KAA9B,EAAqC;AACnC,cAAM,SAAS,GAAG,IAAI,CAAC,IAAL,CAAU,MAAV,EAAkB,QAAQ,CAAC,IAA3B,CAAlB;AACA,cAAM,oBAAoB,GAAG,KAAK,kCAAL,CAAwC,QAAxC,EAAkD,SAAlD,EAA6D,MAA7D,CAA7B,CAFmC,CAGnC;;AACA,cAAM,KAAK,QAAL,CAAc,IAAd,CAAmB,0BAAnB,CAA8C,oBAA9C,CAAN;AACD;AACF;AACF;;AAEO,EAAA,kCAAkC,CAAC,QAAD,EAAyB,SAAzB,EAA4C,MAA5C,EAA0D;AAClG,UAAM,MAAM,GAAG,QAAQ,CAAC,MAAxB,CADkG,CAElG;;AACA,WAAO,EACL,GAAG,QADE;AAEL,MAAA,IAAI,EAAE,SAFD;AAGL,MAAA,MAAM,EAAE,MAAM,IAAI,IAAV,GAAiB,IAAjB,GAAwB,IAAI,UAAJ,CAAe,MAAf,EAAuB,MAAvB,EAAgC,KAAK,QAAL,CAAc,MAAd,CAA6B,MAA7B,CAAhC,CAH3B;AAIL,MAAA,QAAQ,EAAE,KAAK;AAJV,KAAP;AAMD;;AAzGuB;;;;AA4G1B,MAAM,UAAN,SAAyB,cAAzB,CAA+B;AAC7B,EAAA,WAAA,CAAY,IAAZ,EAAmC,MAAnC,EAA4D,OAA5D,EAA6G;AAC3G,UAAM,IAAN;AADiC,SAAA,MAAA,GAAA,MAAA;AAAyB,SAAA,OAAA,GAAA,OAAA;AAE3D,GAH4B,CAK7B;;;AACA,QAAM,KAAN,CAAY,SAAZ,EAA+B,IAA/B,EAAyC,CACvC;AACD;;AAR4B,C", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { Arch, isEnvTrue, log, InvalidConfigurationError } from \"builder-util\"\nimport * as path from \"path\"\nimport { UploadTask } from \"electron-publish/out/publisher\"\nimport { Platform, Target, TargetSpecificOptions } from \"../core\"\nimport { LinuxPackager } from \"../linuxPackager\"\nimport { ArtifactCreated } from \"../packagerApi\"\nimport { isSafeToUnpackElectronOnRemoteBuildServer, PlatformPackager } from \"../platformPackager\"\nimport { executeAppBuilderAsJson } from \"../util/appBuilder\"\nimport { ProjectInfoManager } from \"./ProjectInfoManager\"\n\ninterface TargetInfo {\n  name: string\n  arch: string\n  unpackedDirectory: string\n  outDir: string\n}\n\nexport class RemoteBuilder {\n  private readonly toBuild = new Map<Arch, Array<TargetInfo>>()\n  private buildStarted = false\n\n  constructor(readonly packager: PlatformPackager<any>) {\n  }\n\n  scheduleBuild(target: Target, arch: Arch, unpackedDirectory: string) {\n    if (!isEnvTrue(process.env._REMOTE_BUILD) && this.packager.config.remoteBuild === false) {\n      throw new InvalidConfigurationError(\"Target is not supported on your OS and using of Electron Build Service is disabled (\\\"remoteBuild\\\" option)\")\n    }\n\n    let list = this.toBuild.get(arch)\n    if (list == null) {\n      list = []\n      this.toBuild.set(arch, list)\n    }\n\n    list.push({\n      name: target.name,\n      arch: Arch[arch],\n      unpackedDirectory,\n      outDir: target.outDir,\n    })\n  }\n\n  build(): Promise<any> {\n    if (this.buildStarted) {\n      return Promise.resolve()\n    }\n\n    this.buildStarted = true\n\n    return BluebirdPromise.mapSeries(Array.from(this.toBuild.keys()), (arch: Arch) => {\n      return this._build(this.toBuild.get(arch)!!, this.packager)\n    })\n  }\n\n  // noinspection JSMethodCanBeStatic\n  private async _build(targets: Array<TargetInfo>, packager: PlatformPackager<any>): Promise<void> {\n    if (log.isDebugEnabled) {\n      log.debug({remoteTargets: JSON.stringify(targets, null, 2)}, \"remote building\")\n    }\n\n    const projectInfoManager = new ProjectInfoManager(packager.info)\n\n    const buildRequest: any = {\n      targets: targets.map(it => {\n        return {\n          name: it.name,\n          arch: it.arch,\n          unpackedDirName: path.basename(it.unpackedDirectory),\n        }\n      }),\n      platform: packager.platform.buildConfigurationKey,\n    }\n\n    if (isSafeToUnpackElectronOnRemoteBuildServer(packager)) {\n      buildRequest.electronDownload = {\n        version: packager.info.framework.version,\n        platform: Platform.LINUX.nodeName,\n        arch: targets[0].arch,\n      }\n\n      const linuxPackager = (packager as LinuxPackager)\n      buildRequest.executableName = linuxPackager.executableName\n    }\n\n    const req = Buffer.from(JSON.stringify(buildRequest)).toString(\"base64\")\n    const outDir = targets[0].outDir\n    const args = [\"remote-build\", \"--request\", req, \"--output\", outDir]\n\n    args.push(\"--file\", targets[0].unpackedDirectory)\n    args.push(\"--file\", await projectInfoManager.infoFile.value)\n\n    const buildResourcesDir = packager.buildResourcesDir\n    if (buildResourcesDir === packager.projectDir) {\n      throw new InvalidConfigurationError(`Build resources dir equals to project dir and so, not sent to remote build agent. It will lead to incorrect results.\\nPlease set \"directories.buildResources\" to separate dir or leave default (\"build\" directory in the project root)`)\n    }\n\n    args.push(\"--build-resource-dir\", buildResourcesDir)\n\n    const result: any = await executeAppBuilderAsJson(args)\n    if (result.error != null) {\n      throw new InvalidConfigurationError(`Remote builder error (if you think that it is not your application misconfiguration issue, please file issue to https://github.com/electron-userland/electron-builder/issues):\\n\\n${result.error}`, \"REMOTE_BUILDER_ERROR\")\n    }\n    else if (result.files != null) {\n      for (const artifact of result.files) {\n        const localFile = path.join(outDir, artifact.file)\n        const artifactCreatedEvent = this.artifactInfoToArtifactCreatedEvent(artifact, localFile, outDir)\n        // PublishManager uses outDir and options, real (the same as for local build) values must be used\n        await this.packager.info.callArtifactBuildCompleted(artifactCreatedEvent)\n      }\n    }\n  }\n\n  private artifactInfoToArtifactCreatedEvent(artifact: ArtifactInfo, localFile: string, outDir: string): ArtifactCreated {\n    const target = artifact.target\n    // noinspection SpellCheckingInspection\n    return {\n      ...artifact,\n      file: localFile,\n      target: target == null ? null : new FakeTarget(target, outDir, (this.packager.config as any)[target]),\n      packager: this.packager,\n    }\n  }\n}\n\nclass FakeTarget extends Target {\n  constructor(name: string, readonly outDir: string, readonly options: TargetSpecificOptions | null | undefined) {\n    super(name)\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  async build(appOutDir: string, arch: Arch) {\n    // no build\n  }\n}\n\ninterface ArtifactInfo extends UploadTask {\n  target: string | null\n\n  readonly isWriteUpdateInfo?: boolean\n  readonly updateInfo?: any\n}"], "sourceRoot": ""}