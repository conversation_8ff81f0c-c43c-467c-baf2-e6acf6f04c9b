{"version": 3, "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAIA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAgBA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAGA,MAAM,eAAe,GAAG,IAAI,GAAJ,CAAQ,CAAC,SAAD,EAAY,SAAZ,EAAuB,KAAvB,EAA8B,KAA9B,EAAqC,OAArC,EAA8C,YAA9C,EAA4D,yBAA5D,EAAuF,QAAvF,EAAiG,yBAAjG,EAA4H,aAA5H,CAAR,CAAxB;;AAEM,SAAU,wBAAV,CAAmC,OAAnC,EAA4E;AAChF,OAAK,MAAM,UAAX,IAAyB,MAAM,CAAC,IAAP,CAAY,OAAZ,CAAzB,EAA+C;AAC7C,QAAI,CAAC,eAAe,CAAC,GAAhB,CAAoB,UAApB,CAAD,IAAqC,OAAe,CAAC,UAAD,CAAf,KAAgC,SAAzE,EAAoF;AAClF,YAAM,KAAI,wCAAJ,EAA8B,mBAAmB,UAAU,GAA3D,CAAN;AACD;AACF;AACF;;AAEK,SAAU,KAAV,CAAgB,OAAhB,EAA2D,QAAA,GAAqB,KAAI,oBAAJ,EAAa,OAAb,CAAhF,EAAqG;AACzG,EAAA,wBAAwB,CAAC,OAAD,CAAxB;AAEA,QAAM,cAAc,GAAG,KAAI,gCAAJ,EAAmB,QAAnB,EAA6B,OAA7B,CAAvB;;AACA,QAAM,aAAa,GAAG,MAAK;AACzB,uBAAI,IAAJ,CAAS,qBAAT;;AACA,IAAA,QAAQ,CAAC,iBAAT,CAA2B,MAA3B;AACA,IAAA,cAAc,CAAC,WAAf;AACD,GAJD;;AAKA,EAAA,OAAO,CAAC,IAAR,CAAa,QAAb,EAAuB,aAAvB;AAEA,QAAM,OAAO,GAAG,QAAQ,CAAC,KAAT,GACb,IADa,CACR,MAAM,WAAN,IAAoB;AACxB,UAAM,qBAAqB,GAAG,yCAAgB,WAAW,CAAC,aAAZ,CAA0B,qBAA1C,EAAiE,uBAAjE,CAA9B;;AACA,QAAI,qBAAqB,IAAI,IAA7B,EAAmC;AACjC,YAAM,YAAY,GAAG,mCAAQ,MAAM,OAAO,CAAC,OAAR,CAAgB,qBAAqB,CAAC,WAAD,CAArC,CAAd,CAArB;;AACA,UAAI,YAAY,CAAC,MAAb,KAAwB,CAAxB,IAA6B,CAAC,cAAc,CAAC,SAAjD,EAA4D;AAC1D,eAAO,WAAW,CAAC,aAAnB;AACD;;AAED,YAAM,qBAAqB,GAAG,MAAM,cAAc,CAAC,8BAAf,EAApC;;AACA,UAAI,qBAAqB,IAAI,IAAzB,IAAiC,qBAAqB,CAAC,MAAtB,KAAiC,CAAtE,EAAyE;AACvE,eAAO,WAAW,CAAC,aAAnB;AACD;;AAED,WAAK,MAAM,WAAX,IAA0B,YAA1B,EAAwC;AACtC,QAAA,WAAW,CAAC,aAAZ,CAA0B,IAA1B,CAA+B,WAA/B;;AACA,aAAK,MAAM,oBAAX,IAAmC,qBAAnC,EAA0D;AACxD,UAAA,cAAc,CAAC,cAAf,CAA8B,oBAA9B,EAAoD;AAClD,YAAA,IAAI,EAAE,WAD4C;AAElD,YAAA,IAAI,EAAE;AAF4C,WAApD,EAGG,QAAQ,CAAC,OAHZ;AAID;AACF;AACF;;AACD,WAAO,WAAW,CAAC,aAAnB;AACD,GAzBa,CAAhB;AA2BA,SAAO,+BAAe,OAAf,EAAwB,eAAe,IAAG;AAC/C,QAAI,OAAJ;;AACA,QAAI,eAAJ,EAAqB;AACnB,MAAA,cAAc,CAAC,WAAf;AACA,MAAA,OAAO,GAAG,OAAO,CAAC,OAAR,CAAgB,IAAhB,CAAV;AACD,KAHD,MAIK;AACH,MAAA,OAAO,GAAG,cAAc,CAAC,UAAf,EAAV;AACD;;AAED,WAAO,OAAO,CACX,IADI,CACC,MAAM,OAAO,CAAC,cAAR,CAAuB,QAAvB,EAAiC,aAAjC,CADP,CAAP;AAED,GAZM,CAAP;AAaD,C", "sourcesContent": ["import { executeFinally } from \"builder-util/out/promise\"\nimport { PublishOptions } from \"electron-publish/out/publisher\"\nimport { log, InvalidConfigurationError } from \"builder-util\"\nimport { asArray } from \"builder-util-runtime\"\nimport { Packager } from \"./packager\"\nimport { PackagerOptions } from \"./packagerApi\"\nimport { resolveFunction } from \"./platformPackager\"\nimport { PublishManager } from \"./publish/PublishManager\"\n\nexport { Packager, BuildResult } from \"./packager\"\nexport { PackagerOptions, ArtifactCreated, ArtifactBuildStarted } from \"./packagerApi\"\nexport { TargetConfiguration, Platform, Target, DIR_TARGET, BeforeBuildContext, SourceRepositoryInfo, TargetSpecificOptions, TargetConfigType, DEFAULT_TARGET, CompressionLevel } from \"./core\"\nexport { getArchSuffix, Arch, archFromString } from \"builder-util\"\nexport { Configuration, AfterPackContext, MetadataDirectories } from \"./configuration\"\nexport { ElectronDownloadOptions, ElectronPlatformName } from \"./electron/ElectronFramework\"\nexport { PlatformSpecificBuildOptions, AsarOptions, FileSet, Protocol, ReleaseInfo } from \"./options/PlatformSpecificBuildOptions\"\nexport { FileAssociation } from \"./options/FileAssociation\"\nexport { MacConfiguration, DmgOptions, MasConfiguration, MacOsTargetName, DmgContent, DmgWindow } from \"./options/macOptions\"\nexport { PkgOptions, PkgBackgroundOptions, BackgroundAlignment, BackgroundScaling } from \"./options/pkgOptions\"\nexport { WindowsConfiguration } from \"./options/winOptions\"\nexport { AppXOptions } from \"./options/AppXOptions\"\nexport { MsiOptions } from \"./options/MsiOptions\"\nexport { CommonWindowsInstallerConfiguration } from \"./options/CommonWindowsInstallerConfiguration\"\nexport { NsisOptions, NsisWebOptions, PortableOptions, CommonNsisOptions } from \"./targets/nsis/nsisOptions\"\nexport { LinuxConfiguration, DebOptions, CommonLinuxOptions, LinuxTargetSpecificOptions, AppImageOptions } from \"./options/linuxOptions\"\nexport { SnapOptions } from \"./options/SnapOptions\"\nexport { Metadata, AuthorMetadata, RepositoryInfo } from \"./options/metadata\"\nexport { AppInfo } from \"./appInfo\"\nexport { SquirrelWindowsOptions } from \"./options/SquirrelWindowsOptions\"\nexport { WindowsSignOptions, CustomWindowsSignTaskConfiguration, WindowsSignTaskConfiguration, CustomWindowsSign, FileCodeSigningInfo, CertificateFromStoreInfo } from \"./codeSign/windowsCodeSign\"\nexport { CancellationToken, ProgressInfo } from \"builder-util-runtime\"\nexport { PublishOptions, UploadTask } from \"electron-publish\"\nexport { PublishManager } from \"./publish/PublishManager\"\nexport { PlatformPackager } from \"./platformPackager\"\nexport { Framework, PrepareApplicationStageDirectoryOptions } from \"./Framework\"\nexport { buildForge, ForgeOptions } from \"./forge-maker\"\nexport { SnapStoreOptions } from \"./publish/SnapStorePublisher\"\n\nconst expectedOptions = new Set([\"publish\", \"targets\", \"mac\", \"win\", \"linux\", \"projectDir\", \"platformPackagerFactory\", \"config\", \"effectiveOptionComputed\", \"prepackaged\"])\n\nexport function checkBuildRequestOptions(options: PackagerOptions & PublishOptions) {\n  for (const optionName of Object.keys(options)) {\n    if (!expectedOptions.has(optionName) && (options as any)[optionName] !== undefined) {\n      throw new InvalidConfigurationError(`Unknown option \"${optionName}\"`)\n    }\n  }\n}\n\nexport function build(options: PackagerOptions & PublishOptions, packager: Packager = new Packager(options)): Promise<Array<string>> {\n  checkBuildRequestOptions(options)\n\n  const publishManager = new PublishManager(packager, options)\n  const sigIntHandler = () => {\n    log.warn(\"cancelled by SIGINT\")\n    packager.cancellationToken.cancel()\n    publishManager.cancelTasks()\n  }\n  process.once(\"SIGINT\", sigIntHandler)\n\n  const promise = packager.build()\n    .then(async buildResult => {\n      const afterAllArtifactBuild = resolveFunction(buildResult.configuration.afterAllArtifactBuild, \"afterAllArtifactBuild\")\n      if (afterAllArtifactBuild != null) {\n        const newArtifacts = asArray(await Promise.resolve(afterAllArtifactBuild(buildResult)))\n        if (newArtifacts.length === 0 || !publishManager.isPublish) {\n          return buildResult.artifactPaths\n        }\n\n        const publishConfigurations = await publishManager.getGlobalPublishConfigurations()\n        if (publishConfigurations == null || publishConfigurations.length === 0) {\n          return buildResult.artifactPaths\n        }\n\n        for (const newArtifact of newArtifacts) {\n          buildResult.artifactPaths.push(newArtifact)\n          for (const publishConfiguration of publishConfigurations) {\n            publishManager.scheduleUpload(publishConfiguration, {\n              file: newArtifact,\n              arch: null\n            }, packager.appInfo)\n          }\n        }\n      }\n      return buildResult.artifactPaths\n    })\n\n  return executeFinally(promise, isErrorOccurred => {\n    let promise: Promise<any>\n    if (isErrorOccurred) {\n      publishManager.cancelTasks()\n      promise = Promise.resolve(null)\n    }\n    else {\n      promise = publishManager.awaitTasks()\n    }\n\n    return promise\n      .then(() => process.removeListener(\"SIGINT\", sigIntHandler))\n  })\n}"], "sourceRoot": ""}