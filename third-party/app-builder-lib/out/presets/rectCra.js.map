{"version": 3, "sources": ["../../src/presets/rectCra.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;;;;;AAGA;AACO,eAAe,QAAf,CAAwB,UAAxB,EAA0C;AAC/C,MAAI,CAAC,MAAM,sBAAW,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,QAAtB,EAAgC,aAAhC,CAAX,CAAP,KAAsE,IAA1E,EAAgF;AAC9E;AACA,uBAAI,IAAJ,CAAS,8IAAT;AACD;;AAED,SAAO;AACL,IAAA,WAAW,EAAE;AACX,MAAA,cAAc,EAAE;AADL,KADR;AAIL,IAAA,KAAK,EAAE,CAAC,YAAD,CAJF;AAKL,IAAA,aAAa,EAAE;AACb,MAAA,IAAI,EAAE;AADO;AALV,GAAP;AAQE,C", "sourcesContent": ["import { log } from \"builder-util\"\nimport { statOr<PERSON>ull } from \"builder-util/out/fs\"\nimport * as path from \"path\"\nimport { Configuration } from \"../configuration\"\n\n/** @internal */\nexport async function reactCra(projectDir: string): Promise<Configuration> {\n  if ((await statOr<PERSON>ull(path.join(projectDir, \"public\", \"electron.js\"))) == null) {\n    // noinspection SpellCheckingInspection\n    log.warn(\"public/electron.js not found. Please see https://medium.com/@kitze/%EF%B8%8F-from-react-to-an-electron-app-ready-for-production-a0468ecb1da3\")\n  }\n\n  return {\n    directories: {\n      buildResources: \"assets\"\n    },\n    files: [\"build/**/*\"],\n    extraMetadata: {\n      main: \"build/electron.js\"\n    }\n  }}"], "sourceRoot": ""}