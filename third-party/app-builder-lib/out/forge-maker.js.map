{"version": 3, "sources": ["../src/forge-maker.ts"], "names": [], "mappings": ";;;;;;;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;AAOM,SAAU,UAAV,CAAqB,YAArB,EAAiD,OAAjD,EAAyE;AAC7E,QAAM,MAAM,GAAG,YAAY,CAAC,GAA5B;AACA,SAAO,oBAAM;AACX,IAAA,WAAW,EAAE,MADF;AAEX,IAAA,MAAM,EAAE;AACN,MAAA,WAAW,EAAE;AACX;AACA,QAAA,MAAM,EAAE,IAAI,CAAC,OAAL,CAAa,MAAb,EAAqB,IAArB,EAA2B,MAA3B;AAFG;AADP,KAFG;AAQX,OAAG;AARQ,GAAN,CAAP;AAUD,C", "sourcesContent": ["import * as path from \"path\"\nimport { build } from \"./index\"\nimport { PackagerOptions } from \"./packagerApi\"\n\nexport interface ForgeOptions {\n  readonly dir: string\n}\n\nexport function buildForge(forgeOptions: ForgeOptions, options: PackagerOptions) {\n  const appDir = forgeOptions.dir\n  return build({\n    prepackaged: appDir,\n    config: {\n      directories: {\n        // https://github.com/electron-userland/electron-forge/blob/master/src/makers/generic/zip.js\n        output: path.resolve(appDir, \"..\", \"make\"),\n      },\n    },\n    ...options\n  })\n}"], "sourceRoot": ""}