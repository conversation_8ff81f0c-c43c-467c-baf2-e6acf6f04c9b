"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.createElectronFrameworkSupport = createElectronFrameworkSupport;

function _bluebirdLst() {
  const data = _interopRequireDefault(require("bluebird-lst"));

  _bluebirdLst = function () {
    return data;
  };

  return data;
}

function _builderUtil() {
  const data = require("builder-util");

  _builderUtil = function () {
    return data;
  };

  return data;
}

function _fs() {
  const data = require("builder-util/out/fs");

  _fs = function () {
    return data;
  };

  return data;
}

function _fsExtra() {
  const data = require("fs-extra");

  _fsExtra = function () {
    return data;
  };

  return data;
}

function _lazyVal() {
  const data = require("lazy-val");

  _lazyVal = function () {
    return data;
  };

  return data;
}

var path = _interopRequireWildcard(require("path"));

function _index() {
  const data = require("../index");

  _index = function () {
    return data;
  };

  return data;
}

function _platformPackager() {
  const data = require("../platformPackager");

  _platformPackager = function () {
    return data;
  };

  return data;
}

function _pathManager() {
  const data = require("../util/pathManager");

  _pathManager = function () {
    return data;
  };

  return data;
}

function _electronMac() {
  const data = require("./electronMac");

  _electronMac = function () {
    return data;
  };

  return data;
}

function _electronVersion() {
  const data = require("./electronVersion");

  _electronVersion = function () {
    return data;
  };

  return data;
}

function _getRequireWildcardCache() { if (typeof WeakMap !== "function") return null; var cache = new WeakMap(); _getRequireWildcardCache = function () { return cache; }; return cache; }

function _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function createDownloadOpts(opts, platform, arch, electronVersion) {
  return {
    platform,
    arch,
    version: electronVersion,
    ...opts.electronDownload
  };
}

async function beforeCopyExtraFiles(options) {
  const packager = options.packager;
  const appOutDir = options.appOutDir;

  if (packager.platform === _index().Platform.LINUX) {
    if (!(0, _platformPackager().isSafeToUnpackElectronOnRemoteBuildServer)(packager)) {
      const linuxPackager = packager;
      const executable = path.join(appOutDir, linuxPackager.executableName);
      await (0, _fsExtra().rename)(path.join(appOutDir, "electron"), executable);
    }
  } else if (packager.platform === _index().Platform.WINDOWS) {
    const executable = path.join(appOutDir, `${packager.appInfo.productFilename}.exe`);
    await (0, _fsExtra().rename)(path.join(appOutDir, "electron.exe"), executable);
  } else {
    await (0, _electronMac().createMacApp)(packager, appOutDir, options.asarIntegrity, options.platformName === "mas");
    const wantedLanguages = (0, _builderUtil().asArray)(packager.platformSpecificBuildOptions.electronLanguages);

    if (wantedLanguages.length === 0) {
      return;
    } // noinspection SpellCheckingInspection


    const langFileExt = ".lproj";
    const resourcesDir = packager.getResourcesDir(appOutDir);
    await _bluebirdLst().default.map((0, _fsExtra().readdir)(resourcesDir), file => {
      if (!file.endsWith(langFileExt)) {
        return;
      }

      const language = file.substring(0, file.length - langFileExt.length);

      if (!wantedLanguages.includes(language)) {
        return (0, _fsExtra().remove)(path.join(resourcesDir, file));
      }

      return;
    }, _fs().CONCURRENCY);
  }
}

class ElectronFramework {
  constructor(name, version, distMacOsAppName) {
    this.name = name;
    this.version = version;
    this.distMacOsAppName = distMacOsAppName; // noinspection JSUnusedGlobalSymbols

    this.macOsDefaultTargets = ["zip", "dmg"]; // noinspection JSUnusedGlobalSymbols

    this.defaultAppIdPrefix = "com.electron."; // noinspection JSUnusedGlobalSymbols

    this.isCopyElevateHelper = true; // noinspection JSUnusedGlobalSymbols

    this.isNpmRebuildRequired = true;
  }

  getDefaultIcon(platform) {
    if (platform === _index().Platform.LINUX) {
      return path.join((0, _pathManager().getTemplatePath)("icons"), "electron-linux");
    } else {
      // default icon is embedded into app skeleton
      return null;
    }
  }

  prepareApplicationStageDirectory(options) {
    return unpack(options, createDownloadOpts(options.packager.config, options.platformName, options.arch, this.version), this.distMacOsAppName);
  }

  beforeCopyExtraFiles(options) {
    return beforeCopyExtraFiles(options);
  }

}

async function createElectronFrameworkSupport(configuration, packager) {
  let version = configuration.electronVersion;

  if (version == null) {
    // for prepacked app asar no dev deps in the app.asar
    if (packager.isPrepackedAppAsar) {
      version = await (0, _electronVersion().getElectronVersionFromInstalled)(packager.projectDir);

      if (version == null) {
        throw new Error(`Cannot compute electron version for prepacked asar`);
      }
    } else {
      version = await (0, _electronVersion().computeElectronVersion)(packager.projectDir, new (_lazyVal().Lazy)(() => Promise.resolve(packager.metadata)));
    }

    configuration.electronVersion = version;
  }

  return new ElectronFramework("electron", version, "Electron.app");
}

async function unpack(prepareOptions, options, distMacOsAppName) {
  const packager = prepareOptions.packager;
  const out = prepareOptions.appOutDir;
  let dist = packager.config.electronDist;

  if (dist != null) {
    const zipFile = `electron-v${options.version}-${prepareOptions.platformName}-${options.arch}.zip`;
    const resolvedDist = path.resolve(packager.projectDir, dist);

    if ((await (0, _fs().statOrNull)(path.join(resolvedDist, zipFile))) != null) {
      options.cache = resolvedDist;
      dist = null;
    }
  }

  let isFullCleanup = false;

  if (dist == null) {
    if ((0, _platformPackager().isSafeToUnpackElectronOnRemoteBuildServer)(packager)) {
      return;
    }

    await (0, _builderUtil().executeAppBuilder)(["unpack-electron", "--configuration", JSON.stringify([options]), "--output", out, "--distMacOsAppName", distMacOsAppName]);
  } else {
    isFullCleanup = true;
    const source = packager.getElectronSrcDir(dist);
    const destination = packager.getElectronDestinationDir(out);

    _builderUtil().log.info({
      source,
      destination
    }, "copying Electron");

    await (0, _fsExtra().emptyDir)(out);
    await (0, _fs().copyDir)(source, destination, {
      isUseHardLink: _fs().DO_NOT_USE_HARD_LINKS
    });
  }

  await cleanupAfterUnpack(prepareOptions, distMacOsAppName, isFullCleanup);
}

function cleanupAfterUnpack(prepareOptions, distMacOsAppName, isFullCleanup) {
  const out = prepareOptions.appOutDir;

  const isMac = prepareOptions.packager.platform === _index().Platform.MAC;

  const resourcesPath = isMac ? path.join(out, distMacOsAppName, "Contents", "Resources") : path.join(out, "resources");
  return Promise.all([isFullCleanup ? (0, _fs().unlinkIfExists)(path.join(resourcesPath, "default_app.asar")) : Promise.resolve(), isFullCleanup ? (0, _fs().unlinkIfExists)(path.join(out, "version")) : Promise.resolve(), isMac ? Promise.resolve() : (0, _fsExtra().rename)(path.join(out, "LICENSE"), path.join(out, "LICENSE.electron.txt")).catch(() => {})]);
} 
// __ts-babel@6.0.4
//# sourceMappingURL=ElectronFramework.js.map