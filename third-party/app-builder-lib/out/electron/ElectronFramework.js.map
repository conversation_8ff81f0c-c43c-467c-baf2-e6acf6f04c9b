{"version": 3, "sources": ["../../src/electron/ElectronFramework.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AAGA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAGA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;;;AA+BA,SAAS,kBAAT,CAA4B,IAA5B,EAAiD,QAAjD,EAAiF,IAAjF,EAA+F,eAA/F,EAAsH;AACpH,SAAO;AACL,IAAA,QADK;AAEL,IAAA,IAFK;AAGL,IAAA,OAAO,EAAE,eAHJ;AAIL,OAAG,IAAI,CAAC;AAJH,GAAP;AAMD;;AAED,eAAe,oBAAf,CAAoC,OAApC,EAAwE;AACtE,QAAM,QAAQ,GAAG,OAAO,CAAC,QAAzB;AACA,QAAM,SAAS,GAAG,OAAO,CAAC,SAA1B;;AACA,MAAI,QAAQ,CAAC,QAAT,KAAsB,kBAAS,KAAnC,EAA0C;AACxC,QAAI,CAAC,mEAA0C,QAA1C,CAAL,EAA0D;AACxD,YAAM,aAAa,GAAI,QAAvB;AACA,YAAM,UAAU,GAAG,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,aAAa,CAAC,cAAnC,CAAnB;AACA,YAAM,uBAAO,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,UAArB,CAAP,EAAyC,UAAzC,CAAN;AACD;AACF,GAND,MAOK,IAAI,QAAQ,CAAC,QAAT,KAAsB,kBAAS,OAAnC,EAA4C;AAC/C,UAAM,UAAU,GAAG,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,GAAG,QAAQ,CAAC,OAAT,CAAiB,eAAe,MAAxD,CAAnB;AACA,UAAM,uBAAO,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,cAArB,CAAP,EAA6C,UAA7C,CAAN;AACD,GAHI,MAIA;AACH,UAAM,iCAAa,QAAb,EAAsC,SAAtC,EAAiD,OAAO,CAAC,aAAzD,EAAyE,OAAO,CAAC,YAAR,KAAkD,KAA3H,CAAN;AAEA,UAAM,eAAe,GAAG,4BAAQ,QAAQ,CAAC,4BAAT,CAAsC,iBAA9C,CAAxB;;AACA,QAAI,eAAe,CAAC,MAAhB,KAA2B,CAA/B,EAAkC;AAChC;AACD,KANE,CAQH;;;AACA,UAAM,WAAW,GAAG,QAApB;AACA,UAAM,YAAY,GAAG,QAAQ,CAAC,eAAT,CAAyB,SAAzB,CAArB;AACA,UAAM,uBAAgB,GAAhB,CAAoB,wBAAQ,YAAR,CAApB,EAA2C,IAAI,IAAG;AACtD,UAAI,CAAC,IAAI,CAAC,QAAL,CAAc,WAAd,CAAL,EAAiC;AAC/B;AACD;;AAED,YAAM,QAAQ,GAAG,IAAI,CAAC,SAAL,CAAe,CAAf,EAAkB,IAAI,CAAC,MAAL,GAAc,WAAW,CAAC,MAA5C,CAAjB;;AACA,UAAI,CAAC,eAAe,CAAC,QAAhB,CAAyB,QAAzB,CAAL,EAAyC;AACvC,eAAO,uBAAO,IAAI,CAAC,IAAL,CAAU,YAAV,EAAwB,IAAxB,CAAP,CAAP;AACD;;AACD;AACD,KAVK,EAUH,iBAVG,CAAN;AAWD;AACF;;AAED,MAAM,iBAAN,CAAuB;AAUrB,EAAA,WAAA,CAAqB,IAArB,EAA4C,OAA5C,EAAsE,gBAAtE,EAA8F;AAAzE,SAAA,IAAA,GAAA,IAAA;AAAuB,SAAA,OAAA,GAAA,OAAA;AAA0B,SAAA,gBAAA,GAAA,gBAAA,CAAwB,CAT9F;;AACS,SAAA,mBAAA,GAAsB,CAAC,KAAD,EAAQ,KAAR,CAAtB,CAQqF,CAP9F;;AACS,SAAA,kBAAA,GAAqB,eAArB,CAMqF,CAL9F;;AACS,SAAA,mBAAA,GAAsB,IAAtB,CAIqF,CAH9F;;AACS,SAAA,oBAAA,GAAuB,IAAvB;AAGR;;AAED,EAAA,cAAc,CAAC,QAAD,EAAmB;AAC/B,QAAI,QAAQ,KAAK,kBAAS,KAA1B,EAAiC;AAC/B,aAAO,IAAI,CAAC,IAAL,CAAU,oCAAgB,OAAhB,CAAV,EAAoC,gBAApC,CAAP;AACD,KAFD,MAGK;AACH;AACA,aAAO,IAAP;AACD;AACF;;AAED,EAAA,gCAAgC,CAAC,OAAD,EAAiD;AAC/E,WAAO,MAAM,CAAC,OAAD,EAAU,kBAAkB,CAAC,OAAO,CAAC,QAAR,CAAiB,MAAlB,EAA0B,OAAO,CAAC,YAAlC,EAAgD,OAAO,CAAC,IAAxD,EAA8D,KAAK,OAAnE,CAA5B,EAAyG,KAAK,gBAA9G,CAAb;AACD;;AAED,EAAA,oBAAoB,CAAC,OAAD,EAAqC;AACvD,WAAO,oBAAoB,CAAC,OAAD,CAA3B;AACD;;AA7BoB;;AAgChB,eAAe,8BAAf,CAA8C,aAA9C,EAA4E,QAA5E,EAA8F;AACnG,MAAI,OAAO,GAAG,aAAa,CAAC,eAA5B;;AACA,MAAI,OAAO,IAAI,IAAf,EAAqB;AACnB;AACA,QAAI,QAAQ,CAAC,kBAAb,EAAiC;AAC/B,MAAA,OAAO,GAAG,MAAM,wDAAgC,QAAQ,CAAC,UAAzC,CAAhB;;AACA,UAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,cAAM,IAAI,KAAJ,CAAU,oDAAV,CAAN;AACD;AACF,KALD,MAMK;AACH,MAAA,OAAO,GAAG,MAAM,+CAAuB,QAAQ,CAAC,UAAhC,EAA4C,KAAI,eAAJ,EAAS,MAAM,OAAO,CAAC,OAAR,CAAgB,QAAQ,CAAC,QAAzB,CAAf,CAA5C,CAAhB;AACD;;AACD,IAAA,aAAa,CAAC,eAAd,GAAgC,OAAhC;AACD;;AAED,SAAO,IAAI,iBAAJ,CAAsB,UAAtB,EAAkC,OAAlC,EAA2C,cAA3C,CAAP;AACD;;AAED,eAAe,MAAf,CAAsB,cAAtB,EAA+E,OAA/E,EAAiH,gBAAjH,EAAyI;AACvI,QAAM,QAAQ,GAAG,cAAc,CAAC,QAAhC;AACA,QAAM,GAAG,GAAG,cAAc,CAAC,SAA3B;AAEA,MAAI,IAAI,GAA8B,QAAQ,CAAC,MAAT,CAAgB,YAAtD;;AACA,MAAI,IAAI,IAAI,IAAZ,EAAkB;AAChB,UAAM,OAAO,GAAG,aAAa,OAAO,CAAC,OAAO,IAAI,cAAc,CAAC,YAAY,IAAI,OAAO,CAAC,IAAI,MAA3F;AACA,UAAM,YAAY,GAAG,IAAI,CAAC,OAAL,CAAa,QAAQ,CAAC,UAAtB,EAAkC,IAAlC,CAArB;;AACA,QAAI,CAAC,MAAM,sBAAW,IAAI,CAAC,IAAL,CAAU,YAAV,EAAwB,OAAxB,CAAX,CAAP,KAAwD,IAA5D,EAAkE;AAChE,MAAA,OAAO,CAAC,KAAR,GAAgB,YAAhB;AACA,MAAA,IAAI,GAAG,IAAP;AACD;AACF;;AAED,MAAI,aAAa,GAAG,KAApB;;AACA,MAAI,IAAI,IAAI,IAAZ,EAAkB;AAChB,QAAI,mEAA0C,QAA1C,CAAJ,EAAyD;AACvD;AACD;;AAED,UAAM,sCAAkB,CAAC,iBAAD,EAAoB,iBAApB,EAAuC,IAAI,CAAC,SAAL,CAAe,CAAC,OAAD,CAAf,CAAvC,EAAkE,UAAlE,EAA8E,GAA9E,EAAmF,oBAAnF,EAAyG,gBAAzG,CAAlB,CAAN;AACD,GAND,MAOK;AACH,IAAA,aAAa,GAAG,IAAhB;AACA,UAAM,MAAM,GAAG,QAAQ,CAAC,iBAAT,CAA2B,IAA3B,CAAf;AACA,UAAM,WAAW,GAAG,QAAQ,CAAC,yBAAT,CAAmC,GAAnC,CAApB;;AACA,uBAAI,IAAJ,CAAS;AAAC,MAAA,MAAD;AAAS,MAAA;AAAT,KAAT,EAAgC,kBAAhC;;AACA,UAAM,yBAAS,GAAT,CAAN;AACA,UAAM,mBAAQ,MAAR,EAAgB,WAAhB,EAA6B;AACjC,MAAA,aAAa,EAAE;AADkB,KAA7B,CAAN;AAGD;;AAED,QAAM,kBAAkB,CAAC,cAAD,EAAiB,gBAAjB,EAAmC,aAAnC,CAAxB;AACD;;AAED,SAAS,kBAAT,CAA4B,cAA5B,EAAqF,gBAArF,EAA+G,aAA/G,EAAqI;AACnI,QAAM,GAAG,GAAG,cAAc,CAAC,SAA3B;;AACA,QAAM,KAAK,GAAG,cAAc,CAAC,QAAf,CAAwB,QAAxB,KAAqC,kBAAS,GAA5D;;AACA,QAAM,aAAa,GAAG,KAAK,GAAG,IAAI,CAAC,IAAL,CAAU,GAAV,EAAe,gBAAf,EAAiC,UAAjC,EAA6C,WAA7C,CAAH,GAA+D,IAAI,CAAC,IAAL,CAAU,GAAV,EAAe,WAAf,CAA1F;AAEA,SAAO,OAAO,CAAC,GAAR,CAAY,CACjB,aAAa,GAAG,0BAAe,IAAI,CAAC,IAAL,CAAU,aAAV,EAAyB,kBAAzB,CAAf,CAAH,GAAkE,OAAO,CAAC,OAAR,EAD9D,EAEjB,aAAa,GAAG,0BAAe,IAAI,CAAC,IAAL,CAAU,GAAV,EAAe,SAAf,CAAf,CAAH,GAA+C,OAAO,CAAC,OAAR,EAF3C,EAGjB,KAAK,GAAG,OAAO,CAAC,OAAR,EAAH,GAAuB,uBAAO,IAAI,CAAC,IAAL,CAAU,GAAV,EAAe,SAAf,CAAP,EAAkC,IAAI,CAAC,IAAL,CAAU,GAAV,EAAe,sBAAf,CAAlC,EAA0E,KAA1E,CAAgF,MAAK,CAAe,CAApG,CAHX,CAAZ,CAAP;AAKD,C", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { asArray, executeAppBuilder, log } from \"builder-util\"\nimport { CONCURRENCY, copyDir, DO_NOT_USE_HARD_LINKS, statOrNull, unlinkIfExists } from \"builder-util/out/fs\"\nimport { emptyDir, readdir, remove, rename } from \"fs-extra\"\nimport { Lazy } from \"lazy-val\"\nimport * as path from \"path\"\nimport { Configuration } from \"../configuration\"\nimport { BeforeCopyExtraFilesOptions, Framework, PrepareApplicationStageDirectoryOptions } from \"../Framework\"\nimport { Packager, Platform } from \"../index\"\nimport { LinuxPackager } from \"../linuxPackager\"\nimport MacPackager from \"../macPackager\"\nimport { isSafeToUnpackElectronOnRemoteBuildServer } from \"../platformPackager\"\nimport { getTemplatePath } from \"../util/pathManager\"\nimport { createMacApp } from \"./electronMac\"\nimport { computeElectronVersion, getElectronVersionFromInstalled } from \"./electronVersion\"\n\nexport type ElectronPlatformName = \"darwin\" | \"linux\" | \"win32\" | \"mas\"\n\nexport interface ElectronDownloadOptions {\n  // https://github.com/electron-userland/electron-builder/issues/3077\n  // must be optional\n  version?: string\n\n  /**\n   * The [cache location](https://github.com/electron-userland/electron-download#cache-location).\n   */\n  cache?: string | null\n\n  /**\n   * The mirror.\n   */\n  mirror?: string | null\n\n  /** @private */\n  customDir?: string | null\n  /** @private */\n  customFilename?: string | null\n\n  strictSSL?: boolean\n  isVerifyChecksum?: boolean\n\n  platform?: ElectronPlatformName\n  arch?: string\n}\n\nfunction createDownloadOpts(opts: Configuration, platform: ElectronPlatformName, arch: string, electronVersion: string): ElectronDownloadOptions {\n  return {\n    platform,\n    arch,\n    version: electronVersion,\n    ...opts.electronDownload,\n  }\n}\n\nasync function beforeCopyExtraFiles(options: BeforeCopyExtraFilesOptions) {\n  const packager = options.packager\n  const appOutDir = options.appOutDir\n  if (packager.platform === Platform.LINUX) {\n    if (!isSafeToUnpackElectronOnRemoteBuildServer(packager)) {\n      const linuxPackager = (packager as LinuxPackager)\n      const executable = path.join(appOutDir, linuxPackager.executableName)\n      await rename(path.join(appOutDir, \"electron\"), executable)\n    }\n  }\n  else if (packager.platform === Platform.WINDOWS) {\n    const executable = path.join(appOutDir, `${packager.appInfo.productFilename}.exe`)\n    await rename(path.join(appOutDir, \"electron.exe\"), executable)\n  }\n  else {\n    await createMacApp(packager as MacPackager, appOutDir, options.asarIntegrity, (options.platformName as ElectronPlatformName) === \"mas\")\n\n    const wantedLanguages = asArray(packager.platformSpecificBuildOptions.electronLanguages)\n    if (wantedLanguages.length === 0) {\n      return\n    }\n\n    // noinspection SpellCheckingInspection\n    const langFileExt = \".lproj\"\n    const resourcesDir = packager.getResourcesDir(appOutDir)\n    await BluebirdPromise.map(readdir(resourcesDir), file => {\n      if (!file.endsWith(langFileExt)) {\n        return\n      }\n\n      const language = file.substring(0, file.length - langFileExt.length)\n      if (!wantedLanguages.includes(language)) {\n        return remove(path.join(resourcesDir, file))\n      }\n      return\n    }, CONCURRENCY)\n  }\n}\n\nclass ElectronFramework implements Framework {\n  // noinspection JSUnusedGlobalSymbols\n  readonly macOsDefaultTargets = [\"zip\", \"dmg\"]\n  // noinspection JSUnusedGlobalSymbols\n  readonly defaultAppIdPrefix = \"com.electron.\"\n  // noinspection JSUnusedGlobalSymbols\n  readonly isCopyElevateHelper = true\n  // noinspection JSUnusedGlobalSymbols\n  readonly isNpmRebuildRequired = true\n\n  constructor(readonly name: string, readonly version: string, readonly distMacOsAppName: string) {\n  }\n\n  getDefaultIcon(platform: Platform) {\n    if (platform === Platform.LINUX) {\n      return path.join(getTemplatePath(\"icons\"), \"electron-linux\")\n    }\n    else {\n      // default icon is embedded into app skeleton\n      return null\n    }\n  }\n\n  prepareApplicationStageDirectory(options: PrepareApplicationStageDirectoryOptions) {\n    return unpack(options, createDownloadOpts(options.packager.config, options.platformName, options.arch, this.version), this.distMacOsAppName)\n  }\n\n  beforeCopyExtraFiles(options: BeforeCopyExtraFilesOptions) {\n    return beforeCopyExtraFiles(options)\n  }\n}\n\nexport async function createElectronFrameworkSupport(configuration: Configuration, packager: Packager): Promise<Framework> {\n  let version = configuration.electronVersion\n  if (version == null) {\n    // for prepacked app asar no dev deps in the app.asar\n    if (packager.isPrepackedAppAsar) {\n      version = await getElectronVersionFromInstalled(packager.projectDir)\n      if (version == null) {\n        throw new Error(`Cannot compute electron version for prepacked asar`)\n      }\n    }\n    else {\n      version = await computeElectronVersion(packager.projectDir, new Lazy(() => Promise.resolve(packager.metadata)))\n    }\n    configuration.electronVersion = version\n  }\n\n  return new ElectronFramework(\"electron\", version, \"Electron.app\")\n}\n\nasync function unpack(prepareOptions: PrepareApplicationStageDirectoryOptions, options: ElectronDownloadOptions, distMacOsAppName: string) {\n  const packager = prepareOptions.packager\n  const out = prepareOptions.appOutDir\n\n  let dist: string | null | undefined = packager.config.electronDist\n  if (dist != null) {\n    const zipFile = `electron-v${options.version}-${prepareOptions.platformName}-${options.arch}.zip`\n    const resolvedDist = path.resolve(packager.projectDir, dist)\n    if ((await statOrNull(path.join(resolvedDist, zipFile))) != null) {\n      options.cache = resolvedDist\n      dist = null\n    }\n  }\n\n  let isFullCleanup = false\n  if (dist == null) {\n    if (isSafeToUnpackElectronOnRemoteBuildServer(packager)) {\n      return\n    }\n\n    await executeAppBuilder([\"unpack-electron\", \"--configuration\", JSON.stringify([options]), \"--output\", out, \"--distMacOsAppName\", distMacOsAppName])\n  }\n  else {\n    isFullCleanup = true\n    const source = packager.getElectronSrcDir(dist)\n    const destination = packager.getElectronDestinationDir(out)\n    log.info({source, destination}, \"copying Electron\")\n    await emptyDir(out)\n    await copyDir(source, destination, {\n      isUseHardLink: DO_NOT_USE_HARD_LINKS,\n    })\n  }\n\n  await cleanupAfterUnpack(prepareOptions, distMacOsAppName, isFullCleanup)\n}\n\nfunction cleanupAfterUnpack(prepareOptions: PrepareApplicationStageDirectoryOptions, distMacOsAppName: string, isFullCleanup: boolean) {\n  const out = prepareOptions.appOutDir\n  const isMac = prepareOptions.packager.platform === Platform.MAC\n  const resourcesPath = isMac ? path.join(out, distMacOsAppName, \"Contents\", \"Resources\") : path.join(out, \"resources\")\n\n  return Promise.all([\n    isFullCleanup ? unlinkIfExists(path.join(resourcesPath, \"default_app.asar\")) : Promise.resolve(),\n    isFullCleanup ? unlinkIfExists(path.join(out, \"version\")) : Promise.resolve(),\n    isMac ? Promise.resolve() : rename(path.join(out, \"LICENSE\"), path.join(out, \"LICENSE.electron.txt\")).catch(() => {/* ignore */}),\n  ])\n}\n"], "sourceRoot": ""}