{"version": 3, "sources": ["../../src/electron/electronVersion.ts"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;AAIA,MAAM,gBAAgB,GAAG,CAAC,UAAD,EAAa,mBAAb,EAAkC,2BAAlC,EAA+D,kBAA/D,CAAzB;;AAEO,eAAe,kBAAf,CAAkC,UAAlC,EAAsD,MAAtD,EAA8E,eAAA,GAAiC,KAAI,eAAJ,EAAS,MAAM,4CAAqB,yBAAS,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,cAAtB,CAAT,CAArB,CAAf,CAA/G,EAAoM;AACzM,MAAI,MAAM,IAAI,IAAd,EAAoB;AAClB,IAAA,MAAM,GAAG,MAAM,yBAAU,UAAV,EAAsB,IAAtB,EAA4B,IAA5B,CAAf;AACD;;AACD,MAAI,MAAM,CAAC,eAAP,IAA0B,IAA9B,EAAoC;AAClC,WAAO,MAAM,CAAC,eAAd;AACD;;AACD,SAAO,MAAM,sBAAsB,CAAC,UAAD,EAAa,eAAb,CAAnC;AACD;;AAEM,eAAe,+BAAf,CAA+C,UAA/C,EAAiE;AACtE,OAAK,MAAM,IAAX,IAAmB,gBAAnB,EAAqC;AACnC,QAAI;AACF,aAAO,CAAC,MAAM,yBAAS,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,cAAtB,EAAsC,IAAtC,EAA4C,cAA5C,CAAT,CAAP,EAA8E,OAArF;AACD,KAFD,CAGA,OAAO,CAAP,EAAU;AACR,UAAI,CAAC,CAAC,IAAF,KAAW,QAAf,EAAyB;AACvB,2BAAI,IAAJ,CAAS;AAAC,UAAA,IAAD;AAAO,UAAA,KAAK,EAAE;AAAd,SAAT,EAA2B,2CAA3B;AACD;AACF;AACF;;AACD,SAAO,IAAP;AACD;;AAEM,eAAe,kBAAf,CAAkC,UAAlC,EAAoD;AACzD,OAAK,MAAM,IAAX,IAAmB,gBAAnB,EAAqC;AACnC,QAAI;AACF,aAAQ,MAAM,yBAAS,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,cAAtB,EAAsC,IAAtC,EAA4C,cAA5C,CAAT,CAAd;AACD,KAFD,CAGA,OAAO,CAAP,EAAU;AACR,UAAI,CAAC,CAAC,IAAF,KAAW,QAAf,EAAyB;AACvB,2BAAI,IAAJ,CAAS;AAAC,UAAA,IAAD;AAAO,UAAA,KAAK,EAAE;AAAd,SAAT,EAA2B,sCAA3B;AACD;AACF;AACF;;AACD,SAAO,IAAP;AACD;AAED;;;AACO,eAAe,sBAAf,CAAsC,UAAtC,EAA0D,eAA1D,EAAwF;AAC7F,QAAM,MAAM,GAAG,MAAM,+BAA+B,CAAC,UAAD,CAApD;;AACA,MAAI,MAAM,IAAI,IAAd,EAAoB;AAClB,WAAO,MAAP;AACD;;AAED,QAAM,UAAU,GAAG,uBAAuB,CAAC,MAAM,eAAiB,CAAC,KAAzB,CAA1C;;AACA,MAAI,CAAA,UAAU,KAAA,IAAV,IAAA,UAAU,KAAA,KAAA,CAAV,GAAU,KAAA,CAAV,GAAA,UAAU,CAAE,IAAZ,MAAqB,kBAAzB,EAA6C;AAC3C,uBAAI,IAAJ,CAAS,+FAAT;;AACA,UAAM,OAAO,GAAG,MAAM,iCAAa,OAAb,CAAqB;AACzC,MAAA,QAAQ,EAAE,YAD+B;AAEzC,MAAA,IAAI,EAAE,mCAFmC;AAGzC,MAAA,OAAO,EAAE;AACP,QAAA,MAAM,EAAE;AADD;AAHgC,KAArB,CAAtB;AAOA,UAAM,IAAI,GAAG,oCAAS,OAAT,CAAb;AACA,UAAM,aAAa,GAAG,IAAI,CAAC,OAAL,CAAa,OAAb,EAAsB,KAAtB,EAA6B,iCAA7B,CAAtB;AACA,UAAM,CAAC,GAAG,aAAa,CAAC,OAAd,CAAsB,MAAtB,EAA8B,SAA9B,CAAwC,MAAxC,EAAgD,KAAhD,CAAsD,mBAAtD,EAA6E,CAA7E,CAAV;AACA,WAAO,CAAC,CAAC,UAAF,CAAa,GAAb,IAAoB,CAAC,CAAC,SAAF,CAAY,CAAZ,CAApB,GAAqC,CAA5C;AACD,GAbD,MAcK,IAAI,CAAA,UAAU,KAAA,IAAV,IAAA,UAAU,KAAA,KAAA,CAAV,GAAU,KAAA,CAAV,GAAA,UAAU,CAAE,OAAZ,MAAwB,QAA5B,EAAsC;AACzC,uBAAI,IAAJ,CAAS,+GAAT;;AACA,QAAI;AACF,YAAM,WAAW,GAAG,IAAI,CAAC,KAAL,CAAY,MAAM,iCAAa,OAAb,CAAqB;AACzD,QAAA,QAAQ,EAAE,YAD+C;AAEzD,QAAA,IAAI,EAAE,aAAa,UAAU,CAAC,IAAX,KAAoB,kBAApB,GAAyC,WAAzC,GAAuD,UAAU,kBAF3B;AAGzD,QAAA,OAAO,EAAE;AACP,UAAA,MAAM,EAAE;AADD;AAHgD,OAArB,CAAlB,CAApB;AAOA,YAAM,OAAO,GAAI,WAAW,CAAC,QAAZ,CAAqB,UAArB,CAAgC,GAAhC,CAAD,GAAyC,WAAW,CAAC,QAAZ,CAAqB,SAArB,CAA+B,CAA/B,CAAzC,GAA6E,WAAW,CAAC,QAAzG;;AACA,yBAAI,IAAJ,CAAS;AAAC,QAAA;AAAD,OAAT,EAAoB,WAAW,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,OAAO,EAApE;;AACA,aAAO,OAAP;AACD,KAXD,CAYA,OAAO,CAAP,EAAU;AACR,yBAAI,IAAJ,CAAS,CAAT;AACD;;AAED,UAAM,KAAI,wCAAJ,EAA8B,mEAAmE,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,cAAtB,CAAqC,GAAtI,CAAN;AACD;;AAED,QAAM,OAAO,GAAG,UAAU,KAAA,IAAV,IAAA,UAAU,KAAA,KAAA,CAAV,GAAU,KAAA,CAAV,GAAA,UAAU,CAAE,OAA5B;;AACA,MAAI,OAAO,IAAI,IAAX,IAAmB,CAAC,MAAM,IAAN,CAAW,OAAX,CAAxB,EAA6C;AAC3C,UAAM,cAAc,GAAG,OAAO,IAAI,IAAX,GAAkB,EAAlB,GAAuB,kBAAkB,OAAO,4BAAvE;AACA,UAAM,KAAI,wCAAJ,EAA8B,oHAAoH,cAAc,iGAAhK,CAAN;AACD;;AAED,SAAO,MAAM,GAAC,MAAP,CAAc,OAAd,EAAyB,QAAzB,EAAP;AACD;;AAOD,SAAS,uBAAT,CAAiC,WAAjC,EAAiD;AAC/C,OAAK,MAAM,IAAX,IAAmB,gBAAnB,EAAqC;AACnC,UAAM,eAAe,GAAG,WAAW,CAAC,eAApC;AACA,QAAI,GAAG,GAAG,eAAe,IAAI,IAAnB,GAA0B,IAA1B,GAAiC,eAAe,CAAC,IAAD,CAA1D;;AACA,QAAI,GAAG,IAAI,IAAX,EAAiB;AACf,YAAM,YAAY,GAAG,WAAW,CAAC,YAAjC;AACA,MAAA,GAAG,GAAG,YAAY,IAAI,IAAhB,GAAuB,IAAvB,GAA8B,YAAY,CAAC,IAAD,CAAhD;AACD;;AACD,QAAI,GAAG,IAAI,IAAX,EAAiB;AACf,aAAO;AAAC,QAAA,IAAD;AAAO,QAAA,OAAO,EAAE;AAAhB,OAAP;AACD;AACF;;AACD,SAAO,IAAP;AACD,C", "sourcesContent": ["import { InvalidConfigurationError, log } from \"builder-util\"\nimport { parseXml } from \"builder-util-runtime\"\nimport { httpExecutor } from \"builder-util/out/nodeHttpExecutor\"\nimport { readJson } from \"fs-extra\"\nimport { Lazy } from \"lazy-val\"\nimport * as path from \"path\"\nimport { orNullIfFileNotExist } from \"read-config-file\"\nimport * as semver from \"semver\"\nimport { Configuration } from \"../configuration\"\nimport { getConfig } from \"../util/config\"\n\nexport type MetadataValue = Lazy<{ [key: string]: any } | null>\n\nconst electronPackages = [\"electron\", \"electron-prebuilt\", \"electron-prebuilt-compile\", \"electron-nightly\"]\n\nexport async function getElectronVersion(projectDir: string, config?: Configuration, projectMetadata: MetadataValue = new Lazy(() => orNullIfFileNotExist(readJson(path.join(projectDir, \"package.json\"))))): Promise<string> {\n  if (config == null) {\n    config = await getConfig(projectDir, null, null)\n  }\n  if (config.electronVersion != null) {\n    return config.electronVersion\n  }\n  return await computeElectronVersion(projectDir, projectMetadata)\n}\n\nexport async function getElectronVersionFromInstalled(projectDir: string) {\n  for (const name of electronPackages) {\n    try {\n      return (await readJson(path.join(projectDir, \"node_modules\", name, \"package.json\"))).version\n    }\n    catch (e) {\n      if (e.code !== \"ENOENT\") {\n        log.warn({name, error: e}, `cannot read electron version package.json`)\n      }\n    }\n  }\n  return null\n}\n\nexport async function getElectronPackage(projectDir: string) {\n  for (const name of electronPackages) {\n    try {\n      return (await readJson(path.join(projectDir, \"node_modules\", name, \"package.json\")))\n    }\n    catch (e) {\n      if (e.code !== \"ENOENT\") {\n        log.warn({name, error: e}, `cannot find electron in package.json`)\n      }\n    }\n  }\n  return null\n}\n\n/** @internal */\nexport async function computeElectronVersion(projectDir: string, projectMetadata: MetadataValue): Promise<string> {\n  const result = await getElectronVersionFromInstalled(projectDir)\n  if (result != null) {\n    return result\n  }\n\n  const dependency = findFromPackageMetadata(await projectMetadata!!.value)\n  if (dependency?.name === \"electron-nightly\") {\n    log.info(\"You are using a nightly version of electron, be warned that those builds are highly unstable.\")\n    const feedXml = await httpExecutor.request({\n      hostname: \"github.com\",\n      path: `/electron/nightlies/releases.atom`,\n      headers: {\n        accept: \"application/xml, application/atom+xml, text/xml, */*\",\n      },\n    })\n    const feed = parseXml(feedXml!!)\n    const latestRelease = feed.element(\"entry\", false, `No published versions on GitHub`)\n    const v = latestRelease.element(\"link\").attribute(\"href\").match(/\\/tag\\/v?([^/]+)$/)!![1]\n    return v.startsWith(\"v\") ? v.substring(1) : v\n  }\n  else if (dependency?.version === \"latest\") {\n    log.warn(\"Electron version is set to \\\"latest\\\", but it is recommended to set it to some more restricted version range.\")\n    try {\n      const releaseInfo = JSON.parse((await httpExecutor.request({\n        hostname: \"github.com\",\n        path: `/electron/${dependency.name === \"electron-nightly\" ? \"nightlies\" : \"electron\"}/releases/latest`,\n        headers: {\n          accept: \"application/json\",\n        },\n      }))!!)\n      const version = (releaseInfo.tag_name.startsWith(\"v\")) ? releaseInfo.tag_name.substring(1) : releaseInfo.tag_name\n      log.info({version}, `resolve ${dependency.name}@${dependency.version}`)\n      return version\n    }\n    catch (e) {\n      log.warn(e)\n    }\n\n    throw new InvalidConfigurationError(`Cannot find electron dependency to get electron version in the '${path.join(projectDir, \"package.json\")}'`)\n  }\n\n  const version = dependency?.version\n  if (version == null || !/^\\d/.test(version)) {\n    const versionMessage = version == null ? \"\" : ` and version (\"${version}\") is not fixed in project`\n    throw new InvalidConfigurationError(`Cannot compute electron version from installed node modules - none of the possible electron modules are installed${versionMessage}.\\nSee https://github.com/electron-userland/electron-builder/issues/3984#issuecomment-504968246`)\n  }\n\n  return semver.coerce(version)!!.toString()\n}\n\ninterface NameAndVersion {\n  readonly name: string\n  readonly version: string\n}\n\nfunction findFromPackageMetadata(packageData: any): NameAndVersion | null {\n  for (const name of electronPackages) {\n    const devDependencies = packageData.devDependencies\n    let dep = devDependencies == null ? null : devDependencies[name]\n    if (dep == null) {\n      const dependencies = packageData.dependencies\n      dep = dependencies == null ? null : dependencies[name]\n    }\n    if (dep != null) {\n      return {name, version: dep}\n    }\n  }\n  return null\n}\n"], "sourceRoot": ""}