{"version": 3, "sources": ["../../src/electron/electronMac.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAGA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;;;AAEA,SAAS,QAAT,CAAkB,QAAlB,EAAoC,OAApC,EAAqD,OAArD,EAAoE;AAClE,SAAO,uBAAO,IAAI,CAAC,IAAL,CAAU,QAAV,EAAoB,OAApB,CAAP,EAAqC,IAAI,CAAC,IAAL,CAAU,QAAV,EAAoB,OAApB,CAArC,CAAP;AACD;;AAED,SAAS,WAAT,CAAqB,cAArB,EAAoD,cAApD,EAA4E,OAA5E,EAA6F,MAA7F,EAA2G;AACzG,SAAO,uBAAgB,GAAhB,CAAoB,cAApB,EAAoC,MAAM,IAAG;AAClD,UAAM,kBAAkB,GAAG,IAAI,CAAC,IAAL,CAAU,cAAV,EAA0B,GAAG,MAAM,GAAG,MAAM,MAA5C,EAAoD,UAApD,EAAgE,OAAhE,CAA3B;AACA,WAAO,QAAQ,CAAC,kBAAD,EAAqB,GAAG,MAAM,GAAG,MAAM,EAAvC,EAA2C,OAAO,GAAG,MAArD,CAAR,CACJ,IADI,CACC,MAAM,QAAQ,CAAC,cAAD,EAAiB,GAAG,MAAM,GAAG,MAAM,MAAnC,EAA2C,GAAG,OAAO,GAAG,MAAM,MAA9D,CADf,CAAP;AAED,GAJM,CAAP;AAKD;;AAED,SAAS,0BAAT,CAAoC,aAApC,EAAkE,aAAlE,EAAgG,mBAAhG,EAAoI,iBAApI,EAAsK,cAAtK,EAAmM;AAEjM,QAAM,MAAM,GAAG,CAAC,SAAD,CAAf;;AACA,MAAI,aAAa,IAAI,IAArB,EAA2B;AACzB,IAAA,MAAM,CAAC,IAAP,CAAY,YAAZ;AACD;;AACD,MAAI,aAAa,IAAI,IAArB,EAA2B;AACzB,IAAA,MAAM,CAAC,IAAP,CAAY,YAAZ;AACD;;AACD,MAAI,mBAAmB,IAAI,IAA3B,EAAiC;AAC/B,IAAA,MAAM,CAAC,IAAP,CAAY,oBAAZ;AACD;;AACD,MAAI,iBAAiB,IAAI,IAAzB,EAA+B;AAC7B,IAAA,MAAM,CAAC,IAAP,CAAY,kBAAZ;AACD;;AACD,MAAI,cAAc,IAAI,IAAtB,EAA4B;AAC1B,IAAA,MAAM,CAAC,IAAP,CAAY,eAAZ;AACD;;AACD,SAAO,MAAP;AACD;AAED;;;AACO,eAAe,YAAf,CAA4B,QAA5B,EAAmD,SAAnD,EAAsE,aAAtE,EAA2G,KAA3G,EAAyH;AAC9H,QAAM,OAAO,GAAG,QAAQ,CAAC,OAAzB;AACA,QAAM,WAAW,GAAG,OAAO,CAAC,eAA5B;AAEA,QAAM,YAAY,GAAG,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,QAAQ,CAAC,IAAT,CAAc,SAAd,CAAwB,gBAA7C,EAA+D,UAA/D,CAArB;AACA,QAAM,cAAc,GAAG,IAAI,CAAC,IAAL,CAAU,YAAV,EAAwB,YAAxB,CAAvB;AACA,QAAM,aAAa,GAAG,IAAI,CAAC,IAAL,CAAU,YAAV,EAAwB,SAAxB,EAAmC,YAAnC,CAAtB;AAEA,QAAM,gBAAgB,GAAG,IAAI,CAAC,IAAL,CAAU,YAAV,EAAwB,YAAxB,CAAzB;AACA,QAAM,mBAAmB,GAAG,IAAI,CAAC,IAAL,CAAU,cAAV,EAA0B,qBAA1B,EAAiD,UAAjD,EAA6D,YAA7D,CAA5B;AACA,QAAM,qBAAqB,GAAG,IAAI,CAAC,IAAL,CAAU,cAAV,EAA0B,wBAA1B,EAAoD,UAApD,EAAgE,YAAhE,CAA9B;AACA,QAAM,qBAAqB,GAAG,IAAI,CAAC,IAAL,CAAU,cAAV,EAA0B,wBAA1B,EAAoD,UAApD,EAAgE,YAAhE,CAA9B;AACA,QAAM,2BAA2B,GAAG,IAAI,CAAC,IAAL,CAAU,cAAV,EAA0B,gCAA1B,EAA4D,UAA5D,EAAwE,YAAxE,CAApC;AACA,QAAM,yBAAyB,GAAG,IAAI,CAAC,IAAL,CAAU,cAAV,EAA0B,8BAA1B,EAA0D,UAA1D,EAAsE,YAAtE,CAAlC;AACA,QAAM,sBAAsB,GAAG,IAAI,CAAC,IAAL,CAAU,cAAV,EAA0B,2BAA1B,EAAuD,UAAvD,EAAmE,YAAnE,CAA/B;AACA,QAAM,wBAAwB,GAAG,IAAI,CAAC,IAAL,CAAU,aAAV,EAAyB,2BAAzB,EAAsD,UAAtD,EAAkE,YAAlE,CAAjC;AAEA,QAAM,YAAY,GAAe,MAAM,2CAAwB,CAAC,cAAD,EAAiB,IAAjB,EAAuB,gBAAvB,EAAyC,IAAzC,EAA+C,mBAA/C,EAAoE,IAApE,EAA0E,qBAA1E,EAAiG,IAAjG,EAAuG,qBAAvG,EAA8H,IAA9H,EAAoI,2BAApI,EAAiK,IAAjK,EAAuK,yBAAvK,EAAkM,IAAlM,EAAwM,sBAAxM,EAAgO,IAAhO,EAAsO,wBAAtO,CAAxB,CAAvC;;AAEA,MAAI,YAAY,CAAC,CAAD,CAAZ,IAAmB,IAAvB,EAA6B;AAC3B,UAAM,IAAI,KAAJ,CAAU,yBAAV,CAAN;AACD;;AAED,QAAM,QAAQ,GAAG,YAAY,CAAC,CAAD,CAA7B;AACA,QAAM,WAAW,GAAG,YAAY,CAAC,CAAD,CAAhC;AACA,QAAM,aAAa,GAAG,YAAY,CAAC,CAAD,CAAlC;AACA,QAAM,aAAa,GAAG,YAAY,CAAC,CAAD,CAAlC;AACA,QAAM,mBAAmB,GAAG,YAAY,CAAC,CAAD,CAAxC;AACA,QAAM,iBAAiB,GAAG,YAAY,CAAC,CAAD,CAAtC;AACA,QAAM,cAAc,GAAG,YAAY,CAAC,CAAD,CAAnC;AACA,QAAM,gBAAgB,GAAG,YAAY,CAAC,CAAD,CAArC,CA9B8H,CAgC9H;;AACA,MAAI,YAAY,CAAC,CAAD,CAAZ,IAAmB,IAAvB,EAA6B;AAC3B,IAAA,MAAM,CAAC,MAAP,CAAc,QAAd,EAAwB,YAAY,CAAC,CAAD,CAApC;AACD;;AAED,QAAM,aAAa,GAAG,QAAQ,CAAC,MAA/B;AAEA;;;;;;;AAOA,QAAM,iBAAiB,GAAI,aAAqB,CAAC,kBAAD,CAAhD;;AACA,MAAI,iBAAiB,IAAI,IAAzB,EAA+B;AAC7B,uBAAI,IAAJ,CAAS,8EAAT;AACD;;AACD,QAAM,sBAAsB,GAAG,yCAAyB,QAAQ,CAAC,4BAAT,CAAsC,cAAtC,IAAwD,iBAAxD,IAA6E,GAAG,OAAO,CAAC,mBAAmB,SAApI,CAA/B;AAEA,QAAM,QAAQ,CAAC,eAAT,CAAyB,QAAzB,EAAmC,YAAnC,CAAN,CApD8H,CAsD9H;;AACA,MAAI,CAAC,KAAL,EAAY;AACV,IAAA,qBAAqB,CAAC,QAAD,CAArB;AACD;;AAED,EAAA,WAAW,CAAC,kBAAZ,GAAiC,GAAG,WAAW,SAA/C;AACA,EAAA,WAAW,CAAC,mBAAZ,GAAkC,GAAG,OAAO,CAAC,WAAW,SAAxD;AACA,EAAA,WAAW,CAAC,kBAAZ,GAAiC,sBAAjC;AACA,EAAA,WAAW,CAAC,eAAZ,GAA8B,QAAQ,CAAC,eAAvC;AAEA;;;;;;;;;AASA,WAAS,eAAT,CAAyB,MAAzB,EAAsC,OAAtC,EAAuD,4BAAvD,EAAmG;AACjG,IAAA,MAAM,CAAC,kBAAP,GAA4B,GAAG,WAAW,WAAW,OAAO,EAA5D;AACA,IAAA,MAAM,CAAC,mBAAP,GAA6B,GAAG,OAAO,CAAC,WAAW,WAAW,OAAO,EAArE;AACA,IAAA,MAAM,CAAC,kBAAP,GAA4B,4BAA4B,GACpD,yCAAyB,4BAAzB,CADoD,GAEpD,GAAG,sBAAsB,IAAI,OAAO,CAAC,OAAR,CAAgB,cAAhB,EAAgC,EAAhC,CAAmC,EAFpE;AAGA,IAAA,MAAM,CAAC,eAAP,GAAyB,QAAQ,CAAC,eAAlC;AACD;;AAED,MAAI,mBAAmB,IAAI,IAA3B,EAAiC;AAC/B,IAAA,eAAe,CAAC,mBAAD,EAAsB,YAAtB,EAAoC,QAAQ,CAAC,4BAAT,CAAsC,sBAA1E,CAAf;AACD;;AACD,MAAI,iBAAiB,IAAI,IAAzB,EAA+B;AAC7B,IAAA,eAAe,CAAC,iBAAD,EAAoB,UAApB,EAAgC,QAAQ,CAAC,4BAAT,CAAsC,oBAAtE,CAAf;AACD;;AACD,MAAI,cAAc,IAAI,IAAtB,EAA4B;AAC1B,IAAA,eAAe,CAAC,cAAD,EAAiB,OAAjB,EAA0B,QAAQ,CAAC,4BAAT,CAAsC,iBAAhE,CAAf;AACD;;AACD,MAAI,aAAa,IAAI,IAArB,EAA2B;AACzB,IAAA,eAAe,CAAC,aAAD,EAAgB,IAAhB,EAAsB,QAAQ,CAAC,4BAAT,CAAsC,gBAA5D,CAAf;AACD;;AACD,MAAI,aAAa,IAAI,IAArB,EAA2B;AACzB,IAAA,eAAe,CAAC,aAAD,EAAgB,IAAhB,EAAsB,QAAQ,CAAC,4BAAT,CAAsC,gBAA5D,CAAf;AACD;;AACD,MAAI,gBAAgB,IAAI,IAAxB,EAA8B;AAC5B,IAAA,gBAAgB,CAAC,kBAAjB,GAAsC,GAAG,WAAW,eAApD;AACA,IAAA,gBAAgB,CAAC,mBAAjB,GAAuC,GAAG,OAAO,CAAC,WAAW,eAA7D,CAF4B,CAG5B;;AACA,IAAA,gBAAgB,CAAC,kBAAjB,GAAsC,GAAG,OAAO,CAAC,mBAAmB,cAApE;AACA,IAAA,gBAAgB,CAAC,eAAjB,GAAmC,QAAQ,CAAC,eAA5C;AACD;;AAED,QAAM,SAAS,GAAG,4BAAQ,aAAa,CAAC,SAAtB,EAAiC,MAAjC,CAAwC,4BAAQ,QAAQ,CAAC,4BAAT,CAAsC,SAA9C,CAAxC,CAAlB;;AACA,MAAI,SAAS,CAAC,MAAV,GAAmB,CAAvB,EAA0B;AACxB,IAAA,QAAQ,CAAC,gBAAT,GAA4B,SAAS,CAAC,GAAV,CAAc,QAAQ,IAAG;AACnD,YAAM,OAAO,GAAG,4BAAQ,QAAQ,CAAC,OAAjB,CAAhB;;AACA,UAAI,OAAO,CAAC,MAAR,KAAmB,CAAvB,EAA0B;AACxB,cAAM,KAAI,wCAAJ,EAA8B,aAAa,QAAQ,CAAC,IAAI,0CAAxD,CAAN;AACD;;AACD,aAAO;AACL,QAAA,eAAe,EAAE,QAAQ,CAAC,IADrB;AAEL,QAAA,gBAAgB,EAAE,QAAQ,CAAC,IAAT,IAAiB,QAF9B;AAGL,QAAA,kBAAkB,EAAE,OAAO,CAAC,KAAR;AAHf,OAAP;AAKD,KAV2B,CAA5B;AAWD;;AAED,QAAM,gBAAgB,GAAG,QAAQ,CAAC,gBAAlC;;AACA,MAAI,gBAAgB,CAAC,MAAjB,GAA0B,CAA9B,EAAiC;AAC/B,IAAA,QAAQ,CAAC,qBAAT,GAAiC,MAAM,uBAAgB,GAAhB,CAAoB,gBAApB,EAAsC,MAAM,eAAN,IAAwB;AACnG,YAAM,UAAU,GAAG,4BAAQ,eAAe,CAAC,GAAxB,EAA6B,GAA7B,CAAiC,gCAAjC,CAAnB;AACA,YAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,WAAT,CAAqB,4CAAwB,eAAe,CAAC,IAAxC,EAA8C,IAA9C,CAArB,EAA0E,GAAG,UAAU,CAAC,CAAD,CAAG,OAA1F,CAAzB;AACA,UAAI,QAAQ,GAAG,QAAQ,CAAC,gBAAxB;;AACA,UAAI,UAAU,IAAI,IAAlB,EAAwB;AACtB,QAAA,QAAQ,GAAG,IAAI,CAAC,QAAL,CAAc,UAAd,CAAX;AACA,cAAM,0BAAe,UAAf,EAA2B,IAAI,CAAC,IAAL,CAAU,IAAI,CAAC,IAAL,CAAU,YAAV,EAAwB,WAAxB,CAAV,EAAgD,QAAhD,CAA3B,CAAN;AACD;;AAED,YAAM,MAAM,GAAG;AACb,QAAA,sBAAsB,EAAE,UADX;AAEb,QAAA,gBAAgB,EAAE,eAAe,CAAC,IAAhB,IAAwB,UAAU,CAAC,CAAD,CAFvC;AAGb,QAAA,gBAAgB,EAAE,eAAe,CAAC,IAAhB,IAAwB,QAH7B;AAIb,QAAA,aAAa,EAAE,eAAe,CAAC,IAAhB,IAAwB,SAJ1B;AAKb,QAAA,oBAAoB,EAAE;AALT,OAAf;;AAQA,UAAI,eAAe,CAAC,SAApB,EAA+B;AAC7B,QAAA,MAAM,CAAC,eAAP,GAAyB,IAAzB;AACD;;AACD,aAAO,MAAP;AACD,KArBsC,CAAvC;AAsBD;;AAED,MAAI,aAAa,IAAI,IAArB,EAA2B;AACzB,IAAA,QAAQ,CAAC,aAAT,GAAyB,IAAI,CAAC,SAAL,CAAe,aAAf,CAAzB;AACD;;AAED,QAAM,gBAAgB,GAAQ;AAC5B,KAAC,gBAAD,GAAoB,QADQ;AAE5B,KAAC,mBAAD,GAAuB;AAFK,GAA9B;;AAIA,MAAI,aAAa,IAAI,IAArB,EAA2B;AACzB,IAAA,gBAAgB,CAAC,qBAAD,CAAhB,GAA0C,aAA1C;AACD;;AACD,MAAI,aAAa,IAAI,IAArB,EAA2B;AACzB,IAAA,gBAAgB,CAAC,qBAAD,CAAhB,GAA0C,aAA1C;AACD;;AACD,MAAI,mBAAmB,IAAI,IAA3B,EAAiC;AAC/B,IAAA,gBAAgB,CAAC,2BAAD,CAAhB,GAAgD,mBAAhD;AACD;;AACD,MAAI,iBAAiB,IAAI,IAAzB,EAA+B;AAC7B,IAAA,gBAAgB,CAAC,yBAAD,CAAhB,GAA8C,iBAA9C;AACD;;AACD,MAAI,cAAc,IAAI,IAAtB,EAA4B;AAC1B,IAAA,gBAAgB,CAAC,sBAAD,CAAhB,GAA2C,cAA3C;AACD;;AACD,MAAI,gBAAgB,IAAI,IAAxB,EAA8B;AAC5B,IAAA,gBAAgB,CAAC,wBAAD,CAAhB,GAA6C,gBAA7C;AACD;;AAED,QAAM,OAAO,CAAC,GAAR,CAAY,CAChB,iDAA8B,CAAC,cAAD,CAA9B,EAAgD,gBAAhD,CADgB,EAEhB,QAAQ,CAAC,IAAI,CAAC,IAAL,CAAU,YAAV,EAAwB,OAAxB,CAAD,EAAmC,UAAnC,EAA+C,QAAQ,CAAC,kBAAxD,CAFQ,EAGhB,0BAAe,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,SAArB,CAAf,CAHgB,EAIhB,0BAAe,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,wBAArB,CAAf,CAJgB,CAAZ,CAAN;AAOA,QAAM,WAAW,CAAC,0BAA0B,CAAC,aAAD,EAAgB,aAAhB,EAA+B,mBAA/B,EAAoD,iBAApD,EAAuE,cAAvE,CAA3B,EAAmH,cAAnH,EAAmI,WAAnI,EAAgJ,UAAhJ,CAAjB;;AAEA,MAAI,gBAAgB,IAAI,IAAxB,EAA8B;AAC5B,UAAM,MAAM,GAAG,UAAf;AACA,UAAM,MAAM,GAAG,eAAf;AACA,UAAM,kBAAkB,GAAG,IAAI,CAAC,IAAL,CAAU,aAAV,EAAyB,GAAG,MAAM,GAAG,MAAM,MAA3C,EAAmD,UAAnD,EAA+D,OAA/D,CAA3B;AACA,UAAM,QAAQ,CAAC,kBAAD,EAAqB,GAAG,MAAM,GAAG,MAAM,EAAvC,EAA2C,WAAW,GAAG,MAAzD,CAAR,CACH,IADG,CACE,MAAM,QAAQ,CAAC,aAAD,EAAgB,GAAG,MAAM,GAAG,MAAM,MAAlC,EAA0C,GAAG,WAAW,GAAG,MAAM,MAAjE,CADhB,CAAN;AAED;;AAED,QAAM,OAAO,GAAG,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,GAAG,WAAW,MAAnC,CAAhB;AACA,QAAM,uBAAO,IAAI,CAAC,OAAL,CAAa,YAAb,CAAP,EAAmC,OAAnC,CAAN,CA/L8H,CAgM9H;;AACA,QAAM,GAAG,GAAG,IAAI,CAAC,GAAL,KAAa,IAAzB;AACA,QAAM,uBAAO,OAAP,EAAgB,GAAhB,EAAqB,GAArB,CAAN;AACD;;AAED,SAAS,qBAAT,CAA+B,QAA/B,EAA4C;AAC1C;AACA,MAAI,GAAG,GAAG,QAAQ,CAAC,sBAAnB;;AACA,MAAI,GAAG,IAAI,IAAX,EAAiB;AACf,IAAA,GAAG,GAAG,EAAN;AACA,IAAA,QAAQ,CAAC,sBAAT,GAAkC,GAAlC;AACD;;AAED,EAAA,GAAG,CAAC,uBAAJ,GAA8B,IAA9B,CAR0C,CAS1C;;AACA,EAAA,GAAG,CAAC,sBAAJ,GAA6B,IAA7B;AAEA,MAAI,gBAAgB,GAAG,GAAG,CAAC,kBAA3B;;AACA,MAAI,gBAAgB,IAAI,IAAxB,EAA8B;AAC5B,IAAA,gBAAgB,GAAG,EAAnB;AACA,IAAA,GAAG,CAAC,kBAAJ,GAAyB,gBAAzB;AACD;;AAED,MAAI,gBAAgB,CAAC,SAAjB,IAA8B,IAAlC,EAAwC;AACtC,UAAM,SAAS,GAAG;AAChB,MAAA,4CAA4C,EAAE,KAD9B;AAEhB,MAAA,oBAAoB,EAAE,KAFN;AAGhB,MAAA,2CAA2C,EAAE,IAH7B;AAIhB,MAAA,qCAAqC,EAAE,KAJvB;AAKhB,MAAA,0CAA0C,EAAE;AAL5B,KAAlB;AAOA,IAAA,gBAAgB,CAAC,SAAjB,GAA6B,SAA7B;AACA,IAAA,gBAAgB,CAAC,WAAD,CAAhB,GAAgC,SAAhC;AACD;AACF,C", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { asArray, getPlatformIconFileName, InvalidConfigurationError, log } from \"builder-util\"\nimport { copyOrLinkFile, unlinkIfExists } from \"builder-util/out/fs\"\nimport { rename, utimes } from \"fs-extra\"\nimport * as path from \"path\"\nimport { filterCFBundleIdentifier } from \"../appInfo\"\nimport { AsarIntegrity } from \"../asar/integrity\"\nimport MacPackager from \"../macPackager\"\nimport { normalizeExt } from \"../platformPackager\"\nimport { executeAppBuilderAndWriteJson, executeAppBuilderAsJson } from \"../util/appBuilder\"\n\nfunction doRename(basePath: string, oldName: string, newName: string) {\n  return rename(path.join(basePath, oldName), path.join(basePath, newName))\n}\n\nfunction moveHelpers(helperSuffixes: Array<string>, frameworksPath: string, appName: string, prefix: string): Promise<any> {\n  return BluebirdPromise.map(helperSuffixes, suffix => {\n    const executableBasePath = path.join(frameworksPath, `${prefix}${suffix}.app`, \"Contents\", \"MacOS\")\n    return doRename(executableBasePath, `${prefix}${suffix}`, appName + suffix)\n      .then(() => doRename(frameworksPath, `${prefix}${suffix}.app`, `${appName}${suffix}.app`))\n  })\n}\n\nfunction getAvailableHelperSuffixes(helperEHPlist: string | null, helperNPPlist: string | null, helperRendererPlist: string | null, helperPluginPlist: string | null, helperGPUPlist: string | null) {\n\n  const result = [\" Helper\"]\n  if (helperEHPlist != null) {\n    result.push(\" Helper EH\")\n  }\n  if (helperNPPlist != null) {\n    result.push(\" Helper NP\")\n  }\n  if (helperRendererPlist != null) {\n    result.push(\" Helper (Renderer)\")\n  }\n  if (helperPluginPlist != null) {\n    result.push(\" Helper (Plugin)\")\n  }\n  if (helperGPUPlist != null) {\n    result.push(\" Helper (GPU)\")\n  }\n  return result\n}\n\n/** @internal */\nexport async function createMacApp(packager: MacPackager, appOutDir: string, asarIntegrity: AsarIntegrity | null, isMas: boolean) {\n  const appInfo = packager.appInfo\n  const appFilename = appInfo.productFilename\n\n  const contentsPath = path.join(appOutDir, packager.info.framework.distMacOsAppName, \"Contents\")\n  const frameworksPath = path.join(contentsPath, \"Frameworks\")\n  const loginItemPath = path.join(contentsPath, \"Library\", \"LoginItems\")\n\n  const appPlistFilename = path.join(contentsPath, \"Info.plist\")\n  const helperPlistFilename = path.join(frameworksPath, \"Electron Helper.app\", \"Contents\", \"Info.plist\")\n  const helperEHPlistFilename = path.join(frameworksPath, \"Electron Helper EH.app\", \"Contents\", \"Info.plist\")\n  const helperNPPlistFilename = path.join(frameworksPath, \"Electron Helper NP.app\", \"Contents\", \"Info.plist\")\n  const helperRendererPlistFilename = path.join(frameworksPath, \"Electron Helper (Renderer).app\", \"Contents\", \"Info.plist\")\n  const helperPluginPlistFilename = path.join(frameworksPath, \"Electron Helper (Plugin).app\", \"Contents\", \"Info.plist\")\n  const helperGPUPlistFilename = path.join(frameworksPath, \"Electron Helper (GPU).app\", \"Contents\", \"Info.plist\")\n  const helperLoginPlistFilename = path.join(loginItemPath, \"Electron Login Helper.app\", \"Contents\", \"Info.plist\")\n\n  const plistContent: Array<any> = await executeAppBuilderAsJson([\"decode-plist\", \"-f\", appPlistFilename, \"-f\", helperPlistFilename, \"-f\", helperEHPlistFilename, \"-f\", helperNPPlistFilename, \"-f\", helperRendererPlistFilename, \"-f\", helperPluginPlistFilename, \"-f\", helperGPUPlistFilename, \"-f\", helperLoginPlistFilename])\n\n  if (plistContent[0] == null) {\n    throw new Error(\"corrupted Electron dist\")\n  }\n\n  const appPlist = plistContent[0]!!\n  const helperPlist = plistContent[1]!!\n  const helperEHPlist = plistContent[2]\n  const helperNPPlist = plistContent[3]\n  const helperRendererPlist = plistContent[4]\n  const helperPluginPlist = plistContent[5]\n  const helperGPUPlist = plistContent[6]\n  const helperLoginPlist = plistContent[7]\n\n  // if an extend-info file was supplied, copy its contents in first\n  if (plistContent[8] != null) {\n    Object.assign(appPlist, plistContent[8])\n  }\n\n  const buildMetadata = packager.config!!\n\n  /**\n   * Configure bundleIdentifier for the generic Electron Helper process\n   *\n   * This was the only Helper in Electron 5 and before. Allow users to configure\n   * the bundleIdentifier for continuity.\n   */\n\n  const oldHelperBundleId = (buildMetadata as any)[\"helper-bundle-id\"]\n  if (oldHelperBundleId != null) {\n    log.warn(\"build.helper-bundle-id is deprecated, please set as build.mac.helperBundleId\")\n  }\n  const helperBundleIdentifier = filterCFBundleIdentifier(packager.platformSpecificBuildOptions.helperBundleId || oldHelperBundleId || `${appInfo.macBundleIdentifier}.helper`)\n\n  await packager.applyCommonInfo(appPlist, contentsPath)\n\n  // required for electron-updater proxy\n  if (!isMas) {\n    configureLocalhostAts(appPlist)\n  }\n\n  helperPlist.CFBundleExecutable = `${appFilename} Helper`\n  helperPlist.CFBundleDisplayName = `${appInfo.productName} Helper`\n  helperPlist.CFBundleIdentifier = helperBundleIdentifier\n  helperPlist.CFBundleVersion = appPlist.CFBundleVersion\n\n  /**\n   * Configure bundleIdentifier for Electron 5+ Helper processes\n   *\n   * In Electron 6, parts of the generic Electron Helper process were split into\n   * individual helper processes. Allow users to configure the bundleIdentifiers\n   * for continuity, specifically because macOS keychain access relies on\n   * bundleIdentifiers not changing (i.e. across versions of Electron).\n   */\n\n  function configureHelper(helper: any, postfix: string, userProvidedBundleIdentifier?: string | null) {\n    helper.CFBundleExecutable = `${appFilename} Helper ${postfix}`\n    helper.CFBundleDisplayName = `${appInfo.productName} Helper ${postfix}`\n    helper.CFBundleIdentifier = userProvidedBundleIdentifier\n      ? filterCFBundleIdentifier(userProvidedBundleIdentifier)\n      : `${helperBundleIdentifier}.${postfix.replace(/[^a-z0-9]/gim, \"\")}`\n    helper.CFBundleVersion = appPlist.CFBundleVersion\n  }\n\n  if (helperRendererPlist != null) {\n    configureHelper(helperRendererPlist, \"(Renderer)\", packager.platformSpecificBuildOptions.helperRendererBundleId)\n  }\n  if (helperPluginPlist != null) {\n    configureHelper(helperPluginPlist, \"(Plugin)\", packager.platformSpecificBuildOptions.helperPluginBundleId)\n  }\n  if (helperGPUPlist != null) {\n    configureHelper(helperGPUPlist, \"(GPU)\", packager.platformSpecificBuildOptions.helperGPUBundleId)\n  }\n  if (helperEHPlist != null) {\n    configureHelper(helperEHPlist, \"EH\", packager.platformSpecificBuildOptions.helperEHBundleId)\n  }\n  if (helperNPPlist != null) {\n    configureHelper(helperNPPlist, \"NP\", packager.platformSpecificBuildOptions.helperNPBundleId)\n  }\n  if (helperLoginPlist != null) {\n    helperLoginPlist.CFBundleExecutable = `${appFilename} Login Helper`\n    helperLoginPlist.CFBundleDisplayName = `${appInfo.productName} Login Helper`\n    // noinspection SpellCheckingInspection\n    helperLoginPlist.CFBundleIdentifier = `${appInfo.macBundleIdentifier}.loginhelper`\n    helperLoginPlist.CFBundleVersion = appPlist.CFBundleVersion\n  }\n\n  const protocols = asArray(buildMetadata.protocols).concat(asArray(packager.platformSpecificBuildOptions.protocols))\n  if (protocols.length > 0) {\n    appPlist.CFBundleURLTypes = protocols.map(protocol => {\n      const schemes = asArray(protocol.schemes)\n      if (schemes.length === 0) {\n        throw new InvalidConfigurationError(`Protocol \"${protocol.name}\": must be at least one scheme specified`)\n      }\n      return {\n        CFBundleURLName: protocol.name,\n        CFBundleTypeRole: protocol.role || \"Editor\",\n        CFBundleURLSchemes: schemes.slice()\n      }\n    })\n  }\n\n  const fileAssociations = packager.fileAssociations\n  if (fileAssociations.length > 0) {\n    appPlist.CFBundleDocumentTypes = await BluebirdPromise.map(fileAssociations, async fileAssociation => {\n      const extensions = asArray(fileAssociation.ext).map(normalizeExt)\n      const customIcon = await packager.getResource(getPlatformIconFileName(fileAssociation.icon, true), `${extensions[0]}.icns`)\n      let iconFile = appPlist.CFBundleIconFile\n      if (customIcon != null) {\n        iconFile = path.basename(customIcon)\n        await copyOrLinkFile(customIcon, path.join(path.join(contentsPath, \"Resources\"), iconFile))\n      }\n\n      const result = {\n        CFBundleTypeExtensions: extensions,\n        CFBundleTypeName: fileAssociation.name || extensions[0],\n        CFBundleTypeRole: fileAssociation.role || \"Editor\",\n        LSHandlerRank: fileAssociation.rank || \"Default\",\n        CFBundleTypeIconFile: iconFile\n      } as any\n\n      if (fileAssociation.isPackage) {\n        result.LSTypeIsPackage = true\n      }\n      return result\n    })\n  }\n\n  if (asarIntegrity != null) {\n    appPlist.AsarIntegrity = JSON.stringify(asarIntegrity)\n  }\n\n  const plistDataToWrite: any = {\n    [appPlistFilename]: appPlist,\n    [helperPlistFilename]: helperPlist,\n  }\n  if (helperEHPlist != null) {\n    plistDataToWrite[helperEHPlistFilename] = helperEHPlist\n  }\n  if (helperNPPlist != null) {\n    plistDataToWrite[helperNPPlistFilename] = helperNPPlist\n  }\n  if (helperRendererPlist != null) {\n    plistDataToWrite[helperRendererPlistFilename] = helperRendererPlist\n  }\n  if (helperPluginPlist != null) {\n    plistDataToWrite[helperPluginPlistFilename] = helperPluginPlist\n  }\n  if (helperGPUPlist != null) {\n    plistDataToWrite[helperGPUPlistFilename] = helperGPUPlist\n  }\n  if (helperLoginPlist != null) {\n    plistDataToWrite[helperLoginPlistFilename] = helperLoginPlist\n  }\n\n  await Promise.all([\n    executeAppBuilderAndWriteJson([\"encode-plist\"], plistDataToWrite),\n    doRename(path.join(contentsPath, \"MacOS\"), \"Electron\", appPlist.CFBundleExecutable),\n    unlinkIfExists(path.join(appOutDir, \"LICENSE\")),\n    unlinkIfExists(path.join(appOutDir, \"LICENSES.chromium.html\")),\n  ])\n\n  await moveHelpers(getAvailableHelperSuffixes(helperEHPlist, helperNPPlist, helperRendererPlist, helperPluginPlist, helperGPUPlist), frameworksPath, appFilename, \"Electron\")\n\n  if (helperLoginPlist != null) {\n    const prefix = \"Electron\"\n    const suffix = \" Login Helper\"\n    const executableBasePath = path.join(loginItemPath, `${prefix}${suffix}.app`, \"Contents\", \"MacOS\")\n    await doRename(executableBasePath, `${prefix}${suffix}`, appFilename + suffix)\n      .then(() => doRename(loginItemPath, `${prefix}${suffix}.app`, `${appFilename}${suffix}.app`))\n  }\n\n  const appPath = path.join(appOutDir, `${appFilename}.app`)\n  await rename(path.dirname(contentsPath), appPath)\n  // https://github.com/electron-userland/electron-builder/issues/840\n  const now = Date.now() / 1000\n  await utimes(appPath, now, now)\n}\n\nfunction configureLocalhostAts(appPlist: any) {\n  // https://bencoding.com/2015/07/20/app-transport-security-and-localhost/\n  let ats = appPlist.NSAppTransportSecurity\n  if (ats == null) {\n    ats = {}\n    appPlist.NSAppTransportSecurity = ats\n  }\n\n  ats.NSAllowsLocalNetworking = true\n  // https://github.com/electron-userland/electron-builder/issues/3377#issuecomment-446035814\n  ats.NSAllowsArbitraryLoads = true\n\n  let exceptionDomains = ats.NSExceptionDomains\n  if (exceptionDomains == null) {\n    exceptionDomains = {}\n    ats.NSExceptionDomains = exceptionDomains\n  }\n\n  if (exceptionDomains.localhost == null) {\n    const allowHttp = {\n      NSTemporaryExceptionAllowsInsecureHTTPSLoads: false,\n      NSIncludesSubdomains: false,\n      NSTemporaryExceptionAllowsInsecureHTTPLoads: true,\n      NSTemporaryExceptionMinimumTLSVersion: \"1.0\",\n      NSTemporaryExceptionRequiresForwardSecrecy: false\n    }\n    exceptionDomains.localhost = allowHttp\n    exceptionDomains[\"127.0.0.1\"] = allowHttp\n  }\n}\n"], "sourceRoot": ""}