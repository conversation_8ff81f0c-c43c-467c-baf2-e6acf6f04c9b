"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getElectronVersion = getElectronVersion;
exports.getElectronVersionFromInstalled = getElectronVersionFromInstalled;
exports.getElectronPackage = getElectronPackage;
exports.computeElectronVersion = computeElectronVersion;

function _builderUtil() {
  const data = require("builder-util");

  _builderUtil = function () {
    return data;
  };

  return data;
}

function _builderUtilRuntime() {
  const data = require("builder-util-runtime");

  _builderUtilRuntime = function () {
    return data;
  };

  return data;
}

function _nodeHttpExecutor() {
  const data = require("builder-util/out/nodeHttpExecutor");

  _nodeHttpExecutor = function () {
    return data;
  };

  return data;
}

function _fsExtra() {
  const data = require("fs-extra");

  _fsExtra = function () {
    return data;
  };

  return data;
}

function _lazyVal() {
  const data = require("lazy-val");

  _lazyVal = function () {
    return data;
  };

  return data;
}

var path = _interopRequireWildcard(require("path"));

function _readConfigFile() {
  const data = require("read-config-file");

  _readConfigFile = function () {
    return data;
  };

  return data;
}

function semver() {
  const data = _interopRequireWildcard(require("semver"));

  semver = function () {
    return data;
  };

  return data;
}

function _config() {
  const data = require("../util/config");

  _config = function () {
    return data;
  };

  return data;
}

function _getRequireWildcardCache() { if (typeof WeakMap !== "function") return null; var cache = new WeakMap(); _getRequireWildcardCache = function () { return cache; }; return cache; }

function _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }

const electronPackages = ["electron", "electron-prebuilt", "electron-prebuilt-compile", "electron-nightly"];

async function getElectronVersion(projectDir, config, projectMetadata = new (_lazyVal().Lazy)(() => (0, _readConfigFile().orNullIfFileNotExist)((0, _fsExtra().readJson)(path.join(projectDir, "package.json"))))) {
  if (config == null) {
    config = await (0, _config().getConfig)(projectDir, null, null);
  }

  if (config.electronVersion != null) {
    return config.electronVersion;
  }

  return await computeElectronVersion(projectDir, projectMetadata);
}

async function getElectronVersionFromInstalled(projectDir) {
  for (const name of electronPackages) {
    try {
      return (await (0, _fsExtra().readJson)(path.join(projectDir, "node_modules", name, "package.json"))).version;
    } catch (e) {
      if (e.code !== "ENOENT") {
        _builderUtil().log.warn({
          name,
          error: e
        }, `cannot read electron version package.json`);
      }
    }
  }

  return null;
}

async function getElectronPackage(projectDir) {
  for (const name of electronPackages) {
    try {
      return await (0, _fsExtra().readJson)(path.join(projectDir, "node_modules", name, "package.json"));
    } catch (e) {
      if (e.code !== "ENOENT") {
        _builderUtil().log.warn({
          name,
          error: e
        }, `cannot find electron in package.json`);
      }
    }
  }

  return null;
}
/** @internal */


async function computeElectronVersion(projectDir, projectMetadata) {
  const result = await getElectronVersionFromInstalled(projectDir);

  if (result != null) {
    return result;
  }

  const dependency = findFromPackageMetadata(await projectMetadata.value);

  if ((dependency === null || dependency === void 0 ? void 0 : dependency.name) === "electron-nightly") {
    _builderUtil().log.info("You are using a nightly version of electron, be warned that those builds are highly unstable.");

    const feedXml = await _nodeHttpExecutor().httpExecutor.request({
      hostname: "github.com",
      path: `/electron/nightlies/releases.atom`,
      headers: {
        accept: "application/xml, application/atom+xml, text/xml, */*"
      }
    });
    const feed = (0, _builderUtilRuntime().parseXml)(feedXml);
    const latestRelease = feed.element("entry", false, `No published versions on GitHub`);
    const v = latestRelease.element("link").attribute("href").match(/\/tag\/v?([^/]+)$/)[1];
    return v.startsWith("v") ? v.substring(1) : v;
  } else if ((dependency === null || dependency === void 0 ? void 0 : dependency.version) === "latest") {
    _builderUtil().log.warn("Electron version is set to \"latest\", but it is recommended to set it to some more restricted version range.");

    try {
      const releaseInfo = JSON.parse(await _nodeHttpExecutor().httpExecutor.request({
        hostname: "github.com",
        path: `/electron/${dependency.name === "electron-nightly" ? "nightlies" : "electron"}/releases/latest`,
        headers: {
          accept: "application/json"
        }
      }));
      const version = releaseInfo.tag_name.startsWith("v") ? releaseInfo.tag_name.substring(1) : releaseInfo.tag_name;

      _builderUtil().log.info({
        version
      }, `resolve ${dependency.name}@${dependency.version}`);

      return version;
    } catch (e) {
      _builderUtil().log.warn(e);
    }

    throw new (_builderUtil().InvalidConfigurationError)(`Cannot find electron dependency to get electron version in the '${path.join(projectDir, "package.json")}'`);
  }

  const version = dependency === null || dependency === void 0 ? void 0 : dependency.version;

  if (version == null || !/^\d/.test(version)) {
    const versionMessage = version == null ? "" : ` and version ("${version}") is not fixed in project`;
    throw new (_builderUtil().InvalidConfigurationError)(`Cannot compute electron version from installed node modules - none of the possible electron modules are installed${versionMessage}.\nSee https://github.com/electron-userland/electron-builder/issues/3984#issuecomment-504968246`);
  }

  return semver().coerce(version).toString();
}

function findFromPackageMetadata(packageData) {
  for (const name of electronPackages) {
    const devDependencies = packageData.devDependencies;
    let dep = devDependencies == null ? null : devDependencies[name];

    if (dep == null) {
      const dependencies = packageData.dependencies;
      dep = dependencies == null ? null : dependencies[name];
    }

    if (dep != null) {
      return {
        name,
        version: dep
      };
    }
  }

  return null;
} 
// __ts-babel@6.0.4
//# sourceMappingURL=electronVersion.js.map