{"version": 3, "sources": ["../src/core.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAqBM,MAAO,QAAP,CAAe;AAKnB,EAAA,WAAA,CAAmB,IAAnB,EAAwC,qBAAxC,EAA8E,QAA9E,EAAuG;AAApF,SAAA,IAAA,GAAA,IAAA;AAAqB,SAAA,qBAAA,GAAA,qBAAA;AAAsC,SAAA,QAAA,GAAA,QAAA;AAC7E;;AAED,EAAA,QAAQ,GAAA;AACN,WAAO,KAAK,IAAZ;AACD;;AAED,EAAA,YAAY,CAAC,IAAD,EAAuC,GAAG,KAA1C,EAA4D;AACtE,QAAI,IAAI,IAAI,IAAR,KAAiB,KAAK,IAAI,IAAT,IAAiB,KAAK,CAAC,MAAN,KAAiB,CAAnD,CAAJ,EAA2D;AACzD,aAAO,IAAI,GAAJ,CAAQ,CAAC,CAAC,IAAD,EAAO,IAAI,GAAJ,EAAP,CAAD,CAAR,CAAP;AACD;;AAED,UAAM,UAAU,GAAG,IAAI,GAAJ,EAAnB;;AAEA,SAAK,MAAM,IAAX,IAAoB,KAAK,IAAI,IAAT,IAAiB,KAAK,CAAC,MAAN,KAAiB,CAAlC,GAAsC,CAAC,mCAAe,OAAO,CAAC,IAAvB,CAAD,CAAtC,GAAuE,KAA3F,EAAmG;AACjG,MAAA,UAAU,CAAC,GAAX,CAAe,IAAf,EAAqB,IAAI,IAAI,IAAR,GAAe,EAAf,GAAqB,KAAK,CAAC,OAAN,CAAc,IAAd,IAAsB,IAAtB,GAA6B,CAAC,IAAD,CAAvE;AACD;;AACD,WAAO,IAAI,GAAJ,CAAQ,CAAC,CAAC,IAAD,EAAO,UAAP,CAAD,CAAR,CAAP;AACD;;AAED,SAAO,OAAP,GAAc;AACZ,WAAO,QAAQ,CAAC,UAAT,CAAoB,OAAO,CAAC,QAA5B,CAAP;AACD;;AAED,SAAO,UAAP,CAAkB,IAAlB,EAA8B;AAC5B,IAAA,IAAI,GAAG,IAAI,CAAC,WAAL,EAAP;;AACA,YAAQ,IAAR;AACE,WAAK,QAAQ,CAAC,GAAT,CAAa,QAAlB;AACA,WAAK,QAAQ,CAAC,GAAT,CAAa,IAAlB;AACE,eAAO,QAAQ,CAAC,GAAhB;;AAEF,WAAK,QAAQ,CAAC,OAAT,CAAiB,QAAtB;AACA,WAAK,QAAQ,CAAC,OAAT,CAAiB,IAAtB;AACA,WAAK,QAAQ,CAAC,OAAT,CAAiB,qBAAtB;AACE,eAAO,QAAQ,CAAC,OAAhB;;AAEF,WAAK,QAAQ,CAAC,KAAT,CAAe,QAApB;AACE,eAAO,QAAQ,CAAC,KAAhB;;AAEF;AACE,cAAM,IAAI,KAAJ,CAAU,qBAAqB,IAAI,EAAnC,CAAN;AAdJ;AAgBD;;AA/CkB;;;AACZ,QAAA,CAAA,GAAA,GAAM,IAAI,QAAJ,CAAa,KAAb,EAAoB,KAApB,EAA2B,QAA3B,CAAN;AACA,QAAA,CAAA,KAAA,GAAQ,IAAI,QAAJ,CAAa,OAAb,EAAsB,OAAtB,EAA+B,OAA/B,CAAR;AACA,QAAA,CAAA,OAAA,GAAU,IAAI,QAAJ,CAAa,SAAb,EAAwB,KAAxB,EAA+B,OAA/B,CAAV;;AA+CH,MAAgB,MAAhB,CAAsB;AAI1B,EAAA,WAAA,CAA+B,IAA/B,EAAsD,gBAAA,GAA4B,IAAlF,EAAsF;AAAvD,SAAA,IAAA,GAAA,IAAA;AAAuB,SAAA,gBAAA,GAAA,gBAAA;AACrD;;AAED,QAAM,YAAN,GAAkB,CAChB;AACD;;AAID,EAAA,WAAW,GAAA;AACT,WAAO,OAAO,CAAC,OAAR,EAAP;AACD;;AAfyB;;;AA2BrB,MAAM,cAAc,GAAG,SAAvB;;AACA,MAAM,UAAU,GAAG,KAAnB,C", "sourcesContent": ["import { Arch, archFromString, ArchType } from \"builder-util\"\nimport { AllPublishOptions } from \"builder-util-runtime\"\nimport { SnapStoreOptions } from \"./publish/SnapStorePublisher\"\n\n// https://github.com/YousefED/typescript-json-schema/issues/80\nexport type Publish = AllPublishOptions | SnapStoreOptions | Array<AllPublishOptions | SnapStoreOptions> | null\n\nexport type TargetConfigType = Array<string | TargetConfiguration> | string | TargetConfiguration | null\n\nexport interface TargetConfiguration {\n  /**\n   * The target name. e.g. `snap`.\n   */\n  readonly target: string\n\n  /**\n   * The arch or list of archs.\n   */\n  readonly arch?: Array<ArchType> | ArchType\n}\n\nexport class Platform {\n  static MAC = new Platform(\"mac\", \"mac\", \"darwin\")\n  static LINUX = new Platform(\"linux\", \"linux\", \"linux\")\n  static WINDOWS = new Platform(\"windows\", \"win\", \"win32\")\n\n  constructor(public name: string, public buildConfigurationKey: string, public nodeName: NodeJS.Platform) {\n  }\n\n  toString() {\n    return this.name\n  }\n\n  createTarget(type?: string | Array<string> | null, ...archs: Array<Arch>): Map<Platform, Map<Arch, Array<string>>> {\n    if (type == null && (archs == null || archs.length === 0)) {\n      return new Map([[this, new Map()]])\n    }\n\n    const archToType = new Map()\n\n    for (const arch of (archs == null || archs.length === 0 ? [archFromString(process.arch)] : archs)) {\n      archToType.set(arch, type == null ? [] : (Array.isArray(type) ? type : [type]))\n    }\n    return new Map([[this, archToType]])\n  }\n\n  static current(): Platform {\n    return Platform.fromString(process.platform)\n  }\n\n  static fromString(name: string): Platform {\n    name = name.toLowerCase()\n    switch (name) {\n      case Platform.MAC.nodeName:\n      case Platform.MAC.name:\n        return Platform.MAC\n\n      case Platform.WINDOWS.nodeName:\n      case Platform.WINDOWS.name:\n      case Platform.WINDOWS.buildConfigurationKey:\n        return Platform.WINDOWS\n\n      case Platform.LINUX.nodeName:\n        return Platform.LINUX\n\n      default:\n        throw new Error(`Unknown platform: ${name}`)\n    }\n  }\n}\n\nexport abstract class Target {\n  abstract readonly outDir: string\n  abstract readonly options: TargetSpecificOptions | null | undefined\n\n  protected constructor(readonly name: string, readonly isAsyncSupported: boolean = true) {\n  }\n\n  async checkOptions(): Promise<any> {\n    // ignore\n  }\n\n  abstract build(appOutDir: string, arch: Arch): Promise<any>\n\n  finishBuild(): Promise<any> {\n    return Promise.resolve()\n  }\n}\n\nexport interface TargetSpecificOptions {\n  /**\n   The [artifact file name template](/configuration/configuration#artifact-file-name-template).\n   */\n  readonly artifactName?: string | null\n\n  publish?: Publish\n}\n\nexport const DEFAULT_TARGET = \"default\"\nexport const DIR_TARGET = \"dir\"\n\nexport type CompressionLevel = \"store\" | \"normal\" | \"maximum\"\n\nexport interface BeforeBuildContext {\n  readonly appDir: string\n  readonly electronVersion: string\n  readonly platform: Platform\n  readonly arch: string\n}\n\nexport interface SourceRepositoryInfo {\n  type?: string\n  domain?: string\n  user: string\n  project: string\n}"], "sourceRoot": ""}