{"version": 3, "sources": ["../../src/util/repositoryInfo.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;;;;;AAIM,SAAU,iBAAV,CAA4B,UAA5B,EAAgD,QAAhD,EAAqE,WAArE,EAAkG;AACtG,SAAO,QAAQ,CAAC,UAAD,EAAa,CAAC,WAAW,IAAI,IAAf,GAAsB,IAAtB,GAA6B,WAAW,CAAC,UAA1C,MAA0D,QAAQ,IAAI,IAAZ,GAAmB,IAAnB,GAA0B,QAAQ,CAAC,UAA7F,CAAb,CAAf;AACD;;AAED,eAAe,sBAAf,CAAsC,UAAtC,EAAwD;AACtD,QAAM,IAAI,GAAG,MAAM,qCAAqB,yBAAS,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,MAAtB,EAA8B,QAA9B,CAAT,EAAkD,MAAlD,CAArB,CAAnB;;AACA,MAAI,IAAI,IAAI,IAAZ,EAAkB;AAChB,WAAO,IAAP;AACD;;AAED,QAAM,IAAI,GAAG,IAAI,CAAC,KAAL,CAAW,OAAX,CAAb;AACA,QAAM,CAAC,GAAG,IAAI,CAAC,OAAL,CAAa,mBAAb,CAAV;;AACA,MAAI,CAAC,KAAK,CAAC,CAAX,EAAc;AACZ,QAAI,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAL,CAAZ;;AACA,QAAI,CAAC,CAAC,CAAC,KAAF,CAAQ,WAAR,CAAL,EAA2B;AACzB,MAAA,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAL,CAAR;AACD;;AAED,QAAI,CAAC,CAAC,KAAF,CAAQ,WAAR,CAAJ,EAA0B;AACxB,aAAO,CAAC,CAAC,OAAF,CAAU,YAAV,EAAwB,EAAxB,CAAP;AACD;AACF;;AACD,SAAO,IAAP;AACD;;AAED,eAAe,QAAf,CAAwB,UAAxB,EAA4C,IAA5C,EAAiF;AAC/E,MAAI,IAAI,IAAI,IAAZ,EAAkB;AAChB,WAAO,kBAAkB,CAAC,OAAO,IAAP,KAAgB,QAAhB,GAA2B,IAA3B,GAAkC,IAAI,CAAC,GAAxC,CAAzB;AACD;;AAED,QAAM,IAAI,GAAG,OAAO,CAAC,GAAR,CAAY,gBAAZ,IAAgC,OAAO,CAAC,GAAR,CAAY,kBAAzD;;AACA,MAAI,IAAI,IAAI,IAAZ,EAAkB;AAChB,UAAM,QAAQ,GAAG,IAAI,CAAC,KAAL,CAAW,GAAX,CAAjB;AACA,WAAO;AACL,MAAA,IAAI,EAAE,QAAQ,CAAC,CAAD,CADT;AAEL,MAAA,OAAO,EAAE,QAAQ,CAAC,CAAD;AAFZ,KAAP;AAID;;AAED,QAAM,IAAI,GAAG,OAAO,CAAC,GAAR,CAAY,uBAAzB;AACA,QAAM,OAAO,GAAG,OAAO,CAAC,GAAR,CAAY,uBAA5B;;AACA,MAAI,IAAI,IAAI,IAAR,IAAgB,OAAO,IAAI,IAA/B,EAAqC;AACnC,WAAO;AACL,MAAA,IADK;AAEL,MAAA;AAFK,KAAP;AAID;;AAED,QAAM,GAAG,GAAG,MAAM,sBAAsB,CAAC,UAAD,CAAxC;AACA,SAAO,GAAG,IAAI,IAAP,GAAc,IAAd,GAAqB,kBAAkB,CAAC,GAAD,CAA9C;AACD;;AAED,SAAS,kBAAT,CAA4B,GAA5B,EAAuC;AACrC,QAAM,IAAI,GAAQ,8BAAQ,GAAR,CAAlB;;AACA,MAAI,IAAI,IAAI,IAAZ,EAAkB;AAChB,WAAO,IAAI,CAAC,SAAZ;AACA,WAAO,IAAI,CAAC,QAAZ;AACA,WAAO,IAAI,CAAC,YAAZ;AACA,WAAO,IAAI,CAAC,YAAZ;AACA,WAAO,IAAI,CAAC,WAAZ;AACA,WAAO,IAAI,CAAC,eAAZ;AACA,WAAO,IAAI,CAAC,WAAZ;AACA,WAAO,IAAI,CAAC,cAAZ;AACA,WAAO,IAAI,CAAC,cAAZ;AACA,WAAO,IAAI,CAAC,YAAZ;AACA,WAAO,IAAI,CAAC,aAAZ;AACA,WAAO,IAAI,CAAC,gBAAZ;AACA,WAAO,IAAI,CAAC,YAAZ;AACA,WAAO,IAAI,CAAC,SAAZ;AACA,WAAO,IAAI,CAAC,YAAZ;AACA,WAAO,IAAI,CAAC,UAAZ;AACA,WAAO,IAAI,CAAC,OAAZ;AACA,WAAO,IAAI,CAAC,IAAZ;AACA,WAAO,IAAI,CAAC,kBAAZ;AACA,WAAO,IAAI,CAAC,IAAZ;AACD;;AACD,SAAO,IAAP;AACD,C", "sourcesContent": ["import { orNullIfFileNotExist } from \"builder-util/out/promise\"\nimport { readFile } from \"fs-extra\"\nimport { fromUrl, Info } from \"hosted-git-info\"\nimport * as path from \"path\"\nimport { SourceRepositoryInfo } from \"../core\"\nimport { Metadata, RepositoryInfo } from \"..\"\n\nexport function getRepositoryInfo(projectDir: string, metadata?: Metadata, devMetadata?: Metadata | null): Promise<SourceRepositoryInfo | null> {\n  return _getInfo(projectDir, (devMetadata == null ? null : devMetadata.repository) || (metadata == null ? null : metadata.repository))\n}\n\nasync function getGitUrlFromGitConfig(projectDir: string): Promise<string | null> {\n  const data = await orNullIfFileNotExist(readFile(path.join(projectDir, \".git\", \"config\"), \"utf8\"))\n  if (data == null) {\n    return null\n  }\n\n  const conf = data.split(/\\r?\\n/)\n  const i = conf.indexOf('[remote \"origin\"]')\n  if (i !== -1) {\n    let u = conf[i + 1]\n    if (!u.match(/^\\s*url =/)) {\n      u = conf[i + 2]\n    }\n\n    if (u.match(/^\\s*url =/)) {\n      return u.replace(/^\\s*url = /, \"\")\n    }\n  }\n  return null\n}\n\nasync function _getInfo(projectDir: string, repo?: RepositoryInfo | string | null): Promise<SourceRepositoryInfo | null> {\n  if (repo != null) {\n    return parseRepositoryUrl(typeof repo === \"string\" ? repo : repo.url)\n  }\n\n  const slug = process.env.TRAVIS_REPO_SLUG || process.env.APPVEYOR_REPO_NAME\n  if (slug != null) {\n    const splitted = slug.split(\"/\")\n    return {\n      user: splitted[0],\n      project: splitted[1],\n    }\n  }\n\n  const user = process.env.CIRCLE_PROJECT_USERNAME\n  const project = process.env.CIRCLE_PROJECT_REPONAME\n  if (user != null && project != null) {\n    return {\n      user,\n      project,\n    }\n  }\n\n  const url = await getGitUrlFromGitConfig(projectDir)\n  return url == null ? null : parseRepositoryUrl(url)\n}\n\nfunction parseRepositoryUrl(url: string): Info {\n  const info: any = fromUrl(url)\n  if (info != null) {\n    delete info.protocols\n    delete info.treepath\n    delete info.filetemplate\n    delete info.bugstemplate\n    delete info.gittemplate\n    delete info.tarballtemplate\n    delete info.sshtemplate\n    delete info.sshurltemplate\n    delete info.browsetemplate\n    delete info.docstemplate\n    delete info.httpstemplate\n    delete info.shortcuttemplate\n    delete info.pathtemplate\n    delete info.pathmatch\n    delete info.protocols_re\n    delete info.committish\n    delete info.default\n    delete info.opts\n    delete info.browsefiletemplate\n    delete info.auth\n  }\n  return info\n}"], "sourceRoot": ""}