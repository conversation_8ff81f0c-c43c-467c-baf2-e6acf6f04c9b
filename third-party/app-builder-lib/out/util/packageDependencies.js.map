{"version": 3, "sources": ["../../src/util/packageDependencies.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEM,SAAU,wBAAV,CAAmC,UAAnC,EAAuD,oBAAvD,EAAiG;AACrG,SAAO,KAAI,eAAJ,EAAS,YAAW;AACzB,UAAM,IAAI,GAAG,CAAC,eAAD,EAAkB,OAAlB,EAA2B,UAA3B,CAAb;;AACA,QAAI,oBAAoB,IAAI,IAA5B,EAAkC;AAChC,WAAK,MAAM,IAAX,IAAmB,oBAAnB,EAAyC;AACvC,QAAA,IAAI,CAAC,IAAL,CAAU,eAAV,EAA2B,IAA3B;AACD;AACF;;AACD,WAAO,2CAAoC,IAApC,CAAP;AACD,GARM,CAAP;AASD,C", "sourcesContent": ["import { Lazy } from \"lazy-val\"\nimport { executeAppBuilderAsJson } from \"./appBuilder\"\n\nexport function createLazyProductionDeps(projectDir: string, excludedDependencies: Array<string> | null) {\n  return new Lazy(async () => {\n    const args = [\"node-dep-tree\", \"--dir\", projectDir]\n    if (excludedDependencies != null) {\n      for (const name of excludedDependencies) {\n        args.push(\"--exclude-dep\", name)\n      }\n    }\n    return executeAppBuilderAsJson<Array<any>>(args)\n  })\n}\n\nexport interface NodeModuleDirInfo {\n  readonly dir: string\n  readonly deps: Array<NodeModuleInfo>\n}\n\nexport interface NodeModuleInfo {\n  readonly name: string\n}"], "sourceRoot": ""}