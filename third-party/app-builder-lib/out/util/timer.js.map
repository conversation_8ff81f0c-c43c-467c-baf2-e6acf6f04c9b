{"version": 3, "sources": ["../../src/util/timer.ts"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAMM,MAAO,QAAP,CAAe;AAGnB,EAAA,WAAA,CAA6B,KAA7B,EAA0C;AAAb,SAAA,KAAA,GAAA,KAAA;AAFrB,SAAA,KAAA,GAAQ,OAAO,CAAC,MAAR,EAAR;AAGP;;AAED,EAAA,SAAS,GAAA;AACP,UAAM,GAAG,GAAG,OAAO,CAAC,MAAR,CAAe,KAAK,KAApB,CAAZ;AACA,WAAO,GAAG,GAAG,CAAC,CAAD,CAAG,KAAK,IAAI,CAAC,KAAL,CAAW,GAAG,CAAC,CAAD,CAAH,GAAS,OAApB,CAA4B,IAAjD;AACD;;AAED,EAAA,GAAG,GAAA;AACD,IAAA,OAAO,CAAC,IAAR,CAAa,GAAG,KAAK,KAAK,KAAK,KAAK,SAAL,EAAgB,EAA/C;AACD;;AAbkB;;;;AAgBrB,MAAM,eAAN,CAAqB;AACnB,EAAA,GAAG,GAAA,CACD;AACD;;AAHkB;;AAMf,SAAU,IAAV,CAAe,KAAf,EAA4B;AAChC,SAAO,qBAAM,OAAN,GAAgB,IAAI,QAAJ,CAAa,KAAb,CAAhB,GAAsC,IAAI,eAAJ,EAA7C;AACD,C", "sourcesContent": ["import { debug } from \"builder-util\"\n\nexport interface Timer {\n  end(): void\n}\n\nexport class DevTimer implements Timer {\n  private start = process.hrtime()\n\n  constructor(private readonly label: string) {\n  }\n\n  endAndGet(): string {\n    const end = process.hrtime(this.start)\n    return `${end[0]}s ${Math.round(end[1] / 1000000)}ms`\n  }\n\n  end(): void {\n    console.info(`${this.label}: ${this.endAndGet()}`)\n  }\n}\n\nclass ProductionTimer implements Timer {\n  end(): void {\n    // ignore\n  }\n}\n\nexport function time(label: string): Timer {\n  return debug.enabled ? new DevTimer(label) : new ProductionTimer()\n}"], "sourceRoot": ""}