{"version": 3, "sources": ["../../src/util/macroExpander.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAGM,SAAU,WAAV,CAAsB,OAAtB,EAAuC,IAAvC,EAAwE,OAAxE,EAA0F,KAAA,GAAa,EAAvG,EAA2G,sBAAsB,GAAG,IAApI,EAAwI;AAC5I,MAAI,IAAI,IAAI,IAAZ,EAAkB;AAChB,IAAA,OAAO,GAAG,OAAO,CACjB;AADiB,KAEd,OAFO,CAEC,UAFD,EAEa,EAFb,EAGR;AAHQ,KAIP,OAJO,CAIC,UAJD,EAIa,EAJb,EAKR;AALQ,KAMP,OANO,CAMC,UAND,EAMa,EANb,EAOR;AAPQ,KAQP,OARO,CAQC,UARD,EAQa,EARb,CAAV;AASD;;AAED,SAAO,OAAO,CAAC,OAAR,CAAgB,sBAAhB,EAAwC,CAAC,KAAD,EAAQ,EAAR,KAAsB;AACnE,YAAQ,EAAR;AACE,WAAK,aAAL;AACE,eAAO,sBAAsB,GAAG,OAAO,CAAC,eAAX,GAA6B,OAAO,CAAC,WAAlE;;AAEF,WAAK,MAAL;AACE,YAAI,IAAI,IAAI,IAAZ,EAAkB;AAChB;AACA,iBAAO,EAAP;AACD;;AACD,eAAO,IAAP;;AAEF,WAAK,QAAL;AAAe;AACb,gBAAM,WAAW,GAAG,OAAO,CAAC,WAA5B;;AACA,cAAI,WAAW,IAAI,IAAnB,EAAyB;AACvB,kBAAM,KAAI,wCAAJ,EAA8B,0BAA0B,OAAO,4BAA/D,EAA6F,yCAA7F,CAAN;AACD;;AACD,iBAAO,WAAP;AACD;;AAED,WAAK,UAAL;AACE,eAAO,OAAO,CAAC,QAAf;;AAEF,WAAK,SAAL;AACE,eAAO,OAAO,CAAC,OAAR,IAAmB,QAA1B;;AAEF;AAAS;AACP,cAAI,EAAE,IAAI,OAAV,EAAmB;AACjB,mBAAQ,OAAe,CAAC,EAAD,CAAvB;AACD;;AAED,cAAI,EAAE,CAAC,UAAH,CAAc,MAAd,CAAJ,EAA2B;AACzB,kBAAM,OAAO,GAAG,EAAE,CAAC,SAAH,CAAa,OAAO,MAApB,CAAhB;AACA,kBAAM,QAAQ,GAAG,OAAO,CAAC,GAAR,CAAY,OAAZ,CAAjB;;AACA,gBAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,oBAAM,KAAI,wCAAJ,EAA8B,0BAA0B,OAAO,UAAU,OAAO,iBAAhF,EAAmG,sCAAnG,CAAN;AACD;;AACD,mBAAO,QAAP;AACD;;AAED,gBAAM,KAAK,GAAG,KAAK,CAAC,EAAD,CAAnB;;AACA,cAAI,KAAK,IAAI,IAAb,EAAmB;AACjB,kBAAM,KAAI,wCAAJ,EAA8B,0BAA0B,OAAO,YAAY,EAAE,iBAA7E,EAAgG,wCAAhG,CAAN;AACD,WAFD,MAGK;AACH,mBAAO,KAAP;AACD;AACF;AA9CH;AAgDD,GAjDM,CAAP;AAkDD,C", "sourcesContent": ["import { InvalidConfigurationError } from \"builder-util\"\nimport { AppInfo } from \"../appInfo\"\n\nexport function expandMacro(pattern: string, arch: string | null | undefined, appInfo: AppInfo, extra: any = {}, isProductNameSanitized = true): string {\n  if (arch == null) {\n    pattern = pattern\n    // tslint:disable-next-line:no-invalid-template-strings\n      .replace(\"-${arch}\", \"\")\n      // tslint:disable-next-line:no-invalid-template-strings\n      .replace(\" ${arch}\", \"\")\n      // tslint:disable-next-line:no-invalid-template-strings\n      .replace(\"_${arch}\", \"\")\n      // tslint:disable-next-line:no-invalid-template-strings\n      .replace(\"/${arch}\", \"\")\n  }\n\n  return pattern.replace(/\\${([_a-zA-Z./*]+)}/g, (match, p1): string => {\n    switch (p1) {\n      case \"productName\":\n        return isProductNameSanitized ? appInfo.productFilename : appInfo.productName\n\n      case \"arch\":\n        if (arch == null) {\n          // see above, we remove macro if no arch\n          return \"\"\n        }\n        return arch\n\n      case \"author\": {\n        const companyName = appInfo.companyName\n        if (companyName == null) {\n          throw new InvalidConfigurationError(`cannot expand pattern \"${pattern}\": author is not specified`, \"ERR_ELECTRON_BUILDER_AUTHOR_UNSPECIFIED\")\n        }\n        return companyName\n      }\n\n      case \"platform\":\n        return process.platform\n\n      case \"channel\":\n        return appInfo.channel || \"latest\"\n\n      default: {\n        if (p1 in appInfo) {\n          return (appInfo as any)[p1]\n        }\n\n        if (p1.startsWith(\"env.\")) {\n          const envName = p1.substring(\"env.\".length)\n          const envValue = process.env[envName]\n          if (envValue == null) {\n            throw new InvalidConfigurationError(`cannot expand pattern \"${pattern}\": env ${envName} is not defined`, \"ERR_ELECTRON_BUILDER_ENV_NOT_DEFINED\")\n          }\n          return envValue\n        }\n\n        const value = extra[p1]\n        if (value == null) {\n          throw new InvalidConfigurationError(`cannot expand pattern \"${pattern}\": macro ${p1} is not defined`, \"ERR_ELECTRON_BUILDER_MACRO_NOT_DEFINED\")\n        }\n        else {\n          return value\n        }\n      }\n    }\n  })\n}"], "sourceRoot": ""}