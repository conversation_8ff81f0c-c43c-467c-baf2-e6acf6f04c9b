{"version": 3, "sources": ["../../src/util/NodeModuleCopyHelper.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;;;AAEA,MAAM,aAAa,GAAG,IAAI,GAAJ,CAAQ,CAAC,WAAD,EAAc;AAAe;AAA7B,EAAyD,cAAzD,EAAyE,WAAzE,EAAsF,cAAtF,EAAsG,cAAtG,EAAsH,WAAtH,EAAmI,aAAnI,EAAkJ,YAAlJ,EAAgK,MAAhK,CAAuK,6BAAc,KAAd,CAAoB,GAApB,CAAvK,CAAR,CAAtB;AACA,MAAM,qBAAqB,GAAG,IAAI,GAAJ,CAAQ,CAAC,SAAD,EAAY,eAAZ,EAA6B,gBAA7B,EAA+C,WAA/C,EAA4D,iBAA5D,EAA+E,QAA/E,EAAyF,WAAzF,EAAsG,WAAtG,EAAmH,QAAnH,EAA6H,QAA7H,EAAuI,MAAvI,EAA+I,WAA/I,EAA4J,OAA5J,EAAqK,cAArK,EAAqL,SAArL,EAAgM,UAAhM,EAA4M,MAA5M,CAAR,CAA9B;AAEA;;AACM,MAAO,oBAAP,SAAoC,+BAApC,CAAkD;AACtD,EAAA,WAAA,CAAY,OAAZ,EAAkC,QAAlC,EAAoD;AAClD,UAAM,OAAN,EAAe,OAAO,CAAC,OAAR,KAAoB,IAApB,GAA2B,OAAO,CAAC,YAAR,EAA1C,EAAkE,QAAlE;AACD;;AAED,QAAM,kBAAN,CAAyB,OAAzB,EAA0C,WAA1C,EAAyE,sBAAzE,EAA8G;AAC5G,UAAM,MAAM,GAAG,KAAK,MAApB;AACA,UAAM,QAAQ,GAAG,KAAK,QAAtB;AAEA,UAAM,gBAAgB,GAAG,yCAAgB,KAAK,QAAL,CAAc,MAAd,CAAqB,gBAArC,EAAuD,kBAAvD,CAAzB;AAEA,UAAM,MAAM,GAAkB,EAA9B;AACA,UAAM,KAAK,GAAkB,EAA7B;;AACA,SAAK,MAAM,UAAX,IAAyB,WAAzB,EAAsC;AACpC,YAAM,OAAO,GAAG,OAAO,GAAG,IAAI,CAAC,GAAf,GAAqB,UAArC;AACA,MAAA,KAAK,CAAC,MAAN,GAAe,CAAf,CAFoC,CAGpC;;AACA,YAAM,OAAO,GAAG,IAAI,CAAC,SAAL,CAAe,OAAf,CAAhB;AACA,MAAA,KAAK,CAAC,CAAD,CAAL,GAAW,OAAX;;AAEA,aAAO,KAAK,CAAC,MAAN,GAAe,CAAtB,EAAyB;AACvB,cAAM,OAAO,GAAG,KAAK,CAAC,GAAN,EAAhB;AAEA,cAAM,UAAU,GAAG,MAAM,wBAAQ,OAAR,CAAzB;AACA,QAAA,UAAU,CAAC,IAAX;AAEA,cAAM,UAAU,GAAG,OAAO,KAAK,OAA/B;AACA,cAAM,IAAI,GAAkB,EAA5B,CAPuB,CAQvB;;AACA,cAAM,eAAe,GAAG,MAAM,uBAAgB,GAAhB,CAAoB,UAApB,EAAgC,IAAI,IAAG;AACnE,cAAI,gBAAgB,IAAI,IAAxB,EAA8B;AAC5B,YAAA,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC,GAAf,GAAqB,IAAtB,CAAhB;AACD;;AAED,cAAI,aAAa,CAAC,GAAd,CAAkB,IAAlB,KAA2B,IAAI,CAAC,UAAL,CAAgB,IAAhB,CAA/B,EAAsD;AACpD,mBAAO,IAAP;AACD;;AAED,eAAK,MAAM,GAAX,IAAkB,sBAAlB,EAA0C;AACxC,gBAAI,IAAI,CAAC,QAAL,CAAc,GAAd,CAAJ,EAAwB;AACtB,qBAAO,IAAP;AACD;AACF,WAbkE,CAenE;;;AACA,cAAI,UAAU,KAAK,qBAAqB,CAAC,GAAtB,CAA0B,IAA1B,KAAoC,UAAU,KAAK,YAAf,KAAgC,IAAI,KAAK,OAAT,IAAoB,IAAI,KAAK,MAA7B,IAAuC,IAAI,KAAK,KAAhF,CAAzC,CAAd,EAAiJ;AAC/I,mBAAO,IAAP;AACD;;AAED,cAAI,OAAO,CAAC,QAAR,CAAiB,OAAjB,CAAJ,EAA+B;AAC7B,gBAAI,IAAI,KAAK,cAAT,IAA2B,IAAI,KAAK,UAApC,IAAkD,IAAI,CAAC,QAAL,CAAc,KAAd,CAAlD,IAA0E,IAAI,CAAC,QAAL,CAAc,OAAd,CAA1E,IAAoG,IAAI,CAAC,QAAL,CAAc,WAAd,CAAxG,EAAoI;AAClI,qBAAO,IAAP;AACD;AACF,WAJD,MAKK,IAAI,OAAO,CAAC,QAAR,CAAiB,SAAjB,MAAgC,IAAI,KAAK,OAAT,IAAoB,IAAI,KAAK,YAA7D,CAAJ,EAAgF;AACnF,mBAAO,IAAP;AACD,WAFI,MAGA,IAAI,IAAI,KAAK,KAAT,KAAmB,OAAO,CAAC,QAAR,CAAiB,QAAjB,KAA8B,OAAO,CAAC,QAAR,CAAiB,iBAAjB,CAAjD,CAAJ,EAA2F;AAC9F,mBAAO,IAAP;AACD,WAFI,MAGA,IAAI,OAAO,CAAC,QAAR,CAAiB,aAAjB,MAAoC,IAAI,KAAK,OAAT,IAAoB,IAAI,KAAK,MAAjE,CAAJ,EAA8E;AACjF,mBAAO,IAAP;AACD;;AAED,gBAAM,QAAQ,GAAG,OAAO,GAAG,IAAI,CAAC,GAAf,GAAqB,IAAtC;AACA,iBAAO,sBAAM,QAAN,EACJ,IADI,CACC,IAAI,IAAG;AACX,gBAAI,MAAM,IAAI,IAAV,IAAkB,CAAC,MAAM,CAAC,QAAD,EAAW,IAAX,CAA7B,EAA+C;AAC7C,qBAAO,IAAP;AACD;;AAED,gBAAI,CAAC,IAAI,CAAC,WAAL,EAAL,EAAyB;AACvB,cAAA,QAAQ,CAAC,GAAT,CAAa,QAAb,EAAuB,IAAvB;AACD;;AACD,kBAAM,cAAc,GAAG,KAAK,UAAL,CAAgB,QAAhB,EAA0B,OAA1B,EAAmC,IAAnC,CAAvB;;AACA,gBAAI,cAAc,IAAI,IAAtB,EAA4B;AAC1B,kBAAI,IAAI,CAAC,WAAL,EAAJ,EAAwB;AACtB,gBAAA,IAAI,CAAC,IAAL,CAAU,IAAV;AACA,uBAAO,IAAP;AACD,eAHD,MAIK;AACH,uBAAO,QAAP;AACD;AACF,aARD,MASK;AACH,qBAAO,cAAc,CAClB,IADI,CACC,EAAE,IAAG;AACT;AACA,oBAAI,CAAC,EAAE,IAAI,IAAN,GAAa,IAAb,GAAoB,EAArB,EAAyB,WAAzB,EAAJ,EAA4C;AAC1C,kBAAA,IAAI,CAAC,IAAL,CAAU,IAAV;AACA,yBAAO,IAAP;AACD,iBAHD,MAIK;AACH,yBAAO,QAAP;AACD;AACF,eAVI,CAAP;AAWD;AACF,WAhCI,CAAP;AAiCD,SArE6B,EAqE3B,iBArE2B,CAA9B;;AAuEA,aAAK,MAAM,KAAX,IAAoB,eAApB,EAAqC;AACnC,cAAI,KAAK,IAAI,IAAb,EAAmB;AACjB,YAAA,MAAM,CAAC,IAAP,CAAY,KAAZ;AACD;AACF;;AAED,QAAA,IAAI,CAAC,IAAL;;AACA,aAAK,MAAM,KAAX,IAAoB,IAApB,EAA0B;AACxB,UAAA,KAAK,CAAC,IAAN,CAAW,OAAO,GAAG,IAAI,CAAC,GAAf,GAAqB,KAAhC;AACD;AACF;AACF;;AACD,WAAO,MAAP;AACD;;AAjHqD,C", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { CONCURRENCY } from \"builder-util/out/fs\"\nimport { lstat, readdir } from \"fs-extra\"\nimport * as path from \"path\"\nimport { excludedNames, FileMatcher } from \"../fileMatcher\"\nimport { Packager } from \"../packager\"\nimport { resolveFunction } from \"../platformPackager\"\nimport { FileCopyHelper } from \"./AppFileWalker\"\n\nconst excludedFiles = new Set([\".DS_Store\", \"node_modules\" /* already in the queue */, \"CHANGELOG.md\", \"ChangeLog\", \"changelog.md\", \"Changelog.md\", \"Changelog\", \"binding.gyp\", \".npmignore\"].concat(excludedNames.split(\",\")))\nconst topLevelExcludedFiles = new Set([\"test.js\", \"karma.conf.js\", \".coveralls.yml\", \"README.md\", \"readme.markdown\", \"README\", \"readme.md\", \"Readme.md\", \"Readme\", \"readme\", \"test\", \"__tests__\", \"tests\", \"powered-test\", \"example\", \"examples\", \".bin\"])\n\n/** @internal */\nexport class NodeModuleCopyHelper extends FileCopyHelper {\n  constructor(matcher: FileMatcher, packager: Packager) {\n    super(matcher, matcher.isEmpty() ? null : matcher.createFilter(), packager)\n  }\n\n  async collectNodeModules(baseDir: string, moduleNames: Iterable<string>, nodeModuleExcludedExts: Array<string>): Promise<Array<string>> {\n    const filter = this.filter\n    const metadata = this.metadata\n\n    const onNodeModuleFile = resolveFunction(this.packager.config.onNodeModuleFile, \"onNodeModuleFile\")\n\n    const result: Array<string> = []\n    const queue: Array<string> = []\n    for (const moduleName of moduleNames) {\n      const tmpPath = baseDir + path.sep + moduleName\n      queue.length = 1\n      // The path should be corrected in Windows that when the moduleName is Scoped packages named.\n      const depPath = path.normalize(tmpPath)\n      queue[0] = depPath\n\n      while (queue.length > 0) {\n        const dirPath = queue.pop()!\n\n        const childNames = await readdir(dirPath)\n        childNames.sort()\n\n        const isTopLevel = dirPath === depPath\n        const dirs: Array<string> = []\n        // our handler is async, but we should add sorted files, so, we add file to result not in the mapper, but after map\n        const sortedFilePaths = await BluebirdPromise.map(childNames, name => {\n          if (onNodeModuleFile != null) {\n            onNodeModuleFile(dirPath + path.sep + name)\n          }\n\n          if (excludedFiles.has(name) || name.startsWith(\"._\")) {\n            return null\n          }\n\n          for (const ext of nodeModuleExcludedExts) {\n            if (name.endsWith(ext)) {\n              return null\n            }\n          }\n\n          // noinspection SpellCheckingInspection\n          if (isTopLevel && (topLevelExcludedFiles.has(name) || (moduleName === \"libui-node\" && (name === \"build\" || name === \"docs\" || name === \"src\")))) {\n            return null\n          }\n\n          if (dirPath.endsWith(\"build\")) {\n            if (name === \"gyp-mac-tool\" || name === \"Makefile\" || name.endsWith(\".mk\") || name.endsWith(\".gypi\") || name.endsWith(\".Makefile\")) {\n              return null\n            }\n          }\n          else if (dirPath.endsWith(\"Release\") && (name === \".deps\" || name === \"obj.target\")) {\n            return null\n          }\n          else if (name === \"src\" && (dirPath.endsWith(\"keytar\") || dirPath.endsWith(\"keytar-prebuild\"))) {\n            return null\n          }\n          else if (dirPath.endsWith(\"lzma-native\") && (name === \"build\" || name === \"deps\")) {\n            return null\n          }\n\n          const filePath = dirPath + path.sep + name\n          return lstat(filePath)\n            .then(stat => {\n              if (filter != null && !filter(filePath, stat)) {\n                return null\n              }\n\n              if (!stat.isDirectory()) {\n                metadata.set(filePath, stat)\n              }\n              const consumerResult = this.handleFile(filePath, dirPath, stat)\n              if (consumerResult == null) {\n                if (stat.isDirectory()) {\n                  dirs.push(name)\n                  return null\n                }\n                else {\n                  return filePath\n                }\n              }\n              else {\n                return consumerResult\n                  .then(it => {\n                    // asarUtil can return modified stat (symlink handling)\n                    if ((it == null ? stat : it).isDirectory()) {\n                      dirs.push(name)\n                      return null\n                    }\n                    else {\n                      return filePath\n                    }\n                  })\n              }\n            })\n        }, CONCURRENCY)\n\n        for (const child of sortedFilePaths) {\n          if (child != null) {\n            result.push(child)\n          }\n        }\n\n        dirs.sort()\n        for (const child of dirs) {\n          queue.push(dirPath + path.sep + child)\n        }\n      }\n    }\n    return result\n  }\n}\n"], "sourceRoot": ""}