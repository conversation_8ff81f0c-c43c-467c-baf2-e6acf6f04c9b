{"version": 3, "sources": ["../../src/util/packageMetadata.ts"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;;;AAEA;AACO,eAAe,eAAf,CAA+B,IAA/B,EAA2C;AAChD,QAAM,IAAI,GAAG,MAAM,yBAAS,IAAT,CAAnB;AACA,QAAM,OAAO,CAAC,IAAD,EAAO,IAAP,CAAb;AACA,uCAAc,IAAd,EAHgD,CAIhD;;AACA,SAAO,IAAI,CAAC,OAAZ;AACA,SAAO,IAAI,CAAC,MAAZ;AACA,SAAO,IAAP;AACD;;AAED,eAAe,OAAf,CAAuB,IAAvB,EAAqC,IAArC,EAA8C;AAC5C,MAAI,IAAI,CAAC,YAAL,IAAqB,IAAzB,EAA+B;AAC7B;AACD;;AAED,MAAI,UAAJ;;AACA,MAAI;AACF,IAAA,UAAU,GAAG,MAAM,yBAAS,IAAI,CAAC,OAAL,CAAa,IAAI,CAAC,OAAL,CAAa,IAAb,CAAb,EAAiC,SAAjC,CAAT,EAAsD,MAAtD,CAAnB;AACD,GAFD,CAGA,OAAO,OAAP,EAAgB;AACd;AACD;;AAED,EAAA,IAAI,CAAC,YAAL,GAAoB,UAAU,CAC3B,KADiB,CACX,QADW,EAEjB,GAFiB,CAEb,EAAE,IAAI,EAAE,CAAC,OAAH,CAAW,UAAX,EAAuB,EAAvB,EAA2B,IAA3B,EAFO,CAApB;AAGD;AAED;;;AACM,SAAU,aAAV,CAAwB,QAAxB,EAA4C,WAA5C,EAAqE,cAArE,EAA6F,iBAA7F,EAAsH;AAC1H,QAAM,MAAM,GAAkB,EAA9B;;AACA,QAAM,WAAW,GAAI,eAAD,IAA4B;AAC9C,IAAA,MAAM,CAAC,IAAP,CAAY,mBAAmB,eAAe,0BAA0B,cAAc,GAAtF;AACD,GAFD;;AAIA,QAAM,aAAa,GAAG,CAAC,IAAD,EAAe,KAAf,KAAmD;AACvE,QAAI,oCAAgB,KAAhB,CAAJ,EAA4B;AAC1B,MAAA,WAAW,CAAC,IAAD,CAAX;AACD;AACF,GAJD;;AAMA,MAAK,QAAgB,CAAC,WAAjB,IAAgC,IAArC,EAA2C;AACzC,IAAA,MAAM,CAAC,IAAP,CAAY,wEAAZ;AACD;;AAED,EAAA,aAAa,CAAC,MAAD,EAAS,QAAQ,CAAC,IAAlB,CAAb;;AAEA,MAAI,oCAAgB,QAAQ,CAAC,WAAzB,CAAJ,EAA2C;AACzC,uBAAI,IAAJ,CAAS;AAAC,MAAA;AAAD,KAAT,EAA2B,2CAA3B;AACD;;AACD,MAAI,QAAQ,CAAC,MAAT,IAAmB,IAAvB,EAA6B;AAC3B,uBAAI,IAAJ,CAAS;AAAC,MAAA;AAAD,KAAT,EAA2B,sCAA3B;AACD;;AACD,EAAA,aAAa,CAAC,SAAD,EAAY,QAAQ,CAAC,OAArB,CAAb;;AAEA,MAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,IAAA,iBAAiB,CAAC,QAAQ,CAAC,YAAV,EAAwB,MAAxB,CAAjB;AACD;;AACD,MAAI,QAAQ,KAAK,WAAjB,EAA8B;AAC5B,QAAI,QAAQ,CAAC,KAAT,IAAkB,IAAtB,EAA4B;AAC1B,MAAA,MAAM,CAAC,IAAP,CAAY,4CAA4C,cAAc,gGAAgG,iBAAiB,GAAvL;AACD;AACF;;AAED,QAAM,eAAe,GAAI,QAAgB,CAAC,eAA1C;;AACA,MAAI,eAAe,IAAI,IAAnB,IAA2B,sBAAsB,eAArD,EAAsE;AACpE,uBAAI,IAAJ,CAAS,qSAAT;AACD;;AAED,MAAI,MAAM,CAAC,MAAP,GAAgB,CAApB,EAAuB;AACrB,UAAM,KAAI,wCAAJ,EAA8B,MAAM,CAAC,IAAP,CAAY,IAAZ,CAA9B,CAAN;AACD;AACF;;AAED,SAAS,gBAAT,CAA0B,OAA1B,EAAkE,KAAlE,EAAgG,KAAhG,EAA+G;AAC7G,MAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,WAAO,KAAP;AACD;;AAED,QAAM,OAAO,GAAG,MAAM,GAAC,MAAP,CAAc,OAAd,CAAhB;;AACA,MAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,WAAO,KAAP;AACD;;AAED,SAAO,MAAM,GAAC,SAAP,CAAiB,OAAjB,EAA0B,KAA1B,EAAiC,KAAjC,CAAP;AACD;;AAED,SAAS,iBAAT,CAA2B,YAA3B,EAAuF,MAAvF,EAA4G;AAC1G,MAAI,YAAY,IAAI,IAApB,EAA0B;AACxB;AACD;;AAED,QAAM,cAAc,GAAG,YAAY,CAAC,kBAAD,CAAnC;AACA,QAAM,8BAA8B,GAAG,OAAvC;;AACA,MAAI,cAAc,IAAI,IAAlB,IAA0B,CAAC,gBAAgB,CAAC,cAAD,EAAiB,KAAK,8BAA8B,EAApD,CAA/C,EAAwG;AACtG,IAAA,MAAM,CAAC,IAAP,CAAY,6BAA6B,8BAA8B,iGAAiG,8BAA8B,GAAtM;AACD;;AAED,QAAM,SAAS,GAAG,YAAY,CAAC,mCAAD,CAA9B;;AACA,MAAI,SAAS,IAAI,IAAb,IAAqB,CAAC,gBAAgB,CAAC,SAAD,EAAY,WAAZ,CAA1C,EAAoE;AAClE,IAAA,MAAM,CAAC,IAAP,CAAY,gKAAZ;AACD;;AAED,QAAM,IAAI,GAAG,CAAC,UAAD,EAAa,mBAAb,EAAkC,kBAAlC,CAAb;;AACA,MAAI,OAAO,CAAC,GAAR,CAAY,+CAAZ,KAAgE,MAApE,EAA4E;AAC1E,IAAA,IAAI,CAAC,IAAL,CAAU,kBAAV;AACD;;AACD,OAAK,MAAM,IAAX,IAAmB,IAAnB,EAAyB;AACvB,QAAI,IAAI,IAAI,YAAZ,EAA0B;AACxB,MAAA,MAAM,CAAC,IAAP,CAAY,YAAY,IAAI,0CAAhB,GACR,wEADJ;AAED;AACF;AACF,C", "sourcesContent": ["import { isEmptyOrSpaces, log, InvalidConfigurationError } from \"builder-util\"\nimport { readFile, readJson } from \"fs-extra\"\nimport * as path from \"path\"\nimport * as semver from \"semver\"\nimport { Metadata } from \"..\"\nimport normalizeData from \"normalize-package-data\"\n\n/** @internal */\nexport async function readPackageJson(file: string): Promise<any> {\n  const data = await readJson(file)\n  await authors(file, data)\n  normalizeData(data)\n  // remove not required fields because can be used for remote build\n  delete data.scripts\n  delete data.readme\n  return data\n}\n\nasync function authors(file: string, data: any) {\n  if (data.contributors != null) {\n    return\n  }\n\n  let authorData\n  try {\n    authorData = await readFile(path.resolve(path.dirname(file), \"AUTHORS\"), \"utf8\")\n  }\n  catch (ignored) {\n    return\n  }\n\n  data.contributors = authorData\n    .split(/\\r?\\n/g)\n    .map(it => it.replace(/^\\s*#.*$/, \"\").trim())\n}\n\n/** @internal */\nexport function checkMetadata(metadata: Metadata, devMetadata: any | null, appPackageFile: string, devAppPackageFile: string): void {\n  const errors: Array<string> = []\n  const reportError = (missedFieldName: string) => {\n    errors.push(`Please specify '${missedFieldName}' in the package.json (${appPackageFile})`)\n  }\n\n  const checkNotEmpty = (name: string, value: string | null | undefined) => {\n    if (isEmptyOrSpaces(value)) {\n      reportError(name)\n    }\n  }\n\n  if ((metadata as any).directories != null) {\n    errors.push(`\"directories\" in the root is deprecated, please specify in the \"build\"`)\n  }\n\n  checkNotEmpty(\"name\", metadata.name)\n\n  if (isEmptyOrSpaces(metadata.description)) {\n    log.warn({appPackageFile}, `description is missed in the package.json`)\n  }\n  if (metadata.author == null) {\n    log.warn({appPackageFile}, `author is missed in the package.json`)\n  }\n  checkNotEmpty(\"version\", metadata.version)\n\n  if (metadata != null) {\n    checkDependencies(metadata.dependencies, errors)\n  }\n  if (metadata !== devMetadata) {\n    if (metadata.build != null) {\n      errors.push(`'build' in the application package.json (${appPackageFile}) is not supported since 3.0 anymore. Please move 'build' into the development package.json (${devAppPackageFile})`)\n    }\n  }\n\n  const devDependencies = (metadata as any).devDependencies\n  if (devDependencies != null && \"electron-rebuild\" in devDependencies) {\n    log.info('electron-rebuild not required if you use electron-builder, please consider to remove excess dependency from devDependencies\\n\\nTo ensure your native dependencies are always matched electron version, simply add script `\"postinstall\": \"electron-builder install-app-deps\" to your `package.json`')\n  }\n\n  if (errors.length > 0) {\n    throw new InvalidConfigurationError(errors.join(\"\\n\"))\n  }\n}\n\nfunction versionSatisfies(version: string | semver.SemVer | null, range: string | semver.Range, loose?: boolean): boolean {\n  if (version == null) {\n    return false\n  }\n\n  const coerced = semver.coerce(version)\n  if (coerced == null) {\n    return false\n  }\n\n  return semver.satisfies(coerced, range, loose)\n}\n\nfunction checkDependencies(dependencies: { [key: string]: string } | null | undefined, errors: Array<string>) {\n  if (dependencies == null) {\n    return\n  }\n\n  const updaterVersion = dependencies[\"electron-updater\"]\n  const requiredElectronUpdaterVersion = \"4.0.0\"\n  if (updaterVersion != null && !versionSatisfies(updaterVersion, `>=${requiredElectronUpdaterVersion}`)) {\n    errors.push(`At least electron-updater ${requiredElectronUpdaterVersion} is recommended by current electron-builder version. Please set electron-updater version to \"^${requiredElectronUpdaterVersion}\"`)\n  }\n\n  const swVersion = dependencies[\"electron-builder-squirrel-windows\"]\n  if (swVersion != null && !versionSatisfies(swVersion, \">=20.32.0\")) {\n    errors.push(`At least electron-builder-squirrel-windows 20.32.0 is required by current electron-builder version. Please set electron-builder-squirrel-windows to \"^20.32.0\"`)\n  }\n\n  const deps = [\"electron\", \"electron-prebuilt\", \"electron-rebuild\"]\n  if (process.env.ALLOW_ELECTRON_BUILDER_AS_PRODUCTION_DEPENDENCY !== \"true\") {\n    deps.push(\"electron-builder\")\n  }\n  for (const name of deps) {\n    if (name in dependencies) {\n      errors.push(`Package \"${name}\" is only allowed in \"devDependencies\". `\n        + `Please remove it from the \"dependencies\" section in your package.json.`)\n    }\n  }\n}"], "sourceRoot": ""}