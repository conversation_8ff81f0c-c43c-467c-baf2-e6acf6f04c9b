{"version": 3, "sources": ["../../src/util/yarn.ts"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;AAGO,eAAe,gBAAf,CAAgC,MAAhC,EAAuD,MAAvD,EAAuE,OAAvE,EAAgG,YAAA,GAAwB,KAAxH,EAA6H;AAClI,QAAM,gBAAgB,GAAG;AACvB,IAAA,eAAe,EAAE,MAAM,CAAC,2BAAP,KAAuC,IADjC;AAEvB,IAAA,cAAc,EAAE,4BAAQ,MAAM,CAAC,OAAf,CAFO;AAEkB,OAAG;AAFrB,GAAzB;AAIA,MAAI,uBAAuB,GAAG,KAA9B;;AAEA,OAAK,MAAM,SAAX,IAAwB,CAAC,cAAD,EAAiB,SAAjB,CAAxB,EAAqD;AACnD,QAAI,MAAM,2BAAW,IAAI,CAAC,IAAL,CAAU,MAAV,EAAkB,SAAlB,CAAX,CAAV,EAAoD;AAClD,MAAA,uBAAuB,GAAG,IAA1B;AAEA;AACD;AACF;;AAED,MAAI,YAAY,IAAI,CAAC,uBAArB,EAA8C;AAC5C,UAAM,mBAAmB,CAAC,MAAD,EAAS,gBAAT,CAAzB;AACD,GAFD,MAGK;AACH,UAAM,OAAO,CAAC,MAAD,EAAS,gBAAT,CAAb;AACD;AACF;;AAOD,SAAS,sBAAT,GAA+B;AAC7B,SAAO,IAAI,CAAC,IAAL,CAAU,oBAAV,EAAqB,eAArB,CAAP;AACD;;AAEK,SAAU,SAAV,CAAoB,aAApB,EAAyD,QAAzD,EAAoF,IAApF,EAAkG,eAAlG,EAA0H;AAC9H,QAAM,aAAa,GAAG,IAAI,KAAK,QAAT,GAAoB,KAApB,GAA4B,IAAlD;AACA,QAAM,MAAM,GAAQ,EAClB,GAAG,OAAO,CAAC,GADO;AAElB,IAAA,eAAe,EAAE,aAFC;AAGlB,IAAA,sBAAsB,EAAE,aAHN;AAIlB,IAAA,mBAAmB,EAAE,QAJH;AAKlB,IAAA,4BAA4B,EAAE,eALZ;AAMlB;AACA,IAAA,0BAA0B,EAAE,QAPV;AAQlB,IAAA,wBAAwB,EAAE,IARR;AASlB,IAAA,4BAA4B,EAAE;AATZ,GAApB;;AAYA,MAAI,QAAQ,KAAK,OAAO,CAAC,QAAzB,EAAmC;AACjC,IAAA,MAAM,CAAC,gBAAP,GAA0B,MAA1B;AACD;;AACD,MAAI,QAAQ,KAAK,OAAb,IAAwB,QAAQ,KAAK,QAAzC,EAAmD;AACjD,IAAA,MAAM,CAAC,sBAAP,GAAgC,SAAhC;AACD;;AAED,MAAI,CAAC,aAAa,CAAC,aAAnB,EAAkC;AAChC,WAAO,MAAP;AACD,GAvB6H,CAyB9H;;;AACA,SAAO,EACL,GAAG,MADE;AAEL,IAAA,kBAAkB,EAAE,gCAFf;AAGL,IAAA,iBAAiB,EAAE,aAAa,CAAC,OAH5B;AAIL,IAAA,kBAAkB,EAAE,UAJf;AAKL,IAAA,iBAAiB,EAAE,sBAAsB;AALpC,GAAP;AAOD;;AAED,SAAS,mBAAT,CAA6B,MAA7B,EAA6C,OAA7C,EAAoE;AAClE,QAAM,QAAQ,GAAG,OAAO,CAAC,QAAR,IAAoB,OAAO,CAAC,QAA7C;AACA,QAAM,IAAI,GAAG,OAAO,CAAC,IAAR,IAAgB,OAAO,CAAC,IAArC;AACA,QAAM,cAAc,GAAG,OAAO,CAAC,cAA/B;;AAEA,qBAAI,IAAJ,CAAS;AAAC,IAAA,QAAD;AAAW,IAAA,IAAX;AAAiB,IAAA;AAAjB,GAAT,EAAmC,oCAAnC;;AACA,MAAI,QAAQ,GAAG,OAAO,CAAC,GAAR,CAAY,YAAZ,IAA4B,OAAO,CAAC,GAAR,CAAY,UAAvD;AACA,QAAM,QAAQ,GAAG,CAAC,SAAD,CAAjB;AACA,QAAM,YAAY,GAAG,OAAO,CAAC,GAAR,CAAY,uBAAZ,CAArB;AACA,QAAM,OAAO,GAAG,YAAY,IAAI,IAAhB,IAAwB,YAAY,CAAC,UAAb,CAAwB,SAAxB,CAAxC;;AACA,MAAI,CAAC,OAAL,EAAc;AACZ,QAAI,OAAO,CAAC,GAAR,CAAY,gBAAZ,KAAiC,MAArC,EAA6C;AAC3C,MAAA,QAAQ,CAAC,IAAT,CAAc,gBAAd;AACD;;AACD,IAAA,QAAQ,CAAC,IAAT,CAAc,cAAd;AACD;;AAED,MAAI,CAAC,aAAa,CAAC,QAAD,CAAlB,EAA8B;AAC5B,IAAA,QAAQ,CAAC,IAAT,CAAc,aAAd,EAA6B,WAA7B;AACD;;AAED,MAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,IAAA,QAAQ,GAAG,kBAAkB,EAA7B;AACD,GAFD,MAGK,IAAI,CAAC,OAAL,EAAc;AACjB,IAAA,QAAQ,CAAC,OAAT,CAAiB,QAAjB;AACA,IAAA,QAAQ,GAAG,OAAO,CAAC,GAAR,CAAY,iBAAZ,IAAiC,OAAO,CAAC,GAAR,CAAY,QAA7C,IAAyD,MAApE;AACD;;AAED,MAAI,cAAc,IAAI,IAAtB,EAA4B;AAC1B,IAAA,QAAQ,CAAC,IAAT,CAAc,GAAG,cAAjB;AACD;;AACD,SAAO,0BAAM,QAAN,EAAgB,QAAhB,EAA0B;AAC/B,IAAA,GAAG,EAAE,MAD0B;AAE/B,IAAA,GAAG,EAAE,SAAS,CAAC,OAAO,CAAC,aAAT,EAAwB,QAAxB,EAAkC,IAAlC,EAAwC,OAAO,CAAC,eAAR,KAA4B,IAApE;AAFiB,GAA1B,CAAP;AAID;;AAEM,eAAe,cAAf,CAA8B,QAA9B,EAAyD,IAAzD,EAAuE,aAAvE,EAA0G;AAC/G,qBAAI,IAAJ,CAAS;AAAC,IAAA,QAAD;AAAW,IAAA;AAAX,GAAT,EAA2B,4BAA3B,EAD+G,CAE/G;;;AACA,QAAM,OAAO,GAAG,WAAW,OAAO,CAAC,QAAR,KAAqB,OAArB,GAA+B,MAA/B,GAAwC,EAAE,EAArE;AACA,QAAM,0BAAM,OAAN,EAAe,CAAC,SAAD,CAAf,EAA4B;AAAE,IAAA,GAAG,EAAE,SAAS,CAAC,aAAD,EAAgB,QAAhB,EAA0B,IAA1B,EAAgC,IAAhC;AAAhB,GAA5B,CAAN;AACD;;AAED,SAAS,kBAAT,GAA2B;AACzB,MAAI,OAAO,CAAC,GAAR,CAAY,UAAZ,KAA2B,MAA/B,EAAuC;AACrC,WAAO,OAAO,CAAC,QAAR,KAAqB,OAArB,GAA+B,UAA/B,GAA4C,MAAnD;AACD,GAFD,MAGK;AACH,WAAO,OAAO,CAAC,QAAR,KAAqB,OAArB,GAA+B,SAA/B,GAA2C,KAAlD;AACD;AACF;;AAED,SAAS,aAAT,CAAuB,QAAvB,EAA0D;AACxD,QAAM,SAAS,GAAG,OAAO,CAAC,GAAR,CAAY,qBAA9B;AACA,SAAO,OAAO,CAAC,GAAR,CAAY,UAAZ,KAA2B,MAA3B,IACJ,QAAQ,IAAI,IAAZ,IAAoB,IAAI,CAAC,QAAL,CAAc,QAAd,EAAwB,UAAxB,CAAmC,MAAnC,CADhB,IAEJ,SAAS,IAAI,IAAb,IAAqB,WAAW,IAAX,CAAgB,SAAhB,CAFxB;AAGD;AAcD;;;AACO,eAAe,OAAf,CAAuB,MAAvB,EAAuC,OAAvC,EAA8D;AACnE,QAAM,aAAa,GAAQ;AACzB,IAAA,YAAY,EAAE,MAAM,OAAO,CAAC,cAAR,CAAyB,KADpB;AAEzB,IAAA,YAAY,EAAE,OAAO,CAAC,QAFG;AAGzB,IAAA,QAAQ,EAAE,OAAO,CAAC,QAAR,IAAoB,OAAO,CAAC,QAHb;AAIzB,IAAA,IAAI,EAAE,OAAO,CAAC,IAAR,IAAgB,OAAO,CAAC,IAJL;AAKzB,IAAA,cAAc,EAAE,OAAO,CAAC,cALC;AAMzB,IAAA,QAAQ,EAAE,OAAO,CAAC,GAAR,CAAY,YAAZ,IAA4B,OAAO,CAAC,GAAR,CAAY,UANzB;AAOzB,IAAA,eAAe,EAAE,OAAO,CAAC,eAAR,KAA4B;AAPpB,GAA3B;AAUA,QAAM,GAAG,GAAG,SAAS,CAAC,OAAO,CAAC,aAAT,EAAwB,aAAa,CAAC,QAAtC,EAAgD,aAAa,CAAC,IAA9D,EAAoE,OAAO,CAAC,eAAR,KAA4B,IAAhG,CAArB;AACA,QAAM,iDAA8B,CAAC,sBAAD,CAA9B,EAAwD,aAAxD,EAAuE;AAAC,IAAA,GAAD;AAAM,IAAA,GAAG,EAAE;AAAX,GAAvE,CAAN;AACD,C", "sourcesContent": ["import { asArray, log, spawn } from \"builder-util\"\nimport { pathExists } from \"fs-extra\"\nimport { Lazy } from \"lazy-val\"\nimport { homedir } from \"os\"\nimport * as path from \"path\"\nimport { Configuration } from \"../configuration\"\nimport { executeAppBuilderAndWriteJson } from \"./appBuilder\"\nimport { NodeModuleDirInfo } from \"./packageDependencies\"\n\nexport async function installOrRebuild(config: Configuration, appDir: string, options: RebuildOptions, forceInstall: boolean = false) {\n  const effectiveOptions = {\n    buildFromSource: config.buildDependenciesFromSource === true,\n    additionalArgs: asArray(config.npmArgs), ...options\n  }\n  let isDependenciesInstalled = false\n\n  for (const fileOrDir of [\"node_modules\", \".pnp.js\"]) {\n    if (await pathExists(path.join(appDir, fileOrDir))) {\n      isDependenciesInstalled = true\n\n      break\n    }\n  }\n\n  if (forceInstall || !isDependenciesInstalled) {\n    await installDependencies(appDir, effectiveOptions)\n  }\n  else {\n    await rebuild(appDir, effectiveOptions)\n  }\n}\n\nexport interface DesktopFrameworkInfo {\n  version: string\n  useCustomDist: boolean\n}\n\nfunction getElectronGypCacheDir() {\n  return path.join(homedir(), \".electron-gyp\")\n}\n\nexport function getGypEnv(frameworkInfo: DesktopFrameworkInfo, platform: NodeJS.Platform, arch: string, buildFromSource: boolean) {\n  const npmConfigArch = arch === \"armv7l\" ? \"arm\" : arch\n  const common: any = {\n    ...process.env,\n    npm_config_arch: npmConfigArch,\n    npm_config_target_arch: npmConfigArch,\n    npm_config_platform: platform,\n    npm_config_build_from_source: buildFromSource,\n    // required for node-pre-gyp\n    npm_config_target_platform: platform,\n    npm_config_update_binary: true,\n    npm_config_fallback_to_build: true,\n  }\n\n  if (platform !== process.platform) {\n    common.npm_config_force = \"true\"\n  }\n  if (platform === \"win32\" || platform === \"darwin\") {\n    common.npm_config_target_libc = \"unknown\"\n  }\n\n  if (!frameworkInfo.useCustomDist) {\n    return common\n  }\n\n  // https://github.com/nodejs/node-gyp/issues/21\n  return {\n    ...common,\n    npm_config_disturl: \"https://electronjs.org/headers\",\n    npm_config_target: frameworkInfo.version,\n    npm_config_runtime: \"electron\",\n    npm_config_devdir: getElectronGypCacheDir(),\n  }\n}\n\nfunction installDependencies(appDir: string, options: RebuildOptions): Promise<any> {\n  const platform = options.platform || process.platform\n  const arch = options.arch || process.arch\n  const additionalArgs = options.additionalArgs\n\n  log.info({platform, arch, appDir}, `installing production dependencies`)\n  let execPath = process.env.npm_execpath || process.env.NPM_CLI_JS\n  const execArgs = [\"install\"]\n  const npmUserAgent = process.env[\"npm_config_user_agent\"]\n  const isYarn2 = npmUserAgent != null && npmUserAgent.startsWith(\"yarn/2.\")\n  if (!isYarn2) {\n    if (process.env.NPM_NO_BIN_LINKS === \"true\") {\n      execArgs.push(\"--no-bin-links\")\n    }\n    execArgs.push(\"--production\")\n  }\n\n  if (!isRunningYarn(execPath)) {\n    execArgs.push(\"--cache-min\", \"999999999\")\n  }\n\n  if (execPath == null) {\n    execPath = getPackageToolPath()\n  }\n  else if (!isYarn2) {\n    execArgs.unshift(execPath)\n    execPath = process.env.npm_node_execpath || process.env.NODE_EXE || \"node\"\n  }\n\n  if (additionalArgs != null) {\n    execArgs.push(...additionalArgs)\n  }\n  return spawn(execPath, execArgs, {\n    cwd: appDir,\n    env: getGypEnv(options.frameworkInfo, platform, arch, options.buildFromSource === true),\n  })\n}\n\nexport async function nodeGypRebuild(platform: NodeJS.Platform, arch: string, frameworkInfo: DesktopFrameworkInfo) {\n  log.info({platform, arch}, \"executing node-gyp rebuild\")\n  // this script must be used only for electron\n  const nodeGyp = `node-gyp${process.platform === \"win32\" ? \".cmd\" : \"\"}`\n  await spawn(nodeGyp, [\"rebuild\"], { env: getGypEnv(frameworkInfo, platform, arch, true) })\n}\n\nfunction getPackageToolPath() {\n  if (process.env.FORCE_YARN === \"true\") {\n    return process.platform === \"win32\" ? \"yarn.cmd\" : \"yarn\"\n  }\n  else {\n    return process.platform === \"win32\" ? \"npm.cmd\" : \"npm\"\n  }\n}\n\nfunction isRunningYarn(execPath: string | null | undefined) {\n  const userAgent = process.env.npm_config_user_agent\n  return process.env.FORCE_YARN === \"true\" ||\n    (execPath != null && path.basename(execPath).startsWith(\"yarn\")) ||\n    (userAgent != null && /\\byarn\\b/.test(userAgent))\n}\n\nexport interface RebuildOptions {\n  frameworkInfo: DesktopFrameworkInfo\n  productionDeps?: Lazy<Array<NodeModuleDirInfo>>\n\n  platform?: NodeJS.Platform\n  arch?: string\n\n  buildFromSource?: boolean\n\n  additionalArgs?: Array<string> | null\n}\n\n/** @internal */\nexport async function rebuild(appDir: string, options: RebuildOptions) {\n  const configuration: any = {\n    dependencies: await options.productionDeps!!.value,\n    nodeExecPath: process.execPath,\n    platform: options.platform || process.platform,\n    arch: options.arch || process.arch,\n    additionalArgs: options.additionalArgs,\n    execPath: process.env.npm_execpath || process.env.NPM_CLI_JS,\n    buildFromSource: options.buildFromSource === true,\n  }\n\n  const env = getGypEnv(options.frameworkInfo, configuration.platform, configuration.arch, options.buildFromSource === true)\n  await executeAppBuilderAndWriteJson([\"rebuild-node-modules\"], configuration, {env, cwd: appDir})\n}\n"], "sourceRoot": ""}