{"version": 3, "sources": ["../../src/util/cacheManager.ts"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;;;;;;;AAMM,MAAO,iBAAP,CAAwB;AAW5B,EAAA,WAAA,CAAY,MAAZ,EAA6C,cAA7C,EAAqE,IAArE,EAA+E;AAAlC,SAAA,cAAA,GAAA,cAAA;AAJ7C,SAAA,SAAA,GAAmC,IAAnC;AAEQ,SAAA,SAAA,GAA2B,IAA3B;AAGN,SAAK,QAAL,GAAgB,IAAI,CAAC,IAAL,CAAU,MAAV,EAAkB,QAAlB,EAA4B,oBAAK,IAAL,CAA5B,CAAhB;AACA,SAAK,SAAL,GAAiB,IAAI,CAAC,IAAL,CAAU,KAAK,QAAf,EAAyB,SAAzB,CAAjB;AACA,SAAK,aAAL,GAAqB,IAAI,CAAC,IAAL,CAAU,KAAK,QAAf,EAAyB,WAAzB,CAArB;AACD;;AAED,QAAM,WAAN,CAAkB,MAAlB,EAAgC;AAC9B,SAAK,SAAL,GAAiB,MAAjB;AAEA,SAAK,SAAL,GAAiB,MAAM,qCAAqB,yBAAS,KAAK,aAAd,CAArB,CAAvB;AACA,UAAM,SAAS,GAAG,KAAK,SAAL,IAAkB,IAAlB,GAAyB,IAAzB,GAAgC,KAAK,SAAL,CAAe,gBAAjE;;AACA,QAAI,SAAS,KAAK,MAAlB,EAA0B;AACxB,yBAAI,KAAJ,CAAU;AAAC,QAAA,SAAD;AAAY,QAAA,SAAS,EAAE;AAAvB,OAAV,EAA0C,kCAA1C;;AACA,aAAO,KAAP;AACD;;AAED,uBAAI,KAAJ,CAAU;AAAC,MAAA,SAAS,EAAE,KAAK,SAAjB;AAA4B,MAAA,IAAI,EAAE,KAAK;AAAvC,KAAV,EAAkE,2BAAlE;;AACA,QAAI;AACF,YAAM,oBAAS,KAAK,SAAd,EAAyB,KAAK,cAA9B,EAA8C,KAA9C,CAAN;AACA,aAAO,IAAP;AACD,KAHD,CAIA,OAAO,CAAP,EAAU;AACR,UAAI,CAAC,CAAC,IAAF,KAAW,QAAX,IAAuB,CAAC,CAAC,IAAF,KAAW,SAAtC,EAAiD;AAC/C,2BAAI,KAAJ,CAAU;AAAC,UAAA,KAAK,EAAE,CAAC,CAAC;AAAV,SAAV,EAA2B,+BAA3B;AACD,OAFD,MAGK;AACH,2BAAI,IAAJ,CAAS;AAAC,UAAA,KAAK,EAAE,CAAC,CAAC,KAAF,IAAW;AAAnB,SAAT,EAAgC,+BAAhC;AACD;AACF;;AACD,WAAO,KAAP;AACD;;AAED,QAAM,IAAN,GAAU;AACR,QAAI,KAAK,SAAL,IAAkB,IAAtB,EAA4B;AAC1B,YAAM,IAAI,KAAJ,CAAU,yBAAV,CAAN;AACD;;AAED,QAAI,KAAK,SAAL,IAAkB,IAAtB,EAA4B;AAC1B,WAAK,SAAL,GAAiB;AAAC,QAAA,gBAAgB,EAAE,KAAK;AAAxB,OAAjB;AACD,KAFD,MAGK;AACH,WAAK,SAAL,CAAe,gBAAf,GAAkC,KAAK,SAAvC;AACD;;AAED,QAAI;AACF,YAAM,0BAAU,KAAK,QAAf,CAAN;AACA,YAAM,OAAO,CAAC,GAAR,CAAY,CAAC,0BAAU,KAAK,aAAf,EAA8B,KAAK,SAAnC,CAAD,EAAgD,oBAAS,KAAK,cAAd,EAA8B,KAAK,SAAnC,EAA8C,KAA9C,CAAhD,CAAZ,CAAN;AACD,KAHD,CAIA,OAAO,CAAP,EAAU;AACR,yBAAI,IAAJ,CAAS;AAAC,QAAA,KAAK,EAAE,CAAC,CAAC,KAAF,IAAW;AAAnB,OAAT,EAAgC,yBAAhC;AACD;AACF;;AA9D2B;;;AACrB,iBAAA,CAAA,OAAA,GAAkB,GAAlB;;AAgEF,eAAe,MAAf,CAAsB,IAAtB,EAAkC,KAAlC,EAAsD;AAC3D;AACA,OAAK,MAAM,OAAX,IAAsB,MAAM,uBAAgB,GAAhB,CAAoB,KAApB,EAA2B,EAAE,IAAI,yBAAS,EAAT,CAAjC,CAA5B,EAA4E;AAC1E,IAAA,IAAI,CAAC,MAAL,CAAY,OAAZ;AACD;;AAED,EAAA,IAAI,CAAC,MAAL,CAAY,iBAAiB,CAAC,OAA9B;AACA,SAAO,IAAI,CAAC,MAAL,CAAY,QAAZ,CAAP;AACD,C", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { Arch, log } from \"builder-util\"\nimport { copyFile } from \"builder-util/out/fs\"\nimport { orNullIfFileNotExist } from \"builder-util/out/promise\"\nimport { Hash } from \"crypto\"\nimport { ensureDir, readFile, readJson, writeJson } from \"fs-extra\"\nimport * as path from \"path\"\n\nexport interface BuildCacheInfo {\n  executableDigest: string\n}\n\nexport class BuildCacheManager {\n  static VERSION: string = \"0\"\n\n  readonly cacheDir: string\n  readonly cacheInfoFile: string\n  readonly cacheFile: string\n\n  cacheInfo: BuildCacheInfo | null = null\n\n  private newDigest: string | null = null\n\n  constructor(outDir: string, private readonly executableFile: string, arch: Arch) {\n    this.cacheDir = path.join(outDir, \".cache\", Arch[arch])\n    this.cacheFile = path.join(this.cacheDir, \"app.exe\")\n    this.cacheInfoFile = path.join(this.cacheDir, \"info.json\")\n  }\n\n  async copyIfValid(digest: string): Promise<boolean> {\n    this.newDigest = digest\n\n    this.cacheInfo = await orNullIfFileNotExist(readJson(this.cacheInfoFile))\n    const oldDigest = this.cacheInfo == null ? null : this.cacheInfo.executableDigest\n    if (oldDigest !== digest) {\n      log.debug({oldDigest, newDigest: digest}, \"no valid cached executable found\")\n      return false\n    }\n\n    log.debug({cacheFile: this.cacheFile, file: this.executableFile}, `copying cached executable`)\n    try {\n      await copyFile(this.cacheFile, this.executableFile, false)\n      return true\n    }\n    catch (e) {\n      if (e.code === \"ENOENT\" || e.code === \"ENOTDIR\") {\n        log.debug({error: e.code}, \"copy cached executable failed\")\n      }\n      else {\n        log.warn({error: e.stack || e}, `cannot copy cached executable`)\n      }\n    }\n    return false\n  }\n\n  async save() {\n    if (this.newDigest == null) {\n      throw new Error(\"call copyIfValid before\")\n    }\n\n    if (this.cacheInfo == null) {\n      this.cacheInfo = {executableDigest: this.newDigest}\n    }\n    else {\n      this.cacheInfo.executableDigest = this.newDigest\n    }\n\n    try {\n      await ensureDir(this.cacheDir)\n      await Promise.all([writeJson(this.cacheInfoFile, this.cacheInfo), copyFile(this.executableFile, this.cacheFile, false)])\n    }\n    catch (e) {\n      log.warn({error: e.stack || e}, `cannot save build cache`)\n    }\n  }\n}\n\nexport async function digest(hash: Hash, files: Array<string>) {\n  // do not use pipe - better do bulk file read (https://github.com/yarnpkg/yarn/commit/7a63e0d23c46a4564bc06645caf8a59690f04d01)\n  for (const content of await BluebirdPromise.map(files, it => readFile(it))) {\n    hash.update(content)\n  }\n\n  hash.update(BuildCacheManager.VERSION)\n  return hash.digest(\"base64\")\n}"], "sourceRoot": ""}