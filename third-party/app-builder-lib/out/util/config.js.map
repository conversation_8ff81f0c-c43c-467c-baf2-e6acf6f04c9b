{"version": 3, "sources": ["../../src/util/config.ts"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAGA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;AACA;AACA,MAAM,cAAc,GAAG,OAAO,CAAC,uBAAD,CAA9B,C,CAIA;;;AACA,SAAS,YAAT,CAAsB,MAAtB,EAA6C,iBAA7C,EAA6E;AAC3E;AACA,QAAM,OAAO,GAAG,KAAK,CAAC,OAAN,CAAc,MAAM,CAAC,OAArB,IAAgC,iBAAiB,CAAC,OAAlD,GAA4D,IAA5E;;AACA,MAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,WAAQ,iBAAyB,CAAC,OAAlC;AACD;;AAED,iCAAW,MAAX,EAAmB,iBAAnB;;AAEA,MAAI,OAAO,IAAI,IAAf,EAAqB;AACnB;AACD;;AAED,QAAM,UAAU,GAAG,MAAM,CAAC,OAA1B;;AACA,MAAI,UAAU,CAAC,MAAX,KAAsB,CAA1B,EAA6B;AAC3B,IAAA,MAAM,CAAC,OAAP,GAAiB,OAAjB;AACD,GAFD,MAGK;AACH;AACA,IAAA,MAAM,CAAC,MAAP,CAAc,UAAU,CAAC,CAAD,CAAxB,EAA6B,OAA7B;AACD;AACF;;AAEM,eAAe,SAAf,CAAyB,UAAzB,EACyB,UADzB,EAEyB,iBAFzB,EAGyB,eAAA,GAAuD,KAAI,eAAJ,EAAS,MAAM,4CAAqB,yBAAS,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,cAAtB,CAAT,CAArB,CAAf,CAHhF,EAGqK;AAC1K,QAAM,aAAa,GAAsB;AAAC,IAAA,UAAU,EAAE,OAAb;AAAsB,IAAA,cAAc,EAAE,kBAAtC;AAA0D,IAAA,UAA1D;AAAsE,IAAA;AAAtE,GAAzC;AACA,QAAM,sBAAsB,GAAG,MAAM,iCAA0B,aAA1B,EAAyC,UAAzC,CAArC;AACA,QAAM,MAAM,GAAG,sBAAsB,IAAI,IAA1B,GAAiC,EAAjC,GAAsC,sBAAsB,CAAC,MAA5E;;AACA,MAAI,iBAAiB,IAAI,IAAzB,EAA+B;AAC7B,IAAA,YAAY,CAAC,MAAD,EAAS,iBAAT,CAAZ;AACD;;AAED,MAAI,sBAAsB,IAAI,IAA9B,EAAoC;AAClC,uBAAI,IAAJ,CAAS;AAAC,MAAA,IAAI,EAAE,sBAAsB,CAAC,UAAvB,IAAqC,IAArC,GAA4C,8BAA5C,GAA6E,sBAAsB,CAAC;AAA3G,KAAT,EAAiI,sBAAjI;AACD;;AAED,MAAI,MAAM,CAAC,OAAP,IAAkB,IAAlB,IAA0B,MAAM,CAAC,OAAP,KAAmB,IAAjD,EAAuD;AACrD,UAAM,QAAQ,GAAG,OAAM,eAAe,CAAC,KAAtB,KAA+B,EAAhD;AACA,UAAM,eAAe,GAAG,QAAQ,CAAC,eAAjC;AACA,UAAM,YAAY,GAAG,QAAQ,CAAC,YAA9B;;AACA,QAAK,YAAY,IAAI,IAAhB,IAAwB,mBAAmB,YAA5C,IAA8D,eAAe,IAAI,IAAnB,IAA2B,mBAAmB,eAAhH,EAAkI;AAChI,MAAA,MAAM,CAAC,OAAP,GAAiB,WAAjB;AACD,KAFD,MAGK,IAAI,eAAe,IAAI,IAAnB,IAA2B,sBAAsB,eAArD,EAAsE;AACzE,UAAI,IAAI,GAAG,0CAAX;;AACA,UAAI;AACF,QAAA,IAAI,GAAG,OAAO,CAAC,OAAR,CAAgB,IAAhB,CAAP;AACD,OAFD,CAGA,OAAO,MAAP,EAAe;AACb,QAAA,IAAI,GAAG,OAAO,CAAC,OAAR,CAAgB,uCAAhB,CAAP;AACD;;AACD,MAAA,MAAM,CAAC,OAAP,GAAiB,QAAQ,IAAI,EAA7B;AACD;AACF;;AAED,MAAI,YAAJ;;AACA,MAAI,MAAM,CAAC,OAAP,KAAmB,WAAvB,EAAoC;AAClC,IAAA,YAAY,GAAG,MAAM,yBAAS,UAAT,CAArB;;AACA,uBAAI,IAAJ,CAAS;AAAC,MAAA,MAAM,EAAE,MAAM,CAAC;AAAhB,KAAT,EAAmC,6BAAnC;AACD,GAHD,MAIK,IAAI,MAAM,CAAC,OAAP,IAAkB,IAAtB,EAA4B;AAC/B,UAAM,4BAA4B,GAAG,MAAM,wCAAgC,aAAhC,EAA+C,MAAM,CAAC,OAAtD,CAA3C;;AACA,uBAAI,IAAJ,CAAS;AAAC,MAAA,IAAI,EAAE,4BAA4B,CAAC;AAApC,KAAT,EAA0D,6BAA1D;;AACA,IAAA,YAAY,GAAG,4BAA4B,CAAC,MAA5C;AACD,GAJI,MAKA;AACH,IAAA,YAAY,GAAG,IAAf;AACD;;AAED,SAAO,cAAc,CAAC,MAAD,EAAS,YAAT,CAArB;AACD,C,CAED;;;AACA,SAAS,cAAT,CAAwB,aAAxB,EAAsD,IAAtD,EAAqG;AACnG,MAAI,KAAK,GAAG,aAAa,CAAC,IAAD,CAAzB;;AACA,MAAI,KAAK,IAAI,IAAb,EAAmB;AACjB;AACD;;AAED,MAAI,CAAC,KAAK,CAAC,OAAN,CAAc,KAAd,CAAL,EAA2B;AACzB,IAAA,KAAK,GAAG,CAAC,KAAD,CAAR;AACD;;AAED,EAAA,QAAQ,EAAE,KAAK,IAAI,CAAC,GAAG,CAAb,EAAgB,CAAC,GAAG,KAAK,CAAC,MAA1B,EAAkC,CAAC,EAAnC,EAAuC;AAC/C,QAAI,IAAI,GAAG,KAAK,CAAC,CAAD,CAAhB;;AACA,QAAI,OAAO,IAAP,KAAgB,QAApB,EAA8B;AAC5B;AACA,UAAI,CAAC,KAAK,CAAV,EAAa;AACX,YAAI,aAAa,GAAG,CAAC,GAAG,CAAxB;AACA,YAAI,QAAJ;;AACA,WAAG;AACD,UAAA,QAAQ,GAAG,KAAK,CAAC,aAAa,EAAd,CAAhB;AACD,SAFD,QAES,QAAQ,IAAI,IAFrB;;AAIA,YAAI,QAAQ,CAAC,IAAT,IAAiB,IAAjB,IAAyB,QAAQ,CAAC,EAAT,IAAe,IAA5C,EAAkD;AAChD,cAAI,QAAQ,CAAC,MAAT,IAAmB,IAAvB,EAA6B;AAC3B,YAAA,QAAQ,CAAC,MAAT,GAAkB,CAAC,IAAD,CAAlB;AACD,WAFD,MAGK;AACF,YAAA,QAAQ,CAAC,MAAT,CAAkC,IAAlC,CAAuC,IAAvC;AACF;;AACD,UAAA,KAAK,CAAC,CAAD,CAAL,GAAW,IAAX;AACA,mBAAS,QAAT;AACD;AACF;;AAED,MAAA,IAAI,GAAG;AACL,QAAA,MAAM,EAAE,CAAC,IAAD;AADH,OAAP;AAGA,MAAA,KAAK,CAAC,CAAD,CAAL,GAAW,IAAX;AACD,KAzBD,MA0BK,IAAI,KAAK,CAAC,OAAN,CAAc,IAAd,CAAJ,EAAyB;AAC5B,YAAM,IAAI,KAAJ,CAAU,GAAG,IAAI,kEAAkE,CAAC,IAA1E,GAAiF,IAA3F,CAAN;AACD,KA9B8C,CAgC/C;;;AACA,QAAI,IAAI,CAAC,IAAL,KAAc,GAAlB,EAAuB;AACrB,MAAA,IAAI,CAAC,IAAL,GAAY,SAAZ;AACD;;AAED,QAAI,IAAI,CAAC,EAAL,KAAY,GAAhB,EAAqB;AACnB,MAAA,IAAI,CAAC,EAAL,GAAU,SAAV;AACD;;AAED,QAAI,IAAI,CAAC,MAAL,IAAe,IAAf,IAAuB,OAAO,IAAI,CAAC,MAAZ,KAAuB,QAAlD,EAA4D;AAC1D,MAAA,IAAI,CAAC,MAAL,GAAc,CAAC,IAAI,CAAC,MAAN,CAAd;AACD;AACF;;AAED,EAAA,aAAa,CAAC,IAAD,CAAb,GAAsB,KAAK,CAAC,MAAN,CAAa,EAAE,IAAI,EAAE,IAAI,IAAzB,CAAtB;AACD;;AAED,SAAS,UAAT,CAAoB,aAApB,EAAkD,mBAAlD,EAAsF,mBAAtF,EAA0H,IAA1H,EAAyK;AACvK,QAAM,IAAI,GAAG,aAAa,CAAC,IAAD,CAA1B;AACA,QAAM,UAAU,GAAG,mBAAmB,CAAC,IAAD,CAAtC;;AACA,MAAI,IAAI,IAAI,IAAR,IAAgB,UAAU,IAAI,IAAlC,EAAwC;AACtC;AACD;;AAED,QAAM,MAAM,GAAG,IAAI,CAAC,KAAL,EAAf;AACA,EAAA,mBAAmB,CAAC,IAAD,CAAnB,GAA4B,MAA5B;;AAEA,EAAA,QAAQ,EAAE,KAAK,MAAM,IAAX,IAAmB,mBAAmB,CAAC,KAAvC,EAAgE;AACxE,SAAK,MAAM,YAAX,IAA2B,IAA3B,EAAiC;AAC/B,UAAI,YAAY,CAAC,IAAb,KAAsB,IAAI,CAAC,IAA3B,IAAmC,YAAY,CAAC,EAAb,KAAoB,IAAI,CAAC,EAAhE,EAAoE;AAClE,YAAI,IAAI,CAAC,MAAL,IAAe,IAAnB,EAAyB;AACvB,cAAI,YAAY,CAAC,MAAb,IAAuB,IAA3B,EAAiC;AAC/B,YAAA,YAAY,CAAC,MAAb,GAAsB,IAAI,CAAC,MAAL,CAAY,KAAZ,EAAtB;AACD,WAFD,MAGK;AACH,YAAA,YAAY,CAAC,MAAb,GAAuB,IAAI,CAAC,MAAL,CAA8B,MAA9B,CAAqC,YAAY,CAAC,MAAlD,CAAvB;AACD;AACF;;AAED,iBAAS,QAAT;AACD;AACF,KAduE,CAgBxE;;;AACA,IAAA,MAAM,CAAC,IAAP,CAAY,IAAZ;AACD;AACF;;AAEK,SAAU,cAAV,CAAyB,aAAzB,EAAuD,mBAAvD,EAAgG;AACpG,EAAA,cAAc,CAAC,aAAD,EAAgB,OAAhB,CAAd;AACA,EAAA,cAAc,CAAC,aAAD,EAAgB,YAAhB,CAAd;AACA,EAAA,cAAc,CAAC,aAAD,EAAgB,gBAAhB,CAAd;;AAEA,MAAI,mBAAmB,IAAI,IAA3B,EAAiC;AAC/B,WAAO,+BAAW,gBAAgB,EAA3B,EAA+B,aAA/B,CAAP;AACD;;AAED,EAAA,cAAc,CAAC,mBAAD,EAAsB,OAAtB,CAAd;AACA,EAAA,cAAc,CAAC,mBAAD,EAAsB,YAAtB,CAAd;AACA,EAAA,cAAc,CAAC,mBAAD,EAAsB,gBAAtB,CAAd;AAEA,QAAM,MAAM,GAAG,+BAAW,gBAAgB,EAA3B,EAA+B,mBAA/B,EAAoD,aAApD,CAAf;AACA,EAAA,UAAU,CAAC,aAAD,EAAgB,mBAAhB,EAAqC,MAArC,EAA6C,OAA7C,CAAV;AACA,SAAO,MAAP;AACD;;AAED,SAAS,gBAAT,GAAyB;AACvB,SAAO;AACL,IAAA,WAAW,EAAE;AACX,MAAA,MAAM,EAAE,MADG;AAEX,MAAA,cAAc,EAAE;AAFL;AADR,GAAP;AAMD;;AAED,MAAM,iBAAiB,GAAG,KAAI,eAAJ,EAAS,MAAM,yBAAS,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,IAArB,EAA2B,IAA3B,EAAiC,aAAjC,CAAT,CAAf,CAA1B;;AAEO,eAAe,cAAf,CAA8B,MAA9B,EAAqD,WAArD,EAA6E;AAClF,QAAM,aAAa,GAAG,MAAM,CAAC,aAA7B;;AACA,MAAI,aAAa,IAAI,IAArB,EAA2B;AACzB,QAAI,aAAa,CAAC,KAAd,IAAuB,IAA3B,EAAiC;AAC/B,YAAM,KAAI,wCAAJ,EAA8B,iDAA9B,CAAN;AACD;;AACD,QAAI,aAAa,CAAC,WAAd,IAA6B,IAAjC,EAAuC;AACrC,YAAM,KAAI,wCAAJ,EAA8B,mEAA9B,CAAN;AACD;AACF;;AAED,QAAM,SAAS,GAAQ,MAAvB;;AACA,MAAI,SAAS,CAAC,sBAAV,KAAqC,KAAzC,EAAgD;AAC9C,UAAM,KAAI,wCAAJ,EAA8B,+EAA9B,CAAN;AACD;;AACD,MAAI,SAAS,CAAC,QAAV,IAAsB,IAAtB,IAA8B,SAAS,CAAC,QAAV,CAAmB,iBAAnB,IAAwC,IAA1E,EAAgF;AAC9E,UAAM,KAAI,wCAAJ,EAA8B,4HAA9B,CAAN;AACD,GAjBiF,CAmBlF;;;AACA,EAAA,cAAc,CAAC,MAAM,iBAAiB,CAAC,KAAzB,EAAgC,MAAhC,EAAwC;AACpD,IAAA,IAAI,EAAE,oBAAA,SAAmC,EADW;AAEpD,IAAA,aAAa,EAAE,CAAC,cAAD,EAAyB,KAAzB,KAA+C;AAC5D,UAAI,WAAW,CAAC,SAAhB,EAA2B;AACzB,QAAA,WAAW,CAAC,GAAZ,CAAgB,eAAhB,EAAiC,sCAAkB,KAAlB,CAAjC;AACD;;AAED,YAAM,IAAI,GAAG,4BAAb;AACA,UAAI,GAAG,GAAG,GAAG,IAAI,8BAAjB;AACA,YAAM,OAAO,GAAG,IAAI,GAAJ,CAAQ,CAAC,KAAD,EAAQ,KAAR,EAAe,KAAf,EAAsB,KAAtB,EAA6B,KAA7B,EAAoC,MAApC,EAA4C,MAA5C,EAAoD,OAApD,EAA6D,UAA7D,EAAyE,MAAzE,CAAR,CAAhB;AACA,YAAM,QAAQ,GAAW,KAAK,CAAC,QAAN,IAAkB,IAAlB,GAAyB,IAAzB,GAAgC,KAAK,CAAC,QAA/D;AACA,YAAM,UAAU,GAAG,QAAQ,CAAC,UAAT,CAAoB,GAApB,IAA2B,QAAQ,CAAC,MAAT,CAAgB,CAAhB,EAAmB,WAAnB,EAA3B,GAA8D,IAAjF;;AACA,UAAI,UAAU,IAAI,IAAd,IAAsB,OAAO,CAAC,GAAR,CAAY,UAAZ,CAA1B,EAAmD;AACjD,QAAA,GAAG,GAAG,GAAG,IAAI,kBAAkB,UAAU,EAAzC;AACD;;AAED,aAAO,GAAG,cAAc;YAClB,GAAG;;;;CADT;AAMD;AAtBmD,GAAxC,CAAd;AAwBD;;AAED,MAAM,qBAAqB,GAAG,CAAC,KAAD,EAAQ,KAAR,CAA9B;;AAEO,eAAe,0BAAf,CAA0C,UAA1C,EAA8D,UAA9D,EAAmG;AACxG,MAAI,UAAU,IAAI,IAAlB,EAAwB;AACtB,UAAM,YAAY,GAAG,IAAI,CAAC,OAAL,CAAa,UAAb,EAAyB,UAAzB,CAArB;AACA,UAAM,IAAI,GAAG,MAAM,sBAAW,YAAX,CAAnB;;AACA,QAAI,IAAI,IAAI,IAAZ,EAAkB;AAChB,YAAM,KAAI,wCAAJ,EAA8B,yBAAyB,UAAU,gBAAjE,CAAN;AACD,KAFD,MAGK,IAAI,CAAC,IAAI,CAAC,WAAL,EAAL,EAAyB;AAC5B,YAAM,KAAI,wCAAJ,EAA8B,yBAAyB,UAAU,qBAAjE,CAAN;AACD,KAFI,MAGA,IAAI,UAAU,KAAK,YAAnB,EAAiC;AACpC,yBAAI,IAAJ,CAAS;AAAC,QAAA,YAAY,EAAE;AAAf,OAAT,EAAqC,4FAArC;AACD;;AACD,WAAO,YAAP;AACD;;AAED,OAAK,MAAM,GAAX,IAAkB,qBAAlB,EAAyC;AACvC,UAAM,YAAY,GAAG,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,GAAtB,CAArB;AACA,UAAM,WAAW,GAAG,IAAI,CAAC,IAAL,CAAU,YAAV,EAAwB,cAAxB,CAApB;AACA,UAAM,IAAI,GAAG,MAAM,sBAAW,WAAX,CAAnB;;AACA,QAAI,IAAI,IAAI,IAAR,IAAgB,IAAI,CAAC,MAAL,EAApB,EAAmC;AACjC,aAAO,YAAP;AACD;AACF;;AACD,SAAO,UAAP;AACD,C", "sourcesContent": ["import { Debug<PERSON>ogger, deepAssign, InvalidConfigurationError, log, safeStringify<PERSON>son } from \"builder-util\"\nimport { statOrNull } from \"builder-util/out/fs\"\nimport { readJson } from \"fs-extra\"\nimport { Lazy } from \"lazy-val\"\nimport * as path from \"path\"\nimport { getConfig as _getConfig, loadParentConfig, orNullIfFileNotExist, ReadConfigRequest } from \"read-config-file\"\nimport { FileSet } from \"..\"\nimport { Configuration } from \"../configuration\"\nimport { reactCra } from \"../presets/rectCra\"\n// eslint-disable-next-line @typescript-eslint/no-var-requires\nconst validateSchema = require(\"@develar/schema-utils\")\n\ndeclare const PACKAGE_VERSION: string\n\n// https://github.com/electron-userland/electron-builder/issues/1847\nfunction mergePublish(config: Configuration, configFromOptions: Configuration) {\n  // if config from disk doesn't have publish (or object), no need to handle, it will be simply merged by deepAssign\n  const publish = Array.isArray(config.publish) ? configFromOptions.publish : null\n  if (publish != null) {\n    delete (configFromOptions as any).publish\n  }\n\n  deepAssign(config, configFromOptions)\n\n  if (publish == null) {\n    return\n  }\n\n  const listOnDisk = config.publish as Array<any>\n  if (listOnDisk.length === 0) {\n    config.publish = publish\n  }\n  else {\n    // apply to first\n    Object.assign(listOnDisk[0], publish)\n  }\n}\n\nexport async function getConfig(projectDir: string,\n                                configPath: string | null,\n                                configFromOptions: Configuration | null | undefined,\n                                packageMetadata: Lazy<{ [key: string]: any } | null> = new Lazy(() => orNullIfFileNotExist(readJson(path.join(projectDir, \"package.json\"))))): Promise<Configuration> {\n  const configRequest: ReadConfigRequest = {packageKey: \"build\", configFilename: \"electron-builder\", projectDir, packageMetadata}\n  const configAndEffectiveFile = await _getConfig<Configuration>(configRequest, configPath)\n  const config = configAndEffectiveFile == null ? {} : configAndEffectiveFile.result\n  if (configFromOptions != null) {\n    mergePublish(config, configFromOptions)\n  }\n\n  if (configAndEffectiveFile != null) {\n    log.info({file: configAndEffectiveFile.configFile == null ? 'package.json (\"build\" field)' : configAndEffectiveFile.configFile}, \"loaded configuration\")\n  }\n\n  if (config.extends == null && config.extends !== null) {\n    const metadata = await packageMetadata.value || {}\n    const devDependencies = metadata.devDependencies\n    const dependencies = metadata.dependencies\n    if ((dependencies != null && \"react-scripts\" in dependencies) || (devDependencies != null && \"react-scripts\" in devDependencies)) {\n      config.extends = \"react-cra\"\n    }\n    else if (devDependencies != null && \"electron-webpack\" in devDependencies) {\n      let file = \"electron-webpack/out/electron-builder.js\"\n      try {\n        file = require.resolve(file)\n      }\n      catch (ignore) {\n        file = require.resolve(\"electron-webpack/electron-builder.yml\")\n      }\n      config.extends = `file:${file}`\n    }\n  }\n\n  let parentConfig: Configuration | null\n  if (config.extends === \"react-cra\") {\n    parentConfig = await reactCra(projectDir)\n    log.info({preset: config.extends}, \"loaded parent configuration\")\n  }\n  else if (config.extends != null) {\n    const parentConfigAndEffectiveFile = await loadParentConfig<Configuration>(configRequest, config.extends)\n    log.info({file: parentConfigAndEffectiveFile.configFile}, \"loaded parent configuration\")\n    parentConfig = parentConfigAndEffectiveFile.result\n  }\n  else {\n    parentConfig = null\n  }\n\n  return doMergeConfigs(config, parentConfig)\n}\n\n// normalize for easy merge\nfunction normalizeFiles(configuration: Configuration, name: \"files\" | \"extraFiles\" | \"extraResources\") {\n  let value = configuration[name]\n  if (value == null) {\n    return\n  }\n\n  if (!Array.isArray(value)) {\n    value = [value]\n  }\n\n  itemLoop: for (let i = 0; i < value.length; i++) {\n    let item = value[i]\n    if (typeof item === \"string\") {\n      // merge with previous if possible\n      if (i !== 0) {\n        let prevItemIndex = i - 1\n        let prevItem: FileSet\n        do {\n          prevItem = value[prevItemIndex--] as FileSet\n        } while (prevItem == null)\n\n        if (prevItem.from == null && prevItem.to == null) {\n          if (prevItem.filter == null) {\n            prevItem.filter = [item]\n          }\n          else {\n            (prevItem.filter as Array<string>).push(item)\n          }\n          value[i] = null as any\n          continue itemLoop\n        }\n      }\n\n      item = {\n        filter: [item],\n      }\n      value[i] = item\n    }\n    else if (Array.isArray(item)) {\n      throw new Error(`${name} configuration is invalid, nested array not expected for index ${i}: ` + item)\n    }\n\n    // make sure that merge logic is not complex - unify different presentations\n    if (item.from === \".\") {\n      item.from = undefined\n    }\n\n    if (item.to === \".\") {\n      item.to = undefined\n    }\n\n    if (item.filter != null && typeof item.filter === \"string\") {\n      item.filter = [item.filter]\n    }\n  }\n\n  configuration[name] = value.filter(it => it != null)\n}\n\nfunction mergeFiles(configuration: Configuration, parentConfiguration: Configuration, mergedConfiguration: Configuration, name: \"files\" | \"extraFiles\" | \"extraResources\") {\n  const list = configuration[name] as Array<FileSet> | null\n  const parentList = parentConfiguration[name] as Array<FileSet> | null\n  if (list == null || parentList == null) {\n    return\n  }\n\n  const result = list.slice()\n  mergedConfiguration[name] = result\n\n  itemLoop: for (const item of parentConfiguration.files as Array<FileSet>) {\n    for (const existingItem of list) {\n      if (existingItem.from === item.from && existingItem.to === item.to) {\n        if (item.filter != null) {\n          if (existingItem.filter == null) {\n            existingItem.filter = item.filter.slice()\n          }\n          else {\n            existingItem.filter = (item.filter as Array<string>).concat(existingItem.filter)\n          }\n        }\n\n        continue itemLoop\n      }\n    }\n\n    // existing item not found, simply add\n    result.push(item)\n  }\n}\n\nexport function doMergeConfigs(configuration: Configuration, parentConfiguration: Configuration | null) {\n  normalizeFiles(configuration, \"files\")\n  normalizeFiles(configuration, \"extraFiles\")\n  normalizeFiles(configuration, \"extraResources\")\n\n  if (parentConfiguration == null) {\n    return deepAssign(getDefaultConfig(), configuration)\n  }\n\n  normalizeFiles(parentConfiguration, \"files\")\n  normalizeFiles(parentConfiguration, \"extraFiles\")\n  normalizeFiles(parentConfiguration, \"extraResources\")\n\n  const result = deepAssign(getDefaultConfig(), parentConfiguration, configuration)\n  mergeFiles(configuration, parentConfiguration, result, \"files\")\n  return result\n}\n\nfunction getDefaultConfig(): Configuration {\n  return {\n    directories: {\n      output: \"dist\",\n      buildResources: \"build\",\n    },\n  }\n}\n\nconst schemeDataPromise = new Lazy(() => readJson(path.join(__dirname, \"..\", \"..\", \"scheme.json\")))\n\nexport async function validateConfig(config: Configuration, debugLogger: DebugLogger) {\n  const extraMetadata = config.extraMetadata\n  if (extraMetadata != null) {\n    if (extraMetadata.build != null) {\n      throw new InvalidConfigurationError(`--em.build is deprecated, please specify as -c\"`)\n    }\n    if (extraMetadata.directories != null) {\n      throw new InvalidConfigurationError(`--em.directories is deprecated, please specify as -c.directories\"`)\n    }\n  }\n\n  const oldConfig: any = config\n  if (oldConfig.npmSkipBuildFromSource === false) {\n    throw new InvalidConfigurationError(`npmSkipBuildFromSource is deprecated, please use buildDependenciesFromSource\"`)\n  }\n  if (oldConfig.appImage != null && oldConfig.appImage.systemIntegration != null) {\n    throw new InvalidConfigurationError(`appImage.systemIntegration is deprecated, https://github.com/TheAssassin/AppImageLauncher is used for desktop integration\"`)\n  }\n\n  // noinspection JSUnusedGlobalSymbols\n  validateSchema(await schemeDataPromise.value, config, {\n    name: `electron-builder ${PACKAGE_VERSION}`,\n    postFormatter: (formattedError: string, error: any): string => {\n      if (debugLogger.isEnabled) {\n        debugLogger.add(\"invalidConfig\", safeStringifyJson(error))\n      }\n\n      const site = \"https://www.electron.build\"\n      let url = `${site}/configuration/configuration`\n      const targets = new Set([\"mac\", \"dmg\", \"pkg\", \"mas\", \"win\", \"nsis\", \"appx\", \"linux\", \"appimage\", \"snap\"])\n      const dataPath: string = error.dataPath == null ? null : error.dataPath\n      const targetPath = dataPath.startsWith(\".\") ? dataPath.substr(1).toLowerCase() : null\n      if (targetPath != null && targets.has(targetPath)) {\n        url = `${site}/configuration/${targetPath}`\n      }\n\n      return `${formattedError}\\n  How to fix:\n  1. Open ${url}\n  2. Search the option name on the page (or type in into Search to find across the docs).\n    * Not found? The option was deprecated or not exists (check spelling).\n    * Found? Check that the option in the appropriate place. e.g. \"title\" only in the \"dmg\", not in the root.\n`\n    },\n  })\n}\n\nconst DEFAULT_APP_DIR_NAMES = [\"app\", \"www\"]\n\nexport async function computeDefaultAppDirectory(projectDir: string, userAppDir: string | null | undefined): Promise<string> {\n  if (userAppDir != null) {\n    const absolutePath = path.resolve(projectDir, userAppDir)\n    const stat = await statOrNull(absolutePath)\n    if (stat == null) {\n      throw new InvalidConfigurationError(`Application directory ${userAppDir} doesn't exist`)\n    }\n    else if (!stat.isDirectory()) {\n      throw new InvalidConfigurationError(`Application directory ${userAppDir} is not a directory`)\n    }\n    else if (projectDir === absolutePath) {\n      log.warn({appDirectory: userAppDir}, `Specified application directory equals to project dir — superfluous or wrong configuration`)\n    }\n    return absolutePath\n  }\n\n  for (const dir of DEFAULT_APP_DIR_NAMES) {\n    const absolutePath = path.join(projectDir, dir)\n    const packageJson = path.join(absolutePath, \"package.json\")\n    const stat = await statOrNull(packageJson)\n    if (stat != null && stat.isFile()) {\n      return absolutePath\n    }\n  }\n  return projectDir\n}\n"], "sourceRoot": ""}