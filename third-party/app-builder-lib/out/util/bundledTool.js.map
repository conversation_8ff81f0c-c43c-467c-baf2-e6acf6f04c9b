{"version": 3, "sources": ["../../src/util/bundledTool.ts"], "names": [], "mappings": ";;;;;;;;AAKM,SAAU,UAAV,CAAqB,QAArB,EAA0D,SAA1D,EAAkF;AACtF,QAAM,cAAc,GAAG,QAAQ,GAAG,QAAQ,CAAC,KAAT,CAAe,GAAf,CAAH,GAAyB,EAAxD;AACA,SAAO,SAAS,CAAC,MAAV,CAAiB,cAAjB,EAAiC,MAAjC,CAAwC,EAAE,IAAI,EAAE,CAAC,MAAH,GAAY,CAA1D,EAA6D,IAA7D,CAAkE,GAAlE,CAAP;AACD;;AAEK,SAAU,cAAV,CAAyB,OAAzB,EAA+C;AACnD;AACA,SAAO,EACL,GAAG,OAAO,CAAC,GADN;AAEL,IAAA,iBAAiB,EAAE,UAAU,CAAC,OAAO,CAAC,GAAR,CAAY,iBAAb,EAAgC,OAAhC;AAFxB,GAAP;AAID,C", "sourcesContent": ["export interface ToolInfo {\n  path: string\n  env?: any\n}\n\nexport function computeEnv(oldValue: string | null | undefined, newValues: Array<string>): string {\n  const parsedOldValue = oldValue ? oldValue.split(\":\") : []\n  return newValues.concat(parsedOldValue).filter(it => it.length > 0).join(\":\")\n}\n\nexport function computeToolEnv(libPath: Array<string>): any {\n  // noinspection SpellCheckingInspection\n  return {\n    ...process.env,\n    DYLD_LIBRARY_PATH: computeEnv(process.env.DYLD_LIBRARY_PATH, libPath)\n  }\n}"], "sourceRoot": ""}