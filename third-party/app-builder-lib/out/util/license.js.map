{"version": 3, "sources": ["../../src/util/license.ts"], "names": [], "mappings": ";;;;;;;;;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;AAGM,SAAU,gBAAV,CAA2B,SAA3B,EAAqD,QAArD,EAAoF;AACxF,SAAO,SAAS,CAAC,IAAV,CAAe,CAAC,CAAD,EAAI,CAAJ,KAAS;AAC7B,UAAM,EAAE,GAAG,CAAC,CAAC,QAAF,CAAW,KAAX,IAAoB,CAApB,GAAwB,GAAnC;AACA,UAAM,EAAE,GAAG,CAAC,CAAC,QAAF,CAAW,KAAX,IAAoB,CAApB,GAAwB,GAAnC;AACA,WAAO,EAAE,KAAK,EAAP,GAAY,CAAC,CAAC,aAAF,CAAgB,CAAhB,CAAZ,GAAiC,EAAE,GAAG,EAA7C;AACD,GAJM,EAKJ,GALI,CAKA,IAAI,IAAG;AACV,QAAI,IAAI,GAAG,IAAI,CAAC,KAAL,CAAW,YAAX,EAA0B,CAA1B,CAAX;AACA,QAAI,cAAJ;;AACA,QAAI,IAAI,CAAC,QAAL,CAAc,GAAd,CAAJ,EAAwB;AACtB,MAAA,cAAc,GAAG,IAAjB;AACA,MAAA,IAAI,GAAG,cAAc,CAAC,SAAf,CAAyB,CAAzB,EAA4B,IAAI,CAAC,OAAL,CAAa,GAAb,CAA5B,CAAP;AACD,KAHD,MAIK;AACH,MAAA,IAAI,GAAG,IAAI,CAAC,WAAL,EAAP;AACA,MAAA,cAAc,GAAG,+BAAiB,IAAjB,CAAjB;AACD;;AACD,WAAO;AAAC,MAAA,IAAI,EAAE,IAAI,CAAC,IAAL,CAAU,QAAQ,CAAC,iBAAnB,EAAsC,IAAtC,CAAP;AAAoD,MAAA,IAApD;AAA0D,MAAA,cAA1D;AAA0E,MAAA,QAAQ,EAAG,sBAAqB,IAArB;AAArF,KAAP;AACD,GAjBI,CAAP;AAkBD;;AAEM,eAAe,0BAAf,CAA0C,MAA1C,EAA6E,QAA7E,EAA8G,kBAAA,GAAoC,CAAC,KAAD,EAAQ,KAAR,EAAe,MAAf,CAAlJ,EAAwK;AAC7K,QAAM,aAAa,GAAkB,EAArC;;AACA,OAAK,MAAM,IAAX,IAAmB,CAAC,SAAD,EAAY,MAAZ,CAAnB,EAAwC;AACtC,SAAK,MAAM,GAAX,IAAkB,kBAAlB,EAAsC;AACpC,MAAA,aAAa,CAAC,IAAd,CAAmB,GAAG,IAAI,IAAI,GAAG,EAAjC;AACA,MAAA,aAAa,CAAC,IAAd,CAAmB,GAAG,IAAI,CAAC,WAAL,EAAkB,IAAI,GAAG,EAA/C;AACA,MAAA,aAAa,CAAC,IAAd,CAAmB,GAAG,IAAI,IAAI,GAAG,CAAC,WAAJ,EAAiB,EAA/C;AACA,MAAA,aAAa,CAAC,IAAd,CAAmB,GAAG,IAAI,CAAC,WAAL,EAAkB,IAAI,GAAG,CAAC,WAAJ,EAAiB,EAA7D;AACD;AACF;;AAED,SAAO,MAAM,QAAQ,CAAC,WAAT,CAAqB,MAArB,EAA6B,GAAG,aAAhC,CAAb;AACD;;AAEM,eAAe,eAAf,CAA+B,QAA/B,EAA8D;AACnE,SAAO,gBAAgB,CAAC,CAAC,MAAM,QAAQ,CAAC,YAAhB,EACrB,MADqB,CACd,EAAE,IAAG;AACX,UAAM,IAAI,GAAG,EAAE,CAAC,WAAH,EAAb;AACA,WAAO,CAAC,IAAI,CAAC,UAAL,CAAgB,UAAhB,KAA+B,IAAI,CAAC,UAAL,CAAgB,OAAhB,CAAhC,MAA8D,IAAI,CAAC,QAAL,CAAc,MAAd,KAAyB,IAAI,CAAC,QAAL,CAAc,MAAd,CAAvF,CAAP;AACD,GAJqB,CAAD,EAIjB,QAJiB,CAAvB;AAKD,C", "sourcesContent": ["import * as path from \"path\"\nimport { langIdToName, toLangWithRegion } from \"./langs\"\nimport { PlatformPackager } from \"../platformPackager\"\n\nexport function getLicenseAssets(fileNames: Array<string>, packager: PlatformPackager<any>) {\n  return fileNames.sort((a, b) => {\n    const aW = a.includes(\"_en\") ? 0 : 100\n    const bW = b.includes(\"_en\") ? 0 : 100\n    return aW === bW ? a.localeCompare(b) : aW - bW\n  })\n    .map(file => {\n      let lang = file.match(/_([^.]+)\\./)![1]\n      let langWithRegion\n      if (lang.includes(\"_\")) {\n        langWithRegion = lang\n        lang = langWithRegion.substring(0, lang.indexOf(\"_\"))\n      }\n      else {\n        lang = lang.toLowerCase()\n        langWithRegion = toLangWithRegion(lang)\n      }\n      return {file: path.join(packager.buildResourcesDir, file), lang, langWithRegion, langName: (langIdToName as any)[lang]}\n    })\n}\n\nexport async function getNotLocalizedLicenseFile(custom: string | null | undefined, packager: PlatformPackager<any>, supportedExtension: Array<string> = [\"rtf\", \"txt\", \"html\"]): Promise<string | null> {\n  const possibleFiles: Array<string> = []\n  for (const name of [\"license\", \"eula\"]) {\n    for (const ext of supportedExtension) {\n      possibleFiles.push(`${name}.${ext}`)\n      possibleFiles.push(`${name.toUpperCase()}.${ext}`)\n      possibleFiles.push(`${name}.${ext.toUpperCase()}`)\n      possibleFiles.push(`${name.toUpperCase()}.${ext.toUpperCase()}`)\n    }\n  }\n\n  return await packager.getResource(custom, ...possibleFiles)\n}\n\nexport async function getLicenseFiles(packager: PlatformPackager<any>): Promise<Array<LicenseFile>> {\n  return getLicenseAssets((await packager.resourceList)\n    .filter(it => {\n      const name = it.toLowerCase()\n      return (name.startsWith(\"license_\") || name.startsWith(\"eula_\")) && (name.endsWith(\".rtf\") || name.endsWith(\".txt\"))\n    }), packager)\n}\n\nexport interface LicenseFile {\n  file: string\n  lang: string\n  langWithRegion: string\n  langName: string\n}"], "sourceRoot": ""}