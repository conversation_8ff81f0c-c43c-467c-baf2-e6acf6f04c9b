{"version": 3, "sources": ["../../src/util/pathManager.ts"], "names": [], "mappings": ";;;;;;;;AAAA;;;;;;AAEA,MAAM,IAAI,GAAG,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,IAArB,EAA2B,IAA3B,CAAb;;AAEM,SAAU,eAAV,CAA0B,IAA1B,EAAsC;AAC1C,SAAO,IAAI,CAAC,IAAL,CAAU,IAAV,EAAgB,WAAhB,EAA6B,IAA7B,CAAP;AACD;;AAEK,SAAU,aAAV,CAAwB,IAAxB,EAAqC;AACzC,SAAO,IAAI,IAAI,IAAR,GAAe,IAAI,CAAC,IAAL,CAAU,IAAV,EAAgB,QAAhB,CAAf,GAA2C,IAAI,CAAC,IAAL,CAAU,IAAV,EAAgB,QAAhB,EAA0B,IAA1B,CAAlD;AACD,C", "sourcesContent": ["import * as path from \"path\"\n\nconst root = path.join(__dirname, \"..\", \"..\")\n\nexport function getTemplatePath(file: string) {\n  return path.join(root, \"templates\", file)\n}\n\nexport function getVendorPath(file?: string) {\n  return file == null ? path.join(root, \"vendor\") : path.join(root, \"vendor\", file)\n}"], "sourceRoot": ""}