{"version": 3, "sources": ["../../src/util/AppFileWalker.ts"], "names": [], "mappings": ";;;;;;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAGA;;;;;;AAEA,MAAM,gCAAgC,GAAG,GAAG,IAAI,CAAC,GAAG,cAApD;;AAEA,SAAS,mBAAT,CAA6B,OAA7B,EAAiD;AAC/C,MAAI,CAAC,OAAO,CAAC,uBAAT,KAAqC,OAAO,CAAC,OAAR,MAAqB,OAAO,CAAC,kBAAR,EAA1D,CAAJ,EAA6F;AAC3F,IAAA,OAAO,CAAC,cAAR,CAAuB,MAAvB;AACD;;AACD,SAAO,OAAP;AACD;;AAEK,MAAgB,cAAhB,CAA8B;AAGlC,EAAA,WAAA,CAAyC,OAAzC,EAAwE,MAAxE,EAAkH,QAAlH,EAAoI;AAA3F,SAAA,OAAA,GAAA,OAAA;AAA+B,SAAA,MAAA,GAAA,MAAA;AAA0C,SAAA,QAAA,GAAA,QAAA;AAFzG,SAAA,QAAA,GAAW,IAAI,GAAJ,EAAX;AAGR;;AAES,EAAA,UAAU,CAAC,IAAD,EAAe,MAAf,EAA+B,QAA/B,EAA8C;AAChE,QAAI,CAAC,QAAQ,CAAC,cAAT,EAAL,EAAgC;AAC9B,aAAO,IAAP;AACD;;AAED,WAAO,yBAAS,IAAT,EACJ,IADI,CACE,UAAD,IAAoB;AACxB;AACA,aAAO,KAAK,aAAL,CAAmB,QAAnB,EAA6B,IAA7B,EAAmC,MAAnC,EAA2C,UAA3C,CAAP;AACD,KAJI,CAAP;AAKD;;AAEO,EAAA,aAAa,CAAC,QAAD,EAAkB,IAAlB,EAAgC,MAAhC,EAAgD,UAAhD,EAAkE;AACrF,UAAM,kBAAkB,GAAG,IAAI,CAAC,OAAL,CAAa,MAAb,EAAqB,UAArB,CAA3B;AACA,UAAM,IAAI,GAAG,IAAI,CAAC,QAAL,CAAc,KAAK,OAAL,CAAa,IAA3B,EAAiC,kBAAjC,CAAb;;AACA,QAAI,IAAI,CAAC,UAAL,CAAgB,IAAhB,CAAJ,EAA2B;AACzB;AACA,aAAO,qBAAK,kBAAL,EACJ,IADI,CACC,cAAc,IAAG;AACrB,aAAK,QAAL,CAAc,GAAd,CAAkB,IAAlB,EAAwB,cAAxB;AACA,eAAO,cAAP;AACD,OAJI,CAAP;AAKD,KAPD,MAQK;AACH,YAAM,CAAC,GAAI,QAAX;AACA,MAAA,CAAC,CAAC,YAAF,GAAiB,IAAjB;AACA,MAAA,CAAC,CAAC,kBAAF,GAAuB,IAAI,CAAC,QAAL,CAAc,MAAd,EAAsB,kBAAtB,CAAvB;AACD;;AACD,WAAO,IAAP;AACD;;AAnCiC;;;;AAsCpC,SAAS,eAAT,CAAyB,OAAzB,EAA+C,QAA/C,EAAiE;AAC/D,MAAI,QAAQ,CAAC,+BAAb,EAA8C;AAC5C,WAAO,OAAO,CAAC,OAAR,KAAoB,IAApB,GAA2B,OAAO,CAAC,YAAR,EAAlC;AACD;;AAED,QAAM,iBAAiB,GAAW,CAAC,IAAD,EAAO,QAAP,KAAmB;AACnD,WAAO,EAAE,QAAQ,CAAC,WAAT,MAA0B,IAAI,CAAC,QAAL,CAAc,gCAAd,CAA5B,CAAP;AACD,GAFD;;AAIA,MAAI,OAAO,CAAC,OAAR,EAAJ,EAAuB;AACrB,WAAO,iBAAP;AACD;;AAED,QAAM,MAAM,GAAG,OAAO,CAAC,YAAR,EAAf;AACA,SAAO,CAAC,IAAD,EAAO,QAAP,KAAmB;AACxB,QAAI,CAAC,iBAAiB,CAAC,IAAD,EAAO,QAAP,CAAtB,EAAwC;AACtC,aAAO,KAAP;AACD;;AACD,WAAO,MAAM,CAAC,IAAD,EAAO,QAAP,CAAb;AACD,GALD;AAMD;AAED;;;AACM,MAAO,aAAP,SAA6B,cAA7B,CAA2C;AAC/C,EAAA,WAAA,CAAY,OAAZ,EAAkC,QAAlC,EAAoD;AAClD,UAAM,mBAAmB,CAAC,OAAD,CAAzB,EAAoC,eAAe,CAAC,OAAD,EAAU,QAAV,CAAnD,EAAwE,QAAxE;AACD,GAH8C,CAK/C;AACA;;;AACA,EAAA,OAAO,CAAC,IAAD,EAAe,QAAf,EAAgC,MAAhC,EAAgD,YAAhD,EAA2E;AAChF,QAAI,QAAQ,CAAC,WAAT,EAAJ,EAA4B;AAC1B;AACA;AACA;AACA,UAAI,IAAI,CAAC,QAAL,CAAc,gCAAd,CAAJ,EAAqD;AACnD,eAAO,KAAP;AACD;AACF,KAPD,MAQK;AACH;AACA,WAAK,QAAL,CAAc,GAAd,CAAkB,IAAlB,EAAwB,QAAxB;AACD;;AAED,WAAO,KAAK,UAAL,CAAgB,IAAhB,EAAsB,MAAtB,EAA8B,QAA9B,CAAP;AACD;;AAtB8C,C", "sourcesContent": ["import { Filter, FileConsumer } from \"builder-util/out/fs\"\nimport { readlink, stat, Stats } from \"fs-extra\"\nimport { FileMatcher } from \"../fileMatcher\"\nimport { Packager } from \"../packager\"\nimport * as path from \"path\"\n\nconst nodeModulesSystemDependentSuffix = `${path.sep}node_modules`\n\nfunction addAllPatternIfNeed(matcher: FileMatcher) {\n  if (!matcher.isSpecifiedAsEmptyArray && (matcher.isEmpty() || matcher.containsOnlyIgnore())) {\n    matcher.prependPattern(\"**/*\")\n  }\n  return matcher\n}\n\nexport abstract class FileCopyHelper {\n  readonly metadata = new Map<string, Stats>()\n\n  protected constructor(protected readonly matcher: FileMatcher, readonly filter: Filter | null, protected readonly packager: Packager) {\n  }\n\n  protected handleFile(file: string, parent: string, fileStat: Stats): Promise<Stats | null> | null {\n    if (!fileStat.isSymbolicLink()) {\n      return null\n    }\n\n    return readlink(file)\n      .then((linkTarget): any => {\n        // http://unix.stackexchange.com/questions/105637/is-symlinks-target-relative-to-the-destinations-parent-directory-and-if-so-wh\n        return this.handleSymlink(fileStat, file, parent, linkTarget)\n      })\n  }\n\n  private handleSymlink(fileStat: Stats, file: string, parent: string, linkTarget: string): Promise<Stats> | null {\n    const resolvedLinkTarget = path.resolve(parent, linkTarget)\n    const link = path.relative(this.matcher.from, resolvedLinkTarget)\n    if (link.startsWith(\"..\")) {\n      // outside of project, linked module (https://github.com/electron-userland/electron-builder/issues/675)\n      return stat(resolvedLinkTarget)\n        .then(targetFileStat => {\n          this.metadata.set(file, targetFileStat)\n          return targetFileStat\n        })\n    }\n    else {\n      const s = (fileStat as any)\n      s.relativeLink = link\n      s.linkRelativeToFile = path.relative(parent, resolvedLinkTarget)\n    }\n    return null\n  }\n}\n\nfunction createAppFilter(matcher: FileMatcher, packager: Packager): Filter | null {\n  if (packager.areNodeModulesHandledExternally) {\n    return matcher.isEmpty() ? null : matcher.createFilter()\n  }\n\n  const nodeModulesFilter: Filter = (file, fileStat) => {\n    return !(fileStat.isDirectory() && file.endsWith(nodeModulesSystemDependentSuffix))\n  }\n\n  if (matcher.isEmpty()) {\n    return nodeModulesFilter\n  }\n\n  const filter = matcher.createFilter()\n  return (file, fileStat) => {\n    if (!nodeModulesFilter(file, fileStat)) {\n      return false\n    }\n    return filter(file, fileStat)\n  }\n}\n\n/** @internal */\nexport class AppFileWalker extends FileCopyHelper implements FileConsumer {\n  constructor(matcher: FileMatcher, packager: Packager) {\n    super(addAllPatternIfNeed(matcher), createAppFilter(matcher, packager), packager)\n  }\n\n  // noinspection JSUnusedGlobalSymbols\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  consume(file: string, fileStat: Stats, parent: string, siblingNames: Array<string>): any {\n    if (fileStat.isDirectory()) {\n      // https://github.com/electron-userland/electron-builder/issues/1539\n      // but do not filter if we inside node_modules dir\n      // update: solution disabled, node module resolver should support such setup\n      if (file.endsWith(nodeModulesSystemDependentSuffix)) {\n        return false\n      }\n    }\n    else {\n      // save memory - no need to store stat for directory\n      this.metadata.set(file, fileStat)\n    }\n\n    return this.handleFile(file, parent, fileStat)\n  }\n}"], "sourceRoot": ""}