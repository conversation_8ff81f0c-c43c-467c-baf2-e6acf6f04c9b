"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.digest = digest;
exports.BuildCacheManager = void 0;

function _bluebirdLst() {
  const data = _interopRequireDefault(require("bluebird-lst"));

  _bluebirdLst = function () {
    return data;
  };

  return data;
}

function _builderUtil() {
  const data = require("builder-util");

  _builderUtil = function () {
    return data;
  };

  return data;
}

function _fs() {
  const data = require("builder-util/out/fs");

  _fs = function () {
    return data;
  };

  return data;
}

function _promise() {
  const data = require("builder-util/out/promise");

  _promise = function () {
    return data;
  };

  return data;
}

function _fsExtra() {
  const data = require("fs-extra");

  _fsExtra = function () {
    return data;
  };

  return data;
}

var path = _interopRequireWildcard(require("path"));

function _getRequireWildcardCache() { if (typeof WeakMap !== "function") return null; var cache = new WeakMap(); _getRequireWildcardCache = function () { return cache; }; return cache; }

function _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

class BuildCacheManager {
  constructor(outDir, executableFile, arch) {
    this.executableFile = executableFile;
    this.cacheInfo = null;
    this.newDigest = null;
    this.cacheDir = path.join(outDir, ".cache", _builderUtil().Arch[arch]);
    this.cacheFile = path.join(this.cacheDir, "app.exe");
    this.cacheInfoFile = path.join(this.cacheDir, "info.json");
  }

  async copyIfValid(digest) {
    this.newDigest = digest;
    this.cacheInfo = await (0, _promise().orNullIfFileNotExist)((0, _fsExtra().readJson)(this.cacheInfoFile));
    const oldDigest = this.cacheInfo == null ? null : this.cacheInfo.executableDigest;

    if (oldDigest !== digest) {
      _builderUtil().log.debug({
        oldDigest,
        newDigest: digest
      }, "no valid cached executable found");

      return false;
    }

    _builderUtil().log.debug({
      cacheFile: this.cacheFile,
      file: this.executableFile
    }, `copying cached executable`);

    try {
      await (0, _fs().copyFile)(this.cacheFile, this.executableFile, false);
      return true;
    } catch (e) {
      if (e.code === "ENOENT" || e.code === "ENOTDIR") {
        _builderUtil().log.debug({
          error: e.code
        }, "copy cached executable failed");
      } else {
        _builderUtil().log.warn({
          error: e.stack || e
        }, `cannot copy cached executable`);
      }
    }

    return false;
  }

  async save() {
    if (this.newDigest == null) {
      throw new Error("call copyIfValid before");
    }

    if (this.cacheInfo == null) {
      this.cacheInfo = {
        executableDigest: this.newDigest
      };
    } else {
      this.cacheInfo.executableDigest = this.newDigest;
    }

    try {
      await (0, _fsExtra().ensureDir)(this.cacheDir);
      await Promise.all([(0, _fsExtra().writeJson)(this.cacheInfoFile, this.cacheInfo), (0, _fs().copyFile)(this.executableFile, this.cacheFile, false)]);
    } catch (e) {
      _builderUtil().log.warn({
        error: e.stack || e
      }, `cannot save build cache`);
    }
  }

}

exports.BuildCacheManager = BuildCacheManager;
BuildCacheManager.VERSION = "0";

async function digest(hash, files) {
  // do not use pipe - better do bulk file read (https://github.com/yarnpkg/yarn/commit/7a63e0d23c46a4564bc06645caf8a59690f04d01)
  for (const content of await _bluebirdLst().default.map(files, it => (0, _fsExtra().readFile)(it))) {
    hash.update(content);
  }

  hash.update(BuildCacheManager.VERSION);
  return hash.digest("base64");
} 
// __ts-babel@6.0.4
//# sourceMappingURL=cacheManager.js.map