{"version": 3, "sources": ["../../src/util/appBuilder.ts"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAGM,SAAU,uBAAV,CAAqC,IAArC,EAAwD;AAC5D,SAAO,sCAAkB,IAAlB,EACJ,IADI,CACC,SAAS,IAAG;AAChB,QAAI,SAAS,KAAK,EAAlB,EAAsB;AACpB,aAAO,MAAM,CAAC,MAAP,CAAc,IAAd,CAAP;AACD;;AAED,QAAI;AACF,aAAO,IAAI,CAAC,KAAL,CAAW,SAAX,CAAP;AACD,KAFD,CAGA,OAAO,CAAP,EAAU;AACR,YAAM,IAAI,KAAJ,CAAU,wBAAwB,CAAC,CAAC,OAAO,MAAM,SAAS,GAA1D,CAAN;AACD;AACF,GAZI,CAAP;AAaD;;AAEK,SAAU,6BAAV,CAAwC,IAAxC,EAA6D,IAA7D,EAAwE,YAAA,GAA6B,EAArG,EAAuG;AAC3G,SAAO,sCAAkB,IAAlB,EAAwB,YAAY,IAAG;AAC5C,IAAA,YAAY,CAAC,KAAb,CAAqB,GAArB,CAAyB,IAAI,CAAC,SAAL,CAAe,IAAf,CAAzB;AACD,GAFM,EAEJ,EACD,GAAG,YADF;AAED,IAAA,KAAK,EAAE,CAAC,MAAD,EAAS,MAAT,EAAiB,OAAO,CAAC,MAAzB;AAFN,GAFI,CAAP;AAMD;;AAEK,SAAU,YAAV,CAAuB,EAAvB,EAA0C,cAA1C,EAA0F;AAC9F,OAAK,MAAM,IAAX,IAAmB,MAAM,CAAC,IAAP,CAAY,cAAZ,CAAnB,EAAgD;AAC9C,UAAM,KAAK,GAAG,cAAc,CAAC,IAAD,CAA5B;;AACA,QAAI,KAAK,IAAI,IAAb,EAAmB;AACjB,MAAA,EAAE,CAAC,IAAH,CAAQ,KAAK,IAAI,EAAjB,EAAqB,KAArB;AACD;AACF;AACF,C", "sourcesContent": ["import { executeAppBuilder } from \"builder-util\"\nimport { SpawnOptions } from \"child_process\"\n\nexport function executeAppBuilderAsJson<T>(args: Array<string>): Promise<T> {\n  return executeAppBuilder(args)\n    .then(rawResult => {\n      if (rawResult === \"\") {\n        return Object.create(null) as T\n      }\n\n      try {\n        return JSON.parse(rawResult) as T\n      }\n      catch (e) {\n        throw new Error(`Cannot parse result: ${e.message}: \"${rawResult}\"`)\n      }\n    })\n}\n\nexport function executeAppBuilderAndWriteJson(args: Array<string>, data: any, extraOptions: SpawnOptions = {}): Promise<string> {\n  return executeAppBuilder(args, childProcess => {\n    childProcess.stdin!!.end(JSON.stringify(data))\n  }, {\n    ...extraOptions,\n    stdio: [\"pipe\", \"pipe\", process.stdout]\n  })\n}\n\nexport function objectToArgs(to: Array<string>, argNameToValue: { [key: string]: string | null }): void {\n  for (const name of Object.keys(argNameToValue)) {\n    const value = argNameToValue[name]\n    if (value != null) {\n      to.push(`--${name}`, value)\n    }\n  }\n}\n"], "sourceRoot": ""}