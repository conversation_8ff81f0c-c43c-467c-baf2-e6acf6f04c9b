{"version": 3, "sources": ["../../src/util/filter.ts"], "names": [], "mappings": ";;;;;;;;AAGA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;AAEA;AACM,SAAU,QAAV,CAAmB,OAAnB,EAAqC;AACzC,QAAM,GAAG,GAAG,OAAO,CAAC,GAApB;;AACA,MAAI,GAAG,CAAC,MAAJ,GAAa,CAAjB,EAAoB;AAClB,WAAO,IAAP;AACD;;AAED,OAAK,MAAM,CAAX,IAAgB,GAAG,CAAC,CAAD,CAAnB,EAAwB;AACtB,QAAI,OAAO,CAAP,KAAa,QAAjB,EAA2B;AACzB,aAAO,IAAP;AACD;AACF;;AAED,SAAO,KAAP;AACD,C,CAED;;;AACA,SAAS,cAAT,CAAwB,CAAxB,EAAiC;AAC/B,SAAO,CAAC,CAAC,MAAF,KAAa,CAAb,IAAkB,CAAC,CAAC,QAAF,CAAW,IAAI,CAAC,GAAhB,CAAlB,GAAyC,CAAzC,GAA8C,CAAC,GAAG,IAAI,CAAC,GAA9D;AACD;;AAED,SAAS,eAAT,CAAyB,IAAzB,EAAuC,eAAvC,EAA8D;AAC5D,MAAI,CAAC,IAAI,CAAC,UAAL,CAAgB,eAAhB,CAAL,EAAuC;AACrC,UAAM,KAAK,GAAG,IAAI,CAAC,OAAL,CAAa,uCAAb,CAAd;;AACA,QAAI,KAAK,GAAG,CAAZ,EAAe;AACb,YAAM,IAAI,KAAJ,CAAU,GAAG,IAAI,kBAAkB,eAAe,EAAlD,CAAN;AACD,KAFD,MAGK;AACH,aAAO,IAAI,CAAC,SAAL,CAAe,KAAK,GAAG;AAAE;AAAzB,OAAP;AACD;AACF;;AAED,MAAI,QAAQ,GAAG,IAAI,CAAC,SAAL,CAAe,eAAe,CAAC,MAA/B,CAAf;;AACA,MAAI,IAAI,CAAC,GAAL,KAAa,IAAjB,EAAuB;AACrB,QAAI,QAAQ,CAAC,UAAT,CAAoB,IAApB,CAAJ,EAA+B;AAC7B;AACA,MAAA,QAAQ,GAAG,QAAQ,CAAC,SAAT,CAAmB,CAAnB,CAAX;AACD;;AACD,IAAA,QAAQ,GAAG,QAAQ,CAAC,OAAT,CAAiB,KAAjB,EAAwB,GAAxB,CAAX;AACD;;AACD,SAAO,QAAP;AACD;AAED;;;AACM,SAAU,YAAV,CAAuB,GAAvB,EAAoC,QAApC,EAAgE,eAAhE,EAAyG;AAC7G,QAAM,eAAe,GAAG,cAAc,CAAC,GAAD,CAAtC;AACA,SAAO,CAAC,IAAD,EAAO,IAAP,KAAe;AACpB,QAAI,GAAG,KAAK,IAAZ,EAAkB;AAChB,aAAO,IAAP;AACD;;AAED,UAAM,QAAQ,GAAG,eAAe,CAAC,IAAD,EAAO,eAAP,CAAhC,CALoB,CAMpB;;AACA,WAAO,YAAY,CAAC,QAAD,EAAW,QAAX,EAAqB,IAArB,CAAZ,KAA2C,eAAe,IAAI,IAAnB,IAA2B,IAAI,CAAC,WAAL,EAA3B,IAAiD,CAAC,YAAY,CAAC,QAAD,EAAW,eAAX,EAA4B,IAA5B,CAAzG,CAAP;AACD,GARD;AASD,C,CAED;;;AACA,SAAS,YAAT,CAAsB,IAAtB,EAAoC,QAApC,EAAgE,IAAhE,EAA2E;AACzE,MAAI,KAAK,GAAG,KAAZ;;AACA,OAAK,MAAM,OAAX,IAAsB,QAAtB,EAAgC;AAC9B;AACA;AACA,QAAI,KAAK,KAAK,OAAO,CAAC,MAAtB,EAA8B;AAC5B;AACD,KAL6B,CAO9B;AACA;;;AACA,IAAA,KAAK,GAAG,OAAO,CAAC,KAAR,CAAc,IAAd,EAAoB,IAAI,CAAC,WAAL,MAAsB,CAAC,OAAO,CAAC,MAAnD,CAAR;AACD;;AACD,SAAO,KAAP;AACD,C", "sourcesContent": ["import { Filter } from \"builder-util/out/fs\"\nimport { Stats } from \"fs-extra\"\nimport { Minimatch } from \"minimatch\"\nimport * as path from \"path\"\nimport { NODE_MODULES_PATTERN } from \"../fileTransformer\"\n\n/** @internal */\nexport function hasMagic(pattern: Minimatch) {\n  const set = pattern.set\n  if (set.length > 1) {\n    return true\n  }\n\n  for (const i of set[0]) {\n    if (typeof i !== \"string\") {\n      return true\n    }\n  }\n\n  return false\n}\n\n// sometimes, destination may not contain path separator in the end (path to folder), but the src does. So let's ensure paths have path separators in the end\nfunction ensureEndSlash(s: string) {\n  return s.length === 0 || s.endsWith(path.sep) ? s : (s + path.sep)\n}\n\nfunction getRelativePath(file: string, srcWithEndSlash: string) {\n  if (!file.startsWith(srcWithEndSlash)) {\n    const index = file.indexOf(NODE_MODULES_PATTERN)\n    if (index < 0) {\n      throw new Error(`${file} must be under ${srcWithEndSlash}`)\n    }\n    else {\n      return file.substring(index + 1 /* leading slash */)\n    }\n  }\n\n  let relative = file.substring(srcWithEndSlash.length)\n  if (path.sep === \"\\\\\") {\n    if (relative.startsWith(\"\\\\\")) {\n      // windows problem: double backslash, the above substring call removes root path with a single slash, so here can me some leftovers\n      relative = relative.substring(1)\n    }\n    relative = relative.replace(/\\\\/g, \"/\")\n  }\n  return relative\n}\n\n/** @internal */\nexport function createFilter(src: string, patterns: Array<Minimatch>, excludePatterns?: Array<Minimatch> | null): Filter {\n  const srcWithEndSlash = ensureEndSlash(src)\n  return (file, stat) => {\n    if (src === file) {\n      return true\n    }\n\n    const relative = getRelativePath(file, srcWithEndSlash)\n    // https://github.com/electron-userland/electron-builder/issues/867\n    return minimatchAll(relative, patterns, stat) && (excludePatterns == null || stat.isDirectory() || !minimatchAll(relative, excludePatterns, stat))\n  }\n}\n\n// https://github.com/joshwnj/minimatch-all/blob/master/index.js\nfunction minimatchAll(path: string, patterns: Array<Minimatch>, stat: Stats): boolean {\n  let match = false\n  for (const pattern of patterns) {\n    // If we've got a match, only re-test for exclusions.\n    // if we don't have a match, only re-test for inclusions.\n    if (match !== pattern.negate) {\n      continue\n    }\n\n    // partial match — pattern: foo/bar.txt path: foo — we must allow foo\n    // use it only for non-negate patterns: const m = new Minimatch(\"!node_modules/@(electron-download|electron)/**/*\", {dot: true }); m.match(\"node_modules\", true) will return false, but must be true\n    match = pattern.match(path, stat.isDirectory() && !pattern.negate)\n  }\n  return match\n}\n"], "sourceRoot": ""}