{"version": 3, "sources": ["../../src/util/flags.ts"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEM,SAAU,mBAAV,GAA6B;AACjC,SAAO,8BAAU,OAAO,CAAC,GAAR,CAAY,mBAAtB,CAAP;AACD;;AAEK,SAAU,mBAAV,GAA6B;AACjC,SAAO,CAAC,8BAAU,OAAO,CAAC,GAAR,CAAY,oCAAtB,CAAR;AACD;;AAEK,SAAU,+BAAV,GAAyC;AAC7C,SAAO,OAAO,CAAC,GAAR,CAAY,2BAAZ,KAA4C,OAAnD;AACD,C", "sourcesContent": ["import { isEnvTrue } from \"builder-util\"\n\nexport function isUseSystemSigncode() {\n  return isEnvTrue(process.env.USE_SYSTEM_SIGNCODE)\n}\n\nexport function isBuildCacheEnabled() {\n  return !isEnvTrue(process.env.ELECTRON_BUILDER_DISABLE_BUILD_CACHE)\n}\n\nexport function isAutoDiscoveryCodeSignIdentity() {\n  return process.env.CSC_IDENTITY_AUTO_DISCOVERY !== \"false\"\n}"], "sourceRoot": ""}