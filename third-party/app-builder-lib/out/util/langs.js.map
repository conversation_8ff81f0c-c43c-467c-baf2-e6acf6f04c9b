{"version": 3, "sources": ["../../src/util/langs.ts"], "names": [], "mappings": ";;;;;;;AAAO,MAAM,gBAAgB,GAAG,CAC9B,OAD8B,EACrB,OADqB,EACZ,OADY,EACH,OADG,EACM,OADN,EACe,OADf,EACwB,OADxB,EACiC,OADjC,EAC0C,OAD1C,EACmD,OADnD,EAC4D,OAD5D,EACqE,OADrE,EAE9B,OAF8B,EAErB,OAFqB,EAEZ,OAFY,EAEH,OAFG,EAEM,OAFN,EAEe,OAFf,EAEwB,OAFxB,EAEiC,OAFjC,EAE0C,OAF1C,EAEmD,OAFnD,EAE4D,OAF5D,EAEqE,OAFrE,EAG9B,OAH8B,EAGrB,OAHqB,CAAzB,C,CAMP;;;AAEA,MAAM,oBAAoB,GAAG,IAAI,GAAJ,EAA7B;;AACA,KAAK,MAAM,EAAX,IAAiB,gBAAjB,EAAmC;AACjC,EAAA,oBAAoB,CAAC,GAArB,CAAyB,EAAE,CAAC,SAAH,CAAa,CAAb,EAAgB,EAAE,CAAC,OAAH,CAAW,GAAX,CAAhB,CAAzB,EAA2D,EAA3D;AACD;;AAEK,SAAU,gBAAV,CAA2B,IAA3B,EAAuC;AAC3C,MAAI,IAAI,CAAC,QAAL,CAAc,GAAd,CAAJ,EAAwB;AACtB,WAAO,IAAP;AACD;;AAED,EAAA,IAAI,GAAG,IAAI,CAAC,WAAL,EAAP;AAEA,QAAM,MAAM,GAAG,oBAAoB,CAAC,GAArB,CAAyB,IAAzB,CAAf;AACA,SAAO,MAAM,IAAI,IAAV,GAAiB,GAAG,IAAI,IAAI,IAAI,CAAC,WAAL,EAAkB,EAA9C,GAAmD,MAA1D;AACD;;AAEM,MAAM,IAAI,GAAQ;AACvB,EAAA,KAAK,EAAE,IADgB;AAEvB,EAAA,KAAK,EAAE,IAFgB;AAGvB,EAAA,KAAK,EAAE,KAHgB;AAIvB,EAAA,KAAK,EAAE,KAJgB;AAKvB,EAAA,KAAK,EAAE,IALgB;AAMvB,EAAA,KAAK,EAAE,IANgB;AAOvB,EAAA,KAAK,EAAE,IAPgB;AAQvB,EAAA,KAAK,EAAE,KARgB;AASvB,EAAA,KAAK,EAAE,KATgB;AAUvB,EAAA,KAAK,EAAE,KAVgB;AAWvB,EAAA,KAAK,EAAE,IAXgB;AAYvB,EAAA,KAAK,EAAE,IAZgB;AAavB,EAAA,KAAK,EAAE,IAbgB;AAcvB,EAAA,KAAK,EAAE,KAdgB;AAevB,EAAA,KAAK,EAAE,IAfgB;AAgBvB,EAAA,KAAK,EAAE,KAhBgB;AAiBvB,EAAA,KAAK,EAAE,IAjBgB;AAkBvB,EAAA,KAAK,EAAE,IAlBgB;AAmBvB,EAAA,MAAM,EAAE,IAnBe;AAoBvB,EAAA,KAAK,EAAE,IApBgB;AAqBvB,EAAA,KAAK,EAAE,IArBgB;AAsBvB,EAAA,KAAK,EAAE,IAtBgB;AAuBvB,EAAA,KAAK,EAAE,IAvBgB;AAwBvB,EAAA,KAAK,EAAE,IAxBgB;AAyBvB,EAAA,KAAK,EAAE,IAzBgB;AA0BvB,EAAA,KAAK,EAAE,IA1BgB;AA2BvB,EAAA,KAAK,EAAE,IA3BgB;AA4BvB,EAAA,KAAK,EAAE,IA5BgB;AA6BvB,EAAA,KAAK,EAAE,IA7BgB;AA8BvB,EAAA,KAAK,EAAE,IA9BgB;AA+BvB,EAAA,KAAK,EAAE,IA/BgB;AAgCvB,EAAA,KAAK,EAAE,IAhCgB;AAiCvB,EAAA,KAAK,EAAE,IAjCgB;AAkCvB,EAAA,KAAK,EAAE,IAlCgB;AAmCvB,EAAA,KAAK,EAAE,IAnCgB;AAoCvB,EAAA,KAAK,EAAE,IApCgB;AAqCvB,EAAA,KAAK,EAAE,IArCgB;AAsCvB,EAAA,KAAK,EAAE,IAtCgB;AAuCvB,EAAA,KAAK,EAAE,IAvCgB;AAwCvB,EAAA,MAAM,EAAE,IAxCe;AAyCvB,EAAA,MAAM,EAAE,IAzCe;AA0CvB,EAAA,KAAK,EAAE,IA1CgB;AA2CvB,EAAA,KAAK,EAAE,IA3CgB;AA4CvB,EAAA,KAAK,EAAE,KA5CgB;AA6CvB,EAAA,KAAK,EAAE,IA7CgB;AA8CvB,EAAA,KAAK,EAAE,IA9CgB;AA+CvB,EAAA,KAAK,EAAE,IA/CgB;AAgDvB,EAAA,KAAK,EAAE,IAhDgB;AAiDvB,EAAA,KAAK,EAAE,KAjDgB;AAkDvB,EAAA,KAAK,EAAE,IAlDgB;AAmDvB,EAAA,KAAK,EAAE,KAnDgB;AAoDvB,EAAA,KAAK,EAAE,IApDgB;AAqDvB,EAAA,KAAK,EAAE,KArDgB;AAsDvB,EAAA,KAAK,EAAE,KAtDgB;AAuDvB,EAAA,KAAK,EAAE,IAvDgB;AAwDvB,EAAA,KAAK,EAAE,IAxDgB;AAyDvB,EAAA,KAAK,EAAE,KAzDgB;AA0DvB,EAAA,KAAK,EAAE,KA1DgB;AA2DvB,EAAA,KAAK,EAAE,KA3DgB;AA4DvB,EAAA,KAAK,EAAE,KA5DgB;AA6DvB,EAAA,KAAK,EAAE,IA7DgB;AA8DvB,EAAA,KAAK,EAAE,IA9DgB;AA+DvB,EAAA,KAAK,EAAE,IA/DgB;AAgEvB,EAAA,KAAK,EAAE,KAhEgB;AAiEvB,EAAA,KAAK,EAAE,IAjEgB;AAkEvB,EAAA,KAAK,EAAE,IAlEgB;AAmEvB,EAAA,KAAK,EAAE,KAnEgB;AAoEvB,EAAA,KAAK,EAAE,IApEgB;AAqEvB,EAAA,KAAK,EAAE,KArEgB;AAsEvB,EAAA,KAAK,EAAE,IAtEgB;AAuEvB,EAAA,KAAK,EAAE,KAvEgB;AAwEvB,EAAA,KAAK,EAAE,KAxEgB;AAyEvB,EAAA,KAAK,EAAE,KAzEgB;AA0EvB,EAAA,KAAK,EAAE,KA1EgB;AA2EvB,EAAA,KAAK,EAAE,KA3EgB;AA4EvB,EAAA,KAAK,EAAE,KA5EgB;AA6EvB,EAAA,KAAK,EAAE,IA7EgB;AA8EvB,EAAA,KAAK,EAAE,IA9EgB;AA+EvB,EAAA,KAAK,EAAE,IA/EgB;AAgFvB,EAAA,KAAK,EAAE,IAhFgB;AAiFvB,EAAA,KAAK,EAAE,IAjFgB;AAkFvB,EAAA,MAAM,EAAE,IAlFe;AAmFvB,EAAA,KAAK,EAAE,IAnFgB;AAoFvB,EAAA,KAAK,EAAE,IApFgB;AAqFvB,EAAA,KAAK,EAAE,IArFgB;AAsFvB,EAAA,KAAK,EAAE,IAtFgB;AAuFvB,EAAA,KAAK,EAAE,IAvFgB;AAwFvB,EAAA,KAAK,EAAE,IAxFgB;AAyFvB,EAAA,KAAK,EAAE,IAzFgB;AA0FvB,EAAA,KAAK,EAAE,IA1FgB;AA2FvB,EAAA,KAAK,EAAE,IA3FgB;AA4FvB,EAAA,MAAM,EAAE,IA5Fe;AA6FvB,EAAA,KAAK,EAAE,IA7FgB;AA8FvB,EAAA,MAAM,EAAE,IA9Fe;AA+FvB,EAAA,KAAK,EAAE,IA/FgB;AAgGvB,EAAA,KAAK,EAAE,IAhGgB;AAiGvB,EAAA,KAAK,EAAE,IAjGgB;AAkGvB,EAAA,KAAK,EAAE,IAlGgB;AAmGvB,EAAA,KAAK,EAAE,IAnGgB;AAoGvB,EAAA,KAAK,EAAE,IApGgB;AAqGvB,EAAA,KAAK,EAAE,IArGgB;AAsGvB,EAAA,KAAK,EAAE,IAtGgB;AAuGvB,EAAA,KAAK,EAAE,IAvGgB;AAwGvB,EAAA,KAAK,EAAE,IAxGgB;AAyGvB,EAAA,KAAK,EAAE,IAzGgB;AA0GvB,EAAA,KAAK,EAAE,IA1GgB;AA2GvB,EAAA,KAAK,EAAE,IA3GgB;AA4GvB,EAAA,KAAK,EAAE,IA5GgB;AA6GvB,EAAA,KAAK,EAAE,IA7GgB;AA8GvB,EAAA,KAAK,EAAE,IA9GgB;AA+GvB,EAAA,KAAK,EAAE,IA/GgB;AAgHvB,EAAA,KAAK,EAAE,IAhHgB;AAiHvB,EAAA,KAAK,EAAE,IAjHgB;AAkHvB,EAAA,KAAK,EAAE,IAlHgB;AAmHvB,EAAA,KAAK,EAAE,IAnHgB;AAoHvB,EAAA,MAAM,EAAE,IApHe;AAqHvB,EAAA,KAAK,EAAE,IArHgB;AAsHvB,EAAA,KAAK,EAAE,IAtHgB;AAuHvB,EAAA,KAAK,EAAE,IAvHgB;AAwHvB,EAAA,KAAK,EAAE,IAxHgB;AAyHvB,EAAA,KAAK,EAAE,IAzHgB;AA0HvB,EAAA,KAAK,EAAE,IA1HgB;AA2HvB,EAAA,KAAK,EAAE,IA3HgB;AA4HvB,EAAA,KAAK,EAAE,IA5HgB;AA6HvB,EAAA,KAAK,EAAE,IA7HgB;AA8HvB,EAAA,KAAK,EAAE,IA9HgB;AA+HvB,EAAA,MAAM,EAAE,IA/He;AAgIvB,EAAA,KAAK,EAAE,IAhIgB;AAiIvB,EAAA,KAAK,EAAE,IAjIgB;AAkIvB,EAAA,KAAK,EAAE,IAlIgB;AAmIvB,EAAA,KAAK,EAAE,IAnIgB;AAoIvB,EAAA,KAAK,EAAE,IApIgB;AAqIvB,EAAA,KAAK,EAAE,IArIgB;AAsIvB,EAAA,KAAK,EAAE,IAtIgB;AAuIvB,EAAA,KAAK,EAAE,IAvIgB;AAwIvB,EAAA,KAAK,EAAE,IAxIgB;AAyIvB,EAAA,KAAK,EAAE,IAzIgB;AA0IvB,EAAA,KAAK,EAAE,IA1IgB;AA2IvB,EAAA,KAAK,EAAE,IA3IgB;AA4IvB,EAAA,KAAK,EAAE,IA5IgB;AA6IvB,EAAA,KAAK,EAAE,IA7IgB;AA8IvB,EAAA,KAAK,EAAE,IA9IgB;AA+IvB,EAAA,KAAK,EAAE,IA/IgB;AAgJvB,EAAA,KAAK,EAAE,IAhJgB;AAiJvB,EAAA,KAAK,EAAE,IAjJgB;AAkJvB,EAAA,MAAM,EAAE,IAlJe;AAmJvB,EAAA,MAAM,EAAE,IAnJe;AAoJvB,EAAA,MAAM,EAAE,IApJe;AAqJvB,EAAA,MAAM,EAAE,IArJe;AAsJvB,EAAA,KAAK,EAAE,IAtJgB;AAuJvB,EAAA,KAAK,EAAE,IAvJgB;AAwJvB,EAAA,KAAK,EAAE,IAxJgB;AAyJvB,EAAA,KAAK,EAAE,IAzJgB;AA0JvB,EAAA,KAAK,EAAE,IA1JgB;AA2JvB,EAAA,MAAM,EAAE,IA3Je;AA4JvB,EAAA,KAAK,EAAE,IA5JgB;AA6JvB,EAAA,KAAK,EAAE,IA7JgB;AA8JvB,EAAA,KAAK,EAAE,IA9JgB;AA+JvB,EAAA,KAAK,EAAE,IA/JgB;AAgKvB,EAAA,KAAK,EAAE,IAhKgB;AAiKvB,EAAA,KAAK,EAAE,IAjKgB;AAkKvB,EAAA,MAAM,EAAE,IAlKe;AAmKvB,EAAA,MAAM,EAAE,IAnKe;AAoKvB,EAAA,MAAM,EAAE,IApKe;AAqKvB,EAAA,MAAM,EAAE,IArKe;AAsKvB,EAAA,MAAM,EAAE,IAtKe;AAuKvB,EAAA,MAAM,EAAE,IAvKe;AAwKvB,EAAA,KAAK,EAAE,IAxKgB;AAyKvB,EAAA,KAAK,EAAE,IAzKgB;AA0KvB,EAAA,KAAK,EAAE,IA1KgB;AA2KvB,EAAA,KAAK,EAAE,IA3KgB;AA4KvB,EAAA,KAAK,EAAE,IA5KgB;AA6KvB,EAAA,KAAK,EAAE,IA7KgB;AA8KvB,EAAA,MAAM,EAAE,IA9Ke;AA+KvB,EAAA,KAAK,EAAE,IA/KgB;AAgLvB,EAAA,KAAK,EAAE,IAhLgB;AAiLvB,EAAA,KAAK,EAAE,IAjLgB;AAkLvB,EAAA,KAAK,EAAE,IAlLgB;AAmLvB,EAAA,KAAK,EAAE,IAnLgB;AAoLvB,EAAA,MAAM,EAAE,IApLe;AAqLvB,EAAA,KAAK,EAAE,IArLgB;AAsLvB,EAAA,KAAK,EAAE,IAtLgB;AAuLvB,EAAA,KAAK,EAAE,IAvLgB;AAwLvB,EAAA,KAAK,EAAE,IAxLgB;AAyLvB,EAAA,KAAK,EAAE,IAzLgB;AA0LvB,EAAA,KAAK,EAAE,IA1LgB;AA2LvB,EAAA,KAAK,EAAE,IA3LgB;AA4LvB,EAAA,KAAK,EAAE,IA5LgB;AA6LvB,EAAA,KAAK,EAAE,IA7LgB;AA8LvB,EAAA,MAAM,EAAE,IA9Le;AA+LvB,EAAA,KAAK,EAAE,IA/LgB;AAgMvB,EAAA,KAAK,EAAE,IAhMgB;AAiMvB,EAAA,KAAK,EAAE,IAjMgB;AAkMvB,EAAA,MAAM,EAAE,CAlMe;AAmMvB,EAAA,MAAM,EAAE,KAnMe;AAoMvB,EAAA,KAAK,EAAE,IApMgB;AAqMvB,EAAA,KAAK,EAAE,IArMgB;AAsMvB,EAAA,KAAK,EAAE,IAtMgB;AAuMvB,EAAA,KAAK,EAAE,IAvMgB;AAwMvB,EAAA,KAAK,EAAE,IAxMgB;AAyMvB,EAAA,KAAK,EAAE;AAzMgB,CAAlB,C,CA4MP;;;AACO,MAAM,YAAY,GAAG;AAC1B,EAAA,EAAE,EAAE,QADsB;AAE1B,EAAA,EAAE,EAAE,MAFsB;AAG1B,EAAA,EAAE,EAAE,WAHsB;AAI1B,EAAA,EAAE,EAAE,MAJsB;AAK1B,EAAA,EAAE,EAAE,UALsB;AAM1B,EAAA,EAAE,EAAE,SANsB;AAO1B,EAAA,EAAE,EAAE,QAPsB;AAQ1B,EAAA,EAAE,EAAE,WARsB;AAS1B,EAAA,EAAE,EAAE,UATsB;AAU1B,EAAA,EAAE,EAAE,UAVsB;AAW1B,EAAA,EAAE,EAAE,QAXsB;AAY1B,EAAA,EAAE,EAAE,SAZsB;AAa1B,EAAA,EAAE,EAAE,QAbsB;AAc1B,EAAA,EAAE,EAAE,aAdsB;AAe1B,EAAA,EAAE,EAAE,SAfsB;AAgB1B,EAAA,EAAE,EAAE,SAhBsB;AAiB1B,EAAA,EAAE,EAAE,QAjBsB;AAkB1B,EAAA,EAAE,EAAE,YAlBsB;AAmB1B,EAAA,EAAE,EAAE,SAnBsB;AAoB1B,EAAA,EAAE,EAAE,QApBsB;AAqB1B,EAAA,EAAE,EAAE,SArBsB;AAsB1B,EAAA,EAAE,EAAE,SAtBsB;AAuB1B,EAAA,EAAE,EAAE,QAvBsB;AAwB1B,EAAA,EAAE,EAAE,WAxBsB;AAyB1B,EAAA,EAAE,EAAE,SAzBsB;AA0B1B,EAAA,EAAE,EAAE,SA1BsB;AA2B1B,EAAA,EAAE,EAAE,UA3BsB;AA4B1B,EAAA,EAAE,EAAE,SA5BsB;AA6B1B,EAAA,EAAE,EAAE,UA7BsB;AA8B1B,EAAA,EAAE,EAAE,SA9BsB;AA+B1B,EAAA,EAAE,EAAE,SA/BsB;AAgC1B,EAAA,EAAE,EAAE,SAhCsB;AAiC1B,EAAA,EAAE,EAAE,UAjCsB;AAkC1B,EAAA,EAAE,EAAE,MAlCsB;AAmC1B,EAAA,EAAE,EAAE,UAnCsB;AAoC1B,EAAA,EAAE,EAAE,OApCsB;AAqC1B,EAAA,EAAE,EAAE,QArCsB;AAsC1B,EAAA,EAAE,EAAE,QAtCsB;AAuC1B,EAAA,EAAE,EAAE,OAvCsB;AAwC1B,EAAA,EAAE,EAAE,UAxCsB;AAyC1B,EAAA,EAAE,EAAE,SAzCsB;AA0C1B,EAAA,EAAE,EAAE,WA1CsB;AA2C1B,EAAA,EAAE,EAAE,UA3CsB;AA4C1B,EAAA,EAAE,EAAE,KA5CsB;AA6C1B,EAAA,EAAE,EAAE,SA7CsB;AA8C1B,EAAA,EAAE,EAAE,QA9CsB;AA+C1B,EAAA,EAAE,EAAE,SA/CsB;AAgD1B,EAAA,EAAE,EAAE,QAhDsB;AAiD1B,EAAA,EAAE,EAAE,MAjDsB;AAkD1B,EAAA,EAAE,EAAE,UAlDsB;AAmD1B,EAAA,EAAE,EAAE,UAnDsB;AAoD1B,EAAA,EAAE,EAAE,QApDsB;AAqD1B,EAAA,EAAE,EAAE,OArDsB;AAsD1B,EAAA,EAAE,EAAE,SAtDsB;AAuD1B,EAAA,EAAE,EAAE,UAvDsB;AAwD1B,EAAA,EAAE,EAAE,SAxDsB;AAyD1B,EAAA,EAAE,EAAE,OAzDsB;AA0D1B,EAAA,EAAE,EAAE,QA1DsB;AA2D1B,EAAA,EAAE,EAAE,QA3DsB;AA4D1B,EAAA,EAAE,EAAE,OA5DsB;AA6D1B,EAAA,EAAE,EAAE,WA7DsB;AA8D1B,EAAA,EAAE,EAAE,WA9DsB;AA+D1B,EAAA,EAAE,EAAE,aA/DsB;AAgE1B,EAAA,EAAE,EAAE,YAhEsB;AAiE1B,EAAA,EAAE,EAAE,aAjEsB;AAkE1B,EAAA,EAAE,EAAE,OAlEsB;AAmE1B,EAAA,EAAE,EAAE,MAnEsB;AAoE1B,EAAA,EAAE,EAAE,SApEsB;AAqE1B,EAAA,EAAE,EAAE,KArEsB;AAsE1B,EAAA,EAAE,EAAE,WAtEsB;AAuE1B,EAAA,EAAE,EAAE,SAvEsB;AAwE1B,EAAA,EAAE,EAAE,WAxEsB;AAyE1B,EAAA,EAAE,EAAE,UAzEsB;AA0E1B,EAAA,EAAE,EAAE,UA1EsB;AA2E1B,EAAA,EAAE,EAAE,aA3EsB;AA4E1B,EAAA,EAAE,EAAE,SA5EsB;AA6E1B,EAAA,EAAE,EAAE,QA7EsB;AA8E1B,EAAA,EAAE,EAAE,UA9EsB;AA+E1B,EAAA,EAAE,EAAE,QA/EsB;AAgF1B,EAAA,EAAE,EAAE,OAhFsB;AAiF1B,EAAA,EAAE,EAAE,QAjFsB;AAkF1B,EAAA,EAAE,EAAE,aAlFsB;AAmF1B,EAAA,EAAE,EAAE,QAnFsB;AAoF1B,EAAA,EAAE,EAAE,MApFsB;AAqF1B,EAAA,EAAE,EAAE,OArFsB;AAsF1B,EAAA,EAAE,EAAE,QAtFsB;AAuF1B,EAAA,EAAE,EAAE,SAvFsB;AAwF1B,EAAA,EAAE,EAAE,UAxFsB;AAyF1B,EAAA,EAAE,EAAE,OAzFsB;AA0F1B,EAAA,EAAE,EAAE,eA1FsB;AA2F1B,EAAA,EAAE,EAAE,OA3FsB;AA4F1B,EAAA,EAAE,EAAE,YA5FsB;AA6F1B,EAAA,EAAE,EAAE,SA7FsB;AA8F1B,EAAA,EAAE,EAAE,KA9FsB;AA+F1B,EAAA,EAAE,EAAE,YA/FsB;AAgG1B,EAAA,EAAE,EAAE,cAhGsB;AAiG1B,EAAA,EAAE,EAAE,SAjGsB;AAkG1B,EAAA,EAAE,EAAE,MAlGsB;AAmG1B,EAAA,EAAE,EAAE,YAnGsB;AAoG1B,EAAA,EAAE,EAAE,UApGsB;AAqG1B,EAAA,EAAE,EAAE,OArGsB;AAsG1B,EAAA,EAAE,EAAE,WAtGsB;AAuG1B,EAAA,EAAE,EAAE,SAvGsB;AAwG1B,EAAA,EAAE,EAAE,OAxGsB;AAyG1B,EAAA,EAAE,EAAE,SAzGsB;AA0G1B,EAAA,EAAE,EAAE,aA1GsB;AA2G1B,EAAA,EAAE,EAAE,WA3GsB;AA4G1B,EAAA,EAAE,EAAE,OA5GsB;AA6G1B,EAAA,EAAE,EAAE,QA7GsB;AA8G1B,EAAA,EAAE,EAAE,kBA9GsB;AA+G1B,EAAA,EAAE,EAAE,QA/GsB;AAgH1B,EAAA,EAAE,EAAE,QAhHsB;AAiH1B,EAAA,EAAE,EAAE,kBAjHsB;AAkH1B,EAAA,EAAE,EAAE,mBAlHsB;AAmH1B,EAAA,EAAE,EAAE,WAnHsB;AAoH1B,EAAA,EAAE,EAAE,OApHsB;AAqH1B,EAAA,EAAE,EAAE,kBArHsB;AAsH1B,EAAA,EAAE,EAAE,SAtHsB;AAuH1B,EAAA,EAAE,EAAE,QAvHsB;AAwH1B,EAAA,EAAE,EAAE,qBAxHsB;AAyH1B,EAAA,EAAE,EAAE,OAzHsB;AA0H1B,EAAA,EAAE,EAAE,OA1HsB;AA2H1B,EAAA,EAAE,EAAE,UA3HsB;AA4H1B,EAAA,EAAE,EAAE,SA5HsB;AA6H1B,EAAA,EAAE,EAAE,MA7HsB;AA8H1B,EAAA,EAAE,EAAE,SA9HsB;AA+H1B,EAAA,EAAE,EAAE,QA/HsB;AAgI1B,EAAA,EAAE,EAAE,QAhIsB;AAiI1B,EAAA,EAAE,EAAE,YAjIsB;AAkI1B,EAAA,EAAE,EAAE,SAlIsB;AAmI1B,EAAA,EAAE,EAAE,SAnIsB;AAoI1B,EAAA,EAAE,EAAE,SApIsB;AAqI1B,EAAA,EAAE,EAAE,UArIsB;AAsI1B,EAAA,EAAE,EAAE,SAtIsB;AAuI1B,EAAA,EAAE,EAAE,UAvIsB;AAwI1B,EAAA,EAAE,EAAE,WAxIsB;AAyI1B,EAAA,EAAE,EAAE,QAzIsB;AA0I1B,EAAA,EAAE,EAAE,eA1IsB;AA2I1B,EAAA,EAAE,EAAE,QA3IsB;AA4I1B,EAAA,EAAE,EAAE,OA5IsB;AA6I1B,EAAA,EAAE,EAAE,SA7IsB;AA8I1B,EAAA,EAAE,EAAE,QA9IsB;AA+I1B,EAAA,EAAE,EAAE,OA/IsB;AAgJ1B,EAAA,EAAE,EAAE,SAhJsB;AAiJ1B,EAAA,EAAE,EAAE,QAjJsB;AAkJ1B,EAAA,EAAE,EAAE,SAlJsB;AAmJ1B,EAAA,EAAE,EAAE,QAnJsB;AAoJ1B,EAAA,EAAE,EAAE,gBApJsB;AAqJ1B,EAAA,EAAE,EAAE,SArJsB;AAsJ1B,EAAA,EAAE,EAAE,WAtJsB;AAuJ1B,EAAA,EAAE,EAAE,SAvJsB;AAwJ1B,EAAA,EAAE,EAAE,OAxJsB;AAyJ1B,EAAA,EAAE,EAAE,SAzJsB;AA0J1B,EAAA,EAAE,EAAE,OA1JsB;AA2J1B,EAAA,EAAE,EAAE,QA3JsB;AA4J1B,EAAA,EAAE,EAAE,OA5JsB;AA6J1B,EAAA,EAAE,EAAE,MA7JsB;AA8J1B,EAAA,EAAE,EAAE,UA9JsB;AA+J1B,EAAA,EAAE,EAAE,kBA/JsB;AAgK1B,EAAA,EAAE,EAAE,SAhKsB;AAiK1B,EAAA,EAAE,EAAE,SAjKsB;AAkK1B,EAAA,EAAE,EAAE,QAlKsB;AAmK1B,EAAA,EAAE,EAAE,OAnKsB;AAoK1B,EAAA,EAAE,EAAE,SApKsB;AAqK1B,EAAA,EAAE,EAAE,QArKsB;AAsK1B,EAAA,EAAE,EAAE,OAtKsB;AAuK1B,EAAA,EAAE,EAAE,KAvKsB;AAwK1B,EAAA,EAAE,EAAE,UAxKsB;AAyK1B,EAAA,EAAE,EAAE,QAzKsB;AA0K1B,EAAA,EAAE,EAAE,WA1KsB;AA2K1B,EAAA,EAAE,EAAE,MA3KsB;AA4K1B,EAAA,EAAE,EAAE,OA5KsB;AA6K1B,EAAA,EAAE,EAAE,OA7KsB;AA8K1B,EAAA,EAAE,EAAE,YA9KsB;AA+K1B,EAAA,EAAE,EAAE,SA/KsB;AAgL1B,EAAA,EAAE,EAAE,SAhLsB;AAiL1B,EAAA,EAAE,EAAE,OAjLsB;AAkL1B,EAAA,EAAE,EAAE,OAlLsB;AAmL1B,EAAA,EAAE,EAAE,iBAnLsB;AAoL1B,EAAA,EAAE,EAAE,OApLsB;AAqL1B,EAAA,EAAE,EAAE,SArLsB;AAsL1B,EAAA,EAAE,EAAE,QAtLsB;AAuL1B,EAAA,EAAE,EAAE,QAvLsB;AAwL1B,EAAA,EAAE,EAAE;AAxLsB,CAArB,C", "sourcesContent": ["export const bundledLanguages = [\n  \"en_US\", \"de_DE\", \"fr_FR\", \"es_ES\", \"zh_CN\", \"zh_TW\", \"ja_JP\", \"ko_KR\", \"it_IT\", \"nl_NL\", \"da_DK\", \"sv_SE\",\n  \"nb_NO\", \"fi_FI\", \"ru_RU\", \"pt_PT\", \"pt_BR\", \"pl_PL\", \"uk_UA\", \"cs_CZ\", \"sk_SK\", \"hu_HU\", \"ar_SA\", \"tr_TR\",\n  \"th_TH\", \"vi_VN\",\n]\n\n// todo \"ro_RO\" \"el_GR\" \"et_EE\" \"ka_GE\"\n\nconst langToLangWithRegion = new Map<string, string>()\nfor (const id of bundledLanguages) {\n  langToLangWithRegion.set(id.substring(0, id.indexOf(\"_\")), id)\n}\n\nexport function toLangWithRegion(lang: string): string {\n  if (lang.includes(\"_\")) {\n    return lang\n  }\n\n  lang = lang.toLowerCase()\n\n  const result = langToLangWithRegion.get(lang)\n  return result == null ? `${lang}_${lang.toUpperCase()}` : result\n}\n\nexport const lcid: any = {\n  af_ZA: 1078,\n  am_ET: 1118,\n  ar_AE: 14337,\n  ar_BH: 15361,\n  ar_DZ: 5121,\n  ar_EG: 3073,\n  ar_IQ: 2049,\n  ar_JO: 11265,\n  ar_KW: 13313,\n  ar_LB: 12289,\n  ar_LY: 4097,\n  ar_MA: 6145,\n  ar_OM: 8193,\n  ar_QA: 16385,\n  ar_SA: 1025,\n  ar_SY: 10241,\n  ar_TN: 7169,\n  ar_YE: 9217,\n  arn_CL: 1146,\n  as_IN: 1101,\n  az_AZ: 2092,\n  ba_RU: 1133,\n  be_BY: 1059,\n  bg_BG: 1026,\n  bn_IN: 1093,\n  bo_BT: 2129,\n  bo_CN: 1105,\n  br_FR: 1150,\n  bs_BA: 8218,\n  ca_ES: 1027,\n  co_FR: 1155,\n  cs_CZ: 1029,\n  cy_GB: 1106,\n  da_DK: 1030,\n  de_AT: 3079,\n  de_CH: 2055,\n  de_DE: 1031,\n  de_LI: 5127,\n  de_LU: 4103,\n  div_MV: 1125,\n  dsb_DE: 2094,\n  el_GR: 1032,\n  en_AU: 3081,\n  en_BZ: 10249,\n  en_CA: 4105,\n  en_CB: 9225,\n  en_GB: 2057,\n  en_IE: 6153,\n  en_IN: 18441,\n  en_JA: 8201,\n  en_MY: 17417,\n  en_NZ: 5129,\n  en_PH: 13321,\n  en_TT: 11273,\n  en_US: 1033,\n  en_ZA: 7177,\n  en_ZW: 12297,\n  es_AR: 11274,\n  es_BO: 16394,\n  es_CL: 13322,\n  es_CO: 9226,\n  es_CR: 5130,\n  es_DO: 7178,\n  es_EC: 12298,\n  es_ES: 3082,\n  es_GT: 4106,\n  es_HN: 18442,\n  es_MX: 2058,\n  es_NI: 19466,\n  es_PA: 6154,\n  es_PE: 10250,\n  es_PR: 20490,\n  es_PY: 15370,\n  es_SV: 17418,\n  es_UR: 14346,\n  es_US: 21514,\n  es_VE: 8202,\n  et_EE: 1061,\n  eu_ES: 1069,\n  fa_IR: 1065,\n  fi_FI: 1035,\n  fil_PH: 1124,\n  fo_FO: 1080,\n  fr_BE: 2060,\n  fr_CA: 3084,\n  fr_CH: 4108,\n  fr_FR: 1036,\n  fr_LU: 5132,\n  fr_MC: 6156,\n  fy_NL: 1122,\n  ga_IE: 2108,\n  gbz_AF: 1164,\n  gl_ES: 1110,\n  gsw_FR: 1156,\n  gu_IN: 1095,\n  ha_NG: 1128,\n  he_IL: 1037,\n  hi_IN: 1081,\n  hr_BA: 4122,\n  hr_HR: 1050,\n  hu_HU: 1038,\n  hy_AM: 1067,\n  id_ID: 1057,\n  ii_CN: 1144,\n  is_IS: 1039,\n  it_CH: 2064,\n  it_IT: 1040,\n  iu_CA: 2141,\n  ja_JP: 1041,\n  ka_GE: 1079,\n  kh_KH: 1107,\n  kk_KZ: 1087,\n  kl_GL: 1135,\n  kn_IN: 1099,\n  ko_KR: 1042,\n  kok_IN: 1111,\n  ky_KG: 1088,\n  lb_LU: 1134,\n  lo_LA: 1108,\n  lt_LT: 1063,\n  lv_LV: 1062,\n  mi_NZ: 1153,\n  mk_MK: 1071,\n  ml_IN: 1100,\n  mn_CN: 2128,\n  mn_MN: 1104,\n  moh_CA: 1148,\n  mr_IN: 1102,\n  ms_BN: 2110,\n  ms_MY: 1086,\n  mt_MT: 1082,\n  my_MM: 1109,\n  nb_NO: 1044,\n  ne_NP: 1121,\n  nl_BE: 2067,\n  nl_NL: 1043,\n  nn_NO: 2068,\n  ns_ZA: 1132,\n  oc_FR: 1154,\n  or_IN: 1096,\n  pa_IN: 1094,\n  pl_PL: 1045,\n  ps_AF: 1123,\n  pt_BR: 1046,\n  pt_PT: 2070,\n  qut_GT: 1158,\n  quz_BO: 1131,\n  quz_EC: 2155,\n  quz_PE: 3179,\n  rm_CH: 1047,\n  ro_RO: 1048,\n  ru_RU: 1049,\n  rw_RW: 1159,\n  sa_IN: 1103,\n  sah_RU: 1157,\n  se_FI: 3131,\n  se_NO: 1083,\n  se_SE: 2107,\n  si_LK: 1115,\n  sk_SK: 1051,\n  sl_SI: 1060,\n  sma_NO: 6203,\n  sma_SE: 7227,\n  smj_NO: 4155,\n  smj_SE: 5179,\n  smn_FI: 9275,\n  sms_FI: 8251,\n  sq_AL: 1052,\n  sr_BA: 7194,\n  sr_SP: 3098,\n  sv_FI: 2077,\n  sv_SE: 1053,\n  sw_KE: 1089,\n  syr_SY: 1114,\n  ta_IN: 1097,\n  te_IN: 1098,\n  tg_TJ: 1064,\n  th_TH: 1054,\n  tk_TM: 1090,\n  tmz_DZ: 2143,\n  tn_ZA: 1074,\n  tr_TR: 1055,\n  tt_RU: 1092,\n  ug_CN: 1152,\n  uk_UA: 1058,\n  ur_IN: 2080,\n  ur_PK: 1056,\n  uz_UZ: 2115,\n  vi_VN: 1066,\n  wen_DE: 1070,\n  wo_SN: 1160,\n  xh_ZA: 1076,\n  yo_NG: 1130,\n  zh_CHS: 4,\n  zh_CHT: 31748,\n  zh_CN: 2052,\n  zh_HK: 3076,\n  zh_MO: 5124,\n  zh_SG: 4100,\n  zh_TW: 1028,\n  zu_ZA: 1077\n}\n\n// noinspection SpellCheckingInspection\nexport const langIdToName = {\n  ab: \"Abkhaz\",\n  aa: \"Afar\",\n  af: \"Afrikaans\",\n  ak: \"Akan\",\n  sq: \"Albanian\",\n  am: \"Amharic\",\n  ar: \"Arabic\",\n  an: \"Aragonese\",\n  hy: \"Armenian\",\n  as: \"Assamese\",\n  av: \"Avaric\",\n  ae: \"Avestan\",\n  ay: \"Aymara\",\n  az: \"Azerbaijani\",\n  bm: \"Bambara\",\n  ba: \"Bashkir\",\n  eu: \"Basque\",\n  be: \"Belarusian\",\n  bn: \"Bengali\",\n  bh: \"Bihari\",\n  bi: \"Bislama\",\n  bs: \"Bosnian\",\n  br: \"Breton\",\n  bg: \"Bulgarian\",\n  my: \"Burmese\",\n  ca: \"Catalan\",\n  ch: \"Chamorro\",\n  ce: \"Chechen\",\n  ny: \"Chichewa\",\n  zh: \"Chinese\",\n  cv: \"Chuvash\",\n  kw: \"Cornish\",\n  co: \"Corsican\",\n  cr: \"Cree\",\n  hr: \"Croatian\",\n  cs: \"Czech\",\n  da: \"Danish\",\n  dv: \"Divehi\",\n  nl: \"Dutch\",\n  dz: \"Dzongkha\",\n  en: \"English\",\n  eo: \"Esperanto\",\n  et: \"Estonian\",\n  ee: \"Ewe\",\n  fo: \"Faroese\",\n  fj: \"Fijian\",\n  fi: \"Finnish\",\n  fr: \"French\",\n  ff: \"Fula\",\n  gl: \"Galician\",\n  ka: \"Georgian\",\n  de: \"German\",\n  el: \"Greek\",\n  gn: \"Guaraní\",\n  gu: \"Gujarati\",\n  ht: \"Haitian\",\n  ha: \"Hausa\",\n  he: \"Hebrew\",\n  hz: \"Herero\",\n  hi: \"Hindi\",\n  ho: \"Hiri Motu\",\n  hu: \"Hungarian\",\n  ia: \"Interlingua\",\n  id: \"Indonesian\",\n  ie: \"Interlingue\",\n  ga: \"Irish\",\n  ig: \"Igbo\",\n  ik: \"Inupiaq\",\n  io: \"Ido\",\n  is: \"Icelandic\",\n  it: \"Italian\",\n  iu: \"Inuktitut\",\n  ja: \"Japanese\",\n  jv: \"Javanese\",\n  kl: \"Kalaallisut\",\n  kn: \"Kannada\",\n  kr: \"Kanuri\",\n  ks: \"Kashmiri\",\n  kk: \"Kazakh\",\n  km: \"Khmer\",\n  ki: \"Kikuyu\",\n  rw: \"Kinyarwanda\",\n  ky: \"Kyrgyz\",\n  kv: \"Komi\",\n  kg: \"Kongo\",\n  ko: \"Korean\",\n  ku: \"Kurdish\",\n  kj: \"Kwanyama\",\n  la: \"Latin\",\n  lb: \"Luxembourgish\",\n  lg: \"Ganda\",\n  li: \"Limburgish\",\n  ln: \"Lingala\",\n  lo: \"Lao\",\n  lt: \"Lithuanian\",\n  lu: \"Luba-Katanga\",\n  lv: \"Latvian\",\n  gv: \"Manx\",\n  mk: \"Macedonian\",\n  mg: \"Malagasy\",\n  ms: \"Malay\",\n  ml: \"Malayalam\",\n  mt: \"Maltese\",\n  mi: \"Māori\",\n  mr: \"Marathi\",\n  mh: \"Marshallese\",\n  mn: \"Mongolian\",\n  na: \"Nauru\",\n  nv: \"Navajo\",\n  nd: \"Northern Ndebele\",\n  ne: \"Nepali\",\n  ng: \"Ndonga\",\n  nb: \"Norwegian Bokmål\",\n  nn: \"Norwegian Nynorsk\",\n  no: \"Norwegian\",\n  ii: \"Nuosu\",\n  nr: \"Southern Ndebele\",\n  oc: \"Occitan\",\n  oj: \"Ojibwe\",\n  cu: \"Old Church Slavonic\",\n  om: \"Oromo\",\n  or: \"Oriya\",\n  os: \"Ossetian\",\n  pa: \"Panjabi\",\n  pi: \"Pāli\",\n  fa: \"Persian\",\n  pl: \"Polish\",\n  ps: \"Pashto\",\n  pt: \"Portuguese\",\n  qu: \"Quechua\",\n  rm: \"Romansh\",\n  rn: \"Kirundi\",\n  ro: \"Romanian\",\n  ru: \"Russian\",\n  sa: \"Sanskrit\",\n  sc: \"Sardinian\",\n  sd: \"Sindhi\",\n  se: \"Northern Sami\",\n  sm: \"Samoan\",\n  sg: \"Sango\",\n  sr: \"Serbian\",\n  gd: \"Gaelic\",\n  sn: \"Shona\",\n  si: \"Sinhala\",\n  sk: \"Slovak\",\n  sl: \"Slovene\",\n  so: \"Somali\",\n  st: \"Southern Sotho\",\n  es: \"Spanish\",\n  su: \"Sundanese\",\n  sw: \"Swahili\",\n  ss: \"Swati\",\n  sv: \"Swedish\",\n  ta: \"Tamil\",\n  te: \"Telugu\",\n  tg: \"Tajik\",\n  th: \"Thai\",\n  ti: \"Tigrinya\",\n  bo: \"Tibetan Standard\",\n  tk: \"Turkmen\",\n  tl: \"Tagalog\",\n  tn: \"Tswana\",\n  to: \"Tonga\",\n  tr: \"Turkish\",\n  ts: \"Tsonga\",\n  tt: \"Tatar\",\n  tw: \"Twi\",\n  ty: \"Tahitian\",\n  ug: \"Uyghur\",\n  uk: \"Ukrainian\",\n  ur: \"Urdu\",\n  uz: \"Uzbek\",\n  ve: \"Venda\",\n  vi: \"Vietnamese\",\n  vo: \"Volapük\",\n  wa: \"Walloon\",\n  cy: \"Welsh\",\n  wo: \"Wolof\",\n  fy: \"Western Frisian\",\n  xh: \"Xhosa\",\n  yi: \"Yiddish\",\n  yo: \"Yoruba\",\n  za: \"Zhuang\",\n  zu: \"Zulu\"\n}"], "sourceRoot": ""}