{"version": 3, "sources": ["../../src/util/macosVersion.ts"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;AAEA,MAAM,YAAY,GAAG,KAAI,eAAJ,EAAiB,YAAW;AAC/C,QAAM,IAAI,GAAG,MAAM,yBAAS,kDAAT,EAA6D,MAA7D,CAAnB;AACA,QAAM,OAAO,GAAG,8DAA8D,IAA9D,CAAmE,IAAnE,CAAhB;;AACA,MAAI,CAAC,OAAL,EAAc;AACZ,UAAM,IAAI,KAAJ,CAAU,iCAAV,CAAN;AACD;;AACD,aAAI,KAAJ,CAAU;AAAC,IAAA,OAAO,EAAE,OAAO,CAAC,CAAD;AAAjB,GAAV,EAAiC,eAAjC;;AACA,SAAO,KAAK,CAAC,OAAO,CAAC,CAAD,CAAR,CAAZ;AACD,CARoB,CAArB;;AAUA,SAAS,KAAT,CAAe,OAAf,EAA8B;AAC5B,SAAO,OAAO,CAAC,KAAR,CAAc,GAAd,EAAmB,MAAnB,KAA8B,CAA9B,GAAkC,GAAG,OAAO,IAA5C,GAAmD,OAA1D;AACD;;AAED,eAAe,+BAAf,CAA+C,KAA/C,EAA4D;AAC1D,SAAO,MAAM,GAAC,GAAP,CAAW,MAAM,YAAY,CAAC,KAA9B,EAAqC,KAAK,CAAC,KAAD,CAA1C,CAAP;AACD;;AAEK,SAAU,iBAAV,GAA2B;AAC/B;AACA,SAAO,OAAO,CAAC,QAAR,KAAqB,QAArB,IAAiC,MAAM,GAAC,GAAP,CAAW,oBAAX,EAAwB,QAAxB,CAAxC;AACD;;AAEM,eAAe,aAAf,GAA4B;AACjC,SAAO,OAAO,CAAC,QAAR,KAAqB,QAArB,KAAiC,MAAM,+BAA+B,CAAC,SAAD,CAAtE,CAAP;AACD;;AAEK,SAAU,eAAV,GAAyB;AAC7B,SAAO,OAAO,CAAC,QAAR,KAAqB,QAArB,IAAiC,MAAM,GAAC,GAAP,CAAW,oBAAX,EAAwB,QAAxB,CAAxC;AACD,C", "sourcesContent": ["import { readFile } from \"fs-extra\"\nimport { Lazy } from \"lazy-val\"\nimport * as semver from \"semver\"\nimport { log } from \"builder-util/out/log\"\nimport { release as osRelease } from \"os\"\n\nconst macOsVersion = new Lazy<string>(async () => {\n  const file = await readFile(\"/System/Library/CoreServices/SystemVersion.plist\", \"utf8\")\n  const matches = /<key>ProductVersion<\\/key>[\\s\\S]*<string>([\\d.]+)<\\/string>/.exec(file)\n  if (!matches) {\n    throw new Error(\"Couldn't find the macOS version\")\n  }\n  log.debug({version: matches[1]}, \"macOS version\")\n  return clean(matches[1])\n})\n\nfunction clean(version: string) {\n  return version.split(\".\").length === 2 ? `${version}.0` : version\n}\n\nasync function isOsVersionGreaterThanOrEqualTo(input: string) {\n  return semver.gte(await macOsVersion.value, clean(input))\n}\n\nexport function isMacOsHighSierra(): boolean {\n  // 17.7.0 === 10.13.6\n  return process.platform === \"darwin\" && semver.gte(osRelease(), \"17.7.0\")\n}\n\nexport async function isMacOsSierra() {\n  return process.platform === \"darwin\" && await isOsVersionGreaterThanOrEqualTo(\"10.12.0\")\n}\n\nexport function isMacOsCatalina() {\n  return process.platform === \"darwin\" && semver.gte(osRelease(), \"19.0.0\")\n}"], "sourceRoot": ""}