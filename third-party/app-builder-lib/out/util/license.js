"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getLicenseAssets = getLicenseAssets;
exports.getNotLocalizedLicenseFile = getNotLocalizedLicenseFile;
exports.getLicenseFiles = getLicenseFiles;

var path = _interopRequireWildcard(require("path"));

function _langs() {
  const data = require("./langs");

  _langs = function () {
    return data;
  };

  return data;
}

function _getRequireWildcardCache() { if (typeof WeakMap !== "function") return null; var cache = new WeakMap(); _getRequireWildcardCache = function () { return cache; }; return cache; }

function _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }

function getLicenseAssets(fileNames, packager) {
  return fileNames.sort((a, b) => {
    const aW = a.includes("_en") ? 0 : 100;
    const bW = b.includes("_en") ? 0 : 100;
    return aW === bW ? a.localeCompare(b) : aW - bW;
  }).map(file => {
    let lang = file.match(/_([^.]+)\./)[1];
    let langWithRegion;

    if (lang.includes("_")) {
      langWithRegion = lang;
      lang = langWithRegion.substring(0, lang.indexOf("_"));
    } else {
      lang = lang.toLowerCase();
      langWithRegion = (0, _langs().toLangWithRegion)(lang);
    }

    return {
      file: path.join(packager.buildResourcesDir, file),
      lang,
      langWithRegion,
      langName: _langs().langIdToName[lang]
    };
  });
}

async function getNotLocalizedLicenseFile(custom, packager, supportedExtension = ["rtf", "txt", "html"]) {
  const possibleFiles = [];

  for (const name of ["license", "eula"]) {
    for (const ext of supportedExtension) {
      possibleFiles.push(`${name}.${ext}`);
      possibleFiles.push(`${name.toUpperCase()}.${ext}`);
      possibleFiles.push(`${name}.${ext.toUpperCase()}`);
      possibleFiles.push(`${name.toUpperCase()}.${ext.toUpperCase()}`);
    }
  }

  return await packager.getResource(custom, ...possibleFiles);
}

async function getLicenseFiles(packager) {
  return getLicenseAssets((await packager.resourceList).filter(it => {
    const name = it.toLowerCase();
    return (name.startsWith("license_") || name.startsWith("eula_")) && (name.endsWith(".rtf") || name.endsWith(".txt"));
  }), packager);
} 
// __ts-babel@6.0.4
//# sourceMappingURL=license.js.map