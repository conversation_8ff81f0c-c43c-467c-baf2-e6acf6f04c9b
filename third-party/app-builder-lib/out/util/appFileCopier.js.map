{"version": 3, "sources": ["../../src/util/appFileCopier.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAGA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;;;AAEA,MAAM,wBAAwB,GAAG,GAAG,IAAI,CAAC,GAAG,mBAAmB,IAAI,CAAC,GAAG,EAAvE;AACA;;AACO,MAAM,8BAA8B,GAAG,WAAvC;;;AAED,SAAU,kBAAV,CAA6B,IAA7B,EAA2C,OAA3C,EAAmE;AACvE,MAAI,IAAI,KAAK,OAAO,CAAC,GAArB,EAA0B;AACxB,WAAO,OAAO,CAAC,WAAf;AACD,GAFD,MAGK;AACH,UAAM,GAAG,GAAG,OAAO,CAAC,GAApB;AACA,UAAM,IAAI,GAAG,OAAO,CAAC,WAArB;;AACA,QAAI,IAAI,CAAC,MAAL,GAAc,GAAG,CAAC,MAAlB,IAA4B,IAAI,CAAC,UAAL,CAAgB,GAAhB,CAA5B,IAAoD,IAAI,CAAC,GAAG,CAAC,MAAL,CAAJ,KAAqB,IAAI,CAAC,GAAlF,EAAuF;AACrF,aAAO,IAAI,GAAG,IAAI,CAAC,SAAL,CAAe,GAAG,CAAC,MAAnB,CAAd;AACD,KAFD,MAGK;AACH;AACA;AACA;AACA,UAAI,KAAK,GAAG,IAAI,CAAC,OAAL,CAAa,uCAAb,CAAZ;;AACA,UAAI,KAAK,GAAG,CAAR,IAAa,IAAI,CAAC,QAAL,CAAc,GAAG,IAAI,CAAC,GAAG,cAAzB,CAAjB,EAA2D;AACzD,QAAA,KAAK,GAAG,IAAI,CAAC,MAAL,GAAc,EAAtB;AACD;;AACD,UAAI,KAAK,GAAG,CAAZ,EAAe;AACb,cAAM,IAAI,KAAJ,CAAU,SAAS,IAAI,qCAAqC,OAAO,CAAC,GAAG,GAAvE,CAAN;AACD;;AACD,aAAO,IAAI,GAAG,IAAI,CAAC,SAAL,CAAe,KAAf,CAAd;AACD;AACF;AACF;;AAEM,eAAe,YAAf,CAA4B,OAA5B,EAAsD,QAAtD,EAA0E,WAA1E,EAAsG;AAC3G,QAAM,QAAQ,GAAG,OAAO,CAAC,QAAzB,CAD2G,CAE3G;;AACA,QAAM,WAAW,GAAG,KAAI,+BAAJ,EAAqB,QAAQ,CAAC,iBAA9B,CAApB;AACA,QAAM,iBAAiB,GAAG,IAAI,GAAJ,EAA1B;AAEA,QAAM,UAAU,GAAG,KAAI,gBAAJ,EAAe,IAAI,IAAG;AACvC;AACA,WAAO,EAAE,kCAAW,IAAX,KAAoB,IAAI,CAAC,QAAL,CAAc,OAAd,CAAtB,CAAP;AACD,GAHkB,EAGhB,WAHgB,CAAnB;AAIA,QAAM,KAAK,GAAgB,EAA3B;;AACA,OAAK,IAAI,CAAC,GAAG,CAAR,EAAW,CAAC,GAAG,OAAO,CAAC,KAAR,CAAc,MAAlC,EAA0C,CAAC,GAAG,CAA9C,EAAiD,CAAC,EAAlD,EAAsD;AACpD,UAAM,UAAU,GAAG,OAAO,CAAC,KAAR,CAAc,CAAd,CAAnB;AACA,UAAM,IAAI,GAAG,QAAQ,CAAC,GAAT,CAAa,UAAb,CAAb;;AACA,QAAI,IAAI,IAAI,IAAZ,EAAkB;AAChB;AACA;AACD;;AAED,UAAM,eAAe,GAAG,kBAAkB,CAAC,UAAD,EAAa,OAAb,CAA1C;;AACA,QAAI,IAAI,CAAC,cAAL,EAAJ,EAA2B;AACzB,MAAA,KAAK,CAAC,IAAN,CAAW;AAAC,QAAA,IAAI,EAAE,eAAP;AAAwB,QAAA,IAAI,EAAE,MAAM,yBAAS,UAAT;AAApC,OAAX;AACA;AACD;;AAED,UAAM,UAAU,GAAG,IAAI,CAAC,OAAL,CAAa,eAAb,CAAnB;;AACA,QAAI,CAAC,iBAAiB,CAAC,GAAlB,CAAsB,UAAtB,CAAL,EAAwC;AACtC,MAAA,iBAAiB,CAAC,GAAlB,CAAsB,UAAtB;AACA,YAAM,0BAAU,UAAV,CAAN;AACD;;AAED,IAAA,WAAW,CAAC,OAAZ,CAAoB,UAAU,CAAC,IAAX,CAAgB,UAAhB,EAA4B,eAA5B,EAA6C,IAA7C,CAApB;;AACA,QAAI,WAAW,CAAC,KAAZ,CAAkB,MAAlB,GAA2B,uBAA/B,EAAkD;AAChD,YAAM,WAAW,CAAC,UAAZ,EAAN;AACD;AACF;;AAED,MAAI,WAAW,CAAC,KAAZ,CAAkB,MAAlB,GAA2B,CAA/B,EAAkC;AAChC,UAAM,WAAW,CAAC,UAAZ,EAAN;AACD;;AACD,MAAI,KAAK,CAAC,MAAN,GAAe,CAAnB,EAAsB;AACpB,UAAM,uBAAgB,GAAhB,CAAoB,KAApB,EAA2B,EAAE,IAAI,wBAAQ,EAAE,CAAC,IAAX,EAAiB,EAAE,CAAC,IAApB,CAAjC,EAA4D,iBAA5D,CAAN;AACD;AACF,C,CAYD;;;AACO,eAAe,cAAf,CAA8B,WAA9B,EAA4D,OAA5D,EAAoF;AACzF,MAAI,WAAW,IAAI,IAAnB,EAAyB;AACvB;AACD;;AAED,MAAI,gBAAgB,GAAG,OAAO,CAAC,gBAA/B;;AACA,MAAI,OAAO,CAAC,gBAAR,IAA4B,IAAhC,EAAsC;AACpC,IAAA,gBAAgB,GAAG,IAAI,GAAJ,EAAnB;AACA,IAAA,OAAO,CAAC,gBAAR,GAA2B,gBAA3B;AACD;;AAED,QAAM,QAAQ,GAAG,OAAO,CAAC,QAAzB;AACA,QAAM,uBAAgB,MAAhB,CAAuB,OAAO,CAAC,KAA/B,EAAsC,CAAC,EAAD,EAAK,KAAL,KAAc;AACxD,UAAM,QAAQ,GAAG,QAAQ,CAAC,GAAT,CAAa,EAAb,CAAjB;;AACA,QAAI,QAAQ,IAAI,IAAZ,IAAoB,CAAC,QAAQ,CAAC,MAAT,EAAzB,EAA4C;AAC1C,aAAO,KAAP;AACD;;AAED,UAAM,gBAAgB,GAAG,WAAW,CAAC,EAAD,CAApC;;AACA,QAAI,gBAAgB,IAAI,IAAxB,EAA8B;AAC5B,aAAO,KAAP;AACD;;AAED,QAAI,OAAO,gBAAP,KAA4B,QAA5B,IAAwC,UAAU,gBAAtD,EAAwE;AACtE,aAAQ,gBAAiC,CACtC,IADK,CACA,EAAE,IAAG;AACT,YAAI,EAAE,IAAI,IAAV,EAAgB;AACd,UAAA,gBAAkB,CAAC,GAAnB,CAAuB,KAAvB,EAA8B,EAA9B;AACD;;AACD,eAAO,KAAP;AACD,OANK,CAAR;AAOD;;AACD,IAAA,gBAAkB,CAAC,GAAnB,CAAuB,KAAvB,EAA8B,gBAA9B;AACA,WAAO,KAAP;AACD,GAtBK,EAsBH,iBAtBG,CAAN;AAuBD;;AAEM,eAAe,eAAf,CAA+B,QAA/B,EAA6D,WAA7D,EAAkG,gBAAlG,EAA2I,iBAA3I,EAAqK;AAC1K,QAAM,QAAQ,GAA2B,EAAzC;AACA,QAAM,QAAQ,GAAG,gBAAgB,CAAC,IAAlC;;AAEA,OAAK,MAAM,OAAX,IAAsB,QAAtB,EAAgC;AAC9B,UAAM,UAAU,GAAG,KAAI,8BAAJ,EAAkB,OAAlB,EAA2B,QAA3B,CAAnB;AAEA,UAAM,QAAQ,GAAG,MAAM,sBAAW,OAAO,CAAC,IAAnB,CAAvB;;AACA,QAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,yBAAI,KAAJ,CAAU;AAAC,QAAA,SAAS,EAAE,OAAO,CAAC,IAApB;AAA0B,QAAA,MAAM,EAAE;AAAlC,OAAV,EAA8D,iBAA9D;;AACA;AACD;;AAED,UAAM,KAAK,GAAG,MAAM,gBAAK,OAAO,CAAC,IAAb,EAAmB,UAAU,CAAC,MAA9B,EAAsC,UAAtC,CAApB;AACA,UAAM,QAAQ,GAAG,UAAU,CAAC,QAA5B;AACA,IAAA,QAAQ,CAAC,IAAT,CAAc,eAAe,CAAC;AAAC,MAAA,GAAG,EAAE,OAAO,CAAC,IAAd;AAAoB,MAAA,KAApB;AAA2B,MAAA,QAA3B;AAAqC,MAAA,WAAW,EAAE,OAAO,CAAC;AAA1D,KAAD,CAA7B;AACD;;AAED,MAAI,iBAAJ,EAAuB;AACrB;AACA,IAAA,QAAQ,CAAC,OAAT,CAAiB,MAAM,2BAA2B,CAAC,QAAQ,CAAC,CAAD,CAAT,EAAc,QAAd,CAAlD;AACD;;AACD,SAAO,QAAP;AACD;;AAED,SAAS,yBAAT,CAAmC,gBAAnC,EAA0E;AACxE;AACA,QAAM,MAAM,GAAG,CAAC,IAAD,EAAO,MAAP,EAAe,MAAf,CAAsB,4BAAa,KAAb,CAAmB,GAAnB,EAAwB,GAAxB,CAA4B,EAAE,IAAI,IAAI,EAAE,EAAxC,CAAtB,CAAf;;AACA,MAAI,gBAAgB,CAAC,MAAjB,CAAwB,UAAxB,KAAuC,IAA3C,EAAiD;AAC/C,IAAA,MAAM,CAAC,IAAP,CAAY,MAAZ;AACD;;AACD,MAAI,gBAAgB,CAAC,QAAjB,KAA8B,iBAAS,OAA3C,EAAoD;AAClD;AACA,IAAA,MAAM,CAAC,IAAP,CAAY,MAAZ;AACA,IAAA,MAAM,CAAC,IAAP,CAAY,MAAZ;AACD;;AACD,SAAO,MAAP;AACD;;AAED,SAAS,eAAT,CAAyB,OAAzB,EAAiD;AAC/C,MAAI,OAAO,CAAC,GAAR,IAAe,IAAf,IAAuB,OAAO,CAAC,GAAR,CAAY,MAAZ,KAAuB,CAAlD,EAAqD;AACnD,UAAM,IAAI,KAAJ,CAAU,sBAAV,CAAN;AACD;;AACD,SAAO,OAAP;AACD;AAED;;;AACO,eAAe,yBAAf,CAAyC,gBAAzC,EAAkF,WAAlF,EAA0G;AAC/G,QAAM,IAAI,GAAG,MAAM,gBAAgB,CAAC,IAAjB,CAAsB,qBAAtB,CAA4C,gBAAgB,CAAC,QAA7D,EAAuE,KAA1F;AACA,QAAM,sBAAsB,GAAG,yBAAyB,CAAC,gBAAD,CAAxD,CAF+G,CAG/G;;AACA,QAAM,MAAM,GAAG,IAAI,KAAJ,EAAf;AACA,MAAI,KAAK,GAAG,CAAZ;;AACA,OAAK,MAAM,IAAX,IAAmB,IAAnB,EAAyB;AACvB,UAAM,MAAM,GAAG,IAAI,CAAC,GAApB;AACA,UAAM,WAAW,GAAG,kBAAkB,CAAC,MAAD,EAAS;AAAC,MAAA,GAAG,EAAE,WAAW,CAAC,IAAlB;AAAwB,MAAA,WAAW,EAAE,WAAW,CAAC,EAAjD;AAAqD,MAAA,KAAK,EAAE,EAA5D;AAAgE,MAAA,QAAQ,EAAE;AAA1E,KAAT,CAAtC,CAFuB,CAIvB;AACA;;AACA,UAAM,OAAO,GAAG,KAAI,0BAAJ,EAAgB,IAAI,CAAC,OAAL,CAAa,MAAb,CAAhB,EAAsC,WAAtC,EAAmD,WAAW,CAAC,aAA/D,EAA8E,WAAW,CAAC,QAA1F,CAAhB;AACA,UAAM,MAAM,GAAG,KAAI,4CAAJ,EAAyB,OAAzB,EAAkC,gBAAgB,CAAC,IAAnD,CAAf;AACA,UAAM,KAAK,GAAG,MAAM,MAAM,CAAC,kBAAP,CAA0B,MAA1B,EAAkC,IAAI,CAAC,IAAL,CAAU,GAAV,CAAc,EAAE,IAAI,EAAE,CAAC,IAAvB,CAAlC,EAAgE,sBAAhE,CAApB;AACA,IAAA,MAAM,CAAC,KAAK,EAAN,CAAN,GAAkB,eAAe,CAAC;AAAC,MAAA,GAAG,EAAE,MAAN;AAAc,MAAA,WAAd;AAA2B,MAAA,KAA3B;AAAkC,MAAA,QAAQ,EAAE,MAAM,CAAC;AAAnD,KAAD,CAAjC;AACD;;AACD,SAAO,MAAP;AACD;;AAED,eAAe,2BAAf,CAA2C,WAA3C,EAAyE,QAAzE,EAA2F;AACzF,qBAAI,IAAJ,CAAS,kCAAT;;AAEA,QAAM,oBAAoB,GAAG,MAAM,QAAQ,CAAC,cAAT,CAAwB,UAAxB,CAAmC;AAAC,IAAA,MAAM,EAAE;AAAT,GAAnC,CAAnC;AACA,QAAM,QAAQ,GAAG,IAAI,CAAC,IAAL,CAAU,oBAAV,EAAgC,QAAhC,CAAjB,CAJyF,CAKzF;;AACA,QAAM,0BAAU,QAAV,CAAN;AACA,QAAM,YAAY,GAAG,MAAM,mDAA2B,WAAW,CAAC,GAAvC,EAA4C,QAA5C,CAA3B;AACA,QAAM,cAAc,GAAG,WAAW,CAAC,GAAZ,CAAgB,MAAhB,GAAyB,CAAhD,CARyF,CASzF;;AACA,QAAM,uBAAgB,GAAhB,CAAoB,WAAW,CAAC,KAAhC,EAAuC,IAAI,IAAG;AAClD,QAAI,IAAI,CAAC,QAAL,CAAc,uCAAd,KAAuC,IAAI,CAAC,QAAL,CAAc,wBAAd,CAAvC,IACC,CAAC,IAAI,CAAC,QAAL,CAAc,IAAI,CAAC,GAAnB,EAAwB,cAAxB,CADF,CAC0C;AAD1C,OAEC,CAAC,WAAW,CAAC,QAAZ,CAAqB,GAArB,CAAyB,IAAzB,EAAgC,MAAhC,EAFN,EAEgD;AAC9C,aAAO,IAAP;AACD;;AACD,WAAO,YAAY,CAAC,OAAb,CAAqB,IAArB,EACJ,IADI,CACC,MAAM,IADP,CAAP;AAED,GARK,EAQH,iBARG,CAAN;AAUA,QAAM,YAAY,CAAC,iBAAb,EAAN;AAEA,QAAM,QAAQ,GAAG,IAAI,GAAJ,EAAjB;AACA,QAAM,UAAU,GAAG,MAAM,gBAAK,QAAL,EAAe,IAAI,IAAI,CAAC,IAAI,CAAC,UAAL,CAAgB,GAAhB,CAAxB,EAA8C;AACrE,IAAA,OAAO,EAAE,CAAC,IAAD,EAAO,QAAP,KAAmB;AAC1B,UAAI,QAAQ,CAAC,MAAT,EAAJ,EAAuB;AACrB,QAAA,QAAQ,CAAC,GAAT,CAAa,IAAb,EAAmB,QAAnB;AACD;;AACD,aAAO,IAAP;AACD;AANoE,GAA9C,CAAzB,CAvByF,CAgCzF;;AACA,QAAM,QAAQ,GAAG,GAAG,WAAW,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,8BAA8B,EAA/E;AACA,EAAA,WAAW,CAAC,KAAZ,CAAkB,IAAlB,CAAuB,QAAvB;AACA,EAAA,WAAW,CAAC,QAAZ,CAAqB,GAArB,CAAyB,QAAzB,EAAmC;AAAC,IAAA,MAAM,EAAE,MAAM,IAAf;AAAqB,IAAA,WAAW,EAAE,MAAM,KAAxC;AAA+C,IAAA,cAAc,EAAE,MAAM;AAArE,GAAnC;;AACA,MAAI,WAAW,CAAC,gBAAZ,IAAgC,IAApC,EAA0C;AACxC,IAAA,WAAW,CAAC,gBAAZ,GAA+B,IAAI,GAAJ,EAA/B;AACD;;AACD,EAAA,WAAW,CAAC,gBAAZ,CAA6B,GAA7B,CAAiC,WAAW,CAAC,KAAZ,CAAkB,MAAlB,GAA2B,CAA5D,EAA+D;;kFAEiB,QAAQ,CAAC,QAAT,CAAkB,IAAlB,IAA0B,OAAO;CAFjH;AAIA,SAAO;AAAC,IAAA,GAAG,EAAE,oBAAN;AAA4B,IAAA,KAAK,EAAE,UAAnC;AAA+C,IAAA,QAA/C;AAAyD,IAAA,WAAW,EAAE,WAAW,CAAC;AAAlF,GAAP;AACD,C", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { AsyncTaskManager, log } from \"builder-util\"\nimport { CONCURRENCY, FileCopier, FileTransformer, Link, MAX_FILE_REQUESTS, statOrNull, walk } from \"builder-util/out/fs\"\nimport { ensureDir, readlink, Stats, symlink } from \"fs-extra\"\nimport * as path from \"path\"\nimport { isLibOrExe } from \"../asar/unpackDetector\"\nimport { Platform } from \"../core\"\nimport { excludedExts, FileMatcher } from \"../fileMatcher\"\nimport { createElectronCompilerHost, NODE_MODULES_PATTERN } from \"../fileTransformer\"\nimport { Packager } from \"../packager\"\nimport { PlatformPackager } from \"../platformPackager\"\nimport { AppFileWalker } from \"./AppFileWalker\"\nimport { NodeModuleCopyHelper } from \"./NodeModuleCopyHelper\"\n\nconst BOWER_COMPONENTS_PATTERN = `${path.sep}bower_components${path.sep}`\n/** @internal */\nexport const ELECTRON_COMPILE_SHIM_FILENAME = \"__shim.js\"\n\nexport function getDestinationPath(file: string, fileSet: ResolvedFileSet) {\n  if (file === fileSet.src) {\n    return fileSet.destination\n  }\n  else {\n    const src = fileSet.src\n    const dest = fileSet.destination\n    if (file.length > src.length && file.startsWith(src) && file[src.length] === path.sep) {\n      return dest + file.substring(src.length)\n    }\n    else {\n      // hoisted node_modules\n      // not lastIndexOf, to ensure that nested module (top-level module depends on) copied to parent node_modules, not to top-level directory\n      // project https://github.com/angexis/punchcontrol/commit/cf929aba55c40d0d8901c54df7945e1d001ce022\n      let index = file.indexOf(NODE_MODULES_PATTERN)\n      if (index < 0 && file.endsWith(`${path.sep}node_modules`)) {\n        index = file.length - 13\n      }\n      if (index < 0) {\n        throw new Error(`File \"${file}\" not under the source directory \"${fileSet.src}\"`)\n      }\n      return dest + file.substring(index)\n    }\n  }\n}\n\nexport async function copyAppFiles(fileSet: ResolvedFileSet, packager: Packager, transformer: FileTransformer) {\n  const metadata = fileSet.metadata\n  // search auto unpacked dir\n  const taskManager = new AsyncTaskManager(packager.cancellationToken)\n  const createdParentDirs = new Set<string>()\n\n  const fileCopier = new FileCopier(file => {\n    // https://github.com/electron-userland/electron-builder/issues/3038\n    return !(isLibOrExe(file) || file.endsWith(\".node\"))\n  }, transformer)\n  const links: Array<Link> = []\n  for (let i = 0, n = fileSet.files.length; i < n; i++) {\n    const sourceFile = fileSet.files[i]\n    const stat = metadata.get(sourceFile)\n    if (stat == null) {\n      // dir\n      continue\n    }\n\n    const destinationFile = getDestinationPath(sourceFile, fileSet)\n    if (stat.isSymbolicLink()) {\n      links.push({file: destinationFile, link: await readlink(sourceFile)})\n      continue\n    }\n\n    const fileParent = path.dirname(destinationFile)\n    if (!createdParentDirs.has(fileParent)) {\n      createdParentDirs.add(fileParent)\n      await ensureDir(fileParent)\n    }\n\n    taskManager.addTask(fileCopier.copy(sourceFile, destinationFile, stat))\n    if (taskManager.tasks.length > MAX_FILE_REQUESTS) {\n      await taskManager.awaitTasks()\n    }\n  }\n\n  if (taskManager.tasks.length > 0) {\n    await taskManager.awaitTasks()\n  }\n  if (links.length > 0) {\n    await BluebirdPromise.map(links, it => symlink(it.link, it.file), CONCURRENCY)\n  }\n}\n\n// os path separator is used\nexport interface ResolvedFileSet {\n  src: string\n  destination: string\n\n  files: Array<string>\n  metadata: Map<string, Stats>\n  transformedFiles?: Map<number, string | Buffer> | null\n}\n\n// used only for ASAR, if no asar, file transformed on the fly\nexport async function transformFiles(transformer: FileTransformer, fileSet: ResolvedFileSet): Promise<void> {\n  if (transformer == null) {\n    return\n  }\n\n  let transformedFiles = fileSet.transformedFiles\n  if (fileSet.transformedFiles == null) {\n    transformedFiles = new Map()\n    fileSet.transformedFiles = transformedFiles\n  }\n\n  const metadata = fileSet.metadata\n  await BluebirdPromise.filter(fileSet.files, (it, index) => {\n    const fileStat = metadata.get(it)\n    if (fileStat == null || !fileStat.isFile()) {\n      return false\n    }\n\n    const transformedValue = transformer(it)\n    if (transformedValue == null) {\n      return false\n    }\n\n    if (typeof transformedValue === \"object\" && \"then\" in transformedValue) {\n      return (transformedValue as Promise<any>)\n        .then(it => {\n          if (it != null) {\n            transformedFiles!!.set(index, it)\n          }\n          return false\n        })\n    }\n    transformedFiles!!.set(index, transformedValue as string | Buffer)\n    return false\n  }, CONCURRENCY)\n}\n\nexport async function computeFileSets(matchers: Array<FileMatcher>, transformer: FileTransformer | null, platformPackager: PlatformPackager<any>, isElectronCompile: boolean): Promise<Array<ResolvedFileSet>> {\n  const fileSets: Array<ResolvedFileSet> = []\n  const packager = platformPackager.info\n\n  for (const matcher of matchers) {\n    const fileWalker = new AppFileWalker(matcher, packager)\n\n    const fromStat = await statOrNull(matcher.from)\n    if (fromStat == null) {\n      log.debug({directory: matcher.from, reason: \"doesn't exist\"}, `skipped copying`)\n      continue\n    }\n\n    const files = await walk(matcher.from, fileWalker.filter, fileWalker)\n    const metadata = fileWalker.metadata\n    fileSets.push(validateFileSet({src: matcher.from, files, metadata, destination: matcher.to}))\n  }\n\n  if (isElectronCompile) {\n    // cache files should be first (better IO)\n    fileSets.unshift(await compileUsingElectronCompile(fileSets[0], packager))\n  }\n  return fileSets\n}\n\nfunction getNodeModuleExcludedExts(platformPackager: PlatformPackager<any>) {\n  // do not exclude *.h files (https://github.com/electron-userland/electron-builder/issues/2852)\n  const result = [\".o\", \".obj\"].concat(excludedExts.split(\",\").map(it => `.${it}`))\n  if (platformPackager.config.includePdb !== true) {\n    result.push(\".pdb\")\n  }\n  if (platformPackager.platform !== Platform.WINDOWS) {\n    // https://github.com/electron-userland/electron-builder/issues/1738\n    result.push(\".dll\")\n    result.push(\".exe\")\n  }\n  return result\n}\n\nfunction validateFileSet(fileSet: ResolvedFileSet): ResolvedFileSet {\n  if (fileSet.src == null || fileSet.src.length === 0) {\n    throw new Error(\"fileset src is empty\")\n  }\n  return fileSet\n}\n\n/** @internal */\nexport async function computeNodeModuleFileSets(platformPackager: PlatformPackager<any>, mainMatcher: FileMatcher): Promise<Array<ResolvedFileSet>> {\n  const deps = await platformPackager.info.getNodeDependencyInfo(platformPackager.platform).value\n  const nodeModuleExcludedExts = getNodeModuleExcludedExts(platformPackager)\n  // serial execution because copyNodeModules is concurrent and so, no need to increase queue/pressure\n  const result = new Array<ResolvedFileSet>()\n  let index = 0\n  for (const info of deps) {\n    const source = info.dir\n    const destination = getDestinationPath(source, {src: mainMatcher.from, destination: mainMatcher.to, files: [], metadata: null as any})\n\n    // use main matcher patterns, so, user can exclude some files in such hoisted node modules\n    // source here includes node_modules, but pattern base should be without because users expect that pattern \"!node_modules/loot-core/src{,/**/*}\" will work\n    const matcher = new FileMatcher(path.dirname(source), destination, mainMatcher.macroExpander, mainMatcher.patterns)\n    const copier = new NodeModuleCopyHelper(matcher, platformPackager.info)\n    const files = await copier.collectNodeModules(source, info.deps.map(it => it.name), nodeModuleExcludedExts)\n    result[index++] = validateFileSet({src: source, destination, files, metadata: copier.metadata})\n  }\n  return result\n}\n\nasync function compileUsingElectronCompile(mainFileSet: ResolvedFileSet, packager: Packager): Promise<ResolvedFileSet> {\n  log.info(\"compiling using electron-compile\")\n\n  const electronCompileCache = await packager.tempDirManager.getTempDir({prefix: \"electron-compile-cache\"})\n  const cacheDir = path.join(electronCompileCache, \".cache\")\n  // clear and create cache dir\n  await ensureDir(cacheDir)\n  const compilerHost = await createElectronCompilerHost(mainFileSet.src, cacheDir)\n  const nextSlashIndex = mainFileSet.src.length + 1\n  // pre-compute electron-compile to cache dir - we need to process only subdirectories, not direct files of app dir\n  await BluebirdPromise.map(mainFileSet.files, file => {\n    if (file.includes(NODE_MODULES_PATTERN) || file.includes(BOWER_COMPONENTS_PATTERN)\n      || !file.includes(path.sep, nextSlashIndex) // ignore not root files\n      || !mainFileSet.metadata.get(file)!.isFile()) {\n      return null\n    }\n    return compilerHost.compile(file)\n      .then(() => null)\n  }, CONCURRENCY)\n\n  await compilerHost.saveConfiguration()\n\n  const metadata = new Map<string, Stats>()\n  const cacheFiles = await walk(cacheDir, file => !file.startsWith(\".\"), {\n    consume: (file, fileStat) => {\n      if (fileStat.isFile()) {\n        metadata.set(file, fileStat)\n      }\n      return null\n    }\n  })\n\n  // add shim\n  const shimPath = `${mainFileSet.src}${path.sep}${ELECTRON_COMPILE_SHIM_FILENAME}`\n  mainFileSet.files.push(shimPath)\n  mainFileSet.metadata.set(shimPath, {isFile: () => true, isDirectory: () => false, isSymbolicLink: () => false} as any)\n  if (mainFileSet.transformedFiles == null) {\n    mainFileSet.transformedFiles = new Map()\n  }\n  mainFileSet.transformedFiles.set(mainFileSet.files.length - 1, `\n'use strict';\nrequire('electron-compile').init(__dirname, require('path').resolve(__dirname, '${packager.metadata.main || \"index\"}'), true);\n`)\n  return {src: electronCompileCache, files: cacheFiles, metadata, destination: mainFileSet.destination}\n}"], "sourceRoot": ""}