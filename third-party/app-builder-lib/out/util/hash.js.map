{"version": 3, "sources": ["../../src/util/hash.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AAEM,SAAU,QAAV,CAAmB,IAAnB,EAAiC,SAAA,GAAoB,QAArD,EAA+D,QAAA,GAA6B,QAA5F,EAAsG,OAAtG,EAAmH;AACvH,SAAO,IAAI,OAAJ,CAAoB,CAAC,OAAD,EAAU,MAAV,KAAoB;AAC7C,UAAM,IAAI,GAAG,0BAAW,SAAX,CAAb;AACA,IAAA,IAAI,CACD,EADH,CACM,OADN,EACe,MADf,EAEG,WAFH,CAEe,QAFf;AAIA,8BAAiB,IAAjB,EAAuB,EAAC,GAAG,OAAJ;AAAa,MAAA,aAAa,EAAE,OAAO;AAAK;;AAAxC,KAAvB,EACG,EADH,CACM,OADN,EACe,MADf,EAEG,EAFH,CAEM,KAFN,EAEa,MAAK;AACd,MAAA,IAAI,CAAC,GAAL;AACA,MAAA,OAAO,CAAC,IAAI,CAAC,IAAL,EAAD,CAAP;AACD,KALH,EAMG,IANH,CAMQ,IANR,EAMc;AAAC,MAAA,GAAG,EAAE;AAAN,KANd;AAOD,GAbM,CAAP;AAcD,C", "sourcesContent": ["import { createHash } from \"crypto\"\nimport { createReadStream } from \"fs\"\n\nexport function hashFile(file: string, algorithm: string = \"sha512\", encoding: \"base64\" | \"hex\" = \"base64\", options?: any) {\n  return new Promise<string>((resolve, reject) => {\n    const hash = createHash(algorithm)\n    hash\n      .on(\"error\", reject)\n      .setEncoding(encoding)\n\n    createReadStream(file, {...options, highWaterMark: 1024 * 1024 /* better to use more memory but hash faster */})\n      .on(\"error\", reject)\n      .on(\"end\", () => {\n        hash.end()\n        resolve(hash.read() as string)\n      })\n      .pipe(hash, {end: false})\n  })\n}"], "sourceRoot": ""}