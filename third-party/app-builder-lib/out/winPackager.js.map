{"version": 3, "sources": ["../src/winPackager.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAGA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;;;AAEM,MAAO,WAAP,SAA2B,oCAA3B,CAAiE;AA4FrE,EAAA,WAAA,CAAY,IAAZ,EAA0B;AACxB,UAAM,IAAN,EAAY,iBAAS,OAArB;AA5FO,SAAA,OAAA,GAAU,KAAI,eAAJ,EAAgE,MAAK;AACtF,YAAM,4BAA4B,GAAG,KAAK,4BAA1C;;AACA,UAAI,4BAA4B,CAAC,sBAA7B,IAAuD,IAAvD,IAA+D,4BAA4B,CAAC,eAA7B,IAAgD,IAAnH,EAAyH;AACvH,eAAO,KAAK,EAAL,CAAQ,KAAR,CACJ,IADI,CACC,EAAE,IAAI,oDAA4B,4BAA5B,EAA0D,EAA1D,CADP,EAEJ,KAFI,CAEE,CAAC,IAAG;AACT;AACA,cAAI,4BAA4B,CAAC,IAA7B,IAAqC,IAAzC,EAA+C;AAC7C,kBAAM,CAAN;AACD,WAFD,MAGK;AACH,+BAAI,KAAJ,CAAU;AAAC,cAAA,KAAK,EAAE;AAAR,aAAV,EAAsB,mCAAtB;;AACA,mBAAO,IAAP;AACD;AACF,SAXI,CAAP;AAYD;;AAED,YAAM,eAAe,GAAG,4BAA4B,CAAC,eAArD;;AACA,UAAI,eAAe,IAAI,IAAvB,EAA6B;AAC3B,cAAM,mBAAmB,GAAG,KAAK,cAAL,EAA5B;AACA,eAAO,OAAO,CAAC,OAAR,CAAgB;AACrB,UAAA,IAAI,EAAE,eADe;AAErB,UAAA,QAAQ,EAAE,mBAAmB,IAAI,IAAvB,GAA8B,IAA9B,GAAqC,mBAAmB,CAAC,IAApB;AAF1B,SAAhB,CAAP;AAID;;AAED,YAAM,OAAO,GAAG,KAAK,UAAL,CAAgB,cAAhB,CAAhB;;AACA,UAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,eAAO,OAAO,CAAC,OAAR,CAAgB,IAAhB,CAAP;AACD;;AAED,aAAO,qCAAoB,OAApB,EAA6B,KAAK,IAAL,CAAU,cAAvC,EAAuD,KAAK,UAA5D,EACL;AADK,OAEJ,KAFI,CAEE,CAAC,IAAG;AACT,YAAI,CAAC,YAAY,wCAAjB,EAA4C;AAC1C,gBAAM,KAAI,wCAAJ,EAA8B,oDAAoD,CAAC,CAAC,OAAO,EAA3F,CAAN;AACD,SAFD,MAGK;AACH,gBAAM,CAAN;AACD;AACF,OATI,EAUJ,IAVI,CAUC,IAAI,IAAG;AACX,eAAO;AACL,UAAA,IAAI,EAAE,IADD;AAEL,UAAA,QAAQ,EAAE,KAAK,cAAL;AAFL,SAAP;AAID,OAfI,CAAP;AAgBD,KA/CkB,CAAV;AAiDD,SAAA,SAAA,GAAY,KAAI,eAAJ,EAAS,MAAM,KAAK,gBAAL,CAAsB,KAAtB,CAAf,CAAZ;AAEC,SAAA,EAAA,GAAK,KAAI,eAAJ,EAAoB,MAAM,OAAO,CAAC,QAAR,KAAqB,OAArB,GAA+B,OAAO,CAAC,OAAR,CAAgB,KAAI,eAAJ,GAAhB,CAA/B,GAAkE,wBAAa,KAAK,WAAlB,CAA5F,CAAL;AAEA,SAAA,qBAAA,GAAwB,KAAI,eAAJ,EAA+B,YAAW;AACzE,YAAM,aAAa,GAAI,KAAK,4BAAL,CAA2D,aAAlF;;AACA,UAAI,aAAa,KAAK,IAAtB,EAA4B;AAC1B,eAAO,IAAP;AACD,OAFD,MAGK,IAAI,aAAa,IAAI,IAArB,EAA2B;AAC9B,eAAO,4BAAQ,aAAR,CAAP;AACD;;AAED,YAAM,QAAQ,GAAG,MAAM,KAAK,YAAL,CAAkB,KAAzC;AACA,aAAO,QAAQ,IAAI,IAAZ,GAAmB,IAAnB,GAA0B,CAAC,QAAQ,CAAC,UAAV,CAAjC;AACD,KAXgC,CAAxB;AAaA,SAAA,YAAA,GAAe,KAAI,eAAJ,EAAiC,YAAW;AAClE,YAAM,OAAO,GAAG,MAAM,KAAK,OAAL,CAAa,KAAnC;;AACA,UAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,eAAO,IAAP;AACD;;AAED,UAAI,aAAa,OAAjB,EAA0B;AACxB,cAAM,wBAAwB,GAAI,OAAoC,CAAC,OAAvE;AACA,eAAO;AACL,UAAA,UAAU,EAAE,mCAAQ,wBAAR,EAAkC,GAAlC,CAAsC,IAAtC,CADP;AAEL,UAAA;AAFK,SAAP;AAID;;AAED,YAAM,OAAO,GAAI,OAA+B,CAAC,IAAjD;;AACA,UAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,eAAO,IAAP;AACD;;AACD,aAAO,MAAM,oCAAY,OAAZ,EAAsB,OAA+B,CAAC,QAAhC,IAA4C,EAAlE,CAAb;AACD,KAnBuB,CAAf;AA2BR;;AAND,MAAI,8BAAJ,GAAkC;AAChC,WAAO,KAAK,4BAAL,CAAkC,yBAAlC,KAAgE,KAAvE;AACD;;AAMD,MAAI,aAAJ,GAAiB;AACf,WAAO,CAAC,MAAD,CAAP;AACD;;AAES,EAAA,gBAAgB,GAAA;AACxB,WAAO,uCAAc,uCAAc,KAAK,4BAAL,CAAkC,mBAAhD,EAAqE,OAAO,CAAC,GAAR,CAAY,oBAAjF,CAAd,EAAsH,MAAM,gBAAN,EAAtH,CAAP;AACD;;AAED,EAAA,aAAa,CAAC,OAAD,EAAyB,MAAzB,EAA4F;AACvG,QAAI,iBAAJ;;AACA,UAAM,oBAAoB,GAAG,MAAK;AAChC,UAAI,iBAAiB,IAAI,IAAzB,EAA+B;AAC7B,QAAA,iBAAiB,GAAG,KAAI,6BAAJ,GAApB;AACD;;AACD,aAAO,iBAAP;AACD,KALD;;AAOA,QAAI,MAAJ;;AACA,UAAM,SAAS,GAAG,MAAK;AACrB,UAAI,MAAM,IAAI,IAAd,EAAoB;AAClB,QAAA,MAAM,GAAG,KAAI,4BAAJ,EAAqB,oBAAoB,EAAzC,CAAT;AACD;;AACD,aAAO,MAAP;AACD,KALD;;AAOA,SAAK,MAAM,IAAX,IAAmB,OAAnB,EAA4B;AAC1B,UAAI,IAAI,KAAK,kBAAb,EAAyB;AACvB;AACD;;AAED,UAAI,IAAI,KAAK,MAAT,IAAmB,IAAI,KAAK,UAAhC,EAA4C;AAC1C,QAAA,MAAM,CAAC,IAAD,EAAO,MAAM,IAAI,KAAI,wBAAJ,EAAe,IAAf,EAAqB,MAArB,EAA6B,IAA7B,EAAmC,SAAS,EAA5C,CAAjB,CAAN;AACD,OAFD,MAGK,IAAI,IAAI,KAAK,UAAb,EAAyB;AAC5B;AACA,QAAA,MAAM,CAAC,IAAD,EAAO,MAAM,IAAI,KAAI,wCAAJ,EAAuB,IAAvB,EAA6B,IAAI,CAAC,IAAL,CAAU,MAAV,EAAkB,IAAlB,CAA7B,EAAsD,IAAtD,EAA4D,KAAI,4BAAJ,EAAqB,oBAAoB,EAAzC,CAA5D,CAAjB,CAAN;AACD,OAHI,MAIA;AACH,cAAM,WAAW,GAAiD,CAAC,MAAK;AACtE,kBAAQ,IAAR;AACE,iBAAK,UAAL;AACE,kBAAI;AACF,uBAAO,OAAO,CAAC,mCAAD,CAAP,CAA6C,OAApD;AACD,eAFD,CAGA,OAAO,CAAP,EAAU;AACR,sBAAM,KAAI,wCAAJ,EAA8B,qGAAqG,CAAC,CAAC,KAAF,IAAW,CAAC,EAA/I,CAAN;AACD;;AAEH,iBAAK,MAAL;AACE,qBAAO,OAAO,CAAC,sBAAD,CAAP,CAAgC,OAAvC;;AAEF,iBAAK,KAAL;AACE,qBAAO,OAAO,CAAC,qBAAD,CAAP,CAA+B,OAAtC;;AAEF;AACE,qBAAO,IAAP;AAhBJ;AAkBD,SAnBiE,GAAlE;;AAqBA,QAAA,MAAM,CAAC,IAAD,EAAO,MAAM,IAAI,WAAW,KAAK,IAAhB,GAAuB,yCAAmB,IAAnB,EAAyB,MAAzB,EAAiC,IAAjC,CAAvB,GAAgE,IAAK,WAAL,CAAyB,IAAzB,EAA+B,MAA/B,EAAuC,IAAvC,CAAjF,CAAN;AACD;AACF;AACF;;AAED,EAAA,WAAW,GAAA;AACT,WAAO,KAAK,SAAL,CAAe,KAAtB;AACD;;AAED,QAAM,IAAN,CAAW,IAAX,EAAyB,gBAAzB,EAAkD;AAChD,UAAM,WAAW,GAAuB;AACtC,MAAA,IAAI,EAAE,IADgC;AAEtC,MAAA,IAAI,EAAE,KAAK,OAAL,CAAa,WAFmB;AAGtC,MAAA,IAAI,EAAE,MAAM,KAAK,OAAL,CAAa,iBAAb,EAH0B;AAItC,MAAA,OAAO,EAAE,KAAK;AAJwB,KAAxC;AAOA,UAAM,OAAO,GAAG,MAAM,KAAK,OAAL,CAAa,KAAnC;;AACA,QAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,UAAI,KAAK,4BAAL,CAAkC,IAAlC,IAA0C,IAA9C,EAAoD;AAClD,cAAM,6BAAK,WAAL,EAAkB,IAAlB,CAAN;AACD,OAFD,MAGK,IAAI,KAAK,gBAAT,EAA2B;AAC9B,cAAM,KAAI,wCAAJ,EAA8B,mKAA9B,CAAN;AACD;;AACD;AACD;;AAED,QAAI,gBAAgB,IAAI,IAAxB,EAA8B;AAC5B,MAAA,gBAAgB,GAAG,SAAnB;AACD;;AAED,QAAI,UAAU,OAAd,EAAuB;AACrB,yBAAI,IAAJ,CAAS;AACP,QAAA,IAAI,EAAE,mBAAI,QAAJ,CAAa,IAAb,CADC;AAEP,QAAA,eAAe,EAAG,OAA+B,CAAC;AAF3C,OAAT,EAGG,gBAHH;AAID,KALD,MAMK;AACH,YAAM,IAAI,GAAG,OAAb;;AACA,yBAAI,IAAJ,CAAS;AACP,QAAA,IAAI,EAAE,mBAAI,QAAJ,CAAa,IAAb,CADC;AAEP,QAAA,OAAO,EAAE,IAAI,CAAC,OAFP;AAGP,QAAA,UAAU,EAAE,IAAI,CAAC,UAHV;AAIP,QAAA,KAAK,EAAE,IAAI,CAAC,KAJL;AAKP,QAAA,IAAI,EAAE,IAAI,CAAC,mBAAL,GAA2B,eAA3B,GAA6C;AAL5C,OAAT,EAMG,gBANH;AAOD;;AAED,UAAM,KAAK,MAAL,CAAY,EAChB,GAAG,WADa;AAEhB,MAAA,OAFgB;AAGhB,MAAA,OAAO,EAAE,EACP,GAAG,KAAK;AADD;AAHO,KAAZ,CAAN;AAOD;;AAEO,QAAM,MAAN,CAAa,OAAb,EAAwC;AAC9C,SAAK,IAAI,CAAC,GAAG,CAAb,EAAgB,CAAC,GAAG,CAApB,EAAuB,CAAC,EAAxB,EAA4B;AAC1B,UAAI;AACF,cAAM,6BAAK,OAAL,EAAc,IAAd,CAAN;AACA;AACD,OAHD,CAIA,OAAO,CAAP,EAAU;AACR;AACA,cAAM,OAAO,GAAG,CAAC,CAAC,OAAlB;;AACA,YAAI,OAAO,IAAI,IAAX,IAAmB,OAAO,CAAC,QAAR,CAAiB,4BAAjB,CAAvB,EAAuE;AACrE,6BAAI,IAAJ,CAAS;AAAC,YAAA,KAAK,EAAE,OAAR;AAAiB,YAAA,OAAO,EAAE,CAAC,GAAG;AAA9B,WAAT,EAA2C,aAA3C;;AACA;AACD;;AACD,cAAM,CAAN;AACD;AACF;AACF;;AAED,QAAM,oBAAN,CAA2B,IAA3B,EAAyC,IAAzC,EAAqD,MAArD,EAAqE,YAArE,EAAmG,uBAAnG,EAA2J;AACzJ,UAAM,OAAO,GAAG,KAAK,OAArB;AAEA,UAAM,KAAK,GAAkB,EAA7B;AAEA,UAAM,IAAI,GAAG,CACX,IADW,EAEX,sBAFW,EAEa,iBAFb,EAEgC,OAAO,CAAC,WAFxC,EAGX,sBAHW,EAGa,aAHb,EAG4B,OAAO,CAAC,WAHpC,EAIX,sBAJW,EAIa,gBAJb,EAI+B,OAAO,CAAC,SAJvC,EAKX,oBALW,EAKW,OAAO,CAAC,YAAR,IAAwB,OAAO,CAAC,YAL3C,EAMX,uBANW,EAMc,OAAO,CAAC,mBAAR,IAA+B,OAAO,CAAC,4BAAR,EAN7C,CAAb;;AASA,QAAI,YAAY,IAAI,IAApB,EAA0B;AACxB,MAAA,IAAI,CAAC,IAAL,CACE,sBADF,EAC0B,cAD1B,EAC0C,YAD1C,EAEE,sBAFF,EAE0B,kBAF1B,EAE8C,EAF9C;AAID;;AAED,QAAI,uBAAuB,IAAI,IAA3B,IAAmC,uBAAuB,KAAK,WAAnE,EAAgF;AAC9E,MAAA,IAAI,CAAC,IAAL,CAAU,iCAAV,EAA6C,uBAA7C;AACD;;AAED,4BAAI,OAAO,CAAC,WAAZ,EAAyB,EAAE,IAAI,IAAI,CAAC,IAAL,CAAU,sBAAV,EAAkC,aAAlC,EAAiD,EAAjD,CAA/B;AACA,4BAAI,KAAK,4BAAL,CAAkC,eAAtC,EAAuD,EAAE,IAAI,IAAI,CAAC,IAAL,CAAU,sBAAV,EAAkC,iBAAlC,EAAqD,EAArD,CAA7D;AACA,UAAM,QAAQ,GAAG,MAAM,KAAK,WAAL,EAAvB;AACA,4BAAI,QAAJ,EAAc,EAAE,IAAG;AACjB,MAAA,KAAK,CAAC,IAAN,CAAW,EAAX;AACA,MAAA,IAAI,CAAC,IAAL,CAAU,YAAV,EAAwB,EAAxB;AACD,KAHD;AAKA,UAAM,MAAM,GAAG,KAAK,MAApB;AACA,UAAM,qBAAqB,GAAG,CAAC,mCAAD,IAA0B,eAA1B,IAAkC,MAAM,CAAC,YAAP,IAAuB,IAAzD,GAAgE,IAAhE,GAAuE,MAAM,KAAK,OAAL,CAAa,KAAxH;AACA,QAAI,iBAAiB,GAA6B,IAAlD,CAnCyJ,CAoCzJ;;AACA,QAAI,qBAAqB,IAAI,IAA7B,EAAmC;AACjC,YAAM,OAAO,GAAI,qBAA6C,CAAC,IAA/D;;AACA,UAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,QAAA,KAAK,CAAC,IAAN,CAAW,OAAX;AACD;;AAED,YAAM,KAAK,GAAG,mBAAK,kBAAL,CAAd;AACA,YAAM,IAAI,GAAG,0BAAW,QAAX,CAAb;AACA,MAAA,IAAI,CAAC,MAAL,CAAY,MAAM,CAAC,eAAP,IAA0B,oBAAtC;AACA,MAAA,IAAI,CAAC,MAAL,CAAY,IAAI,CAAC,SAAL,CAAe,KAAK,4BAApB,CAAZ;AACA,MAAA,IAAI,CAAC,MAAL,CAAY,IAAI,CAAC,SAAL,CAAe,IAAf,CAAZ;AACA,MAAA,IAAI,CAAC,MAAL,CAAY,KAAK,4BAAL,CAAkC,eAAlC,IAAqD,oBAAjE;AACA,MAAA,IAAI,CAAC,MAAL,CAAY,KAAK,4BAAL,CAAkC,sBAAlC,IAA4D,gBAAxE;AAEA,MAAA,iBAAiB,GAAG,KAAI,iCAAJ,EAAsB,MAAtB,EAA8B,IAA9B,EAAoC,IAApC,CAApB;;AACA,UAAI,MAAM,iBAAiB,CAAC,WAAlB,CAA8B,MAAM,4BAAO,IAAP,EAAa,KAAb,CAApC,CAAV,EAAoE;AAClE,QAAA,KAAK,CAAC,GAAN;AACA;AACD;;AACD,MAAA,KAAK,CAAC,GAAN;AACD;;AAED,UAAM,KAAK,GAAG,mBAAK,WAAL,CAAd,CA3DyJ,CA4DzJ;;AACA,QAAI,OAAO,CAAC,QAAR,KAAqB,OAArB,IAAgC,KAAK,IAAL,CAAU,SAAV,CAAoB,IAApB,KAA6B,UAAjE,EAA6E;AAC3E,YAAM,sCAAkB,CAAC,QAAD,EAAW,QAAX,EAAqB,IAAI,CAAC,SAAL,CAAe,IAAf,CAArB,CAAlB,EAA8D;AAAU;AAAxE,QAA6F,EAA7F,EAAiG;AAAE;AAAnG,OAAN;AACD;;AAED,UAAM,KAAK,IAAL,CAAU,IAAV,CAAN;AACA,IAAA,KAAK,CAAC,GAAN;;AAEA,QAAI,iBAAiB,IAAI,IAAzB,EAA+B;AAC7B,YAAM,iBAAiB,CAAC,IAAlB,EAAN;AACD;AACF;;AAEO,EAAA,UAAU,GAAA;AAChB,WAAO,KAAK,4BAAL,CAAkC,QAAlC,KAA+C,IAAtD;AACD;;AAES,EAAA,8BAA8B,CAAC,WAAD,EAA8B;AACpE,QAAI,KAAK,4BAAL,CAAkC,qBAAlC,KAA4D,KAAhE,EAAuE;AACrE,aAAO,IAAP;AACD;;AAED,WAAO,IAAI,IAAG;AACZ,UAAI,IAAI,CAAC,QAAL,CAAc,MAAd,KAA0B,KAAK,UAAL,MAAqB,IAAI,CAAC,QAAL,CAAc,MAAd,CAAnD,EAA2E;AACzE,cAAM,SAAS,GAAG,IAAI,CAAC,OAAL,CAAa,IAAb,CAAlB;;AACA,YAAI,SAAS,KAAK,WAAW,CAAC,SAA9B,EAAyC;AACvC,iBAAO,KAAI,yBAAJ,EAAwB,IAAI,IAAI,KAAK,IAAL,CAAU,IAAV,CAAhC,CAAP;AACD;AACF;;AACD,aAAO,IAAP;AACD,KARD;AASD;;AAES,QAAM,OAAN,CAAc,WAAd,EAA6C,MAA7C,EAA4D;AACpE,UAAM,WAAW,GAAG,GAAG,KAAK,OAAL,CAAa,eAAe,MAAnD;;AACA,QAAI,KAAK,4BAAL,CAAkC,qBAAlC,KAA4D,KAAhE,EAAuE;AACrE;AACD;;AAED,UAAM,uBAAgB,GAAhB,CAAoB,wBAAQ,WAAW,CAAC,SAApB,CAApB,EAAqD,IAAD,IAAsB;AAC9E,UAAI,IAAI,KAAK,WAAb,EAA0B;AACxB,eAAO,KAAK,oBAAL,CAA0B,IAAI,CAAC,IAAL,CAAU,WAAW,CAAC,SAAtB,EAAiC,WAAjC,CAA1B,EAAyE,WAAW,CAAC,IAArF,EAA2F,WAAW,CAAC,MAAvG,EAA+G,IAAI,CAAC,QAAL,CAAc,WAAd,EAA2B,MAA3B,CAA/G,EAAmJ,KAAK,4BAAL,CAAkC,uBAArL,CAAP;AACD,OAFD,MAGK,IAAI,IAAI,CAAC,QAAL,CAAc,MAAd,KAA0B,KAAK,UAAL,MAAqB,IAAI,CAAC,QAAL,CAAc,MAAd,CAAnD,EAA2E;AAC9E,eAAO,KAAK,IAAL,CAAU,IAAI,CAAC,IAAL,CAAU,WAAW,CAAC,SAAtB,EAAiC,IAAjC,CAAV,CAAP;AACD;;AACD,aAAO,IAAP;AACD,KARK,CAAN;;AAUA,QAAI,CAAC,MAAL,EAAa;AACX;AACD;;AAED,UAAM,eAAe,GAAG,IAAI,CAAC,IAAL,CAAU,WAAW,CAAC,SAAtB,EAAiC,WAAjC,EAA8C,mBAA9C,CAAxB,CApBoE,CAqBpE;;AACA,UAAM,UAAU,GAAG,MAAM,gBAAK,eAAL,EAAsB,CAAC,IAAD,EAAO,IAAP,KAAgB,IAAI,CAAC,WAAL,MAAsB,IAAI,CAAC,QAAL,CAAc,MAAd,CAAtB,IAAgD,KAAK,UAAL,MAAqB,IAAI,CAAC,QAAL,CAAc,MAAd,CAA3G,CAAzB;AACA,UAAM,uBAAgB,GAAhB,CAAoB,UAApB,EAAgC,IAAI,IAAI,KAAK,IAAL,CAAU,IAAV,CAAxC,EAAyD;AAAC,MAAA,WAAW,EAAE;AAAd,KAAzD,CAAN;AACD;;AA5VoE,C", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { Arch, asArray, InvalidConfigurationError, log, use, executeAppBuilder } from \"builder-util\"\nimport { parseDn } from \"builder-util-runtime\"\nimport { CopyFileTransformer, FileTransformer, walk } from \"builder-util/out/fs\"\nimport { createHash } from \"crypto\"\nimport { readdir } from \"fs-extra\"\nimport isCI from \"is-ci\"\nimport { Lazy } from \"lazy-val\"\nimport * as path from \"path\"\nimport { downloadCertificate } from \"./codeSign/codesign\"\nimport { CertificateFromStoreInfo, CertificateInfo, FileCodeSigningInfo, getCertificateFromStoreInfo, getCertInfo, sign, WindowsSignOptions } from \"./codeSign/windowsCodeSign\"\nimport { AfterPackContext } from \"./configuration\"\nimport { DIR_TARGET, Platform, Target } from \"./core\"\nimport { RequestedExecutionLevel, WindowsConfiguration } from \"./options/winOptions\"\nimport { Packager } from \"./packager\"\nimport { chooseNotNull, PlatformPackager } from \"./platformPackager\"\nimport AppXTarget from \"./targets/AppxTarget\"\nimport { NsisTarget } from \"./targets/nsis/NsisTarget\"\nimport { AppPackageHelper, CopyElevateHelper } from \"./targets/nsis/nsisUtil\"\nimport { WebInstallerTarget } from \"./targets/nsis/WebInstallerTarget\"\nimport { createCommonTarget } from \"./targets/targetFactory\"\nimport { BuildCacheManager, digest } from \"./util/cacheManager\"\nimport { isBuildCacheEnabled } from \"./util/flags\"\nimport { time } from \"./util/timer\"\nimport { getWindowsVm, VmManager } from \"./vm/vm\"\n\nexport class WinPackager extends PlatformPackager<WindowsConfiguration> {\n  readonly cscInfo = new Lazy<FileCodeSigningInfo | CertificateFromStoreInfo | null>(() => {\n    const platformSpecificBuildOptions = this.platformSpecificBuildOptions\n    if (platformSpecificBuildOptions.certificateSubjectName != null || platformSpecificBuildOptions.certificateSha1 != null) {\n      return this.vm.value\n        .then(vm => getCertificateFromStoreInfo(platformSpecificBuildOptions, vm))\n        .catch(e => {\n          // https://github.com/electron-userland/electron-builder/pull/2397\n          if (platformSpecificBuildOptions.sign == null) {\n            throw e\n          }\n          else {\n            log.debug({error: e}, \"getCertificateFromStoreInfo error\")\n            return null\n          }\n        })\n    }\n\n    const certificateFile = platformSpecificBuildOptions.certificateFile\n    if (certificateFile != null) {\n      const certificatePassword = this.getCscPassword()\n      return Promise.resolve({\n        file: certificateFile,\n        password: certificatePassword == null ? null : certificatePassword.trim(),\n      })\n    }\n\n    const cscLink = this.getCscLink(\"WIN_CSC_LINK\")\n    if (cscLink == null) {\n      return Promise.resolve(null)\n    }\n\n    return downloadCertificate(cscLink, this.info.tempDirManager, this.projectDir)\n      // before then\n      .catch(e => {\n        if (e instanceof InvalidConfigurationError) {\n          throw new InvalidConfigurationError(`Env WIN_CSC_LINK is not correct, cannot resolve: ${e.message}`)\n        }\n        else {\n          throw e\n        }\n      })\n      .then(path => {\n        return {\n          file: path!!,\n          password: this.getCscPassword(),\n        }\n      })\n  })\n\n  private _iconPath = new Lazy(() => this.getOrConvertIcon(\"ico\"))\n\n  readonly vm = new Lazy<VmManager>(() => process.platform === \"win32\" ? Promise.resolve(new VmManager()) : getWindowsVm(this.debugLogger))\n\n  readonly computedPublisherName = new Lazy<Array<string> | null>(async () => {\n    const publisherName = (this.platformSpecificBuildOptions as WindowsConfiguration).publisherName\n    if (publisherName === null) {\n      return null\n    }\n    else if (publisherName != null) {\n      return asArray(publisherName)\n    }\n\n    const certInfo = await this.lazyCertInfo.value\n    return certInfo == null ? null : [certInfo.commonName]\n  })\n\n  readonly lazyCertInfo = new Lazy<CertificateInfo | null>(async () => {\n    const cscInfo = await this.cscInfo.value\n    if (cscInfo == null) {\n      return null\n    }\n\n    if (\"subject\" in cscInfo) {\n      const bloodyMicrosoftSubjectDn = (cscInfo as CertificateFromStoreInfo).subject\n      return {\n        commonName: parseDn(bloodyMicrosoftSubjectDn).get(\"CN\")!!,\n        bloodyMicrosoftSubjectDn,\n      }\n    }\n\n    const cscFile = (cscInfo as FileCodeSigningInfo).file\n    if (cscFile == null) {\n      return null\n    }\n    return await getCertInfo(cscFile, (cscInfo as FileCodeSigningInfo).password || \"\")\n  })\n\n  get isForceCodeSigningVerification(): boolean {\n    return this.platformSpecificBuildOptions.verifyUpdateCodeSignature !== false\n  }\n\n  constructor(info: Packager) {\n    super(info, Platform.WINDOWS)\n  }\n\n  get defaultTarget(): Array<string> {\n    return [\"nsis\"]\n  }\n\n  protected doGetCscPassword(): string | undefined | null {\n    return chooseNotNull(chooseNotNull(this.platformSpecificBuildOptions.certificatePassword, process.env.WIN_CSC_KEY_PASSWORD), super.doGetCscPassword())\n  }\n\n  createTargets(targets: Array<string>, mapper: (name: string, factory: (outDir: string) => Target) => void): void {\n    let copyElevateHelper: CopyElevateHelper | null\n    const getCopyElevateHelper = () => {\n      if (copyElevateHelper == null) {\n        copyElevateHelper = new CopyElevateHelper()\n      }\n      return copyElevateHelper\n    }\n\n    let helper: AppPackageHelper | null\n    const getHelper = () => {\n      if (helper == null) {\n        helper = new AppPackageHelper(getCopyElevateHelper())\n      }\n      return helper\n    }\n\n    for (const name of targets) {\n      if (name === DIR_TARGET) {\n        continue\n      }\n\n      if (name === \"nsis\" || name === \"portable\") {\n        mapper(name, outDir => new NsisTarget(this, outDir, name, getHelper()))\n      }\n      else if (name === \"nsis-web\") {\n        // package file format differs from nsis target\n        mapper(name, outDir => new WebInstallerTarget(this, path.join(outDir, name), name, new AppPackageHelper(getCopyElevateHelper())))\n      }\n      else {\n        const targetClass: typeof NsisTarget | typeof AppXTarget | null = (() => {\n          switch (name) {\n            case \"squirrel\":\n              try {\n                return require(\"electron-builder-squirrel-windows\").default\n              }\n              catch (e) {\n                throw new InvalidConfigurationError(`Module electron-builder-squirrel-windows must be installed in addition to build Squirrel.Windows: ${e.stack || e}`)\n              }\n\n            case \"appx\":\n              return require(\"./targets/AppxTarget\").default\n\n            case \"msi\":\n              return require(\"./targets/MsiTarget\").default\n\n            default:\n              return null\n          }\n        })()\n\n        mapper(name, outDir => targetClass === null ? createCommonTarget(name, outDir, this) : new (targetClass as any)(this, outDir, name))\n      }\n    }\n  }\n\n  getIconPath() {\n    return this._iconPath.value\n  }\n\n  async sign(file: string, logMessagePrefix?: string): Promise<void> {\n    const signOptions: WindowsSignOptions = {\n      path: file,\n      name: this.appInfo.productName,\n      site: await this.appInfo.computePackageUrl(),\n      options: this.platformSpecificBuildOptions,\n    }\n\n    const cscInfo = await this.cscInfo.value\n    if (cscInfo == null) {\n      if (this.platformSpecificBuildOptions.sign != null) {\n        await sign(signOptions, this)\n      }\n      else if (this.forceCodeSigning) {\n        throw new InvalidConfigurationError(`App is not signed and \"forceCodeSigning\" is set to true, please ensure that code signing configuration is correct, please see https://electron.build/code-signing`)\n      }\n      return\n    }\n\n    if (logMessagePrefix == null) {\n      logMessagePrefix = \"signing\"\n    }\n\n    if (\"file\" in cscInfo) {\n      log.info({\n        file: log.filePath(file),\n        certificateFile: (cscInfo as FileCodeSigningInfo).file,\n      }, logMessagePrefix)\n    }\n    else {\n      const info = cscInfo as CertificateFromStoreInfo\n      log.info({\n        file: log.filePath(file),\n        subject: info.subject,\n        thumbprint: info.thumbprint,\n        store: info.store,\n        user: info.isLocalMachineStore ? \"local machine\" : \"current user\",\n      }, logMessagePrefix)\n    }\n\n    await this.doSign({\n      ...signOptions,\n      cscInfo,\n      options: {\n        ...this.platformSpecificBuildOptions,\n      },\n    })\n  }\n\n  private async doSign(options: WindowsSignOptions) {\n    for (let i = 0; i < 3; i++) {\n      try {\n        await sign(options, this)\n        break\n      }\n      catch (e) {\n        // https://github.com/electron-userland/electron-builder/issues/1414\n        const message = e.message\n        if (message != null && message.includes(\"Couldn't resolve host name\")) {\n          log.warn({error: message, attempt: i + 1}, `cannot sign`)\n          continue\n        }\n        throw e\n      }\n    }\n  }\n\n  async signAndEditResources(file: string, arch: Arch, outDir: string, internalName?: string | null, requestedExecutionLevel?: RequestedExecutionLevel | null) {\n    const appInfo = this.appInfo\n\n    const files: Array<string> = []\n\n    const args = [\n      file,\n      \"--set-version-string\", \"FileDescription\", appInfo.productName,\n      \"--set-version-string\", \"ProductName\", appInfo.productName,\n      \"--set-version-string\", \"LegalCopyright\", appInfo.copyright,\n      \"--set-file-version\", appInfo.shortVersion || appInfo.buildVersion,\n      \"--set-product-version\", appInfo.shortVersionWindows || appInfo.getVersionInWeirdWindowsForm(),\n    ]\n\n    if (internalName != null) {\n      args.push(\n        \"--set-version-string\", \"InternalName\", internalName,\n        \"--set-version-string\", \"OriginalFilename\", \"\",\n      )\n    }\n\n    if (requestedExecutionLevel != null && requestedExecutionLevel !== \"asInvoker\") {\n      args.push(\"--set-requested-execution-level\", requestedExecutionLevel)\n    }\n\n    use(appInfo.companyName, it => args.push(\"--set-version-string\", \"CompanyName\", it!))\n    use(this.platformSpecificBuildOptions.legalTrademarks, it => args.push(\"--set-version-string\", \"LegalTrademarks\", it!))\n    const iconPath = await this.getIconPath()\n    use(iconPath, it => {\n      files.push(it)\n      args.push(\"--set-icon\", it)\n    })\n\n    const config = this.config\n    const cscInfoForCacheDigest = !isBuildCacheEnabled() || isCI || config.electronDist != null ? null : await this.cscInfo.value\n    let buildCacheManager: BuildCacheManager | null = null\n    // resources editing doesn't change executable for the same input and executed quickly - no need to complicate\n    if (cscInfoForCacheDigest != null) {\n      const cscFile = (cscInfoForCacheDigest as FileCodeSigningInfo).file\n      if (cscFile != null) {\n        files.push(cscFile)\n      }\n\n      const timer = time(\"executable cache\")\n      const hash = createHash(\"sha512\")\n      hash.update(config.electronVersion || \"no electronVersion\")\n      hash.update(JSON.stringify(this.platformSpecificBuildOptions))\n      hash.update(JSON.stringify(args))\n      hash.update(this.platformSpecificBuildOptions.certificateSha1 || \"no certificateSha1\")\n      hash.update(this.platformSpecificBuildOptions.certificateSubjectName || \"no subjectName\")\n\n      buildCacheManager = new BuildCacheManager(outDir, file, arch)\n      if (await buildCacheManager.copyIfValid(await digest(hash, files))) {\n        timer.end()\n        return\n      }\n      timer.end()\n    }\n\n    const timer = time(\"wine&sign\")\n    // rcedit crashed of executed using wine, resourcehacker works\n    if (process.platform === \"win32\" || this.info.framework.name === \"electron\") {\n      await executeAppBuilder([\"rcedit\", \"--args\", JSON.stringify(args)], undefined /* child-process */, {}, 3 /* retry five times */)\n    }\n\n    await this.sign(file)\n    timer.end()\n\n    if (buildCacheManager != null) {\n      await buildCacheManager.save()\n    }\n  }\n\n  private isSignDlls(): boolean {\n    return this.platformSpecificBuildOptions.signDlls === true\n  }\n\n  protected createTransformerForExtraFiles(packContext: AfterPackContext): FileTransformer | null {\n    if (this.platformSpecificBuildOptions.signAndEditExecutable === false) {\n      return null\n    }\n\n    return file => {\n      if (file.endsWith(\".exe\") || (this.isSignDlls() && file.endsWith(\".dll\"))) {\n        const parentDir = path.dirname(file)\n        if (parentDir !== packContext.appOutDir) {\n          return new CopyFileTransformer(file => this.sign(file))\n        }\n      }\n      return null\n    }\n  }\n\n  protected async signApp(packContext: AfterPackContext, isAsar: boolean): Promise<any> {\n    const exeFileName = `${this.appInfo.productFilename}.exe`\n    if (this.platformSpecificBuildOptions.signAndEditExecutable === false) {\n      return\n    }\n\n    await BluebirdPromise.map(readdir(packContext.appOutDir), (file: string): any => {\n      if (file === exeFileName) {\n        return this.signAndEditResources(path.join(packContext.appOutDir, exeFileName), packContext.arch, packContext.outDir, path.basename(exeFileName, \".exe\"), this.platformSpecificBuildOptions.requestedExecutionLevel)\n      }\n      else if (file.endsWith(\".exe\") || (this.isSignDlls() && file.endsWith(\".dll\"))) {\n        return this.sign(path.join(packContext.appOutDir, file))\n      }\n      return null\n    })\n\n    if (!isAsar) {\n      return\n    }\n\n    const outResourcesDir = path.join(packContext.appOutDir, \"resources\", \"app.asar.unpacked\")\n    // noinspection JSUnusedLocalSymbols\n    const fileToSign = await walk(outResourcesDir, (file, stat) => stat.isDirectory() || file.endsWith(\".exe\") || (this.isSignDlls() && file.endsWith(\".dll\")))\n    await BluebirdPromise.map(fileToSign, file => this.sign(file), {concurrency: 4})\n  }\n}"], "sourceRoot": ""}