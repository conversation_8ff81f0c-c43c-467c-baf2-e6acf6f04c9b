{"version": 3, "sources": ["../../src/vm/WineVm.ts"], "names": [], "mappings": ";;;;;;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;;;;;AAEM,MAAO,aAAP,SAA6B,eAA7B,CAAsC;AAC1C,EAAA,WAAA,GAAA;AACE;AACD,GAHyC,CAK1C;;;AACA,EAAA,IAAI,CAAC,IAAD,EAAe,IAAf,EAAoC,OAApC,EAA+D,eAAA,GAA2B,IAA1F,EAA8F;AAChG,WAAO,sBAAS,IAAT,EAAe,IAAf,EAAqB,IAArB,EAA2B,OAA3B,CAAP;AACD,GARyC,CAU1C;;;AACA,EAAA,KAAK,CAAC,IAAD,EAAe,IAAf,EAAoC,OAApC,EAA4D,YAA5D,EAA4F;AAC/F,UAAM,IAAI,KAAJ,CAAU,aAAV,CAAN;AACD;;AAED,EAAA,QAAQ,CAAC,IAAD,EAAa;AACnB,WAAO,IAAI,CAAC,KAAL,CAAW,IAAX,CAAgB,IAAhB,EAAsB,IAAtB,CAAP;AACD;;AAjByC,C", "sourcesContent": ["import { SpawnOptions, ExecFileOptions } from \"child_process\"\nimport { ExtraSpawnOptions } from \"builder-util\"\nimport { execWine } from \"../wine\"\nimport { VmManager } from \"./vm\"\nimport * as path from \"path\"\n\nexport class WineVmManager extends VmManager {\n  constructor() {\n    super()\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  exec(file: string, args: Array<string>, options?: ExecFileOptions, isLogOutIfDebug: boolean = true): Promise<string> {\n    return execWine(file, null, args, options)\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  spawn(file: string, args: Array<string>, options?: SpawnOptions, extraOptions?: ExtraSpawnOptions): Promise<any> {\n    throw new Error(\"Unsupported\")\n  }\n\n  toVmFile(file: string): string {\n    return path.win32.join(\"Z:\", file)\n  }\n}\n"], "sourceRoot": ""}