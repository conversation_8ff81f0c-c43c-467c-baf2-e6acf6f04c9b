{"version": 3, "sources": ["../../src/vm/vm.ts"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;AAEM,MAAO,SAAP,CAAgB;AACpB,MAAI,OAAJ,GAAW;AACT,WAAO,IAAI,CAAC,GAAZ;AACD;;AAED,EAAA,IAAI,CAAC,IAAD,EAAe,IAAf,EAAoC,OAApC,EAA+D,eAAe,GAAG,IAAjF,EAAqF;AACvF,WAAO,yBAAK,IAAL,EAAW,IAAX,EAAiB,OAAjB,EAA0B,eAA1B,CAAP;AACD;;AAED,EAAA,KAAK,CAAC,IAAD,EAAe,IAAf,EAAoC,OAApC,EAA4D,YAA5D,EAA4F;AAC/F,WAAO,0BAAM,IAAN,EAAY,IAAZ,EAAkB,OAAlB,EAA2B,YAA3B,CAAP;AACD;;AAED,EAAA,QAAQ,CAAC,IAAD,EAAa;AACnB,WAAO,IAAP;AACD;;AAfmB;;;;AAkBf,eAAe,YAAf,CAA4B,WAA5B,EAAoD;AACzD,QAAM,MAAM,GAAG,CAAC,MAAM,gCAAY,WAAZ,CAAP,EAAiC,MAAjC,CAAwC,EAAE,IAAI,EAAE,CAAC,EAAH,KAAU,QAAxD,CAAf;;AACA,MAAI,MAAM,CAAC,MAAP,KAAkB,CAAtB,EAAyB;AACvB,UAAM,KAAI,wCAAJ,EAA8B,iFAA9B,CAAN;AACD,GAJwD,CAMzD;;;AACA,SAAO,KAAI,iCAAJ,EAAuB,MAAM,CAAC,IAAP,CAAY,EAAE,IAAI,EAAE,CAAC,KAAH,KAAa,SAA/B,KAA6C,MAAM,CAAC,IAAP,CAAY,EAAE,IAAI,EAAE,CAAC,KAAH,KAAa,WAA/B,CAA7C,IAA4F,MAAM,CAAC,CAAD,CAAzH,CAAP;AACD,C", "sourcesContent": ["import { DebugLogger, exec, ExtraSpawnOptions, InvalidConfigurationError, spawn } from \"builder-util\"\nimport { ExecFileOptions, SpawnOptions } from \"child_process\"\nimport * as path from \"path\"\nimport { ParallelsVmManager, parseVmList } from \"./ParallelsVm\"\n\nexport class VmManager {\n  get pathSep(): string {\n    return path.sep\n  }\n\n  exec(file: string, args: Array<string>, options?: ExecFileOptions, isLogOutIfDebug = true): Promise<string> {\n    return exec(file, args, options, isLogOutIfDebug)\n  }\n\n  spawn(file: string, args: Array<string>, options?: SpawnOptions, extraOptions?: ExtraSpawnOptions): Promise<any> {\n    return spawn(file, args, options, extraOptions)\n  }\n\n  toVmFile(file: string): string {\n    return file\n  }\n}\n\nexport async function getWindowsVm(debugLogger: DebugLogger): Promise<VmManager> {\n  const vmList = (await parseVmList(debugLogger)).filter(it => it.os === \"win-10\")\n  if (vmList.length === 0) {\n    throw new InvalidConfigurationError(\"Cannot find suitable Parallels Desktop virtual machine (Windows 10 is required)\")\n  }\n\n  // prefer running or suspended vm\n  return new ParallelsVmManager(vmList.find(it => it.state === \"running\") || vmList.find(it => it.state === \"suspended\") || vmList[0])\n}"], "sourceRoot": ""}