{"version": 3, "sources": ["../../src/vm/MonoVm.ts"], "names": [], "mappings": ";;;;;;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEM,MAAO,aAAP,SAA6B,eAA7B,CAAsC;AAC1C,EAAA,WAAA,GAAA;AACE;AACD;;AAED,EAAA,IAAI,CAAC,IAAD,EAAe,IAAf,EAAoC,OAApC,EAA+D,eAAe,GAAG,IAAjF,EAAqF;AACvF,WAAO,yBAAK,MAAL,EAAa,CAAC,IAAD,EAAO,MAAP,CAAc,IAAd,CAAb,EAAkC,EACvC,GAAG;AADoC,KAAlC,EAEJ,eAFI,CAAP;AAGD;;AAED,EAAA,KAAK,CAAC,IAAD,EAAe,IAAf,EAAoC,OAApC,EAA4D,YAA5D,EAA4F;AAC/F,WAAO,0BAAM,MAAN,EAAc,CAAC,IAAD,EAAO,MAAP,CAAc,IAAd,CAAd,EAAmC,OAAnC,EAA4C,YAA5C,CAAP;AACD;;AAbyC,C", "sourcesContent": ["import { SpawnOptions, ExecFileOptions } from \"child_process\"\nimport { exec, ExtraSpawnOptions, spawn } from \"builder-util\"\nimport { VmManager } from \"./vm\"\n\nexport class MonoVmManager extends VmManager {\n  constructor() {\n    super()\n  }\n\n  exec(file: string, args: Array<string>, options?: ExecFileOptions, isLogOutIfDebug = true): Promise<string> {\n    return exec(\"mono\", [file].concat(args), {\n      ...options,\n    }, isLogOutIfDebug)\n  }\n\n  spawn(file: string, args: Array<string>, options?: SpawnOptions, extraOptions?: ExtraSpawnOptions): Promise<any> {\n    return spawn(\"mono\", [file].concat(args), options, extraOptions)\n  }\n}"], "sourceRoot": ""}