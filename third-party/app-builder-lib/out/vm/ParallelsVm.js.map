{"version": 3, "sources": ["../../src/vm/ParallelsVm.ts"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AACO,eAAe,WAAf,CAA2B,WAA3B,EAAmD;AACxD;AACA,MAAI,OAAO,GAAG,MAAM,yBAAK,QAAL,EAAe,CAAC,MAAD,EAAS,IAAT,EAAe,IAAf,EAAqB,MAArB,CAAf,EAA6C,SAA7C,EAAwD,KAAxD,CAApB;AACA,EAAA,WAAW,CAAC,GAAZ,CAAgB,gBAAhB,EAAkC,OAAlC;AAEA,EAAA,OAAO,GAAG,OAAO,CAAC,SAAR,CAAkB,OAAO,CAAC,OAAR,CAAgB,KAAhB,CAAlB,CAAV,CALwD,CAOxD;;AACA,QAAM,MAAM,GAAuB,EAAnC;;AACA,OAAK,MAAM,IAAX,IAAmB,OAAO,CAAC,KAAR,CAAc,MAAd,EAAsB,GAAtB,CAA0B,EAAE,IAAI,EAAE,CAAC,IAAH,EAAhC,EAA2C,MAA3C,CAAkD,EAAE,IAAI,EAAE,CAAC,MAAH,GAAY,CAApE,CAAnB,EAA2F;AACzF,UAAM,EAAE,GAAQ,EAAhB;;AACA,SAAK,MAAM,IAAX,IAAmB,IAAI,CAAC,KAAL,CAAW,IAAX,CAAnB,EAAqC;AACnC,YAAM,IAAI,GAAG,oBAAoB,IAApB,CAAyB,IAAzB,CAAb;;AACA,UAAI,IAAI,IAAI,IAAZ,EAAkB;AAChB;AACD;;AAED,YAAM,GAAG,GAAG,IAAI,CAAC,CAAD,CAAJ,CAAQ,WAAR,EAAZ;;AACA,UAAI,GAAG,KAAK,IAAR,IAAgB,GAAG,KAAK,IAAxB,IAAgC,GAAG,KAAK,MAAxC,IAAkD,GAAG,KAAK,OAA1D,IAAqE,GAAG,KAAK,MAAjF,EAAyF;AACvF,QAAA,EAAE,CAAC,GAAD,CAAF,GAAU,IAAI,CAAC,CAAD,CAAJ,CAAQ,IAAR,EAAV;AACD;AACF;;AACD,IAAA,MAAM,CAAC,IAAP,CAAY,EAAZ;AACD;;AACD,SAAO,MAAP;AACD;AAED;;;AACM,MAAO,kBAAP,SAAkC,eAAlC,CAA2C;AAK/C,EAAA,WAAA,CAA6B,EAA7B,EAA4C;AAC1C;AAD2B,SAAA,EAAA,GAAA,EAAA;AAFrB,SAAA,eAAA,GAAkB,KAAlB;AAKN,SAAK,YAAL,GAAoB,KAAK,SAAL,EAApB;AACD;;AAED,MAAI,OAAJ,GAAW;AACT,WAAO,GAAP;AACD;;AAEO,EAAA,kBAAkB,CAAC,KAAD,EAAa;AACrC,QAAI,KAAK,CAAC,OAAN,CAAc,QAAd,CAAuB,oDAAvB,CAAJ,EAAkF;AAChF,YAAM,IAAI,KAAJ,CAAU,0CAA0C,KAAK,EAAL,CAAQ,IAAI,qFAAqF,KAAK,CAAC,OAAO,EAAlK,CAAN;AACD;;AAED,uBAAI,IAAJ,CAAS,8EAAT;;AACA,UAAM,KAAN;AACD;;AAED,QAAM,IAAN,CAAW,IAAX,EAAyB,IAAzB,EAA8C,OAA9C,EAAuE;AACrE,UAAM,KAAK,mBAAL,EAAN,CADqE,CAErE;;AACA,WAAO,MAAM,yBAAK,QAAL,EAAe,CAAC,MAAD,EAAS,KAAK,EAAL,CAAQ,EAAjB,EAAqB,gBAArB,EAAuC,IAAI,CAAC,UAAL,CAAgB,GAAhB,IAAuB,yBAAyB,CAAC,IAAD,CAAhD,GAAyD,IAAhG,EAAsG,MAAtG,CAA6G,IAA7G,CAAf,EAAmI,OAAnI,EACV,KADU,CACJ,KAAK,IAAI,KAAK,kBAAL,CAAwB,KAAxB,CADL,CAAb;AAED;;AAED,QAAM,KAAN,CAAY,IAAZ,EAA0B,IAA1B,EAA+C,OAA/C,EAAuE,YAAvE,EAAuG;AACrG,UAAM,KAAK,mBAAL,EAAN;AACA,WAAO,MAAM,0BAAM,QAAN,EAAgB,CAAC,MAAD,EAAS,KAAK,EAAL,CAAQ,EAAjB,EAAqB,IAArB,EAA2B,MAA3B,CAAkC,IAAlC,CAAhB,EAAyD,OAAzD,EAAkE,YAAlE,EACV,KADU,CACJ,KAAK,IAAI,KAAK,kBAAL,CAAwB,KAAxB,CADL,CAAb;AAED;;AAEO,QAAM,SAAN,GAAe;AACrB,UAAM,IAAI,GAAG,KAAK,EAAL,CAAQ,EAArB;AACA,UAAM,KAAK,GAAG,KAAK,EAAL,CAAQ,KAAtB;;AACA,QAAI,KAAK,KAAK,SAAd,EAAyB;AACvB;AACD;;AAED,QAAI,CAAC,KAAK,eAAV,EAA2B;AACzB,WAAK,eAAL,GAAuB,IAAvB,CADyB,CAEzB;;AACA,MAAA,OAAO,CAAC,iBAAD,CAAP,CAA4B,QAAD,IAAkC;AAC3D,cAAM,QAAQ,GAAG,CAAC,SAAD,EAAY,IAAZ,CAAjB;;AACA,YAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,6CAAa,QAAb,EAAuB,QAAvB;AACD,SAFD,MAGK;AACH,mCAAK,QAAL,EAAe,QAAf,EACG,IADH,CACQ,QADR,EAEG,KAFH,CAES,QAFT;AAGD;AACF,OAVD;AAWD;;AACD,UAAM,yBAAK,QAAL,EAAe,CAAC,OAAD,EAAU,IAAV,CAAf,CAAN;AACD;;AAEO,EAAA,mBAAmB,GAAA;AACzB,QAAI,YAAY,GAAG,KAAK,YAAxB;;AACA,QAAI,YAAY,IAAI,IAApB,EAA0B;AACxB,MAAA,YAAY,GAAG,KAAK,SAAL,EAAf;AACA,WAAK,YAAL,GAAoB,YAApB;AACD;;AACD,WAAO,YAAP;AACD;;AAED,EAAA,QAAQ,CAAC,IAAD,EAAa;AACnB;AACA,WAAO,yBAAyB,CAAC,IAAD,CAAhC;AACD;;AA1E8C;;;;AA6E3C,SAAU,yBAAV,CAAoC,IAApC,EAAgD;AACpD,MAAI,IAAI,CAAC,UAAL,CAAgB,MAAhB,CAAJ,EAA6B;AAC3B,WAAO,IAAP;AACD;;AACD,SAAO,oBAAoB,IAAI,CAAC,OAAL,CAAa,KAAb,EAAoB,IAApB,CAA3B;AACD,C", "sourcesContent": ["import { exec, spawn, DebugLogger, ExtraSpawnOptions, log } from \"builder-util\"\nimport { SpawnOptions, execFileSync, ExecFileOptions } from \"child_process\"\nimport { VmManager } from \"./vm\"\n\n/** @internal */\nexport async function parseVmList(debugLogger: DebugLogger) {\n  // do not log output if debug - it is huge, logged using debugLogger\n  let rawList = await exec(\"prlctl\", [\"list\", \"-i\", \"-s\", \"name\"], undefined, false)\n  debugLogger.add(\"parallels.list\", rawList)\n\n  rawList = rawList.substring(rawList.indexOf(\"ID:\"))\n\n  // let match: Array<string> | null\n  const result: Array<ParallelsVm> = []\n  for (const info of rawList.split(\"\\n\\n\").map(it => it.trim()).filter(it => it.length > 0)) {\n    const vm: any = {}\n    for (const line of info.split(\"\\n\")) {\n      const meta = /^([^:(\"]+): (.*)$/.exec(line)\n      if (meta == null) {\n        continue\n      }\n\n      const key = meta[1].toLowerCase()\n      if (key === \"id\" || key === \"os\" || key === \"name\" || key === \"state\" || key === \"name\") {\n        vm[key] = meta[2].trim()\n      }\n    }\n    result.push(vm)\n  }\n  return result\n}\n\n/** @internal */\nexport class ParallelsVmManager extends VmManager {\n  private startPromise: Promise<any>\n\n  private isExitHookAdded = false\n\n  constructor(private readonly vm: ParallelsVm) {\n    super()\n\n    this.startPromise = this.doStartVm()\n  }\n\n  get pathSep(): string {\n    return \"/\"\n  }\n\n  private handleExecuteError(error: Error): any {\n    if (error.message.includes(\"Unable to open new session in this virtual machine\")) {\n      throw new Error(`Please ensure that your are logged in \"${this.vm.name}\" parallels virtual machine. In the future please do not stop VM, but suspend.\\n\\n${error.message}`)\n    }\n\n    log.warn(\"ensure that 'Share folders' is set to 'All Disks', see https://goo.gl/E6XphP\")\n    throw error\n  }\n\n  async exec(file: string, args: Array<string>, options?: ExecFileOptions): Promise<string> {\n    await this.ensureThatVmStarted()\n    // it is important to use \"--current-user\" to execute command under logged in user - to access certs.\n    return await exec(\"prlctl\", [\"exec\", this.vm.id, \"--current-user\", file.startsWith(\"/\") ? macPathToParallelsWindows(file) : file].concat(args), options)\n      .catch(error => this.handleExecuteError(error))\n  }\n\n  async spawn(file: string, args: Array<string>, options?: SpawnOptions, extraOptions?: ExtraSpawnOptions): Promise<any> {\n    await this.ensureThatVmStarted()\n    return await spawn(\"prlctl\", [\"exec\", this.vm.id, file].concat(args), options, extraOptions)\n      .catch(error => this.handleExecuteError(error))\n  }\n\n  private async doStartVm() {\n    const vmId = this.vm.id\n    const state = this.vm.state\n    if (state === \"running\") {\n      return\n    }\n\n    if (!this.isExitHookAdded) {\n      this.isExitHookAdded = true\n      // eslint-disable-next-line @typescript-eslint/no-var-requires\n      require(\"async-exit-hook\")((callback: (() => void) | null) => {\n        const stopArgs = [\"suspend\", vmId]\n        if (callback == null) {\n          execFileSync(\"prlctl\", stopArgs)\n        }\n        else {\n          exec(\"prlctl\", stopArgs)\n            .then(callback)\n            .catch(callback)\n        }\n      })\n    }\n    await exec(\"prlctl\", [\"start\", vmId])\n  }\n\n  private ensureThatVmStarted() {\n    let startPromise = this.startPromise\n    if (startPromise == null) {\n      startPromise = this.doStartVm()\n      this.startPromise = startPromise\n    }\n    return startPromise\n  }\n\n  toVmFile(file: string): string {\n    // https://stackoverflow.com/questions/4742992/cannot-access-network-drive-in-powershell-running-as-administrator\n    return macPathToParallelsWindows(file)\n  }\n}\n\nexport function macPathToParallelsWindows(file: string) {\n  if (file.startsWith(\"C:\\\\\")) {\n    return file\n  }\n  return \"\\\\\\\\Mac\\\\Host\\\\\" + file.replace(/\\//g, \"\\\\\")\n}\n\nexport interface ParallelsVm {\n  id: string\n  name: string\n  os: \"win-10\" | \"ubuntu\" | \"elementary\"\n  state: \"running\" | \"suspended\" | \"stopped\"\n}"], "sourceRoot": ""}