"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.parseVmList = parseVmList;
exports.macPathToParallelsWindows = macPathToParallelsWindows;
exports.ParallelsVmManager = void 0;

function _builderUtil() {
  const data = require("builder-util");

  _builderUtil = function () {
    return data;
  };

  return data;
}

function _child_process() {
  const data = require("child_process");

  _child_process = function () {
    return data;
  };

  return data;
}

function _vm() {
  const data = require("./vm");

  _vm = function () {
    return data;
  };

  return data;
}

/** @internal */
async function parseVmList(debugLogger) {
  // do not log output if debug - it is huge, logged using debugLogger
  let rawList = await (0, _builderUtil().exec)("prlctl", ["list", "-i", "-s", "name"], undefined, false);
  debugLogger.add("parallels.list", rawList);
  rawList = rawList.substring(rawList.indexOf("ID:")); // let match: Array<string> | null

  const result = [];

  for (const info of rawList.split("\n\n").map(it => it.trim()).filter(it => it.length > 0)) {
    const vm = {};

    for (const line of info.split("\n")) {
      const meta = /^([^:("]+): (.*)$/.exec(line);

      if (meta == null) {
        continue;
      }

      const key = meta[1].toLowerCase();

      if (key === "id" || key === "os" || key === "name" || key === "state" || key === "name") {
        vm[key] = meta[2].trim();
      }
    }

    result.push(vm);
  }

  return result;
}
/** @internal */


class ParallelsVmManager extends _vm().VmManager {
  constructor(vm) {
    super();
    this.vm = vm;
    this.isExitHookAdded = false;
    this.startPromise = this.doStartVm();
  }

  get pathSep() {
    return "/";
  }

  handleExecuteError(error) {
    if (error.message.includes("Unable to open new session in this virtual machine")) {
      throw new Error(`Please ensure that your are logged in "${this.vm.name}" parallels virtual machine. In the future please do not stop VM, but suspend.\n\n${error.message}`);
    }

    _builderUtil().log.warn("ensure that 'Share folders' is set to 'All Disks', see https://goo.gl/E6XphP");

    throw error;
  }

  async exec(file, args, options) {
    await this.ensureThatVmStarted(); // it is important to use "--current-user" to execute command under logged in user - to access certs.

    return await (0, _builderUtil().exec)("prlctl", ["exec", this.vm.id, "--current-user", file.startsWith("/") ? macPathToParallelsWindows(file) : file].concat(args), options).catch(error => this.handleExecuteError(error));
  }

  async spawn(file, args, options, extraOptions) {
    await this.ensureThatVmStarted();
    return await (0, _builderUtil().spawn)("prlctl", ["exec", this.vm.id, file].concat(args), options, extraOptions).catch(error => this.handleExecuteError(error));
  }

  async doStartVm() {
    const vmId = this.vm.id;
    const state = this.vm.state;

    if (state === "running") {
      return;
    }

    if (!this.isExitHookAdded) {
      this.isExitHookAdded = true; // eslint-disable-next-line @typescript-eslint/no-var-requires

      require("async-exit-hook")(callback => {
        const stopArgs = ["suspend", vmId];

        if (callback == null) {
          (0, _child_process().execFileSync)("prlctl", stopArgs);
        } else {
          (0, _builderUtil().exec)("prlctl", stopArgs).then(callback).catch(callback);
        }
      });
    }

    await (0, _builderUtil().exec)("prlctl", ["start", vmId]);
  }

  ensureThatVmStarted() {
    let startPromise = this.startPromise;

    if (startPromise == null) {
      startPromise = this.doStartVm();
      this.startPromise = startPromise;
    }

    return startPromise;
  }

  toVmFile(file) {
    // https://stackoverflow.com/questions/4742992/cannot-access-network-drive-in-powershell-running-as-administrator
    return macPathToParallelsWindows(file);
  }

}

exports.ParallelsVmManager = ParallelsVmManager;

function macPathToParallelsWindows(file) {
  if (file.startsWith("C:\\")) {
    return file;
  }

  return "\\\\Mac\\Host\\" + file.replace(/\//g, "\\");
} 
// __ts-babel@6.0.4
//# sourceMappingURL=ParallelsVm.js.map