{"version": 3, "sources": ["../src/fileMatcher.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AAGA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;;;AAEA;AACA,MAAM,gBAAgB,GAAG;AAAC,EAAA,GAAG,EAAE;AAAN,CAAzB,C,CAEA;;AACO,MAAM,aAAa,GAAG,gCAC3B,gFAD2B,GAE3B,sDAF2B,GAG3B,+FAH2B,GAI3B,iDAJK;;AAMA,MAAM,YAAY,GAAG,6DAArB;;;AAEP,SAAS,gBAAT,CAA0B,IAA1B,EAAsC;AACpC,MAAI,IAAI,CAAC,GAAL,KAAa,GAAjB,EAAsB;AACpB,IAAA,IAAI,GAAG,IAAI,CAAC,OAAL,CAAa,KAAb,EAAoB,IAAI,CAAC,GAAzB,CAAP;AACD;;AACD,MAAI,IAAI,CAAC,GAAL,KAAa,IAAjB,EAAuB;AACrB,IAAA,IAAI,GAAG,IAAI,CAAC,OAAL,CAAa,KAAb,EAAoB,IAAI,CAAC,GAAzB,CAAP;AACD;;AAED,MAAI,IAAI,CAAC,QAAL,CAAc,IAAI,CAAC,GAAnB,CAAJ,EAA6B;AAC3B,WAAO,IAAI,CAAC,SAAL,CAAe,CAAf,EAAkB,IAAI,CAAC,MAAL,GAAc,CAAhC,CAAP;AACD,GAFD,MAGK;AACH,WAAO,IAAP;AACD;AACF;AAED;;;AACM,MAAO,WAAP,CAAkB;AAUtB,EAAA,WAAA,CAAY,IAAZ,EAA0B,EAA1B,EAA+C,aAA/C,EAA2F,QAA3F,EAA+I;AAAhG,SAAA,aAAA,GAAA,aAAA;AAJ/C,SAAA,eAAA,GAA2C,IAA3C;AAKE,SAAK,IAAL,GAAY,gBAAgB,CAAC,aAAa,CAAC,IAAD,CAAd,CAA5B;AACA,SAAK,EAAL,GAAU,gBAAgB,CAAC,aAAa,CAAC,EAAD,CAAd,CAA1B;AACA,SAAK,QAAL,GAAgB,4BAAQ,QAAR,EAAkB,GAAlB,CAAsB,EAAE,IAAI,KAAK,gBAAL,CAAsB,EAAtB,CAA5B,CAAhB;AACA,SAAK,uBAAL,GAA+B,KAAK,CAAC,OAAN,CAAc,QAAd,KAA2B,QAAQ,CAAC,MAAT,KAAoB,CAA9E;AACD;;AAED,EAAA,gBAAgB,CAAC,OAAD,EAAgB;AAC9B,QAAI,OAAO,CAAC,UAAR,CAAmB,IAAnB,CAAJ,EAA8B;AAC5B,MAAA,OAAO,GAAG,OAAO,CAAC,SAAR,CAAkB,KAAK,MAAvB,CAAV;AACD;;AACD,WAAO,IAAI,CAAC,KAAL,CAAW,SAAX,CAAqB,KAAK,aAAL,CAAmB,OAAO,CAAC,OAAR,CAAgB,KAAhB,EAAuB,GAAvB,CAAnB,CAArB,CAAP;AACD;;AAED,EAAA,UAAU,CAAC,OAAD,EAAgB;AACxB,SAAK,QAAL,CAAc,IAAd,CAAmB,KAAK,gBAAL,CAAsB,OAAtB,CAAnB;AACD;;AAED,EAAA,cAAc,CAAC,OAAD,EAAgB;AAC5B,SAAK,QAAL,CAAc,OAAd,CAAsB,KAAK,gBAAL,CAAsB,OAAtB,CAAtB;AACD;;AAED,EAAA,OAAO,GAAA;AACL,WAAO,KAAK,QAAL,CAAc,MAAd,KAAyB,CAAhC;AACD;;AAED,EAAA,kBAAkB,GAAA;AAChB,WAAO,CAAC,KAAK,OAAL,EAAD,IAAmB,KAAK,QAAL,CAAc,IAAd,CAAmB,EAAE,IAAI,CAAC,EAAE,CAAC,UAAH,CAAc,GAAd,CAA1B,KAAiD,IAA3E;AACD;;AAED,EAAA,qBAAqB,CAAC,MAAD,EAA2B,OAA3B,EAA2C;AAC9D,UAAM,YAAY,GAAG,OAAO,IAAI,IAAX,GAAkB,IAAlB,GAAyB,IAAI,CAAC,QAAL,CAAc,OAAd,EAAuB,KAAK,IAA5B,CAA9C;;AAEA,QAAI,KAAK,QAAL,CAAc,MAAd,KAAyB,CAAzB,IAA8B,YAAY,IAAI,IAAlD,EAAwD;AACtD;AACA,MAAA,MAAM,CAAC,IAAP,CAAY,KAAI,sBAAJ,EAAc,YAAd,EAA4B,gBAA5B,CAAZ;AACA;AACD;;AAED,SAAK,IAAI,OAAT,IAAoB,KAAK,QAAzB,EAAmC;AACjC,UAAI,YAAY,IAAI,IAApB,EAA0B;AACxB,QAAA,OAAO,GAAG,IAAI,CAAC,IAAL,CAAU,YAAV,EAAwB,OAAxB,CAAV;AACD;;AAED,YAAM,aAAa,GAAG,KAAI,sBAAJ,EAAc,OAAd,EAAuB,gBAAvB,CAAtB;AACA,MAAA,MAAM,CAAC,IAAP,CAAY,aAAZ,EANiC,CAQjC;;AACA,UAAI,CAAC,OAAO,CAAC,QAAR,CAAiB,GAAjB,CAAD,IAA0B,CAAC,wBAAS,aAAT,CAA/B,EAAwD;AACtD;AACA;AACA,QAAA,MAAM,CAAC,IAAP,CAAY,KAAI,sBAAJ,EAAc,GAAG,OAAO,OAAxB,EAAiC,gBAAjC,CAAZ;AACD;AACF;AACF;;AAED,EAAA,YAAY,GAAA;AACV,UAAM,cAAc,GAAqB,EAAzC;AACA,SAAK,qBAAL,CAA2B,cAA3B;AACA,WAAO,4BAAa,KAAK,IAAlB,EAAwB,cAAxB,EAAwC,KAAK,eAA7C,CAAP;AACD;;AAED,EAAA,QAAQ,GAAA;AACN,WAAO,SAAS,KAAK,IAAI,SAAS,KAAK,EAAE,eAAe,KAAK,QAAL,CAAc,IAAd,CAAmB,IAAnB,CAAwB,EAAhF;AACD;;AA1EqB;AA6ExB;;;;;AACM,SAAU,mBAAV,CAA8B,MAA9B,EAA8C,WAA9C,EAAmE,aAAnE,EAA+G,4BAA/G,EAA2K,gBAA3K,EAAoN,MAApN,EAAoO,iBAApO,EAA8P;AAClQ,QAAM,QAAQ,GAAG,gBAAgB,CAAC,IAAlC;AACA,QAAM,gBAAgB,GAAG,IAAI,CAAC,OAAL,CAAa,QAAQ,CAAC,UAAtB,EAAkC,QAAQ,CAAC,iBAA3C,CAAzB;AAEA,MAAI,QAAQ,GAAG,QAAQ,CAAC,kBAAT,GAA8B,IAA9B,GAAqC,eAAe,CAAC,QAAQ,CAAC,MAAV,EAAkB,OAAlB,EAA2B,WAA3B,EAAwC;AACzG,IAAA,aADyG;AAEzG,IAAA,kBAAkB,EAAE,4BAFqF;AAGzG,IAAA,YAAY,EAAE,MAH2F;AAIzG,IAAA,UAAU,EAAE;AAJ6F,GAAxC,CAAnE;;AAMA,MAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,IAAA,QAAQ,GAAG,CAAC,IAAI,WAAJ,CAAgB,MAAhB,EAAwB,WAAxB,EAAqC,aAArC,CAAD,CAAX;AACD;;AAED,QAAM,OAAO,GAAG,QAAQ,CAAC,CAAD,CAAxB,CAdkQ,CAelQ;;AACA,MAAI,OAAO,CAAC,IAAR,KAAiB,MAArB,EAA6B;AAC3B,WAAO,QAAP;AACD,GAlBiQ,CAoBlQ;;;AACA,QAAM,QAAQ,GAAG,OAAO,CAAC,QAAzB;AAEA,QAAM,mBAAmB,GAAkB,EAA3C,CAvBkQ,CAwBlQ;;AACA,MAAI,CAAC,OAAO,CAAC,uBAAT,KAAqC,OAAO,CAAC,OAAR,MAAqB,OAAO,CAAC,kBAAR,EAA1D,CAAJ,EAA6F;AAC3F,IAAA,mBAAmB,CAAC,IAApB,CAAyB,MAAzB;AACD,GAFD,MAGK,IAAI,CAAC,QAAQ,CAAC,QAAT,CAAkB,cAAlB,CAAL,EAAwC;AAC3C,IAAA,QAAQ,CAAC,IAAT,CAAc,cAAd;AACD,GA9BiQ,CAgClQ;;;AACA,QAAM,wBAAwB,GAAG,IAAI,CAAC,QAAL,CAAc,OAAO,CAAC,IAAtB,EAA4B,gBAA5B,CAAjC;;AACA,MAAI,wBAAwB,CAAC,MAAzB,KAAoC,CAApC,IAAyC,CAAC,wBAAwB,CAAC,UAAzB,CAAoC,GAApC,CAA9C,EAAwF;AACtF,IAAA,mBAAmB,CAAC,IAApB,CAAyB,IAAI,wBAAwB,UAArD;AACD;;AAED,QAAM,cAAc,GAAG,OAAO,CAAC,gBAAR,CAAyB,IAAI,CAAC,QAAL,CAAc,QAAQ,CAAC,UAAvB,EAAmC,MAAnC,CAAzB,CAAvB;;AACA,MAAI,CAAC,cAAc,CAAC,UAAf,CAA0B,GAA1B,CAAL,EAAqC;AACnC,IAAA,mBAAmB,CAAC,IAApB,CAAyB,IAAI,cAAc,UAA3C;AACD,GAzCiQ,CA2ClQ;;;AACA,MAAI,WAAW,GAAG,CAAlB;;AACA,OAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAT,GAAkB,CAA/B,EAAkC,CAAC,IAAI,CAAvC,EAA0C,CAAC,EAA3C,EAA+C;AAC7C,QAAI,QAAQ,CAAC,CAAD,CAAR,CAAY,UAAZ,CAAuB,KAAvB,CAAJ,EAAmC;AACjC,MAAA,WAAW,GAAG,CAAC,GAAG,CAAlB;AACA;AACD;AACF;;AACD,EAAA,QAAQ,CAAC,MAAT,CAAgB,WAAhB,EAA6B,CAA7B,EAAgC,GAAG,mBAAnC;AAEA,EAAA,QAAQ,CAAC,IAAT,CAAc,UAAU,YAAY,GAAG,QAAQ,CAAC,MAAT,CAAgB,UAAhB,KAA+B,IAA/B,GAAsC,EAAtC,GAA2C,MAAM,EAAxF;AACA,EAAA,QAAQ,CAAC,IAAT,CAAc,SAAd;AACA,EAAA,QAAQ,CAAC,IAAT,CAAc,iDAAd;AACA,EAAA,QAAQ,CAAC,IAAT,CAAc,QAAQ,aAAa,GAAnC;;AAEA,MAAI,iBAAJ,EAAuB;AACrB,IAAA,QAAQ,CAAC,IAAT,CAAc,iBAAd;AACD;;AACD,EAAA,QAAQ,CAAC,IAAT,CAAc,gBAAd,EA7DkQ,CA+DlQ;AACA;;AACA,EAAA,QAAQ,CAAC,IAAT,CAAc,gBAAd;AACA,EAAA,QAAQ,CAAC,IAAT,CAAc,cAAd;AAEA,QAAM,WAAW,GAAG,QAAQ,CAAC,WAA7B;;AACA,MAAI,WAAW,CAAC,SAAhB,EAA2B;AACzB;AACA,IAAA,WAAW,CAAC,GAAZ,CAAgB,GAAG,aAAa,CAAC,SAAD,CAAW,6BAA3C,EAA0E,QAA1E;AACD;;AACD,SAAO,QAAP;AACD;AAED;;;AACM,SAAU,wBAAV,CAAmC,MAAnC,EAAmD,WAAnD,EAAwE,aAAxE,EAAoH,4BAApH,EAAgL,QAAhL,EAAkM;AACtM;AACA;AACA,QAAM,OAAO,GAAG,IAAI,WAAJ,CAAgB,MAAhB,EAAwB,WAAxB,EAAqC,aAArC,CAAhB;;AAEA,WAAS,WAAT,CAAqB,QAArB,EAA4F;AAC1F,QAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB;AACD,KAFD,MAGK,IAAI,CAAC,KAAK,CAAC,OAAN,CAAc,QAAd,CAAL,EAA8B;AACjC,UAAI,OAAO,QAAP,KAAoB,QAApB,IAAgC,QAAQ,CAAC,UAAT,CAAoB,GAApB,CAApC,EAA8D;AAC5D,QAAA,OAAO,CAAC,UAAR,CAAmB,QAAnB;AACA;AACD,OAJgC,CAKjC;;;AACA;AACD;;AAED,SAAK,MAAM,OAAX,IAAsB,QAAtB,EAAgC;AAC9B,UAAI,OAAO,OAAP,KAAmB,QAAvB,EAAiC;AAC/B,YAAI,OAAO,CAAC,UAAR,CAAmB,GAAnB,CAAJ,EAA6B;AAC3B,UAAA,OAAO,CAAC,UAAR,CAAmB,OAAnB;AACD;AACF,OAJD,MAKK;AACH,cAAM,OAAO,GAAI,OAAjB;;AACA,YAAI,OAAO,CAAC,IAAR,IAAgB,IAAhB,IAAwB,OAAO,CAAC,IAAR,KAAiB,GAA7C,EAAkD;AAChD,eAAK,MAAM,CAAX,IAAgB,4BAAQ,OAAO,CAAC,MAAhB,CAAhB,EAAyC;AACvC,YAAA,OAAO,CAAC,UAAR,CAAmB,CAAnB;AACD;AACF;AACF;AACF;AACF;;AAED,EAAA,WAAW,CAAC,QAAQ,CAAC,MAAT,CAAgB,KAAjB,CAAX;AACA,EAAA,WAAW,CAAC,4BAA4B,CAAC,KAA9B,CAAX;;AAEA,MAAI,CAAC,OAAO,CAAC,OAAR,EAAL,EAAwB;AACtB,IAAA,OAAO,CAAC,cAAR,CAAuB,MAAvB;AACD;;AAED,QAAM,WAAW,GAAG,QAAQ,CAAC,WAA7B;;AACA,MAAI,WAAW,CAAC,SAAhB,EAA2B;AACzB;AACA,IAAA,WAAW,CAAC,GAAZ,CAAgB,GAAG,aAAa,CAAC,SAAD,CAAW,yBAA3C,EAAsE,OAAO,CAAC,QAA9E;AACD;;AAED,SAAO,OAAP;AACD;AAUD;;;AACM,SAAU,eAAV,CAA0B,MAA1B,EAAiD,IAAjD,EAAoI,kBAApI,EAAgK,OAAhK,EAA+L;AACnM,QAAM,cAAc,GAAG,IAAI,WAAJ,CAAgB,OAAO,CAAC,UAAxB,EAAoC,kBAApC,EAAwD,OAAO,CAAC,aAAhE,CAAvB;AACA,QAAM,YAAY,GAAuB,EAAzC;;AAEA,WAAS,WAAT,CAAqB,QAArB,EAA4F;AAC1F,QAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB;AACD,KAFD,MAGK,IAAI,CAAC,KAAK,CAAC,OAAN,CAAc,QAAd,CAAL,EAA8B;AACjC,UAAI,OAAO,QAAP,KAAoB,QAAxB,EAAkC;AAChC,QAAA,cAAc,CAAC,UAAf,CAA0B,QAA1B;AACA;AACD;;AACD,MAAA,QAAQ,GAAG,CAAC,QAAD,CAAX;AACD;;AAED,SAAK,MAAM,OAAX,IAAsB,QAAtB,EAAgC;AAC9B,UAAI,OAAO,OAAP,KAAmB,QAAvB,EAAiC;AAC/B;AACA,QAAA,cAAc,CAAC,UAAf,CAA0B,OAA1B;AACD,OAHD,MAIK,IAAI,IAAI,KAAK,YAAb,EAA2B;AAC9B,cAAM,IAAI,KAAJ,CAAU,4CAA4C,IAAI,GAA1D,CAAN;AACD,OAFI,MAGA;AACH,cAAM,IAAI,GAAG,OAAO,CAAC,IAAR,IAAgB,IAAhB,GAAuB,OAAO,CAAC,UAA/B,GAA4C,IAAI,CAAC,OAAL,CAAa,OAAO,CAAC,UAArB,EAAiC,OAAO,CAAC,IAAzC,CAAzD;AACA,cAAM,EAAE,GAAG,OAAO,CAAC,EAAR,IAAc,IAAd,GAAqB,kBAArB,GAA0C,IAAI,CAAC,OAAL,CAAa,kBAAb,EAAiC,OAAO,CAAC,EAAzC,CAArD;AACA,QAAA,YAAY,CAAC,IAAb,CAAkB,IAAI,WAAJ,CAAgB,IAAhB,EAAsB,EAAtB,EAA0B,OAAO,CAAC,aAAlC,EAAiD,OAAO,CAAC,MAAzD,CAAlB;AACD;AACF;AACF;;AAED,MAAI,IAAI,KAAK,gBAAb,EAA+B;AAC7B,IAAA,WAAW,CAAE,MAAc,CAAC,IAAD,CAAhB,CAAX;AACD;;AACD,EAAA,WAAW,CAAE,OAAO,CAAC,kBAAR,CAAmC,IAAnC,CAAF,CAAX;;AAEA,MAAI,CAAC,cAAc,CAAC,OAAf,EAAL,EAA+B;AAC7B;AACA,IAAA,YAAY,CAAC,OAAb,CAAqB,cAArB;AACD,GAxCkM,CA0CnM;;;AACA,QAAM,cAAc,GAAG,cAAc,CAAC,gBAAf,CAAgC,IAAI,CAAC,QAAL,CAAc,OAAO,CAAC,UAAtB,EAAkC,OAAO,CAAC,YAA1C,CAAhC,CAAvB;;AACA,MAAI,CAAC,cAAc,CAAC,UAAf,CAA0B,GAA1B,CAAL,EAAqC;AACnC,IAAA,cAAc,CAAC,UAAf,CAA0B,IAAI,cAAc,qBAA5C;AACD;;AAED,SAAO,YAAY,CAAC,MAAb,KAAwB,CAAxB,GAA4B,IAA5B,GAAmC,YAA1C;AACD;AAED;;;AACM,SAAU,SAAV,CAAoB,QAApB,EAAyD,WAAzD,EAA8F,aAA9F,EAAqH;AACzH,MAAI,QAAQ,IAAI,IAAZ,IAAoB,QAAQ,CAAC,MAAT,KAAoB,CAA5C,EAA+C;AAC7C,WAAO,OAAO,CAAC,OAAR,EAAP;AACD;;AAED,SAAO,uBAAgB,GAAhB,CAAoB,QAApB,EAA8B,MAAO,OAAP,IAA+B;AAClE,UAAM,QAAQ,GAAG,MAAM,sBAAW,OAAO,CAAC,IAAnB,CAAvB;;AACA,QAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,yBAAI,IAAJ,CAAS;AAAC,QAAA,IAAI,EAAE,OAAO,CAAC;AAAf,OAAT,EAA+B,2BAA/B;;AACA;AACD;;AAED,QAAI,QAAQ,CAAC,MAAT,EAAJ,EAAuB;AACrB,YAAM,MAAM,GAAG,MAAM,sBAAW,OAAO,CAAC,EAAnB,CAArB,CADqB,CAErB;;AACA,UAAI,MAAM,IAAI,IAAV,IAAkB,MAAM,CAAC,WAAP,EAAtB,EAA4C;AAC1C,eAAO,MAAM,0BAAe,OAAO,CAAC,IAAvB,EAA6B,IAAI,CAAC,IAAL,CAAU,OAAO,CAAC,EAAlB,EAAsB,IAAI,CAAC,QAAL,CAAc,OAAO,CAAC,IAAtB,CAAtB,CAA7B,EAAiF,QAAjF,EAA2F,aAA3F,CAAb;AACD;;AAED,YAAM,0BAAU,IAAI,CAAC,OAAL,CAAa,OAAO,CAAC,EAArB,CAAV,CAAN;AACA,aAAO,MAAM,0BAAe,OAAO,CAAC,IAAvB,EAA6B,OAAO,CAAC,EAArC,EAAyC,QAAzC,CAAb;AACD;;AAED,QAAI,OAAO,CAAC,OAAR,MAAqB,OAAO,CAAC,kBAAR,EAAzB,EAAuD;AACrD,MAAA,OAAO,CAAC,cAAR,CAAuB,MAAvB;AACD;;AACD,uBAAI,KAAJ,CAAU;AAAC,MAAA;AAAD,KAAV,EAAqB,6BAArB;;AACA,WAAO,MAAM,mBAAQ,OAAO,CAAC,IAAhB,EAAsB,OAAO,CAAC,EAA9B,EAAkC;AAAC,MAAA,MAAM,EAAE,OAAO,CAAC,YAAR,EAAT;AAAiC,MAAA,WAAjC;AAA8C,MAAA,aAAa,EAAE,aAAa,GAAG,oBAAH,GAAoB;AAA9F,KAAlC,CAAb;AACD,GAvBM,CAAP;AAwBD,C", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { asArray, log } from \"builder-util\"\nimport { copyDir, copyOrLinkFile, Filter, statOrNull, FileTransformer, USE_HARD_LINKS } from \"builder-util/out/fs\"\nimport { ensureDir } from \"fs-extra\"\nimport { Minimatch } from \"minimatch\"\nimport * as path from \"path\"\nimport { Configuration, FileSet, Packager, PlatformSpecificBuildOptions } from \"./index\"\nimport { PlatformPackager } from \"./platformPackager\"\nimport { createFilter, hasMagic } from \"./util/filter\"\n\n// https://github.com/electron-userland/electron-builder/issues/733\nconst minimatchOptions = {dot: true}\n\n// noinspection SpellCheckingInspection\nexport const excludedNames = \".git,.hg,.svn,CVS,RCS,SCCS,\" +\n  \"__pycache__,.DS_Store,thumbs.db,.gitignore,.gitkeep,.gitattributes,.npmignore,\" +\n  \".idea,.vs,.flowconfig,.jshintrc,.eslintrc,.circleci,\" +\n  \".yarn-integrity,.yarn-metadata.json,yarn-error.log,yarn.lock,package-lock.json,npm-debug.log,\" +\n  \"appveyor.yml,.travis.yml,circle.yml,.nyc_output\"\n\nexport const excludedExts = \"iml,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,suo,xproj,cc,d.ts\"\n\nfunction ensureNoEndSlash(file: string): string {\n  if (path.sep !== \"/\") {\n    file = file.replace(/\\//g, path.sep)\n  }\n  if (path.sep !== \"\\\\\") {\n    file = file.replace(/\\\\/g, path.sep)\n  }\n\n  if (file.endsWith(path.sep)) {\n    return file.substring(0, file.length - 1)\n  }\n  else {\n    return file\n  }\n}\n\n/** @internal */\nexport class FileMatcher {\n  readonly from: string\n  readonly to: string\n\n  readonly patterns: Array<string>\n\n  excludePatterns: Array<Minimatch> | null = null\n\n  readonly isSpecifiedAsEmptyArray: boolean\n\n  constructor(from: string, to: string, readonly macroExpander: (pattern: string) => string, patterns?: Array<string> | string | null | undefined) {\n    this.from = ensureNoEndSlash(macroExpander(from))\n    this.to = ensureNoEndSlash(macroExpander(to))\n    this.patterns = asArray(patterns).map(it => this.normalizePattern(it))\n    this.isSpecifiedAsEmptyArray = Array.isArray(patterns) && patterns.length === 0\n  }\n\n  normalizePattern(pattern: string) {\n    if (pattern.startsWith(\"./\")) {\n      pattern = pattern.substring(\"./\".length)\n    }\n    return path.posix.normalize(this.macroExpander(pattern.replace(/\\\\/g, \"/\")))\n  }\n\n  addPattern(pattern: string) {\n    this.patterns.push(this.normalizePattern(pattern))\n  }\n\n  prependPattern(pattern: string) {\n    this.patterns.unshift(this.normalizePattern(pattern))\n  }\n\n  isEmpty() {\n    return this.patterns.length === 0\n  }\n\n  containsOnlyIgnore(): boolean {\n    return !this.isEmpty() && this.patterns.find(it => !it.startsWith(\"!\")) == null\n  }\n\n  computeParsedPatterns(result: Array<Minimatch>, fromDir?: string): void {\n    const relativeFrom = fromDir == null ? null : path.relative(fromDir, this.from)\n\n    if (this.patterns.length === 0 && relativeFrom != null) {\n      // file mappings, from here is a file\n      result.push(new Minimatch(relativeFrom, minimatchOptions))\n      return\n    }\n\n    for (let pattern of this.patterns) {\n      if (relativeFrom != null) {\n        pattern = path.join(relativeFrom, pattern)\n      }\n\n      const parsedPattern = new Minimatch(pattern, minimatchOptions)\n      result.push(parsedPattern)\n\n      // do not add if contains dot (possibly file if has extension)\n      if (!pattern.includes(\".\") && !hasMagic(parsedPattern)) {\n        // https://github.com/electron-userland/electron-builder/issues/545\n        // add **/*\n        result.push(new Minimatch(`${pattern}/**/*`, minimatchOptions))\n      }\n    }\n  }\n\n  createFilter(): Filter {\n    const parsedPatterns: Array<Minimatch> = []\n    this.computeParsedPatterns(parsedPatterns)\n    return createFilter(this.from, parsedPatterns, this.excludePatterns)\n  }\n\n  toString() {\n    return `from: ${this.from}, to: ${this.to}, patterns: ${this.patterns.join(\", \")}`\n  }\n}\n\n/** @internal */\nexport function getMainFileMatchers(appDir: string, destination: string, macroExpander: (pattern: string) => string, platformSpecificBuildOptions: PlatformSpecificBuildOptions, platformPackager: PlatformPackager<any>, outDir: string, isElectronCompile: boolean): Array<FileMatcher> {\n  const packager = platformPackager.info\n  const buildResourceDir = path.resolve(packager.projectDir, packager.buildResourcesDir)\n\n  let matchers = packager.isPrepackedAppAsar ? null : getFileMatchers(packager.config, \"files\", destination, {\n    macroExpander,\n    customBuildOptions: platformSpecificBuildOptions,\n    globalOutDir: outDir,\n    defaultSrc: appDir,\n  })\n  if (matchers == null) {\n    matchers = [new FileMatcher(appDir, destination, macroExpander)]\n  }\n\n  const matcher = matchers[0]\n  // add default patterns, but only if from equals to app dir\n  if (matcher.from !== appDir) {\n    return matchers\n  }\n\n  // https://github.com/electron-userland/electron-builder/issues/1741#issuecomment-311111418 so, do not use inclusive patterns\n  const patterns = matcher.patterns\n\n  const customFirstPatterns: Array<string> = []\n  // electron-webpack - we need to copy only package.json and node_modules from root dir (and these files are added by default), so, explicit empty array is specified\n  if (!matcher.isSpecifiedAsEmptyArray && (matcher.isEmpty() || matcher.containsOnlyIgnore())) {\n    customFirstPatterns.push(\"**/*\")\n  }\n  else if (!patterns.includes(\"package.json\")) {\n    patterns.push(\"package.json\")\n  }\n\n  // https://github.com/electron-userland/electron-builder/issues/1482\n  const relativeBuildResourceDir = path.relative(matcher.from, buildResourceDir)\n  if (relativeBuildResourceDir.length !== 0 && !relativeBuildResourceDir.startsWith(\".\")) {\n    customFirstPatterns.push(`!${relativeBuildResourceDir}{,/**/*}`)\n  }\n\n  const relativeOutDir = matcher.normalizePattern(path.relative(packager.projectDir, outDir))\n  if (!relativeOutDir.startsWith(\".\")) {\n    customFirstPatterns.push(`!${relativeOutDir}{,/**/*}`)\n  }\n\n  // add our default exclusions after last user possibly defined \"all\"/permissive pattern\n  let insertIndex = 0\n  for (let i = patterns.length - 1; i >= 0; i--) {\n    if (patterns[i].startsWith(\"**/\")) {\n      insertIndex = i + 1\n      break\n    }\n  }\n  patterns.splice(insertIndex, 0, ...customFirstPatterns)\n\n  patterns.push(`!**/*.{${excludedExts}${packager.config.includePdb === true ? \"\" : \",pdb\"}`)\n  patterns.push(\"!**/._*\")\n  patterns.push(\"!**/electron-builder.{yaml,yml,json,json5,toml}\")\n  patterns.push(`!**/{${excludedNames}}`)\n\n  if (isElectronCompile) {\n    patterns.push(\"!.cache{,/**/*}\")\n  }\n  patterns.push(\"!.yarn{,/**/*}\")\n\n  // https://github.com/electron-userland/electron-builder/issues/1969\n  // exclude ony for app root, use .yarnclean to clean node_modules\n  patterns.push(\"!.editorconfig\")\n  patterns.push(\"!.yarnrc.yml\")\n\n  const debugLogger = packager.debugLogger\n  if (debugLogger.isEnabled) {\n    //tslint:disable-next-line:no-invalid-template-strings\n    debugLogger.add(`${macroExpander(\"${arch}\")}.firstOrDefaultFilePatterns`, patterns)\n  }\n  return matchers\n}\n\n/** @internal */\nexport function getNodeModuleFileMatcher(appDir: string, destination: string, macroExpander: (pattern: string) => string, platformSpecificBuildOptions: PlatformSpecificBuildOptions, packager: Packager): FileMatcher {\n  // https://github.com/electron-userland/electron-builder/pull/2948#issuecomment-392241632\n  // grab only excludes\n  const matcher = new FileMatcher(appDir, destination, macroExpander)\n\n  function addPatterns(patterns: Array<string | FileSet> | string | null | undefined | FileSet) {\n    if (patterns == null) {\n      return\n    }\n    else if (!Array.isArray(patterns)) {\n      if (typeof patterns === \"string\" && patterns.startsWith(\"!\")) {\n        matcher.addPattern(patterns)\n        return\n      }\n      // ignore object form\n      return\n    }\n\n    for (const pattern of patterns) {\n      if (typeof pattern === \"string\") {\n        if (pattern.startsWith(\"!\")) {\n          matcher.addPattern(pattern)\n        }\n      }\n      else {\n        const fileSet = (pattern as FileSet)\n        if (fileSet.from == null || fileSet.from === \".\") {\n          for (const p of asArray(fileSet.filter)) {\n            matcher.addPattern(p)\n          }\n        }\n      }\n    }\n  }\n\n  addPatterns(packager.config.files)\n  addPatterns(platformSpecificBuildOptions.files)\n\n  if (!matcher.isEmpty()) {\n    matcher.prependPattern(\"**/*\")\n  }\n\n  const debugLogger = packager.debugLogger\n  if (debugLogger.isEnabled) {\n    //tslint:disable-next-line:no-invalid-template-strings\n    debugLogger.add(`${macroExpander(\"${arch}\")}.nodeModuleFilePatterns`, matcher.patterns)\n  }\n\n  return matcher\n}\n\nexport interface GetFileMatchersOptions {\n  readonly macroExpander: (pattern: string) => string\n  readonly customBuildOptions: PlatformSpecificBuildOptions\n  readonly globalOutDir: string\n\n  readonly defaultSrc: string\n}\n\n/** @internal */\nexport function getFileMatchers(config: Configuration, name: \"files\" | \"extraFiles\" | \"extraResources\" | \"asarUnpack\" | \"extraDistFiles\", defaultDestination: string, options: GetFileMatchersOptions): Array<FileMatcher> | null {\n  const defaultMatcher = new FileMatcher(options.defaultSrc, defaultDestination, options.macroExpander)\n  const fileMatchers: Array<FileMatcher> = []\n\n  function addPatterns(patterns: Array<string | FileSet> | string | null | undefined | FileSet) {\n    if (patterns == null) {\n      return\n    }\n    else if (!Array.isArray(patterns)) {\n      if (typeof patterns === \"string\") {\n        defaultMatcher.addPattern(patterns)\n        return\n      }\n      patterns = [patterns]\n    }\n\n    for (const pattern of patterns) {\n      if (typeof pattern === \"string\") {\n        // use normalize to transform ./foo to foo\n        defaultMatcher.addPattern(pattern)\n      }\n      else if (name === \"asarUnpack\") {\n        throw new Error(`Advanced file copying not supported for \"${name}\"`)\n      }\n      else {\n        const from = pattern.from == null ? options.defaultSrc : path.resolve(options.defaultSrc, pattern.from)\n        const to = pattern.to == null ? defaultDestination : path.resolve(defaultDestination, pattern.to)\n        fileMatchers.push(new FileMatcher(from, to, options.macroExpander, pattern.filter))\n      }\n    }\n  }\n\n  if (name !== \"extraDistFiles\") {\n    addPatterns((config as any)[name])\n  }\n  addPatterns((options.customBuildOptions as any)[name])\n\n  if (!defaultMatcher.isEmpty()) {\n    // default matcher should be first in the array\n    fileMatchers.unshift(defaultMatcher)\n  }\n\n  // we cannot exclude the whole out dir, because sometimes users want to use some file in the out dir in the patterns\n  const relativeOutDir = defaultMatcher.normalizePattern(path.relative(options.defaultSrc, options.globalOutDir))\n  if (!relativeOutDir.startsWith(\".\")) {\n    defaultMatcher.addPattern(`!${relativeOutDir}/*-unpacked{,/**/*}`)\n  }\n\n  return fileMatchers.length === 0 ? null : fileMatchers\n}\n\n/** @internal */\nexport function copyFiles(matchers: Array<FileMatcher> | null, transformer: FileTransformer | null, isUseHardLink?: boolean): Promise<any> {\n  if (matchers == null || matchers.length === 0) {\n    return Promise.resolve()\n  }\n\n  return BluebirdPromise.map(matchers, async (matcher: FileMatcher) => {\n    const fromStat = await statOrNull(matcher.from)\n    if (fromStat == null) {\n      log.warn({from: matcher.from}, `file source doesn't exist`)\n      return\n    }\n\n    if (fromStat.isFile()) {\n      const toStat = await statOrNull(matcher.to)\n      // https://github.com/electron-userland/electron-builder/issues/1245\n      if (toStat != null && toStat.isDirectory()) {\n        return await copyOrLinkFile(matcher.from, path.join(matcher.to, path.basename(matcher.from)), fromStat, isUseHardLink)\n      }\n\n      await ensureDir(path.dirname(matcher.to))\n      return await copyOrLinkFile(matcher.from, matcher.to, fromStat)\n    }\n\n    if (matcher.isEmpty() || matcher.containsOnlyIgnore()) {\n      matcher.prependPattern(\"**/*\")\n    }\n    log.debug({matcher}, \"copying files using pattern\")\n    return await copyDir(matcher.from, matcher.to, {filter: matcher.createFilter(), transformer, isUseHardLink: isUseHardLink ? USE_HARD_LINKS : null})\n  })\n}"], "sourceRoot": ""}