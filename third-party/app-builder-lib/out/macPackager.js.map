{"version": 3, "sources": ["../src/macPackager.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAIA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;;;AAEc,MAAO,WAAP,SAA2B,oCAA3B,CAA6D;AA0BzE,EAAA,WAAA,CAAY,IAAZ,EAA0B;AACxB,UAAM,IAAN,EAAY,iBAAS,GAArB;AA1BO,SAAA,eAAA,GAAkB,KAAI,eAAJ,EAA0B,MAAK;AACxD,YAAM,OAAO,GAAG,KAAK,UAAL,EAAhB;;AACA,UAAI,OAAO,IAAI,IAAX,IAAmB,OAAO,CAAC,QAAR,KAAqB,QAA5C,EAAsD;AACpD,eAAO,OAAO,CAAC,OAAR,CAAgB;AAAC,UAAA,YAAY,EAAE,OAAO,CAAC,GAAR,CAAY,YAAZ,IAA4B;AAA3C,SAAhB,CAAP;AACD;;AAED,aAAO,mCAAe;AACpB,QAAA,MAAM,EAAE,KAAK,IAAL,CAAU,cADE;AAEpB,QAAA,OAFoB;AAGpB,QAAA,cAAc,EAAE,KAAK,cAAL,EAHI;AAIpB,QAAA,QAAQ,EAAE,uCAAc,KAAK,4BAAL,CAAkC,gBAAhD,EAAkE,OAAO,CAAC,GAAR,CAAY,kBAA9E,CAJU;AAKpB,QAAA,eAAe,EAAE,uCAAc,KAAK,4BAAL,CAAkC,uBAAhD,EAAyE,OAAO,CAAC,GAAR,CAAY,0BAArF,CALG;AAMpB,QAAA,UAAU,EAAE,KAAK;AANG,OAAf,EAQJ,IARI,CAQC,MAAM,IAAG;AACb,cAAM,YAAY,GAAG,MAAM,CAAC,YAA5B;;AACA,YAAI,YAAY,IAAI,IAApB,EAA0B;AACxB,eAAK,IAAL,CAAU,oBAAV,CAA+B,MAAM,mCAAe,YAAf,CAArC;AACD;;AACD,eAAO,MAAP;AACD,OAdI,CAAP;AAeD,KArB0B,CAAlB;AAuBD,SAAA,SAAA,GAAY,KAAI,eAAJ,EAAS,MAAM,KAAK,gBAAL,CAAsB,MAAtB,CAAf,CAAZ;AAIP;;AAED,MAAI,aAAJ,GAAiB;AACf,WAAO,KAAK,IAAL,CAAU,SAAV,CAAoB,mBAA3B;AACD,GAhCwE,CAkCzE;;;AACU,EAAA,cAAc,CAAC,OAAD,EAAiB;AACvC,WAAO,KAAI,kBAAJ,EAAY,KAAK,IAAjB,EAAuB,KAAK,4BAAL,CAAkC,aAAzD,EAAwE,KAAK,4BAA7E,CAAP;AACD;;AAED,QAAM,WAAN,GAAiB;AACf,WAAO,KAAK,SAAL,CAAe,KAAtB;AACD;;AAED,EAAA,aAAa,CAAC,OAAD,EAAyB,MAAzB,EAA4F;AACvG,SAAK,MAAM,IAAX,IAAmB,OAAnB,EAA4B;AAC1B,cAAQ,IAAR;AACE,aAAK,kBAAL;AACE;;AAEF,aAAK,KAAL;AAAY;AACV;AACA,kBAAM;AAAC,cAAA;AAAD,gBAAc,OAAO,CAAC,aAAD,CAA3B;;AACA,YAAA,MAAM,CAAC,IAAD,EAAO,MAAM,IAAI,IAAI,SAAJ,CAAc,IAAd,EAAoB,MAApB,CAAjB,CAAN;AACA;AACD;;AAED,aAAK,KAAL;AACE;AACA,UAAA,MAAM,CAAC,IAAD,EAAO,MAAM,IAAI,KAAI,8BAAJ,EAAkB,IAAlB,EAAwB,MAAxB,EAAgC,IAAhC,EAAsC,IAAtC,CAAjB,CAAN;AACA;;AAEF,aAAK,KAAL;AACE,UAAA,MAAM,CAAC,IAAD,EAAO,MAAM,IAAI,KAAI,gBAAJ,EAAc,IAAd,EAAoB,MAApB,CAAjB,CAAN;AACA;;AAEF;AACE,UAAA,MAAM,CAAC,IAAD,EAAO,MAAM,IAAI,IAAI,KAAK,KAAT,IAAkB,IAAI,KAAK,SAA3B,GAAuC,KAAI,2BAAJ,EAAe,IAAf,CAAvC,GAA8D,yCAAmB,IAAnB,EAAyB,MAAzB,EAAiC,IAAjC,CAA/E,CAAN;AACA;AAtBJ;AAwBD;AACF;;AAES,QAAM,MAAN,CAAa,MAAb,EAA6B,SAA7B,EAAgD,YAAhD,EAAoF,IAApF,EAAgG,4BAAhG,EAAgJ,OAAhJ,EAAsK;AAC9K,YAAQ,IAAR;AACE;AAAS;AACP,iBAAO,MAAM,MAAN,CAAa,MAAb,EAAqB,SAArB,EAAgC,KAAK,QAAL,CAAc,QAA9C,EAAgF,IAAhF,EAAsF,KAAK,4BAA3F,EAAyH,OAAzH,CAAP;AACD;;AACD,WAAK,oBAAK,SAAV;AAAqB;AACnB,gBAAM,OAAO,GAAG,oBAAK,GAArB;;AACA,gBAAM,YAAY,GAAG,SAAS,GAAG,GAAZ,GAAkB,oBAAK,OAAL,CAAvC;;AACA,gBAAM,MAAM,MAAN,CAAa,MAAb,EAAqB,YAArB,EAAmC,YAAnC,EAAiD,OAAjD,EAA0D,4BAA1D,EAAwF,OAAxF,EAAiG,KAAjG,CAAN;;AACA,gBAAM,SAAS,GAAG,oBAAK,KAAvB;;AACA,gBAAM,eAAe,GAAG,SAAS,GAAG,GAAZ,GAAkB,oBAAK,SAAL,CAA1C;;AACA,gBAAM,MAAM,MAAN,CAAa,MAAb,EAAqB,eAArB,EAAsC,YAAtC,EAAoD,SAApD,EAA+D,4BAA/D,EAA6F,OAA7F,EAAsG,KAAtG,CAAN;AACA,gBAAM,SAAS,GAAG,KAAK,IAAL,CAAU,SAA5B;;AACA,6BAAI,IAAJ,CAAS;AACP,YAAA,QAAQ,EAAE,YADH;AAEP,YAAA,IAAI,EAAE,oBAAK,IAAL,CAFC;AAGP,aAAC,GAAG,SAAS,CAAC,IAAI,EAAlB,GAAuB,SAAS,CAAC,OAH1B;AAIP,YAAA,SAAS,EAAE,mBAAI,QAAJ,CAAa,SAAb;AAJJ,WAAT,EAKG,WALH;;AAMA,gBAAM,OAAO,GAAG,GAAG,KAAK,OAAL,CAAa,eAAe,MAA/C;;AACA,gBAAM;AAAE,YAAA;AAAF,cAAuB,OAAO,CAAC,qBAAD,CAApC;;AACA,gBAAM,gBAAgB,CAAC;AACrB,YAAA,UAAU,EAAE,IAAI,CAAC,IAAL,CAAU,YAAV,EAAwB,OAAxB,CADS;AAErB,YAAA,YAAY,EAAE,IAAI,CAAC,IAAL,CAAU,eAAV,EAA2B,OAA3B,CAFO;AAGrB,YAAA,UAAU,EAAE,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,OAArB,CAHS;AAIrB,YAAA,KAAK,EAAE;AAJc,WAAD,CAAtB;AAMA,gBAAM,KAAK,GAAG,uBAAU,OAAO,CAAC,IAAD,CAAP,CAAc,KAAxB,CAAd;AACA,gBAAM,KAAK,CAAC,YAAD,EAAe;AAAE,YAAA,SAAS,EAAE;AAAb,WAAf,CAAX;AACA,gBAAM,KAAK,CAAC,eAAD,EAAkB;AAAE,YAAA,SAAS,EAAE;AAAb,WAAlB,CAAX;AACA,gBAAM,KAAK,eAAL,CAAqB,MAArB,EAA6B,SAA7B,EAAwC,YAAxC,EAAsD,IAAtD,EAA4D,4BAA5D,EAA0F,OAA1F,CAAN;AACA;AACD;AA/BH;AAiCD;;AAED,QAAM,IAAN,CAAW,MAAX,EAA2B,IAA3B,EAAuC,OAAvC,EAA+D,WAA/D,EAA4F;AAC1F,QAAI,aAAa,GAAwB,IAAzC;AAEA,UAAM,MAAM,GAAG,OAAO,CAAC,MAAR,KAAmB,CAAnB,IAAwB,OAAO,CAAC,IAAR,CAAa,EAAE,IAAI,EAAE,CAAC,IAAH,KAAY,KAAZ,IAAqB,EAAE,CAAC,IAAH,KAAY,SAApD,CAAvC;AACA,UAAM,WAAW,GAAG,KAAK,eAAL,CAAqB,WAAzC;;AAEA,QAAI,CAAC,MAAD,IAAW,OAAO,CAAC,MAAR,GAAiB,CAAhC,EAAmC;AACjC,YAAM,OAAO,GAAG,WAAW,IAAI,IAAf,GAAsB,IAAI,CAAC,IAAL,CAAU,KAAK,gBAAL,CAAsB,MAAtB,EAA8B,IAA9B,CAAV,EAA+C,GAAG,KAAK,OAAL,CAAa,eAAe,MAA9E,CAAtB,GAA8G,WAA9H;AACA,MAAA,aAAa,GAAG,CAAC,WAAW,GAAG,OAAO,CAAC,OAAR,EAAH,GAAuB,KAAK,MAAL,CAAY,MAAZ,EAAoB,IAAI,CAAC,OAAL,CAAa,OAAb,CAApB,EAA2C,KAAK,QAAL,CAAc,QAAzD,EAA2F,IAA3F,EAAiG,KAAK,4BAAtG,EAAoI,OAApI,CAAnC,EACb,IADa,CACR,MAAM,KAAK,4BAAL,CAAkC,OAAlC,EAA2C,IAA3C,EAAiD,OAAjD,EAA0D,WAA1D,CADE,CAAhB;AAED;;AAED,SAAK,MAAM,MAAX,IAAqB,OAArB,EAA8B;AAC5B,YAAM,UAAU,GAAG,MAAM,CAAC,IAA1B;;AACA,UAAI,EAAE,UAAU,KAAK,KAAf,IAAwB,UAAU,KAAK,SAAzC,CAAJ,EAAyD;AACvD;AACD;;AAED,YAAM,eAAe,GAAG,+BAAW,EAAX,EAAe,KAAK,4BAApB,EAAkD,KAAK,MAAL,CAAY,GAA9D,CAAxB;;AACA,UAAI,UAAU,KAAK,SAAnB,EAA8B;AAC5B,uCAAW,eAAX,EAA4B,KAAK,MAAL,CAAY,MAAxC,EAAgD;AAC9C,UAAA,IAAI,EAAE;AADwC,SAAhD;AAGD;;AAED,YAAM,YAAY,GAAG,IAAI,CAAC,IAAL,CAAU,MAAV,EAAkB,GAAG,UAAU,GAAG,kCAAc,IAAd,CAAmB,EAArD,CAArB;;AACA,UAAI,WAAW,IAAI,IAAnB,EAAyB;AACvB,cAAM,KAAK,MAAL,CAAY,MAAZ,EAAoB,YAApB,EAAkC,KAAlC,EAAyC,IAAzC,EAA+C,eAA/C,EAAgE,CAAC,MAAD,CAAhE,CAAN;AACA,cAAM,KAAK,IAAL,CAAU,IAAI,CAAC,IAAL,CAAU,YAAV,EAAwB,GAAG,KAAK,OAAL,CAAa,eAAe,MAAvD,CAAV,EAA0E,YAA1E,EAAwF,eAAxF,EAAyG,IAAzG,CAAN;AACD,OAHD,MAIK;AACH,cAAM,KAAK,IAAL,CAAU,WAAV,EAAuB,YAAvB,EAAqC,eAArC,EAAsD,IAAtD,CAAN;AACD;AACF;;AAED,QAAI,aAAa,IAAI,IAArB,EAA2B;AACzB,YAAM,aAAN;AACD;AACF;;AAEO,QAAM,IAAN,CAAW,OAAX,EAA4B,MAA5B,EAAmD,UAAnD,EAAwF,IAAxF,EAAyG;AAC/G,QAAI,CAAC,mCAAL,EAAsB;AACpB;AACD;;AAED,UAAM,KAAK,GAAG,UAAU,IAAI,IAA5B;AACA,UAAM,OAAO,GAAG,UAAU,IAAI,IAAd,GAAqB,KAAK,4BAA1B,GAAyD,UAAzE;AACA,UAAM,SAAS,GAAG,OAAO,CAAC,QAA1B;;AAEA,QAAI,CAAC,KAAD,IAAU,SAAS,KAAK,IAA5B,EAAkC;AAChC,UAAI,KAAK,gBAAT,EAA2B;AACzB,cAAM,KAAI,wCAAJ,EAA8B,yEAA9B,CAAN;AACD;;AACD,yBAAI,IAAJ,CAAS;AAAC,QAAA,MAAM,EAAE;AAAT,OAAT,EAAyD,4BAAzD;;AACA;AACD;;AAED,UAAM,YAAY,GAAG,CAAC,MAAM,KAAK,eAAL,CAAqB,KAA5B,EAAmC,YAAxD;AACA,UAAM,YAAY,GAAG,OAAO,CAAC,IAA7B;AACA,UAAM,IAAI,GAAG,YAAY,IAAI,cAA7B;AACA,UAAM,aAAa,GAAG,IAAI,KAAK,aAA/B;AACA,UAAM,eAAe,GAAG,kBAAkB,CAAC,KAAD,EAAQ,aAAR,CAA1C;AACA,QAAI,QAAQ,GAAG,MAAM,iCAAa,eAAb,EAA8B,SAA9B,EAAyC,YAAzC,CAArB;;AACA,QAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,UAAI,CAAC,KAAD,IAAU,CAAC,aAAX,IAA4B,YAAY,KAAK,cAAjD,EAAiE;AAC/D,QAAA,QAAQ,GAAG,MAAM,iCAAa,eAAb,EAA8B,SAA9B,EAAyC,YAAzC,CAAjB;;AACA,YAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,6BAAI,IAAJ,CAAS,gGAAT;AACD;AACF;;AAED,UAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,cAAM,gCAAY,KAAZ,EAAmB,eAAnB,EAAoC,SAApC,EAA+C,YAA/C,EAA6D,KAAK,gBAAlE,CAAN;AACA;AACD;AACF;;AAED,QAAI,CAAC,wCAAL,EAA0B;AACxB,YAAM,KAAI,wCAAJ,EAA8B,+CAA9B,CAAN;AACD;;AAED,QAAI,MAAM,GAAG,OAAO,CAAC,UAArB;;AACA,QAAI,KAAK,CAAC,OAAN,CAAc,MAAd,CAAJ,EAA2B;AACzB,UAAI,MAAM,CAAC,MAAP,IAAiB,CAArB,EAAwB;AACtB,QAAA,MAAM,GAAG,IAAT;AACD;AACF,KAJD,MAKK,IAAI,MAAM,IAAI,IAAd,EAAoB;AACvB,MAAA,MAAM,GAAG,MAAM,CAAC,MAAP,KAAkB,CAAlB,GAAsB,IAAtB,GAA6B,CAAC,MAAD,CAAtC;AACD;;AAED,UAAM,QAAQ,GAAG,MAAM,IAAI,IAAV,GAAiB,IAAjB,GAAwB,MAAM,CAAC,GAAP,CAAW,EAAE,IAAI,IAAI,MAAJ,CAAW,EAAX,CAAjB,CAAzC;AAEA,UAAM,WAAW,GAAQ;AACvB,6BAAuB,KADA;AAEvB;AACA;AACA,MAAA,MAAM,EAAG,IAAD,IAAiB;AACvB,YAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,eAAK,MAAM,MAAX,IAAqB,QAArB,EAA+B;AAC7B,gBAAI,MAAM,CAAC,IAAP,CAAY,IAAZ,CAAJ,EAAuB;AACrB,qBAAO,IAAP;AACD;AACF;AACF;;AACD,eAAO,IAAI,CAAC,QAAL,CAAc,OAAd,KAA0B,IAAI,CAAC,UAAL,CAAgB,mBAAhB,EAAqC,OAAO,CAAC,MAA7C,CAA1B,IACL,IAAI,CAAC,QAAL,CAAc,yCAAd,CADK,IACwD,IAAI,CAAC,QAAL,CAAc,kDAAd,CADxD,IAC6H,IAAI,CAAC,QAAL,CAAc,0CAAd,CADpI;AAGE;;;;AAIH,OAnBsB;AAoBvB,MAAA,QAAQ,EAAE,QApBa;AAqBvB,MAAA,IArBuB;AAsBvB,MAAA,QAAQ,EAAE,KAAK,GAAG,KAAH,GAAW,QAtBH;AAuBvB,MAAA,OAAO,EAAE,KAAK,MAAL,CAAY,eAvBE;AAwBvB,MAAA,GAAG,EAAE,OAxBkB;AAyBvB,MAAA,QAAQ,EAAE,YAAY,IAAI,SAzBH;AA0BvB,MAAA,QAAQ,EAAE,OAAO,CAAC,QAAR,IAAoB,SA1BP;AA2BvB,MAAA,YAAY,EAAE,KAAK,IAAI,KAAK,4BAAL,CAAkC,YAAlC,IAAkD,IAA3D,GAAkE,SAAlE,GAA8E,MAAM,KAAK,WAAL,CAAiB,KAAK,4BAAL,CAAkC,YAAnD,CA3B3E;AA4BvB;AACA;AACA,2BAAqB,OAAO,CAAC,gBAAR,KAA6B,IA9B3B;AA+BvB;AACA,uBAAiB,OAAO,CAAC,YAhCF;AAiCvB,MAAA,eAAe,EAAE,KAAK,GAAG,UAAU,IAAI,UAAU,CAAC,eAAX,KAA+B,IAAhD,GAAuD,OAAO,CAAC,eAAR,KAA4B;AAjClF,KAAzB;AAoCA,UAAM,KAAK,iBAAL,CAAuB,WAAvB,EAAoC,UAApC,CAAN;;AACA,uBAAI,IAAJ,CAAS;AACP,MAAA,IAAI,EAAE,mBAAI,QAAJ,CAAa,OAAb,CADC;AAEP,MAAA,YAAY,EAAE,QAAQ,CAAC,IAFhB;AAGP,MAAA,YAAY,EAAE,QAAQ,CAAC,IAHhB;AAIP,MAAA,mBAAmB,EAAE,WAAW,CAAC,sBAAD,CAAX,IAAuC;AAJrD,KAAT,EAKG,SALH;;AAMA,UAAM,KAAK,MAAL,CAAY,WAAZ,CAAN,CAhG+G,CAkG/G;;AACA,QAAI,UAAU,IAAI,IAAd,IAAsB,CAAC,aAA3B,EAA0C;AACxC,YAAM,QAAQ,GAAG,aAAa,GAAG,eAAH,GAAqB,mCAAnD;AACA,YAAM,oBAAoB,GAAG,MAAM,iCAAa,QAAb,EAAuB,UAAU,CAAC,QAAlC,EAA4C,YAA5C,CAAnC;;AACA,UAAI,oBAAoB,IAAI,IAA5B,EAAkC;AAChC,cAAM,KAAI,wCAAJ,EAA8B,sBAAsB,QAAQ,kFAA5D,CAAN;AACD,OALuC,CAOxC;;;AACA,YAAM,YAAY,GAAG,KAAK,yBAAL,CAA+B,UAA/B,EAA2C,KAA3C,EAAkD,IAAlD,CAArB;AACA,YAAM,YAAY,GAAG,IAAI,CAAC,IAAL,CAAU,MAAV,EAAmB,YAAnB,CAArB;AACA,YAAM,KAAK,MAAL,CAAY,OAAZ,EAAqB,YAArB,EAAmC,oBAAnC,EAAyD,YAAzD,CAAN;AACA,YAAM,KAAK,uBAAL,CAA6B,YAA7B,EAA2C,IAA3C,EAAiD,oBAAK,GAAtD,EAA2D,KAAK,uBAAL,CAA6B,YAA7B,EAA2C,KAA3C,EAAkD,IAAlD,CAA3D,CAAN;AACD;AACF;;AAEO,QAAM,iBAAN,CAAwB,WAAxB,EAA0C,UAA1C,EAA6E;AACnF,UAAM,YAAY,GAAG,MAAM,KAAK,YAAhC;AACA,UAAM,iBAAiB,GAAG,UAAU,IAAI,KAAK,4BAA7C;AACA,UAAM,kBAAkB,GAAG,UAAU,IAAI,IAAd,GAAqB,KAArB,GAA6B,KAAxD;AAEA,QAAI,YAAY,GAAG,iBAAiB,CAAC,YAArC;;AACA,QAAI,YAAY,IAAI,IAApB,EAA0B;AACxB,YAAM,CAAC,GAAG,gBAAgB,kBAAkB,QAA5C;;AACA,UAAI,YAAY,CAAC,QAAb,CAAsB,CAAtB,CAAJ,EAA8B;AAC5B,QAAA,YAAY,GAAG,IAAI,CAAC,IAAL,CAAU,KAAK,IAAL,CAAU,iBAApB,EAAuC,CAAvC,CAAf;AACD,OAFD,MAGK;AACH,QAAA,YAAY,GAAG,oCAAgB,wBAAhB,CAAf;AACD;AACF;;AACD,IAAA,WAAW,CAAC,YAAZ,GAA2B,YAA3B;AAEA,QAAI,mBAAmB,GAAG,iBAAiB,CAAC,mBAA5C;;AACA,QAAI,mBAAmB,IAAI,IAA3B,EAAiC;AAC/B,YAAM,CAAC,GAAG,gBAAgB,kBAAkB,gBAA5C;;AACA,UAAI,YAAY,CAAC,QAAb,CAAsB,CAAtB,CAAJ,EAA8B;AAC5B,QAAA,mBAAmB,GAAG,IAAI,CAAC,IAAL,CAAU,KAAK,IAAL,CAAU,iBAApB,EAAuC,CAAvC,CAAtB;AACD,OAFD,MAGK;AACH,QAAA,mBAAmB,GAAG,oCAAgB,wBAAhB,CAAtB;AACD;AACF;;AACD,IAAA,WAAW,CAAC,sBAAD,CAAX,GAAsC,mBAAtC;;AAEA,QAAI,iBAAiB,CAAC,mBAAlB,IAAyC,IAA7C,EAAmD;AACjD,MAAA,WAAW,CAAC,sBAAD,CAAX,GAAsC,iBAAiB,CAAC,mBAAxD;AACD;;AACD,IAAA,WAAW,CAAC,0BAAD,CAAX,GAA0C,iBAAiB,CAAC,uBAA5D;AACD,GAvSwE,CAySzE;;;AACU,QAAM,MAAN,CAAa,IAAb,EAA8B;AACtC,WAAO,kCAAU,IAAV,CAAP;AACD,GA5SwE,CA8SzE;;;AACU,QAAM,MAAN,CAAa,OAAb,EAA8B,OAA9B,EAA+C,QAA/C,EAAmE,QAAnE,EAAsG;AAC9G;AACA,UAAM,uBAAO,IAAI,CAAC,OAAL,CAAa,OAAb,CAAP,CAAN;AAEA,UAAM,IAAI,GAAG,oCAAwB,QAAxB,EAAkC,QAAlC,CAAb;AACA,IAAA,IAAI,CAAC,IAAL,CAAU,aAAV,EAAyB,OAAzB,EAAkC,eAAlC;AACA,IAAA,IAAI,CAAC,IAAL,CAAU,OAAV;AACA,WAAO,MAAM,yBAAK,cAAL,EAAqB,IAArB,CAAb;AACD;;AAEM,EAAA,iBAAiB,CAAC,IAAD,EAAa;AACnC,WAAO,IAAI,CAAC,OAAL,CAAa,KAAK,UAAlB,EAA8B,IAA9B,EAAoC,KAAK,IAAL,CAAU,SAAV,CAAoB,gBAAxD,CAAP;AACD;;AAEM,EAAA,yBAAyB,CAAC,SAAD,EAAkB;AAChD,WAAO,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,KAAK,IAAL,CAAU,SAAV,CAAoB,gBAAzC,CAAP;AACD,GA/TwE,CAiUzE;;;AACA,QAAM,eAAN,CAAsB,QAAtB,EAAqC,YAArC,EAAyD;AACvD,UAAM,OAAO,GAAG,KAAK,OAArB;AACA,UAAM,WAAW,GAAG,OAAO,CAAC,eAA5B,CAFuD,CAIvD;;AACA,IAAA,QAAQ,CAAC,kBAAT,GAA8B,WAAW,CAAC,QAAZ,CAAqB,SAArB,IAAkC,WAAW,CAAC,SAAZ,CAAsB,CAAtB,EAAyB,WAAW,CAAC,MAAZ,GAAqB,UAAU,MAAxD,CAAlC,GAAoG,WAAlI;AAEA,UAAM,IAAI,GAAG,MAAM,KAAK,WAAL,EAAnB;;AACA,QAAI,IAAI,IAAI,IAAZ,EAAkB;AAChB,YAAM,OAAO,GAAG,QAAQ,CAAC,gBAAzB;AACA,YAAM,aAAa,GAAG,IAAI,CAAC,IAAL,CAAU,YAAV,EAAwB,WAAxB,CAAtB;;AACA,UAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,cAAM,0BAAe,IAAI,CAAC,IAAL,CAAU,aAAV,EAAyB,OAAzB,CAAf,CAAN;AACD;;AACD,YAAM,YAAY,GAAG,GAAG,WAAW,OAAnC;AACA,MAAA,QAAQ,CAAC,gBAAT,GAA4B,YAA5B;AACA,YAAM,oBAAS,IAAT,EAAe,IAAI,CAAC,IAAL,CAAU,aAAV,EAAyB,YAAzB,CAAf,CAAN;AACD;;AACD,IAAA,QAAQ,CAAC,YAAT,GAAwB,OAAO,CAAC,WAAhC;AACA,IAAA,QAAQ,CAAC,mBAAT,GAA+B,OAAO,CAAC,WAAvC;AAEA,UAAM,oBAAoB,GAAG,KAAK,4BAAL,CAAkC,oBAA/D;;AACA,QAAI,oBAAoB,IAAI,IAA5B,EAAkC;AAChC,MAAA,QAAQ,CAAC,sBAAT,GAAkC,oBAAlC;AACD;;AAED,IAAA,QAAQ,CAAC,kBAAT,GAA8B,OAAO,CAAC,mBAAtC;AAEA,IAAA,QAAQ,CAAC,0BAAT,GAAsC,KAAK,4BAAL,CAAkC,kBAAlC,IAAwD,OAAO,CAAC,OAAtG;AACA,IAAA,QAAQ,CAAC,eAAT,GAA2B,OAAO,CAAC,YAAnC;AAEA,4BAAI,KAAK,4BAAL,CAAkC,QAAlC,IAA+C,KAAK,MAAL,CAAoB,QAAvE,EAAiF,EAAE,IAAI,QAAQ,CAAC,yBAAT,GAAqC,EAA5H;AACA,IAAA,QAAQ,CAAC,wBAAT,GAAoC,OAAO,CAAC,SAA5C;;AAEA,QAAI,KAAK,4BAAL,CAAkC,eAAtC,EAAuD;AACrD,MAAA,QAAQ,CAAC,8BAAT,GAA0C,KAA1C;AACD;;AAED,UAAM,UAAU,GAAG,KAAK,4BAAL,CAAkC,UAArD;;AACA,QAAI,UAAU,IAAI,IAAlB,EAAwB;AACtB,MAAA,MAAM,CAAC,MAAP,CAAc,QAAd,EAAwB,UAAxB;AACD;AACF;;AAES,QAAM,OAAN,CAAc,WAAd,EAA6C,MAA7C,EAA4D;AACpE,UAAM,WAAW,GAAG,GAAG,KAAK,OAAL,CAAa,eAAe,MAAnD;AAEA,UAAM,uBAAgB,GAAhB,CAAoB,wBAAQ,WAAW,CAAC,SAApB,CAApB,EAAqD,IAAD,IAAsB;AAC9E,UAAI,IAAI,KAAK,WAAb,EAA0B;AACxB,eAAO,KAAK,IAAL,CAAU,IAAI,CAAC,IAAL,CAAU,WAAW,CAAC,SAAtB,EAAiC,IAAjC,CAAV,EAAkD,IAAlD,EAAwD,IAAxD,EAA8D,IAA9D,CAAP;AACD;;AACD,aAAO,IAAP;AACD,KALK,CAAN;;AAOA,QAAI,CAAC,MAAL,EAAa;AACX;AACD;;AAED,UAAM,eAAe,GAAG,IAAI,CAAC,IAAL,CAAU,WAAW,CAAC,SAAtB,EAAiC,WAAjC,EAA8C,mBAA9C,CAAxB;AACA,UAAM,uBAAgB,GAAhB,CAAoB,iCAAiB,wBAAQ,eAAR,CAAjB,EAA2C,EAA3C,CAApB,EAAqE,IAAD,IAAsB;AAC9F,UAAI,IAAI,CAAC,QAAL,CAAc,MAAd,CAAJ,EAA2B;AACzB,eAAO,KAAK,IAAL,CAAU,IAAI,CAAC,IAAL,CAAU,eAAV,EAA2B,IAA3B,CAAV,EAA4C,IAA5C,EAAkD,IAAlD,EAAwD,IAAxD,CAAP;AACD,OAFD,MAGK;AACH,eAAO,IAAP;AACD;AACF,KAPK,CAAN;AAQD;;AArYwE;;;;AAwY3E,SAAS,kBAAT,CAA4B,KAA5B,EAA4C,aAA5C,EAAkE;AAChE,MAAI,aAAJ,EAAmB;AACjB,WAAO,eAAP;AACD;;AACD,SAAO,KAAK,GAAG,qCAAH,GAA2C,0BAAvD;AACD,C", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { deepAssign, Arch, AsyncTaskManager, exec, InvalidConfigurationError, log, use, getArchSuffix } from \"builder-util\"\nimport { signAsync, SignOptions } from \"../electron-osx-sign\"\nimport { mkdirs, readdir } from \"fs-extra\"\nimport { Lazy } from \"lazy-val\"\nimport * as path from \"path\"\nimport { copyFile, unlinkIfExists } from \"builder-util/out/fs\"\nimport { orIfFileNotExist } from \"builder-util/out/promise\"\nimport { AppInfo } from \"./appInfo\"\nimport { CertType, CodeSigningInfo, createKeychain, findIdentity, Identity, isSignAllowed, removeKeychain, reportError } from \"./codeSign/macCodeSign\"\nimport { DIR_TARGET, Platform, Target } from \"./core\"\nimport { AfterPackContext, ElectronPlatformName } from \"./index\"\nimport { MacConfiguration, MasConfiguration } from \"./options/macOptions\"\nimport { Packager } from \"./packager\"\nimport { chooseNotNull, PlatformPackager } from \"./platformPackager\"\nimport { ArchiveTarget } from \"./targets/ArchiveTarget\"\nimport { PkgTarget, prepareProductBuildArgs } from \"./targets/pkg\"\nimport { createCommonTarget, NoOpTarget } from \"./targets/targetFactory\"\nimport { isMacOsHighSierra } from \"./util/macosVersion\"\nimport { getTemplatePath } from \"./util/pathManager\"\nimport { promisify } from \"util\"\n\nexport default class MacPackager extends PlatformPackager<MacConfiguration> {\n  readonly codeSigningInfo = new Lazy<CodeSigningInfo>(() => {\n    const cscLink = this.getCscLink()\n    if (cscLink == null || process.platform !== \"darwin\") {\n      return Promise.resolve({keychainFile: process.env.CSC_KEYCHAIN || null})\n    }\n\n    return createKeychain({\n      tmpDir: this.info.tempDirManager,\n      cscLink,\n      cscKeyPassword: this.getCscPassword(),\n      cscILink: chooseNotNull(this.platformSpecificBuildOptions.cscInstallerLink, process.env.CSC_INSTALLER_LINK),\n      cscIKeyPassword: chooseNotNull(this.platformSpecificBuildOptions.cscInstallerKeyPassword, process.env.CSC_INSTALLER_KEY_PASSWORD),\n      currentDir: this.projectDir\n    })\n      .then(result => {\n        const keychainFile = result.keychainFile\n        if (keychainFile != null) {\n          this.info.disposeOnBuildFinish(() => removeKeychain(keychainFile))\n        }\n        return result\n      })\n  })\n\n  private _iconPath = new Lazy(() => this.getOrConvertIcon(\"icns\"))\n\n  constructor(info: Packager) {\n    super(info, Platform.MAC)\n  }\n\n  get defaultTarget(): Array<string> {\n    return this.info.framework.macOsDefaultTargets\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  protected prepareAppInfo(appInfo: AppInfo): AppInfo {\n    return new AppInfo(this.info, this.platformSpecificBuildOptions.bundleVersion, this.platformSpecificBuildOptions)\n  }\n\n  async getIconPath(): Promise<string | null> {\n    return this._iconPath.value\n  }\n\n  createTargets(targets: Array<string>, mapper: (name: string, factory: (outDir: string) => Target) => void): void {\n    for (const name of targets) {\n      switch (name) {\n        case DIR_TARGET:\n          break\n\n        case \"dmg\": {\n          // eslint-disable-next-line @typescript-eslint/no-var-requires\n          const {DmgTarget} = require(\"dmg-builder\")\n          mapper(name, outDir => new DmgTarget(this, outDir))\n          break\n        }\n\n        case \"zip\":\n          // https://github.com/electron-userland/electron-builder/issues/2313\n          mapper(name, outDir => new ArchiveTarget(name, outDir, this, true))\n          break\n\n        case \"pkg\":\n          mapper(name, outDir => new PkgTarget(this, outDir))\n          break\n\n        default:\n          mapper(name, outDir => name === \"mas\" || name === \"mas-dev\" ? new NoOpTarget(name) : createCommonTarget(name, outDir, this))\n          break\n      }\n    }\n  }\n\n  protected async doPack(outDir: string, appOutDir: string, platformName: ElectronPlatformName, arch: Arch, platformSpecificBuildOptions: MacConfiguration, targets: Array<Target>): Promise<any> {\n    switch (arch) {\n      default: {\n        return super.doPack(outDir, appOutDir, this.platform.nodeName as ElectronPlatformName, arch, this.platformSpecificBuildOptions, targets);\n      }\n      case Arch.universal: {\n        const x64Arch = Arch.x64;\n        const x64AppOutDir = appOutDir + '-' + Arch[x64Arch];\n        await super.doPack(outDir, x64AppOutDir, platformName, x64Arch, platformSpecificBuildOptions, targets, false);\n        const arm64Arch = Arch.arm64;\n        const arm64AppOutPath = appOutDir + '-' + Arch[arm64Arch];\n        await super.doPack(outDir, arm64AppOutPath, platformName, arm64Arch, platformSpecificBuildOptions, targets, false);\n        const framework = this.info.framework\n        log.info({\n          platform: platformName,\n          arch: Arch[arch],\n          [`${framework.name}`]: framework.version,\n          appOutDir: log.filePath(appOutDir),\n        }, `packaging`)\n        const appFile = `${this.appInfo.productFilename}.app`;\n        const { makeUniversalApp } = require('@electron/universal');\n        await makeUniversalApp({\n          x64AppPath: path.join(x64AppOutDir, appFile),\n          arm64AppPath: path.join(arm64AppOutPath, appFile),\n          outAppPath: path.join(appOutDir, appFile),\n          force: true\n        });\n        const rmdir = promisify(require('fs').rmdir);\n        await rmdir(x64AppOutDir, { recursive: true });\n        await rmdir(arm64AppOutPath, { recursive: true });\n        await this.doSignAfterPack(outDir, appOutDir, platformName, arch, platformSpecificBuildOptions, targets);\n        break;\n      }\n    }\n  }\n\n  async pack(outDir: string, arch: Arch, targets: Array<Target>, taskManager: AsyncTaskManager): Promise<any> {\n    let nonMasPromise: Promise<any> | null = null\n\n    const hasMas = targets.length !== 0 && targets.some(it => it.name === \"mas\" || it.name === \"mas-dev\")\n    const prepackaged = this.packagerOptions.prepackaged\n\n    if (!hasMas || targets.length > 1) {\n      const appPath = prepackaged == null ? path.join(this.computeAppOutDir(outDir, arch), `${this.appInfo.productFilename}.app`) : prepackaged\n      nonMasPromise = (prepackaged ? Promise.resolve() : this.doPack(outDir, path.dirname(appPath), this.platform.nodeName as ElectronPlatformName, arch, this.platformSpecificBuildOptions, targets))\n        .then(() => this.packageInDistributableFormat(appPath, arch, targets, taskManager))\n    }\n\n    for (const target of targets) {\n      const targetName = target.name\n      if (!(targetName === \"mas\" || targetName === \"mas-dev\")) {\n        continue\n      }\n\n      const masBuildOptions = deepAssign({}, this.platformSpecificBuildOptions, this.config.mas)\n      if (targetName === \"mas-dev\") {\n        deepAssign(masBuildOptions, this.config.masDev, {\n          type: \"development\",\n        })\n      }\n\n      const targetOutDir = path.join(outDir, `${targetName}${getArchSuffix(arch)}`)\n      if (prepackaged == null) {\n        await this.doPack(outDir, targetOutDir, \"mas\", arch, masBuildOptions, [target])\n        await this.sign(path.join(targetOutDir, `${this.appInfo.productFilename}.app`), targetOutDir, masBuildOptions, arch)\n      }\n      else {\n        await this.sign(prepackaged, targetOutDir, masBuildOptions, arch)\n      }\n    }\n\n    if (nonMasPromise != null) {\n      await nonMasPromise\n    }\n  }\n\n  private async sign(appPath: string, outDir: string | null, masOptions: MasConfiguration | null, arch: Arch | null): Promise<void> {\n    if (!isSignAllowed()) {\n      return\n    }\n\n    const isMas = masOptions != null\n    const options = masOptions == null ? this.platformSpecificBuildOptions : masOptions\n    const qualifier = options.identity\n\n    if (!isMas && qualifier === null) {\n      if (this.forceCodeSigning) {\n        throw new InvalidConfigurationError(\"identity explicitly is set to null, but forceCodeSigning is set to true\")\n      }\n      log.info({reason: \"identity explicitly is set to null\"}, \"skipped macOS code signing\")\n      return\n    }\n\n    const keychainFile = (await this.codeSigningInfo.value).keychainFile\n    const explicitType = options.type\n    const type = explicitType || \"distribution\"\n    const isDevelopment = type === \"development\"\n    const certificateType = getCertificateType(isMas, isDevelopment)\n    let identity = await findIdentity(certificateType, qualifier, keychainFile)\n    if (identity == null) {\n      if (!isMas && !isDevelopment && explicitType !== \"distribution\") {\n        identity = await findIdentity(\"Mac Developer\", qualifier, keychainFile)\n        if (identity != null) {\n          log.warn(\"Mac Developer is used to sign app — it is only for development and testing, not for production\")\n        }\n      }\n\n      if (identity == null) {\n        await reportError(isMas, certificateType, qualifier, keychainFile, this.forceCodeSigning)\n        return\n      }\n    }\n\n    if (!isMacOsHighSierra()) {\n      throw new InvalidConfigurationError(\"macOS High Sierra 10.13.6 is required to sign\")\n    }\n\n    let filter = options.signIgnore\n    if (Array.isArray(filter)) {\n      if (filter.length == 0) {\n        filter = null\n      }\n    }\n    else if (filter != null) {\n      filter = filter.length === 0 ? null : [filter]\n    }\n\n    const filterRe = filter == null ? null : filter.map(it => new RegExp(it))\n\n    const signOptions: any = {\n      \"identity-validation\": false,\n      // https://github.com/electron-userland/electron-builder/issues/1699\n      // kext are signed by the chipset manufacturers. You need a special certificate (only available on request) from Apple to be able to sign kext.\n      ignore: (file: string) => {\n        if (filterRe != null) {\n          for (const regExp of filterRe) {\n            if (regExp.test(file)) {\n              return true\n            }\n          }\n        }\n        return file.endsWith(\".kext\") || file.startsWith(\"/Contents/PlugIns\", appPath.length) ||\n          file.includes(\"/node_modules/puppeteer/.local-chromium\") ||  file.includes(\"/node_modules/playwright-firefox/.local-browsers\") || file.includes(\"/node_modules/playwright/.local-browsers\") \n          \n          /* Those are browser automating modules, browser (chromium, nightly) cannot be signed\n          https://github.com/electron-userland/electron-builder/issues/2010 \n          https://github.com/electron-userland/electron-builder/issues/5383\n          */\n      },\n      identity: identity!,\n      type,\n      platform: isMas ? \"mas\" : \"darwin\",\n      version: this.config.electronVersion,\n      app: appPath,\n      keychain: keychainFile || undefined,\n      binaries: options.binaries || undefined,\n      requirements: isMas || this.platformSpecificBuildOptions.requirements == null ? undefined : await this.getResource(this.platformSpecificBuildOptions.requirements),\n      // https://github.com/electron-userland/electron-osx-sign/issues/196\n      // will fail on 10.14.5+ because a signed but unnotarized app is also rejected.\n      \"gatekeeper-assess\": options.gatekeeperAssess === true,\n      // https://github.com/electron-userland/electron-builder/issues/1480\n      \"strict-verify\": options.strictVerify,\n      hardenedRuntime: isMas ? masOptions && masOptions.hardenedRuntime === true : options.hardenedRuntime !== false,\n    }\n\n    await this.adjustSignOptions(signOptions, masOptions)\n    log.info({\n      file: log.filePath(appPath),\n      identityName: identity.name,\n      identityHash: identity.hash,\n      provisioningProfile: signOptions[\"provisioning-profile\"] || \"none\",\n    }, \"signing\")\n    await this.doSign(signOptions)\n\n    // https://github.com/electron-userland/electron-builder/issues/1196#issuecomment-312310209\n    if (masOptions != null && !isDevelopment) {\n      const certType = isDevelopment ? \"Mac Developer\" : \"3rd Party Mac Developer Installer\"\n      const masInstallerIdentity = await findIdentity(certType, masOptions.identity, keychainFile)\n      if (masInstallerIdentity == null) {\n        throw new InvalidConfigurationError(`Cannot find valid \"${certType}\" identity to sign MAS installer, please see https://electron.build/code-signing`)\n      }\n\n      // mas uploaded to AppStore, so, use \"-\" instead of space for name\n      const artifactName = this.expandArtifactNamePattern(masOptions, \"pkg\", arch)\n      const artifactPath = path.join(outDir!, artifactName)\n      await this.doFlat(appPath, artifactPath, masInstallerIdentity, keychainFile)\n      await this.dispatchArtifactCreated(artifactPath, null, Arch.x64, this.computeSafeArtifactName(artifactName, \"pkg\", arch))\n    }\n  }\n\n  private async adjustSignOptions(signOptions: any, masOptions: MasConfiguration | null) {\n    const resourceList = await this.resourceList\n    const customSignOptions = masOptions || this.platformSpecificBuildOptions\n    const entitlementsSuffix = masOptions == null ? \"mac\" : \"mas\"\n\n    let entitlements = customSignOptions.entitlements\n    if (entitlements == null) {\n      const p = `entitlements.${entitlementsSuffix}.plist`\n      if (resourceList.includes(p)) {\n        entitlements = path.join(this.info.buildResourcesDir, p)\n      }\n      else {\n        entitlements = getTemplatePath(\"entitlements.mac.plist\")\n      }\n    }\n    signOptions.entitlements = entitlements\n\n    let entitlementsInherit = customSignOptions.entitlementsInherit\n    if (entitlementsInherit == null) {\n      const p = `entitlements.${entitlementsSuffix}.inherit.plist`\n      if (resourceList.includes(p)) {\n        entitlementsInherit = path.join(this.info.buildResourcesDir, p)\n      }\n      else {\n        entitlementsInherit = getTemplatePath(\"entitlements.mac.plist\")\n      }\n    }\n    signOptions[\"entitlements-inherit\"] = entitlementsInherit\n\n    if (customSignOptions.provisioningProfile != null) {\n      signOptions[\"provisioning-profile\"] = customSignOptions.provisioningProfile\n    }\n    signOptions['entitlements-loginhelper'] = customSignOptions.entitlementsLoginHelper\n  }\n\n  //noinspection JSMethodCanBeStatic\n  protected async doSign(opts: SignOptions): Promise<any> {\n    return signAsync(opts)\n  }\n\n  //noinspection JSMethodCanBeStatic\n  protected async doFlat(appPath: string, outFile: string, identity: Identity, keychain: string | null | undefined): Promise<any> {\n    // productbuild doesn't created directory for out file\n    await mkdirs(path.dirname(outFile))\n\n    const args = prepareProductBuildArgs(identity, keychain)\n    args.push(\"--component\", appPath, \"/Applications\")\n    args.push(outFile)\n    return await exec(\"productbuild\", args)\n  }\n\n  public getElectronSrcDir(dist: string) {\n    return path.resolve(this.projectDir, dist, this.info.framework.distMacOsAppName)\n  }\n\n  public getElectronDestinationDir(appOutDir: string) {\n    return path.join(appOutDir, this.info.framework.distMacOsAppName)\n  }\n\n  // todo fileAssociations\n  async applyCommonInfo(appPlist: any, contentsPath: string) {\n    const appInfo = this.appInfo\n    const appFilename = appInfo.productFilename\n\n    // https://github.com/electron-userland/electron-builder/issues/1278\n    appPlist.CFBundleExecutable = appFilename.endsWith(\" Helper\") ? appFilename.substring(0, appFilename.length - \" Helper\".length) : appFilename\n\n    const icon = await this.getIconPath()\n    if (icon != null) {\n      const oldIcon = appPlist.CFBundleIconFile\n      const resourcesPath = path.join(contentsPath, \"Resources\")\n      if (oldIcon != null) {\n        await unlinkIfExists(path.join(resourcesPath, oldIcon))\n      }\n      const iconFileName = `${appFilename}.icns`\n      appPlist.CFBundleIconFile = iconFileName\n      await copyFile(icon, path.join(resourcesPath, iconFileName))\n    }\n    appPlist.CFBundleName = appInfo.productName\n    appPlist.CFBundleDisplayName = appInfo.productName\n\n    const minimumSystemVersion = this.platformSpecificBuildOptions.minimumSystemVersion\n    if (minimumSystemVersion != null) {\n      appPlist.LSMinimumSystemVersion = minimumSystemVersion\n    }\n\n    appPlist.CFBundleIdentifier = appInfo.macBundleIdentifier\n\n    appPlist.CFBundleShortVersionString = this.platformSpecificBuildOptions.bundleShortVersion || appInfo.version\n    appPlist.CFBundleVersion = appInfo.buildVersion\n\n    use(this.platformSpecificBuildOptions.category || (this.config as any).category, it => appPlist.LSApplicationCategoryType = it)\n    appPlist.NSHumanReadableCopyright = appInfo.copyright\n\n    if (this.platformSpecificBuildOptions.darkModeSupport) {\n      appPlist.NSRequiresAquaSystemAppearance = false\n    }\n\n    const extendInfo = this.platformSpecificBuildOptions.extendInfo\n    if (extendInfo != null) {\n      Object.assign(appPlist, extendInfo)\n    }\n  }\n\n  protected async signApp(packContext: AfterPackContext, isAsar: boolean): Promise<any> {\n    const appFileName = `${this.appInfo.productFilename}.app`\n\n    await BluebirdPromise.map(readdir(packContext.appOutDir), (file: string): any => {\n      if (file === appFileName) {\n        return this.sign(path.join(packContext.appOutDir, file), null, null, null)\n      }\n      return null\n    })\n\n    if (!isAsar) {\n      return\n    }\n\n    const outResourcesDir = path.join(packContext.appOutDir, \"resources\", \"app.asar.unpacked\")\n    await BluebirdPromise.map(orIfFileNotExist(readdir(outResourcesDir), []), (file: string): any => {\n      if (file.endsWith(\".app\")) {\n        return this.sign(path.join(outResourcesDir, file), null, null, null)\n      }\n      else {\n        return null\n      }\n    })\n  }\n}\n\nfunction getCertificateType(isMas: boolean, isDevelopment: boolean): CertType {\n  if (isDevelopment) {\n    return \"Mac Developer\"\n  }\n  return isMas ? \"3rd Party Mac Developer Application\" : \"Developer ID Application\"\n}\n"], "sourceRoot": ""}