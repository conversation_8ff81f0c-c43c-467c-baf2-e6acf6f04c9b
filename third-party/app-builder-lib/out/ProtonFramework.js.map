{"version": 3, "sources": ["../src/ProtonFramework.ts"], "names": [], "mappings": ";;;;;;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEM,MAAO,eAAP,SAA+B,gCAA/B,CAA6C;AAMjD,EAAA,WAAA,CAAY,OAAZ,EAA6B,gBAA7B,EAAuD,aAAvD,EAA6E;AAC3E,UAAM,OAAN,EAAe,gBAAf,EAAiC,aAAjC;AANO,SAAA,IAAA,GAAO,QAAP,CAKoE,CAH7E;;AACS,SAAA,kBAAA,GAAqB,oBAArB;AAIR;;AAED,EAAA,cAAc,CAAC,QAAD,EAAmB;AAC/B,QAAI,QAAQ,KAAK,iBAAS,OAA1B,EAAmC;AACjC,aAAO,oCAAgB,uCAAhB,CAAP;AACD,KAFD,MAGK,IAAI,QAAQ,KAAK,iBAAS,KAA1B,EAAiC;AACpC,aAAO,oCAAgB,2BAAhB,CAAP;AACD,KAFI,MAGA;AACH,aAAO,oCAAgB,wCAAhB,CAAP;AACD;AACF;;AAED,EAAA,iBAAiB,GAAA;AACf,QAAI,KAAJ;AACA,UAAM,YAAY,GAAQ;AAAC,MAAA,GAAG,EAAE,KAAN;AAAa,MAAA,UAAU,EAAE;AAAzB,KAA1B;;AACA,QAAI,OAAO,CAAC,GAAR,CAAY,qBAAZ,KAAsC,MAA1C,EAAkD;AAChD,MAAA,KAAK,GAAG,OAAO,CAAC,aAAD,CAAf,CADgD,CAEhD;;AACA,MAAA,KAAK,GAAG,aAAa,CAAC,KAAD,EAAQ,YAAR,EAAsB,KAAK,OAA3B,CAArB;AACD,KAJD,MAKK;AACH,UAAI;AACF,QAAA,KAAK,GAAG,OAAO,CAAC,YAAD,CAAf;AACD,OAFD,CAGA,OAAO,CAAP,EAAU;AACR;AACA,2BAAI,KAAJ,CAAU,IAAV,EAAgB,yCAAhB;;AACA,eAAO,IAAP;AACD;AACF;;AAED,uBAAI,IAAJ,CAAS;AAAC,MAAA,OAAO,EAAE,6CAAkB,YAAlB,EAAgC,IAAI,GAAJ,CAAgB,CAAC,SAAD,CAAhB,CAAhC;AAAV,KAAT,EAAmF,mCAAnF;;AACA,WAAQ,IAAD,IAA8B;AACnC,UAAI,EAAE,IAAI,CAAC,QAAL,CAAc,KAAd,KAAwB,IAAI,CAAC,QAAL,CAAc,MAAd,CAA1B,KAAoD,IAAI,CAAC,QAAL,CAAc,uCAAd,CAAxD,EAA6F;AAC3F,eAAO,IAAP;AACD;;AAED,aAAO,IAAI,OAAJ,CAAY,CAAC,OAAD,EAAU,MAAV,KAAoB;AACrC,eAAO,KAAK,CAAC,aAAN,CAAoB,IAApB,EAA0B,YAA1B,EAAwC,CAAC,KAAD,EAAe,MAAf,KAA8B;AAC3E,cAAI,KAAK,IAAI,IAAb,EAAmB;AACjB,YAAA,OAAO,CAAC,MAAM,CAAC,IAAR,CAAP;AACD,WAFD,MAGK;AACH,YAAA,MAAM,CAAC,KAAD,CAAN;AACD;AACF,SAPM,CAAP;AAQD,OATM,CAAP;AAUD,KAfD;AAgBD;;AA1DgD;;;;AA6DnD,SAAS,aAAT,CAAuB,KAAvB,EAAmC,YAAnC,EAAsD,WAAtD,EAAyE;AACvE;AACA,EAAA,YAAY,CAAC,OAAb,GAAuB,CACrB,CAAC,OAAO,CAAC,mBAAD,CAAP,CAA6B,OAA9B,EAAuC;AAAC,IAAA,OAAO,EAAE;AAAC,MAAA,IAAI,EAAE;AAAP;AAAV,GAAvC,CADqB,EAErB,OAAO,CAAC,qBAAD,CAFc,CAAvB;AAIA,EAAA,YAAY,CAAC,OAAb,GAAuB,CACrB;AACA,EAAA,OAAO,CAAC,sCAAD,CAAP,CAAgD,OAF3B,EAIrB;AACA,EAAA,OAAO,CAAC,4CAAD,CAAP,CAAsD,OALjC,EAMrB,OAAO,CAAC,qDAAD,CAAP,CAA+D,OAN1C,EAOrB,CAAC,OAAO,CAAC,0CAAD,CAAP,CAAoD,OAArD,EAA8D;AAAC,IAAA,KAAK,EAAE;AAAR,GAA9D,CAPqB,EAQrB,CAAC,OAAO,CAAC,0CAAD,CAAP,CAAoD,OAArD,EAA8D;AAAC,IAAA,QAAQ,EAAE;AAAX,GAA9D,CARqB,EASrB,CAAC,OAAO,CAAC,oDAAD,CAAP,CAA8D,OAA/D,EAAwE;AAAC,IAAA,KAAK,EAAE;AAAR,GAAxE,CATqB,EAUrB,OAAO,CAAC,uCAAD,CAAP,CAAiD,OAV5B,EAYrB;AACA,GAAC,OAAO,CAAC,mCAAD,CAAP,CAA6C,OAA9C,EAAuD;AAAC,IAAA,MAAM,EAAE;AAAT,GAAvD,CAbqB,EAcrB,OAAO,CAAC,sCAAD,CAAP,CAAgD,OAd3B,EAerB,OAAO,CAAC,8CAAD,CAAP,CAAwD,OAfnC,EAgBrB,OAAO,CAAC,0CAAD,CAAP,CAAoD,OAhB/B,EAiBrB,OAAO,CAAC,0CAAD,CAAP,CAAoD,OAjB/B,EAmBrB;AACA,EAAA,OAAO,CAAC,qCAAD,CAAP,CAA+C,OApB1B,EAqBrB,OAAO,CAAC,kCAAD,CAAP,CAA4C,OArBvB,EAsBrB,CAAC,OAAO,CAAC,yCAAD,CAAP,CAAmD,OAApD,EAA6D;AAAC,IAAA,KAAK,EAAE;AAAR,GAA7D,CAtBqB,EAuBrB,OAAO,CAAC,qCAAD,CAAP,CAA+C,OAvB1B,CAAvB;AAyBA,EAAA,YAAY,CAAC,OAAb,GAAuB,KAAvB;AACA,SAAO,KAAP;AACD,C", "sourcesContent": ["import { FileTransformer } from \"builder-util/out/fs\"\nimport { log } from \"builder-util\"\nimport { safeStringifyJson } from \"builder-util-runtime\"\nimport { Platform } from \"./core\"\nimport { NODE_MODULES_PATTERN } from \"./fileTransformer\"\nimport { LibUiFramework } from \"./frameworks/LibUiFramework\"\nimport { getTemplatePath } from \"./util/pathManager\"\n\nexport class ProtonFramework extends LibUiFramework {\n  readonly name = \"proton\"\n\n  // noinspection JSUnusedGlobalSymbols\n  readonly defaultAppIdPrefix = \"com.proton-native.\"\n\n  constructor(version: string, distMacOsAppName: string, isUseLaunchUi: boolean) {\n    super(version, distMacOsAppName, isUseLaunchUi)\n  }\n\n  getDefaultIcon(platform: Platform): string {\n    if (platform === Platform.WINDOWS) {\n      return getTemplatePath(\"icons/proton-native/proton-native.ico\")\n    }\n    else if (platform === Platform.LINUX) {\n      return getTemplatePath(\"icons/proton-native/linux\")\n    }\n    else {\n      return getTemplatePath(\"icons/proton-native/proton-native.icns\")\n    }\n  }\n\n  createTransformer(): FileTransformer | null {\n    let babel: any\n    const babelOptions: any = {ast: false, sourceMaps: \"inline\"}\n    if (process.env.TEST_SET_BABEL_PRESET === \"true\") {\n      babel = require(\"@babel/core\")\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      babel = testOnlyBabel(babel, babelOptions, this.version)\n    }\n    else {\n      try {\n        babel = require(\"babel-core\")\n      }\n      catch (e) {\n        // babel isn't installed\n        log.debug(null, \"don't transpile source code using Babel\")\n        return null\n      }\n    }\n\n    log.info({options: safeStringifyJson(babelOptions, new Set<string>([\"presets\"]))}, \"transpile source code using Babel\")\n    return (file): Promise<any> | null => {\n      if (!(file.endsWith(\".js\") || file.endsWith(\".jsx\")) || file.includes(NODE_MODULES_PATTERN)) {\n        return null\n      }\n\n      return new Promise((resolve, reject) => {\n        return babel.transformFile(file, babelOptions, (error: Error, result: any) => {\n          if (error == null) {\n            resolve(result.code)\n          }\n          else {\n            reject(error)\n          }\n        })\n      })\n    }\n  }\n}\n\nfunction testOnlyBabel(babel: any, babelOptions: any, nodeVersion: string): any {\n  // out test dir can be located outside of electron-builder node_modules and babel cannot resolve string names of preset\n  babelOptions.presets = [\n    [require(\"@babel/preset-env\").default, {targets: {node: nodeVersion}}],\n    require(\"@babel/preset-react\"),\n  ]\n  babelOptions.plugins = [\n    // stage 0\n    require(\"@babel/plugin-proposal-function-bind\").default,\n\n    // stage 1\n    require(\"@babel/plugin-proposal-export-default-from\").default,\n    require(\"@babel/plugin-proposal-logical-assignment-operators\").default,\n    [require(\"@babel/plugin-proposal-optional-chaining\").default, {loose: false}],\n    [require(\"@babel/plugin-proposal-pipeline-operator\").default, {proposal: \"minimal\"}],\n    [require(\"@babel/plugin-proposal-nullish-coalescing-operator\").default, {loose: false}],\n    require(\"@babel/plugin-proposal-do-expressions\").default,\n\n    // stage 2\n    [require(\"@babel/plugin-proposal-decorators\").default, {legacy: true}],\n    require(\"@babel/plugin-proposal-function-sent\").default,\n    require(\"@babel/plugin-proposal-export-namespace-from\").default,\n    require(\"@babel/plugin-proposal-numeric-separator\").default,\n    require(\"@babel/plugin-proposal-throw-expressions\").default,\n\n    // stage 3\n    require(\"@babel/plugin-syntax-dynamic-import\").default,\n    require(\"@babel/plugin-syntax-import-meta\").default,\n    [require(\"@babel/plugin-proposal-class-properties\").default, {loose: false}],\n    require(\"@babel/plugin-proposal-json-strings\").default,\n  ]\n  babelOptions.babelrc = false\n  return babel\n}"], "sourceRoot": ""}