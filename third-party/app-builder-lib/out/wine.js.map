{"version": 3, "sources": ["../src/wine.ts"], "names": [], "mappings": ";;;;;;;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AACM,SAAU,QAAV,CAAmB,IAAnB,EAAiC,MAAA,GAAwB,IAAzD,EAA+D,OAAA,GAAyB,EAAxF,EAA4F,OAAA,GAA2B,EAAvH,EAAyH;AAC7H,MAAI,OAAO,CAAC,QAAR,KAAqB,OAAzB,EAAkC;AAChC,QAAI,OAAO,CAAC,OAAR,IAAmB,IAAvB,EAA6B;AAC3B;AACA,MAAA,OAAO,CAAC,OAAR,GAAkB,MAAM,IAAxB;AACD;;AACD,WAAO,yBAAK,IAAL,EAAW,OAAX,EAAoB,OAApB,CAAP;AACD;;AAED,QAAM,WAAW,GAAG,CAClB,MADkB,EAElB,QAFkB,EAER,IAFQ,CAApB;;AAIA,MAAI,MAAM,IAAI,IAAd,EAAoB;AAClB,IAAA,WAAW,CAAC,IAAZ,CAAiB,OAAjB,EAA0B,MAA1B;AACD;;AACD,MAAI,OAAO,CAAC,MAAR,GAAiB,CAArB,EAAwB;AACtB,IAAA,WAAW,CAAC,IAAZ,CAAiB,QAAjB,EAA2B,IAAI,CAAC,SAAL,CAAe,OAAf,CAA3B;AACD;;AACD,SAAO,sCAAkB,WAAlB,EAA+B,SAA/B,EAA0C,OAA1C,CAAP;AACD;AAED;;;AACM,SAAU,4BAAV,CAAuC,IAAvC,EAA4D,OAA5D,EAA2E;AAC/E,MAAI,OAAO,CAAC,QAAR,KAAqB,OAAzB,EAAkC;AAChC,IAAA,IAAI,CAAC,OAAL,CAAa,OAAb;AACD;;AACD,SAAO,IAAP;AACD,C", "sourcesContent": ["import { ExecFileOptions } from \"child_process\"\nimport { exec, executeAppBuilder } from \"builder-util\"\n\n/** @private */\nexport function execWine(file: string, file64: string | null = null, appArgs: Array<string> = [], options: ExecFileOptions = {}): Promise<string> {\n  if (process.platform === \"win32\") {\n    if (options.timeout == null) {\n      // 2 minutes\n      options.timeout = 120 * 1000\n    }\n    return exec(file, appArgs, options)\n  }\n\n  const commandArgs = [\n    \"wine\",\n    \"--ia32\", file,\n  ]\n  if (file64 != null) {\n    commandArgs.push(\"--x64\", file64)\n  }\n  if (appArgs.length > 0) {\n    commandArgs.push(\"--args\", JSON.stringify(appArgs))\n  }\n  return executeAppBuilder(commandArgs, undefined, options)\n}\n\n/** @private */\nexport function prepareWindowsExecutableArgs(args: Array<string>, exePath: string) {\n  if (process.platform !== \"win32\") {\n    args.unshift(exePath)\n  }\n  return args\n}"], "sourceRoot": ""}