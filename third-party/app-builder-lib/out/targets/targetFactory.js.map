{"version": 3, "sources": ["../../src/targets/targetFactory.ts"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA,MAAM,cAAc,GAAG,IAAI,GAAJ,CAAQ,CAAC,KAAD,EAAQ,IAAR,EAAc,QAAd,EAAwB,QAAxB,EAAkC,QAAlC,EAA4C,SAA5C,CAAR,CAAvB;;AAEM,SAAU,2BAAV,CAAsC,GAAtC,EAAqE,gBAArE,EAA8G,QAA9G,EAAgI;AACpI,OAAK,MAAM,WAAX,IAA0B,GAAG,CAAC,MAAJ,EAA1B,EAAwC;AACtC,QAAI,WAAW,CAAC,MAAZ,GAAqB,CAAzB,EAA4B;AAC1B;AACA,aAAO,GAAP;AACD;AACF;;AAED,QAAM,YAAY,GAAoB,GAAG,CAAC,IAAJ,KAAa,CAAb,GAAiB,CAAC,OAAO,CAAC,IAAT,CAAjB,GAA8C,KAAK,CAAC,IAAN,CAAW,GAAG,CAAC,IAAJ,EAAX,EAAuB,GAAvB,CAA2B,EAAE,IAAI,oBAAK,EAAL,CAAjC,CAApF;AACA,QAAM,MAAM,GAAG,IAAI,GAAJ,CAAQ,GAAR,CAAf;;AACA,OAAK,MAAM,MAAX,IAAqB,4BAAQ,gBAAgB,CAAC,4BAAjB,CAA8C,MAAtD,EAA8D,GAA9D,CAAuF,EAAE,IAAI,OAAO,EAAP,KAAc,QAAd,GAAyB;AAAC,IAAA,MAAM,EAAE;AAAT,GAAzB,GAAwC,EAArI,CAArB,EAA+J;AAC7J,QAAI,IAAI,GAAG,MAAM,CAAC,MAAlB;AACA,QAAI,KAAK,GAAG,MAAM,CAAC,IAAnB;AACA,UAAM,SAAS,GAAG,IAAI,CAAC,WAAL,CAAiB,GAAjB,CAAlB;;AACA,QAAI,SAAS,GAAG,CAAhB,EAAmB;AACjB,MAAA,IAAI,GAAG,MAAM,CAAC,MAAP,CAAc,SAAd,CAAwB,CAAxB,EAA2B,SAA3B,CAAP;;AACA,UAAI,KAAK,IAAI,IAAb,EAAmB;AACjB,QAAA,KAAK,GAAG,MAAM,CAAC,MAAP,CAAc,SAAd,CAAwB,SAAS,GAAG,CAApC,CAAR;AACD;AACF;;AAED,SAAK,MAAM,IAAX,IAAmB,KAAK,IAAI,IAAT,GAAgB,YAAhB,GAA+B,4BAAQ,KAAR,CAAlD,EAAkE;AAChE,mCAAS,MAAT,EAAiB,mCAAe,IAAf,CAAjB,EAAuC,IAAvC;AACD;AACF;;AAED,MAAI,MAAM,CAAC,IAAP,KAAgB,CAApB,EAAuB;AACrB,UAAM,aAAa,GAAG,gBAAgB,CAAC,aAAvC;;AACA,QAAI,GAAG,CAAC,IAAJ,KAAa,CAAb,IAAkB,QAAQ,KAAK,aAAS,KAAxC,KAAkD,OAAO,CAAC,QAAR,KAAqB,QAArB,IAAiC,OAAO,CAAC,QAAR,KAAqB,OAAxG,CAAJ,EAAsH;AACpH,MAAA,MAAM,CAAC,GAAP,CAAW,oBAAK,GAAhB,EAAqB,aAArB,EADoH,CAEpH;AACA;AACD,KAJD,MAKK;AACH,WAAK,MAAM,IAAX,IAAmB,YAAnB,EAAiC;AAC/B,QAAA,MAAM,CAAC,GAAP,CAAW,mCAAe,IAAf,CAAX,EAAiC,aAAjC;AACD;AACF;AACF;;AAED,SAAO,MAAP;AACD;;AAEK,SAAU,aAAV,CAAwB,YAAxB,EAA2D,OAA3D,EAAmF,MAAnF,EAAmG,QAAnG,EAAkI;AACtI,QAAM,MAAM,GAAkB,EAA9B;;AAEA,QAAM,MAAM,GAAG,CAAC,IAAD,EAAe,OAAf,KAAsD;AACnE,QAAI,MAAM,GAAG,YAAY,CAAC,GAAb,CAAiB,IAAjB,CAAb;;AACA,QAAI,MAAM,IAAI,IAAd,EAAoB;AAClB,MAAA,MAAM,GAAG,OAAO,CAAC,MAAD,CAAhB;AACA,MAAA,YAAY,CAAC,GAAb,CAAiB,IAAjB,EAAuB,MAAvB;AACD;;AACD,IAAA,MAAM,CAAC,IAAP,CAAY,MAAZ;AACD,GAPD;;AASA,QAAM,OAAO,GAAG,gBAAgB,CAAC,OAAD,EAAU,QAAQ,CAAC,aAAnB,CAAhC;AACA,EAAA,QAAQ,CAAC,aAAT,CAAuB,OAAvB,EAAgC,MAAhC;AACA,SAAO,MAAP;AACD;;AAED,SAAS,gBAAT,CAA0B,OAA1B,EAAkD,aAAlD,EAA8E;AAC5E,QAAM,IAAI,GAAkB,EAA5B;;AACA,OAAK,MAAM,CAAX,IAAgB,OAAhB,EAAyB;AACvB,UAAM,IAAI,GAAG,CAAC,CAAC,WAAF,GAAgB,IAAhB,EAAb;;AACA,QAAI,IAAI,KAAK,kBAAb,EAA6B;AAC3B,MAAA,IAAI,CAAC,IAAL,CAAU,GAAG,aAAb;AACD,KAFD,MAGK;AACH,MAAA,IAAI,CAAC,IAAL,CAAU,IAAV;AACD;AACF;;AACD,SAAO,IAAP;AACD;;AAEK,SAAU,kBAAV,CAA6B,MAA7B,EAA6C,MAA7C,EAA6D,QAA7D,EAA4F;AAChG,MAAI,cAAc,CAAC,GAAf,CAAmB,MAAnB,CAAJ,EAAgC;AAC9B,WAAO,KAAI,8BAAJ,EAAkB,MAAlB,EAA0B,MAA1B,EAAkC,QAAlC,CAAP;AACD,GAFD,MAGK,IAAI,MAAM,KAAK,cAAf,EAA2B;AAC9B,WAAO,IAAI,UAAJ,CAAe,cAAf,CAAP;AACD,GAFI,MAGA;AACH,UAAM,IAAI,KAAJ,CAAU,mBAAmB,MAAM,EAAnC,CAAN;AACD;AACF;;AAEK,MAAO,UAAP,SAA0B,UAA1B,CAAgC;AAGpC,EAAA,WAAA,CAAY,IAAZ,EAAwB;AACtB,UAAM,IAAN;AAHO,SAAA,OAAA,GAAU,IAAV;AAIR;;AAED,MAAI,MAAJ,GAAU;AACR,UAAM,IAAI,KAAJ,CAAU,YAAV,CAAN;AACD,GATmC,CAWpC;;;AACA,QAAM,KAAN,CAAY,SAAZ,EAA+B,IAA/B,EAAyC,CACvC;AACD;;AAdmC,C", "sourcesContent": ["import { addValue, Arch, archFromString, ArchType, asArray } from \"builder-util\"\nimport { DEFAULT_TARGET, DIR_TARGET, Platform, Target, TargetConfiguration } from \"..\"\nimport { PlatformPackager } from \"../platformPackager\"\nimport { ArchiveTarget } from \"./ArchiveTarget\"\n\nconst archiveTargets = new Set([\"zip\", \"7z\", \"tar.xz\", \"tar.lz\", \"tar.gz\", \"tar.bz2\"])\n\nexport function computeArchToTargetNamesMap(raw: Map<Arch, Array<string>>, platformPackager: PlatformPackager<any>, platform: Platform): Map<Arch, Array<string>> {\n  for (const targetNames of raw.values()) {\n    if (targetNames.length > 0) {\n      // https://github.com/electron-userland/electron-builder/issues/1355\n      return raw\n    }\n  }\n\n  const defaultArchs: Array<ArchType> = raw.size === 0 ? [process.arch as ArchType] : Array.from(raw.keys()).map(it => Arch[it] as ArchType)\n  const result = new Map(raw)\n  for (const target of asArray(platformPackager.platformSpecificBuildOptions.target).map<TargetConfiguration>(it => typeof it === \"string\" ? {target: it} : it)) {\n    let name = target.target\n    let archs = target.arch\n    const suffixPos = name.lastIndexOf(\":\")\n    if (suffixPos > 0) {\n      name = target.target.substring(0, suffixPos)\n      if (archs == null) {\n        archs = target.target.substring(suffixPos + 1) as ArchType\n      }\n    }\n\n    for (const arch of archs == null ? defaultArchs : asArray(archs)) {\n      addValue(result, archFromString(arch), name)\n    }\n  }\n\n  if (result.size === 0) {\n    const defaultTarget = platformPackager.defaultTarget\n    if (raw.size === 0 && platform === Platform.LINUX && (process.platform === \"darwin\" || process.platform === \"win32\")) {\n      result.set(Arch.x64, defaultTarget)\n      // cannot enable arm because of native dependencies - e.g. keytar doesn't provide pre-builds for arm\n      // result.set(Arch.armv7l, [\"snap\"])\n    }\n    else {\n      for (const arch of defaultArchs) {\n        result.set(archFromString(arch), defaultTarget)\n      }\n    }\n  }\n\n  return result\n}\n\nexport function createTargets(nameToTarget: Map<string, Target>, rawList: Array<string>, outDir: string, packager: PlatformPackager<any>): Array<Target> {\n  const result: Array<Target> = []\n\n  const mapper = (name: string, factory: (outDir: string) => Target) => {\n    let target = nameToTarget.get(name)\n    if (target == null) {\n      target = factory(outDir)\n      nameToTarget.set(name, target)\n    }\n    result.push(target)\n  }\n\n  const targets = normalizeTargets(rawList, packager.defaultTarget)\n  packager.createTargets(targets, mapper)\n  return result\n}\n\nfunction normalizeTargets(targets: Array<string>, defaultTarget: Array<string>): Array<string> {\n  const list: Array<string> = []\n  for (const t of targets) {\n    const name = t.toLowerCase().trim()\n    if (name === DEFAULT_TARGET) {\n      list.push(...defaultTarget)\n    }\n    else {\n      list.push(name)\n    }\n  }\n  return list\n}\n\nexport function createCommonTarget(target: string, outDir: string, packager: PlatformPackager<any>): Target {\n  if (archiveTargets.has(target)) {\n    return new ArchiveTarget(target, outDir, packager)\n  }\n  else if (target === DIR_TARGET) {\n    return new NoOpTarget(DIR_TARGET)\n  }\n  else {\n    throw new Error(`Unknown target: ${target}`)\n  }\n}\n\nexport class NoOpTarget extends Target {\n  readonly options = null\n\n  constructor(name: string) {\n    super(name)\n  }\n\n  get outDir(): string {\n    throw new Error(\"NoOpTarget\")\n  }\n\n  // eslint-disable-next-line\n  async build(appOutDir: string, arch: Arch): Promise<any> {\n    // no build\n  }\n}"], "sourceRoot": ""}