{"version": 3, "sources": ["../../src/targets/MsiTarget.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;;;AAEA,MAAM,qCAAqC,GAAG,2BAAK,KAAL,CAAW,sCAAX,CAA9C;;AACA,MAAM,WAAW,GAAG,mBAApB;AAEA,MAAM,qBAAqB,GAAG,oBAA9B;AAEA,MAAM,eAAe,GAAG,KAAI,eAAJ,EAAgC,YAAW;AACjE,QAAM,QAAQ,GAAG,CAAC,MAAM,yBAAS,IAAI,CAAC,IAAL,CAAU,oCAAgB,KAAhB,CAAV,EAAkC,cAAlC,CAAT,EAA4D,MAA5D,CAAP,EACd,OADc,CACN,KADM,EACC,IADD,EAEd,OAFc,CAEN,KAFM,EAEC,IAFD,EAGd,OAHc,CAGN,cAHM,EAGU,SAHV,CAAjB;AAIA,SAAO,GAAG,GAAC,OAAJ,CAAY,QAAZ,CAAP;AACD,CANuB,CAAxB,C,CAQA;;AACc,MAAO,SAAP,SAAyB,cAAzB,CAA+B;AAK3C,EAAA,WAAA,CAA6B,QAA7B,EAA6D,MAA7D,EAA2E;AACzE,UAAM,KAAN;AAD2B,SAAA,QAAA,GAAA,QAAA;AAAgC,SAAA,MAAA,GAAA,MAAA;AAJ5C,SAAA,EAAA,GAAK,OAAO,CAAC,QAAR,KAAqB,OAArB,GAA+B,KAAI,eAAJ,GAA/B,GAAiD,KAAI,uBAAJ,GAAtD;AAER,SAAA,OAAA,GAAsB,+BAAW,KAAK,QAAL,CAAc,4BAAzB,EAAuD,KAAK,QAAL,CAAc,MAAd,CAAqB,GAA5E,CAAtB;AAIR;;AAED,QAAM,KAAN,CAAY,SAAZ,EAA+B,IAA/B,EAAyC;AACvC,UAAM,QAAQ,GAAG,KAAK,QAAtB;AACA,UAAM,YAAY,GAAG,QAAQ,CAAC,+BAAT,CAAyC,KAAK,OAA9C,EAAuD,KAAvD,EAA8D,IAA9D,CAArB;AACA,UAAM,YAAY,GAAG,IAAI,CAAC,IAAL,CAAU,KAAK,MAAf,EAAuB,YAAvB,CAArB;AACA,UAAM,QAAQ,CAAC,IAAT,CAAc,wBAAd,CAAuC;AAC3C,MAAA,qBAAqB,EAAE,KADoB;AAE3C,MAAA,IAAI,EAAE,YAFqC;AAG3C,MAAA;AAH2C,KAAvC,CAAN;AAMA,UAAM,QAAQ,GAAG,MAAM,kCAAe,IAAf,EAAqB,QAArB,EAA+B,IAA/B,CAAvB;AACA,UAAM,EAAE,GAAG,KAAK,EAAhB;AAEA,UAAM,aAAa,GAAG,gEAAoB,KAAK,OAAzB,EAAkC,KAAK,QAAvC,CAAtB;;AAEA,QAAI,aAAa,CAAC,UAAlB,EAA8B;AAC5B;AACA;AACA,yBAAI,IAAJ,CAAS,kEAAT;AACD;;AAED,UAAM,WAAW,GAAG,QAAQ,CAAC,WAAT,CAAqB,aAArB,CAApB;AACA,UAAM,WAAW,GAAG,CAAC,gBAAD,CAApB;AACA,UAAM,MAAM,GAAG,aAAa,CAAC,UAAd,GAA4B,QAAQ,CAAC,WAAT,CAAqB,qBAArB,CAA5B,GAA0E,IAAzF;AACA,UAAM,0BAAU,WAAV,EAAuB,MAAM,KAAK,aAAL,CAAmB,SAAnB,EAA8B,IAA9B,EAAoC,aAApC,CAA7B,CAAN;;AACA,QAAI,MAAM,KAAK,IAAf,EAAqB;AACnB,YAAM,0BAAU,MAAV,EAAkB,MAAM,yBAAS,IAAI,CAAC,IAAL,CAAU,oCAAgB,KAAhB,CAAV,EAAkC,qBAAlC,CAAT,EAAmE,MAAnE,CAAxB,CAAN;AACA,MAAA,WAAW,CAAC,IAAZ,CAAiB,qBAAqB,CAAC,OAAtB,CAA8B,MAA9B,EAAsC,SAAtC,CAAjB;AACD,KA5BsC,CA8BvC;;;AACA,UAAM,UAAU,GAAG,MAAM,kCAAc,KAAd,EAAqB,cAArB,EAAqC,0FAArC,CAAzB,CA/BuC,CAiCvC;;AACA,UAAM,UAAU,GAAG,CACjB,OADiB,EACR,IAAI,KAAK,oBAAK,IAAd,GAAqB,KAArB,GAA8B,IAAI,KAAK,oBAAK,KAAd,GAAsB,OAAtB,GAAgC,KADtD,EAEjB,YAAY,EAAE,CAAC,QAAH,CAAY,SAAZ,CAAsB,EAFjB,EAGjB,MAHiB,CAGV,KAAK,gBAAL,EAHU,CAAnB;AAIA,IAAA,UAAU,CAAC,IAAX,CAAgB,aAAhB;;AACA,QAAI,MAAM,KAAK,IAAf,EAAqB;AACnB,MAAA,UAAU,CAAC,IAAX,CAAgB,qBAAhB;AACD;;AACD,UAAM,EAAE,CAAC,IAAH,CAAQ,EAAE,CAAC,QAAH,CAAY,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,YAAtB,CAAZ,CAAR,EAA0D,UAA1D,EAAsE;AAC1E,MAAA,GAAG,EAAE,QAAQ,CAAC;AAD4D,KAAtE,CAAN;AAIA,UAAM,KAAK,KAAL,CAAW,WAAX,EAAwB,EAAxB,EAA4B,YAA5B,EAA0C,SAA1C,EAAqD,UAArD,EAAiE,QAAQ,CAAC,GAA1E,CAAN;AAEA,UAAM,QAAQ,CAAC,OAAT,EAAN;AAEA,UAAM,QAAQ,CAAC,IAAT,CAAc,YAAd,CAAN;AAEA,UAAM,QAAQ,CAAC,IAAT,CAAc,0BAAd,CAAyC;AAC7C,MAAA,IAAI,EAAE,YADuC;AAE7C,MAAA,QAF6C;AAG7C,MAAA,IAH6C;AAI7C,MAAA,gBAAgB,EAAE,QAAQ,CAAC,uBAAT,CAAiC,YAAjC,EAA+C,KAA/C,CAJ2B;AAK7C,MAAA,MAAM,EAAE,IALqC;AAM7C,MAAA,iBAAiB,EAAE;AAN0B,KAAzC,CAAN;AAQD;;AAEO,QAAM,KAAN,CAAY,WAAZ,EAAwC,EAAxC,EAAuD,YAAvD,EAA6E,SAA7E,EAAgG,UAAhG,EAAoH,OAApH,EAAmI;AACzI;AACA,UAAM,SAAS,GAAG,CAChB,MADgB,EACR,EAAE,CAAC,QAAH,CAAY,YAAZ,CADQ,EAEhB,IAFgB,EAGhB;AACA,WAJgB,EAKhB;AACA;AACA,aAPgB,EAQhB,YAAY,EAAE,CAAC,QAAH,CAAY,SAAZ,CAAsB,EARlB,EAUhB,MAVgB,CAUT,KAAK,gBAAL,EAVS,CAAlB,CAFyI,CAczI;;AACA,QAAI,OAAO,CAAC,QAAR,KAAqB,OAAzB,EAAkC;AAChC;AACA,MAAA,SAAS,CAAC,IAAV,CAAe,OAAf;AACD;;AAED,QAAI,KAAK,OAAL,CAAa,QAAb,KAA0B,KAA9B,EAAqC;AACnC,MAAA,SAAS,CAAC,IAAV,CAAe,MAAf,EAAuB,gBAAvB;AACD,KAtBwI,CAwBzI;;;AACA,IAAA,SAAS,CAAC,IAAV,CAAe,GAAG,WAAlB;AACA,UAAM,EAAE,CAAC,IAAH,CAAQ,EAAE,CAAC,QAAH,CAAY,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,WAAtB,CAAZ,CAAR,EAAyD,SAAzD,EAAoE;AACxE,MAAA,GAAG,EAAE;AADmE,KAApE,CAAN;AAGD;;AAEO,EAAA,gBAAgB,GAAA;AACtB,UAAM,IAAI,GAAkB,CAAC,WAAD,CAA5B;;AACA,QAAI,KAAK,OAAL,CAAa,gBAAb,KAAkC,KAAtC,EAA6C;AAC3C,MAAA,IAAI,CAAC,IAAL,CAAU,KAAV;AACD;;AACD,WAAO,IAAP;AACD;;AAEO,QAAM,aAAN,CAAoB,SAApB,EAAuC,IAAvC,EAAmD,aAAnD,EAAoG;AAC1G,UAAM,OAAO,GAAG,KAAK,QAAL,CAAc,OAA9B;AACA,UAAM;AAAC,MAAA,KAAD;AAAQ,MAAA;AAAR,QAAgB,MAAM,KAAK,sBAAL,CAA4B,SAA5B,CAA5B;AAEA,UAAM,WAAW,GAAG,OAAO,CAAC,WAA5B;;AACA,QAAI,CAAC,WAAL,EAAkB;AAChB,yBAAI,IAAJ,CAAS,2EAAT;AACD;;AAED,UAAM,WAAW,GAAG,KAAK,QAAL,CAAc,WAAlC;AACA,UAAM,OAAO,GAAG,KAAK,OAArB;AACA,UAAM,QAAQ,GAAG,MAAM,KAAK,QAAL,CAAc,WAAd,EAAvB;AACA,WAAO,CAAC,MAAM,eAAe,CAAC,KAAvB,EAA8B,EACnC,GAAG,aADgC;AAEnC,MAAA,uBAAuB,EAAE,aAAa,CAAC,uBAAd,KAA0C,qEAA8B,KAF9D;AAGnC,MAAA,gBAAgB,EAAE,OAAO,CAAC,cAAR,KAA2B,KAHV;AAInC,MAAA,QAAQ,EAAE,QAAQ,IAAI,IAAZ,GAAmB,IAAnB,GAA0B,KAAK,EAAL,CAAQ,QAAR,CAAiB,QAAjB,CAJD;AAKnC,MAAA,gBAAgB,EAAE,WAAW,KAAK,OAAhB,GAA0B,MAA1B,GAAmC,MALlB;AAMnC,MAAA,OAAO,EAAE,OAAO,CAAC,4BAAR,EAN0B;AAOnC,MAAA,WAAW,EAAE,OAAO,CAAC,WAPc;AAQnC,MAAA,WAAW,EAAE,CAAC,OAAO,CAAC,WAAR,IAAuB,2BAAK,EAAL,CAAQ,OAAO,CAAC,EAAhB,EAAoB,qCAApB,CAAxB,EAAoF,WAApF,EARsB;AASnC,MAAA,YAAY,EAAE,WAAW,IAAI,OAAO,CAAC,WATF;AAUnC,MAAA,cAAc,EAAE,OAAO,CAAC,WAVW;AAWnC;AACA,MAAA,cAAc,EAAE,IAAI,KAAK,oBAAK,GAAd,GAAoB,sBAApB,GAA6C,oBAZ1B;AAanC;AACA,MAAA,4BAA4B,EAAE,iDAA8B,OAA9B,EAAuC,aAAa,CAAC,YAAd,KAA+B,IAAtE,CAdK;AAenC,MAAA,IAfmC;AAgBnC,MAAA;AAhBmC,KAA9B,CAAP;AAkBD;;AAEO,QAAM,sBAAN,CAA6B,SAA7B,EAA8C;AACpD,UAAM,OAAO,GAAG,KAAK,QAAL,CAAc,OAA9B;AACA,QAAI,2BAA2B,GAAG,KAAlC;AACA,UAAM,QAAQ,GAAG,IAAI,GAAJ,EAAjB;AACA,UAAM,IAAI,GAAkB,EAA5B;AACA,UAAM,SAAS,GAAG,IAAI,MAAJ,CAAW,CAAX,CAAlB;AACA,UAAM,aAAa,GAAG,gEAAoB,KAAK,OAAzB,EAAkC,KAAK,QAAvC,CAAtB;AACA,UAAM,KAAK,GAAG,MAAM,uBAAgB,GAAhB,CAAoB,gBAAK,SAAL,CAApB,EAAqC,IAAI,IAAG;AAC9D,YAAM,WAAW,GAAG,IAAI,CAAC,SAAL,CAAe,SAAS,CAAC,MAAV,GAAmB,CAAlC,CAApB;AAEA,YAAM,SAAS,GAAG,WAAW,CAAC,WAAZ,CAAwB,IAAI,CAAC,GAA7B,CAAlB;AACA,YAAM,QAAQ,GAAG,SAAS,GAAG,CAAZ,GAAgB,WAAW,CAAC,SAAZ,CAAsB,SAAS,GAAG,CAAlC,CAAhB,GAAuD,WAAxE;AACA,UAAI,WAAW,GAAkB,IAAjC;AACA,UAAI,OAAO,GAAG,EAAd,CAN8D,CAO9D;;AACA,UAAI,SAAS,GAAG,CAAhB,EAAmB;AACjB;AACA;AACA;AACA,QAAA,OAAO,GAAG,WAAW,CAAC,SAAZ,CAAsB,CAAtB,EAAyB,SAAzB,CAAV,CAJiB,CAKjB;;AACA,QAAA,WAAW,GAAG,MAAM,0BAAW,KAAX,EAAkB,MAAlB,CAAyB,OAAzB,EAAkC,MAAlC,CAAyC,QAAzC,EAAmD,OAAnD,CAA2D,KAA3D,EAAkE,GAAlE,EAAuE,OAAvE,CAA+E,KAA/E,EAAsF,GAAtF,EAA2F,OAA3F,CAAmG,KAAnG,EAA0G,EAA1G,CAApB;;AACA,YAAI,CAAC,QAAQ,CAAC,GAAT,CAAa,OAAb,CAAL,EAA4B;AAC1B,UAAA,QAAQ,CAAC,GAAT,CAAa,OAAb;AACA,UAAA,IAAI,CAAC,IAAL,CAAU,kBAAkB,WAAW,WAAW,WAAW,MAAM,OAAO,CAAC,OAAR,CAAgB,KAAhB,EAAuB,IAAvB,CAA4B,OAA/F;AACD;AACF,OAXD,MAYK,IAAI,CAAC,2BAAL,EAAkC;AACrC,QAAA,2BAA2B,GAAG,IAA9B;AACD,OAtB6D,CAwB9D;AACA;;;AACA,UAAI,MAAM,GAAG,aAAa,WAAW,KAAK,IAAhB,GAAuB,EAAvB,GAA4B,eAAe,WAAW,GAAG,GAAnF;AACA,MAAA,MAAM,IAAI,KAAK,SAAS,iBAAiB,QAAQ,0BAA0B,IAAI,CAAC,GAAG,GAAG,WAAW,gCAAjG;AACA,YAAM,gBAAgB,GAAG,WAAW,KAAK,GAAG,OAAO,CAAC,eAAe,MAAnE;;AACA,UAAI,gBAAJ,EAAsB;AACpB,QAAA,MAAM,IAAI,sBAAV;AACD,OAFD,MAGK,IAAI,WAAW,KAAK,IAApB,EAA0B;AAC7B,QAAA,MAAM,IAAI,QAAQ,IAAI,CAAC,QAAL,CAAc,WAAd,CAA0B,KAA5C;AACD;;AAED,YAAM,uBAAuB,GAAG,aAAa,CAAC,uBAAd,KAA0C,qEAA8B,KAAxG;;AACA,UAAI,gBAAgB,KAAK,uBAAuB,IAAI,aAAa,CAAC,yBAA9C,CAApB,EAA8F;AAC5F,QAAA,MAAM,IAAI,KAAV;AACA,cAAM,YAAY,GAAG,aAAa,CAAC,YAAnC;;AACA,YAAI,uBAAJ,EAA6B;AAC3B,UAAA,MAAM,IAAI,GAAG,SAAS,oEAAoE,YAAY,4EAAtG;AACD;;AAED,cAAM,eAAe,GAAG,aAAa,CAAC,YAAd,IAA8B,IAAtD;AACA,cAAM,4BAA4B,GAAG,eAAe,GAAG,mBAAH,GAAyB,mBAA7E;;AACA,YAAI,aAAa,CAAC,yBAAlB,EAA6C;AAC3C,cAAI,eAAJ,EAAqB;AACnB,YAAA,IAAI,CAAC,IAAL,CAAU,kBAAkB,4BAA4B,+BAA+B,aAAa,CAAC,YAAY,OAAjH;AACD;;AACD,UAAA,MAAM,IAAI,GAAG,SAAS,iDAAiD,4BAA4B,WAAW,YAAY,2EAA1H;AACA,UAAA,MAAM,IAAI,GAAG,SAAS,6DAA6D,KAAK,QAAL,CAAc,OAAd,CAAsB,EAAE,OAA3G;AACA,UAAA,MAAM,IAAI,GAAG,SAAS,iBAAtB;AACD;;AACD,QAAA,MAAM,IAAI,GAAG,SAAS,SAAtB;;AAEA,YAAI,eAAJ,EAAqB;AACnB,UAAA,MAAM,IAAI,qBAAqB,4BAA4B,gBAAgB,4BAA4B,sBAAvG;AACD;AACF,OAtBD,MAuBK;AACH,QAAA,MAAM,IAAI,IAAV;AACD;;AAED,aAAO,GAAG,MAAM,KAAK,SAAS,cAA9B;AACD,KAjEmB,CAApB;AAmEA,WAAO;AAAC,MAAA,IAAI,EAAE,YAAY,CAAC,IAAD,EAAO,CAAP,CAAnB;AAA8B,MAAA,KAAK,EAAE,YAAY,CAAC,KAAD,EAAQ,CAAR;AAAjD,KAAP;AACD;;AAzN0C;;;;AA4N7C,SAAS,YAAT,CAAsB,IAAtB,EAA2C,WAA3C,EAA+D;AAC7D,QAAM,KAAK,GAAG,IAAI,MAAJ,CAAW,WAAW,GAAG,CAAzB,CAAd;AACA,SAAO,IAAI,CAAC,IAAL,CAAU,KAAK,KAAK,EAApB,CAAP;AACD,C", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { Arch, log, deepAssign } from \"builder-util\"\nimport { UUID } from \"builder-util-runtime\"\nimport { getBinFromUrl } from \"../binDownload\"\nimport { walk } from \"builder-util/out/fs\"\nimport { createHash } from \"crypto\"\nimport * as ejs from \"ejs\"\nimport { readFile, writeFile } from \"fs-extra\"\nimport { Lazy } from \"lazy-val\"\nimport * as path from \"path\"\nimport { MsiOptions } from \"../\"\nimport { Target } from \"../core\"\nimport { DesktopShortcutCreationPolicy, FinalCommonWindowsInstallerOptions, getEffectiveOptions } from \"../options/CommonWindowsInstallerConfiguration\"\nimport { getTemplatePath } from \"../util/pathManager\"\nimport { VmManager } from \"../vm/vm\"\nimport { WineVmManager } from \"../vm/WineVm\"\nimport { WinPackager } from \"../winPackager\"\nimport { createStageDir, getWindowsInstallationDirName } from \"./targetUtil\"\n\nconst ELECTRON_BUILDER_UPGRADE_CODE_NS_UUID = UUID.parse(\"d752fe43-5d44-44d5-9fc9-6dd1bf19d5cc\")\nconst ROOT_DIR_ID = \"APPLICATIONFOLDER\"\n\nconst ASSISTED_UI_FILE_NAME = \"WixUI_Assisted.wxs\"\n\nconst projectTemplate = new Lazy<(data: any) => string>(async () => {\n  const template = (await readFile(path.join(getTemplatePath(\"msi\"), \"template.xml\"), \"utf8\"))\n    .replace(/{{/g, \"<%\")\n    .replace(/}}/g, \"%>\")\n    .replace(/\\${([^}]+)}/g, \"<%=$1%>\")\n  return ejs.compile(template)\n})\n\n// WiX doesn't support Mono, so, dontnet462 is required to be installed for wine (preinstalled in our bundled wine)\nexport default class MsiTarget extends Target {\n  private readonly vm = process.platform === \"win32\" ? new VmManager() : new WineVmManager()\n\n  readonly options: MsiOptions = deepAssign(this.packager.platformSpecificBuildOptions, this.packager.config.msi)\n\n  constructor(private readonly packager: WinPackager, readonly outDir: string) {\n    super(\"msi\")\n  }\n\n  async build(appOutDir: string, arch: Arch) {\n    const packager = this.packager\n    const artifactName = packager.expandArtifactBeautyNamePattern(this.options, \"msi\", arch)\n    const artifactPath = path.join(this.outDir, artifactName)\n    await packager.info.callArtifactBuildStarted({\n      targetPresentableName: \"MSI\",\n      file: artifactPath,\n      arch,\n    })\n\n    const stageDir = await createStageDir(this, packager, arch)\n    const vm = this.vm\n\n    const commonOptions = getEffectiveOptions(this.options, this.packager)\n\n    if (commonOptions.isAssisted) {\n      // F*** *** ***  ***  ***  ***  ***  ***  ***  ***  ***  ***  *** WiX  ***  ***  ***  ***  ***  ***  ***  ***  ***\n      // cannot understand how to set MSIINSTALLPERUSER on radio box change. In any case installed per user.\n      log.warn(`MSI DOESN'T SUPPORT assisted installer. Please use NSIS instead.`)\n    }\n\n    const projectFile = stageDir.getTempFile(\"project.wxs\")\n    const objectFiles = [\"project.wixobj\"]\n    const uiFile = commonOptions.isAssisted ?  stageDir.getTempFile(ASSISTED_UI_FILE_NAME) : null\n    await writeFile(projectFile, await this.writeManifest(appOutDir, arch, commonOptions))\n    if (uiFile !== null) {\n      await writeFile(uiFile, await readFile(path.join(getTemplatePath(\"msi\"), ASSISTED_UI_FILE_NAME), \"utf8\"))\n      objectFiles.push(ASSISTED_UI_FILE_NAME.replace(\".wxs\", \".wixobj\"))\n    }\n\n    // noinspection SpellCheckingInspection\n    const vendorPath = await getBinFromUrl(\"wix\", \"4.0.0.5512.2\", \"/X5poahdCc3199Vt6AP7gluTlT1nxi9cbbHhZhCMEu+ngyP1LiBMn+oZX7QAZVaKeBMc2SjVp7fJqNLqsUnPNQ==\")\n\n    // noinspection SpellCheckingInspection\n    const candleArgs = [\n      \"-arch\", arch === Arch.ia32 ? \"x86\" : (arch === Arch.arm64 ? \"arm64\" : \"x64\"),\n      `-dappDir=${vm.toVmFile(appOutDir)}`,\n    ].concat(this.getCommonWixArgs())\n    candleArgs.push(\"project.wxs\")\n    if (uiFile !== null) {\n      candleArgs.push(ASSISTED_UI_FILE_NAME)\n    }\n    await vm.exec(vm.toVmFile(path.join(vendorPath, \"candle.exe\")), candleArgs, {\n      cwd: stageDir.dir,\n    })\n\n    await this.light(objectFiles, vm, artifactPath, appOutDir, vendorPath, stageDir.dir)\n\n    await stageDir.cleanup()\n\n    await packager.sign(artifactPath)\n\n    await packager.info.callArtifactBuildCompleted({\n      file: artifactPath,\n      packager,\n      arch,\n      safeArtifactName: packager.computeSafeArtifactName(artifactName, \"msi\"),\n      target: this,\n      isWriteUpdateInfo: false,\n    })\n  }\n\n  private async light(objectFiles: Array<string>, vm: VmManager, artifactPath: string, appOutDir: string, vendorPath: string, tempDir: string) {\n    // noinspection SpellCheckingInspection\n    const lightArgs = [\n      \"-out\", vm.toVmFile(artifactPath),\n      \"-v\",\n      // https://github.com/wixtoolset/issues/issues/5169\n      \"-spdb\",\n      // https://sourceforge.net/p/wix/bugs/2405/\n      // error LGHT1076 : ICE61: This product should remove only older versions of itself. The Maximum version is not less than the current product. (1.1.0.42 1.1.0.42)\n      \"-sw1076\",\n      `-dappDir=${vm.toVmFile(appOutDir)}`,\n      // \"-dcl:high\",\n    ].concat(this.getCommonWixArgs())\n\n    // http://windows-installer-xml-wix-toolset.687559.n2.nabble.com/Build-3-5-2229-0-give-me-the-following-error-error-LGHT0216-An-unexpected-Win32-exception-with-errorn-td5707443.html\n    if (process.platform !== \"win32\") {\n      // noinspection SpellCheckingInspection\n      lightArgs.push(\"-sval\")\n    }\n\n    if (this.options.oneClick === false) {\n      lightArgs.push(\"-ext\", \"WixUIExtension\")\n    }\n\n    // objectFiles - only filenames, we set current directory to our temp stage dir\n    lightArgs.push(...objectFiles)\n    await vm.exec(vm.toVmFile(path.join(vendorPath, \"light.exe\")), lightArgs, {\n      cwd: tempDir,\n    })\n  }\n\n  private getCommonWixArgs() {\n    const args: Array<string> = [\"-pedantic\"]\n    if (this.options.warningsAsErrors !== false) {\n      args.push(\"-wx\")\n    }\n    return args\n  }\n\n  private async writeManifest(appOutDir: string, arch: Arch, commonOptions: FinalCommonWindowsInstallerOptions) {\n    const appInfo = this.packager.appInfo\n    const {files, dirs} = await this.computeFileDeclaration(appOutDir)\n\n    const companyName = appInfo.companyName\n    if (!companyName) {\n      log.warn(`Manufacturer is not set for MSI — please set \"author\" in the package.json`)\n    }\n\n    const compression = this.packager.compression\n    const options = this.options\n    const iconPath = await this.packager.getIconPath()\n    return (await projectTemplate.value)({\n      ...commonOptions,\n      isCreateDesktopShortcut: commonOptions.isCreateDesktopShortcut !== DesktopShortcutCreationPolicy.NEVER,\n      isRunAfterFinish: options.runAfterFinish !== false,\n      iconPath: iconPath == null ? null : this.vm.toVmFile(iconPath),\n      compressionLevel: compression === \"store\" ? \"none\" : \"high\",\n      version: appInfo.getVersionInWeirdWindowsForm(),\n      productName: appInfo.productName,\n      upgradeCode: (options.upgradeCode || UUID.v5(appInfo.id, ELECTRON_BUILDER_UPGRADE_CODE_NS_UUID)).toUpperCase(),\n      manufacturer: companyName || appInfo.productName,\n      appDescription: appInfo.description,\n      // https://stackoverflow.com/questions/1929038/compilation-error-ice80-the-64bitcomponent-uses-32bitdirectory\n      programFilesId: arch === Arch.x64 ? \"ProgramFiles64Folder\" : \"ProgramFilesFolder\",\n      // wix in the name because special wix format can be used in the name\n      installationDirectoryWixName: getWindowsInstallationDirName(appInfo, commonOptions.isPerMachine === true),\n      dirs,\n      files,\n    })\n  }\n\n  private async computeFileDeclaration(appOutDir: string) {\n    const appInfo = this.packager.appInfo\n    let isRootDirAddedToRemoveTable = false\n    const dirNames = new Set<string>()\n    const dirs: Array<string> = []\n    const fileSpace = \" \".repeat(6)\n    const commonOptions = getEffectiveOptions(this.options, this.packager)\n    const files = await BluebirdPromise.map(walk(appOutDir), file => {\n      const packagePath = file.substring(appOutDir.length + 1)\n\n      const lastSlash = packagePath.lastIndexOf(path.sep)\n      const fileName = lastSlash > 0 ? packagePath.substring(lastSlash + 1) : packagePath\n      let directoryId: string | null = null\n      let dirName = \"\"\n      // Wix Directory.FileSource doesn't work - https://stackoverflow.com/questions/21519388/wix-filesource-confusion\n      if (lastSlash > 0) {\n        // This Name attribute may also define multiple directories using the inline directory syntax.\n        // For example, \"ProgramFilesFolder:\\My Company\\My Product\\bin\" would create a reference to a Directory element with Id=\"ProgramFilesFolder\" then create directories named \"My Company\" then \"My Product\" then \"bin\" nested beneath each other.\n        // This syntax is a shortcut to defining each directory in an individual Directory element.\n        dirName = packagePath.substring(0, lastSlash)\n        // https://github.com/electron-userland/electron-builder/issues/3027\n        directoryId = \"d\" + createHash(\"md5\").update(dirName).digest(\"base64\").replace(/\\//g, \"_\").replace(/\\+/g, \".\").replace(/=+$/, \"\")\n        if (!dirNames.has(dirName)) {\n          dirNames.add(dirName)\n          dirs.push(`<Directory Id=\"${directoryId}\" Name=\"${ROOT_DIR_ID}:\\\\${dirName.replace(/\\//g, \"\\\\\")}\\\\\"/>`)\n        }\n      }\n      else if (!isRootDirAddedToRemoveTable) {\n        isRootDirAddedToRemoveTable = true\n      }\n\n      // since RegistryValue can be part of Component, *** *** *** *** *** *** *** *** *** wix cannot auto generate guid\n      // https://stackoverflow.com/questions/1405100/change-my-component-guid-in-wix\n      let result = `<Component${directoryId === null ? \"\" : ` Directory=\"${directoryId}\"`}>`\n      result += `\\n${fileSpace}  <File Name=\"${fileName}\" Source=\"$(var.appDir)${path.sep}${packagePath}\" ReadOnly=\"yes\" KeyPath=\"yes\"`\n      const isMainExecutable = packagePath === `${appInfo.productFilename}.exe`\n      if (isMainExecutable) {\n        result += ' Id=\"mainExecutable\"'\n      }\n      else if (directoryId === null) {\n        result += ` Id=\"${path.basename(packagePath)}_f\"`\n      }\n\n      const isCreateDesktopShortcut = commonOptions.isCreateDesktopShortcut !== DesktopShortcutCreationPolicy.NEVER\n      if (isMainExecutable && (isCreateDesktopShortcut || commonOptions.isCreateStartMenuShortcut)) {\n        result += `>\\n`\n        const shortcutName = commonOptions.shortcutName\n        if (isCreateDesktopShortcut) {\n          result += `${fileSpace}  <Shortcut Id=\"desktopShortcut\" Directory=\"DesktopFolder\" Name=\"${shortcutName}\" WorkingDirectory=\"APPLICATIONFOLDER\" Advertise=\"yes\" Icon=\"icon.ico\"/>\\n`\n        }\n\n        const hasMenuCategory = commonOptions.menuCategory != null\n        const startMenuShortcutDirectoryId = hasMenuCategory ? \"AppProgramMenuDir\" : \"ProgramMenuFolder\"\n        if (commonOptions.isCreateStartMenuShortcut) {\n          if (hasMenuCategory) {\n            dirs.push(`<Directory Id=\"${startMenuShortcutDirectoryId}\" Name=\"ProgramMenuFolder:\\\\${commonOptions.menuCategory}\\\\\"/>`)\n          }\n          result += `${fileSpace}  <Shortcut Id=\"startMenuShortcut\" Directory=\"${startMenuShortcutDirectoryId}\" Name=\"${shortcutName}\" WorkingDirectory=\"APPLICATIONFOLDER\" Advertise=\"yes\" Icon=\"icon.ico\">\\n`\n          result += `${fileSpace}    <ShortcutProperty Key=\"System.AppUserModel.ID\" Value=\"${this.packager.appInfo.id}\"/>\\n`\n          result += `${fileSpace}  </Shortcut>\\n`\n        }\n        result += `${fileSpace}</File>`\n\n        if (hasMenuCategory) {\n          result += `<RemoveFolder Id=\"${startMenuShortcutDirectoryId}\" Directory=\"${startMenuShortcutDirectoryId}\" On=\"uninstall\"/>\\n`\n        }\n      }\n      else {\n        result += `/>`\n      }\n\n      return `${result}\\n${fileSpace}</Component>`\n    })\n\n    return {dirs: listToString(dirs, 2), files: listToString(files, 3)}\n  }\n}\n\nfunction listToString(list: Array<string>, indentLevel:  number) {\n  const space = \" \".repeat(indentLevel * 2)\n  return list.join(`\\n${space}`)\n}\n"], "sourceRoot": ""}