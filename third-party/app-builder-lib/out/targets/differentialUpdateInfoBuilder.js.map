{"version": 3, "sources": ["../../src/targets/differentialUpdateInfoBuilder.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;;AAGA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;AAGO,MAAM,qBAAqB,GAAG,WAA9B;;;AAED,SAAU,mCAAV,CAA8C,YAA9C,EAAoE,YAApE,EAAqH;AACzH,MAAI,YAAY,IAAI,IAApB,EAA0B;AACxB,WAAO,IAAP;AACD;;AAED,QAAM,IAAI,GAAG,MAAM,CAAC,IAAP,CAAY,YAAZ,CAAb;;AACA,MAAI,IAAI,CAAC,MAAL,IAAe,CAAnB,EAAsB;AACpB,WAAO,IAAP;AACD;;AAED,QAAM,QAAQ,GAAwC,EAAtD;;AACA,OAAK,MAAM,IAAX,IAAmB,IAAnB,EAAyB;AACvB,UAAM,eAAe,GAAG,YAAY,CAAC,IAAD,CAApC;AACA,UAAM,IAAI,GAAG,IAAI,CAAC,QAAL,CAAc,eAAe,CAAC,IAA9B,CAAb;AACA,IAAA,QAAQ,CAAC,IAAD,CAAR,GAAiB,EACf,GAAG,eADY;AAEf,MAAA,IAAI,EAAE,IAFS;AAGf;AACA,MAAA;AAJe,KAAjB;AAMD;;AACD,SAAO;AAAC,IAAA;AAAD,GAAP;AACD;;AAEK,SAAU,wCAAV,CAAmD,cAAnD,EAAiF;AACrF;;;;;;;;;;;;;;AAqBA,EAAA,cAAc,CAAC,QAAf,GAA0B,CAA1B,CAtBqF,CAuBrF;;AACA,EAAA,cAAc,CAAC,KAAf,GAAuB,KAAvB,CAxBqF,CAyBrF;;AACA,EAAA,cAAc,CAAC,WAAf,GAA6B,QAA7B;AACA,SAAO,cAAP;AACD;;AAEM,eAAe,cAAf,CAA8B,IAA9B,EAA0C;AAC/C,qBAAI,IAAJ,CAAS;AAAC,IAAA,IAAI,EAAE,mBAAI,QAAJ,CAAa,IAAb;AAAP,GAAT,EAAqC,6BAArC;;AACA,SAAO,MAAM,2CAA4C,CAAC,UAAD,EAAa,SAAb,EAAwB,IAAxB,EAA8B,eAA9B,EAA+C,SAA/C,CAA5C,CAAb;AACD;;AAEM,eAAe,cAAf,CAA8B,IAA9B,EAA4C,MAA5C,EAA4D,QAA5D,EAA6F,gBAA7F,EAA4H;AACjI,QAAM,YAAY,GAAG,GAAG,IAAI,GAAG,qBAAqB,EAApD;;AACA,qBAAI,IAAJ,CAAS;AAAC,IAAA,YAAY,EAAE,mBAAI,QAAJ,CAAa,YAAb;AAAf,GAAT,EAAqD,oBAArD;;AACA,QAAM,UAAU,GAAG,MAAM,2CAA4C,CAAC,UAAD,EAAa,SAAb,EAAwB,IAAxB,EAA8B,UAA9B,EAA0C,YAA1C,CAA5C,CAAzB;AACA,QAAM,QAAQ,CAAC,IAAT,CAAc,0BAAd,CAAyC;AAC7C,IAAA,IAAI,EAAE,YADuC;AAE7C,IAAA,gBAAgB,EAAE,gBAAgB,IAAI,IAApB,GAA2B,IAA3B,GAAkC,GAAG,gBAAgB,GAAG,qBAAqB,EAFlD;AAG7C,IAAA,MAH6C;AAI7C,IAAA,IAAI,EAAE,IAJuC;AAK7C,IAAA,QAL6C;AAM7C,IAAA;AAN6C,GAAzC,CAAN;AAQA,SAAO,UAAP;AACD,C", "sourcesContent": ["import { log } from \"builder-util\"\nimport { BlockMapDataHolder, PackageFileInfo } from \"builder-util-runtime\"\nimport * as path from \"path\"\nimport { Target } from \"../core\"\nimport { PlatformPackager } from \"../platformPackager\"\nimport { executeAppBuilderAsJson } from \"../util/appBuilder\"\nimport { ArchiveOptions } from \"./archive\"\n\nexport const BLOCK_MAP_FILE_SUFFIX = \".blockmap\"\n\nexport function createNsisWebDifferentialUpdateInfo(artifactPath: string, packageFiles: { [arch: string]: PackageFileInfo }) {\n  if (packageFiles == null) {\n    return null\n  }\n\n  const keys = Object.keys(packageFiles)\n  if (keys.length <= 0) {\n    return null\n  }\n\n  const packages: { [arch: string]: PackageFileInfo } = {}\n  for (const arch of keys) {\n    const packageFileInfo = packageFiles[arch]\n    const file = path.basename(packageFileInfo.path)\n    packages[arch] = {\n      ...packageFileInfo,\n      path: file,\n      // https://github.com/electron-userland/electron-builder/issues/2583\n      file,\n    } as any\n  }\n  return {packages}\n}\n\nexport function configureDifferentialAwareArchiveOptions(archiveOptions: ArchiveOptions): ArchiveOptions {\n  /*\n   * dict size 64 MB: Full: 33,744.88 KB, To download: 17,630.3 KB (52%)\n   * dict size 16 MB: Full: 33,936.84 KB, To download: 16,175.9 KB (48%)\n   * dict size  8 MB: Full: 34,187.59 KB, To download:  8,229.9 KB (24%)\n   * dict size  4 MB: Full: 34,628.73 KB, To download: 3,782.97 KB (11%)\n\n   as we can see, if file changed in one place, all block is invalidated (and update size approximately equals to dict size)\n\n   1 MB is used:\n\n   1MB:\n\n   2018/01/11 11:54:41:0045 File has 59 changed blocks\n   2018/01/11 11:54:41:0050 Full: 71,588.59 KB, To download: 1,243.39 KB (2%)\n\n   4MB:\n\n   2018/01/11 11:31:43:0440 Full: 70,303.55 KB, To download: 4,843.27 KB (7%)\n   2018/01/11 11:31:43:0435 File has 234 changed blocks\n\n   */\n  archiveOptions.dictSize = 1\n  // solid compression leads to a lot of changed blocks\n  archiveOptions.solid = false\n  // do not allow to change compression level to avoid different packages\n  archiveOptions.compression = \"normal\"\n  return archiveOptions\n}\n\nexport async function appendBlockmap(file: string): Promise<BlockMapDataHolder> {\n  log.info({file: log.filePath(file)}, \"building embedded block map\")\n  return await executeAppBuilderAsJson<BlockMapDataHolder>([\"blockmap\", \"--input\", file, \"--compression\", \"deflate\"])\n}\n\nexport async function createBlockmap(file: string, target: Target, packager: PlatformPackager<any>, safeArtifactName: string | null): Promise<BlockMapDataHolder> {\n  const blockMapFile = `${file}${BLOCK_MAP_FILE_SUFFIX}`\n  log.info({blockMapFile: log.filePath(blockMapFile)}, \"building block map\")\n  const updateInfo = await executeAppBuilderAsJson<BlockMapDataHolder>([\"blockmap\", \"--input\", file, \"--output\", blockMapFile])\n  await packager.info.callArtifactBuildCompleted({\n    file: blockMapFile,\n    safeArtifactName: safeArtifactName == null ? null : `${safeArtifactName}${BLOCK_MAP_FILE_SUFFIX}`,\n    target,\n    arch: null,\n    packager,\n    updateInfo,\n  })\n  return updateInfo\n}"], "sourceRoot": ""}