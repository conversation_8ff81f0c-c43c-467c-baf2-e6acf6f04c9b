{"version": 3, "sources": ["../../../src/targets/nsis/nsisLicense.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AAIA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;AAEO,eAAe,kBAAf,CAAkC,QAAlC,EAAyD,OAAzD,EAA+E,eAA/E,EAAqH,SAArH,EAA6I;AAClJ,QAAM,OAAO,GAAG,MAAM,2CAA2B,OAAO,CAAC,OAAnC,EAA4C,QAA5C,CAAtB;;AACA,MAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,QAAI,WAAJ;;AACA,QAAI,OAAO,CAAC,QAAR,CAAiB,OAAjB,CAAJ,EAA+B;AAC7B,MAAA,WAAW,GAAG,CACZ,kDADY,EAEZ,sBAFY,EAGZ,0CAHY,EAIZ,2BAJY,EAKZ,+DALY,EAMZ,aANY,EAQZ,kCAAkC,IAAI,CAAC,IAAL,CAAU,4BAAV,EAA4B,mBAA5B,CAAgD,GARtE,CAAd;AAUD,KAXD,MAYK;AACH,MAAA,WAAW,GAAG,CAAC,kCAAkC,OAAO,GAA1C,CAAd;AACD;;AAED,IAAA,eAAe,CAAC,KAAhB,CAAsB,aAAtB,EAAqC,WAArC;;AACA,QAAI,OAAO,CAAC,QAAR,CAAiB,OAAjB,CAAJ,EAA+B;AAC7B,MAAA,eAAe,CAAC,KAAhB,CAAsB,iBAAtB,EAAyC,CAAC,0CAA0C,OAAO,GAAlD,CAAzC;AACD;;AACD;AACD;;AAED,QAAM,YAAY,GAAG,MAAM,gCAAgB,QAAhB,CAA3B;;AACA,MAAI,YAAY,CAAC,MAAb,KAAwB,CAA5B,EAA+B;AAC7B;AACD;;AAED,QAAM,WAAW,GAAkB,EAAnC;AACA,QAAM,gBAAgB,GAAG,IAAI,GAAJ,CAAQ,SAAR,CAAzB;AAEA,MAAI,WAAW,GAAkB,IAAjC;;AACA,OAAK,MAAM,IAAX,IAAmB,YAAnB,EAAiC;AAC/B,IAAA,gBAAgB,CAAC,MAAjB,CAAwB,IAAI,CAAC,cAA7B;;AACA,QAAI,WAAW,IAAI,IAAnB,EAAyB;AACvB,MAAA,WAAW,GAAG,IAAI,CAAC,IAAnB;AACD;;AACD,IAAA,WAAW,CAAC,IAAZ,CAAiB,gCAAgC,cAAK,IAAI,CAAC,cAAV,KAA6B,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,GAArG;AACD;;AAED,OAAK,MAAM,CAAX,IAAgB,gBAAhB,EAAkC;AAChC,IAAA,WAAW,CAAC,IAAZ,CAAiB,gCAAgC,cAAK,CAAL,CAAO,KAAK,WAAW,GAAxE;AACD;;AAED,EAAA,WAAW,CAAC,IAAZ,CAAiB,+CAAjB;AACA,EAAA,eAAe,CAAC,KAAhB,CAAsB,aAAtB,EAAqC,WAArC;AACD,C", "sourcesContent": ["import { lcid } from \"../../util/langs\"\nimport { getLicenseFiles, getNotLocalizedLicenseFile } from \"../../util/license\"\nimport * as path from \"path\"\nimport { WinPackager } from \"../../winPackager\"\nimport { NsisOptions } from \"./nsisOptions\"\nimport { NsisScriptGenerator } from \"./nsisScriptGenerator\"\nimport { nsisTemplatesDir } from \"./nsisUtil\"\n\nexport async function computeLicensePage(packager: WinPackager, options: NsisOptions, scriptGenerator: NsisScriptGenerator, languages: Array<string>): Promise<void> {\n  const license = await getNotLocalizedLicenseFile(options.license, packager)\n  if (license != null) {\n    let licensePage: Array<string>\n    if (license.endsWith(\".html\")) {\n      licensePage = [\n        \"!define MUI_PAGE_CUSTOMFUNCTION_SHOW LicenseShow\",\n        \"Function LicenseShow\",\n        \"  FindWindow $R0 `#32770` `` $HWNDPARENT\",\n        \"  GetDlgItem $R0 $R0 1000\",\n        \"EmbedHTML::Load /replace $R0 file://$PLUGINSDIR\\\\license.html\",\n        \"FunctionEnd\",\n\n        `!insertmacro MUI_PAGE_LICENSE \"${path.join(nsisTemplatesDir, \"empty-license.txt\")}\"`,\n      ]\n    }\n    else {\n      licensePage = [`!insertmacro MUI_PAGE_LICENSE \"${license}\"`]\n    }\n\n    scriptGenerator.macro(\"licensePage\", licensePage)\n    if (license.endsWith(\".html\")) {\n      scriptGenerator.macro(\"addLicenseFiles\", [`File /oname=$PLUGINSDIR\\\\license.html \"${license}\"`])\n    }\n    return\n  }\n\n  const licenseFiles = await getLicenseFiles(packager)\n  if (licenseFiles.length === 0) {\n    return\n  }\n\n  const licensePage: Array<string> = []\n  const unspecifiedLangs = new Set(languages)\n\n  let defaultFile: string | null = null\n  for (const item of licenseFiles) {\n    unspecifiedLangs.delete(item.langWithRegion)\n    if (defaultFile == null) {\n      defaultFile = item.file\n    }\n    licensePage.push(`LicenseLangString MUILicense ${lcid[item.langWithRegion] || item.lang} \"${item.file}\"`)\n  }\n\n  for (const l of unspecifiedLangs) {\n    licensePage.push(`LicenseLangString MUILicense ${lcid[l]} \"${defaultFile}\"`)\n  }\n\n  licensePage.push('!insertmacro MUI_PAGE_LICENSE \"$(MUILicense)\"')\n  scriptGenerator.macro(\"licensePage\", licensePage)\n}\n"], "sourceRoot": ""}