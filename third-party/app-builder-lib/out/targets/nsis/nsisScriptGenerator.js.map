{"version": 3, "sources": ["../../../src/targets/nsis/nsisScriptGenerator.ts"], "names": [], "mappings": ";;;;;;;AAAM,MAAO,mBAAP,CAA0B;AAAhC,EAAA,WAAA,GAAA;AACmB,SAAA,KAAA,GAAuB,EAAvB;AA+ClB;;AA7CC,EAAA,aAAa,CAAC,IAAD,EAAa;AACxB,SAAK,KAAL,CAAW,IAAX,CAAgB,mBAAmB,IAAI,GAAvC;AACD;;AAED,EAAA,YAAY,CAAC,UAAD,EAAqB,GAArB,EAAgC;AAC1C,SAAK,KAAL,CAAW,IAAX,CAAgB,kBAAkB,UAAU,KAAK,GAAG,GAApD;AACD;;AAED,EAAA,OAAO,CAAC,IAAD,EAAa;AAClB,SAAK,KAAL,CAAW,IAAX,CAAgB,aAAa,IAAI,GAAjC;AACD;;AAED,EAAA,KAAK,CAAC,IAAD,EAAe,KAAf,EAAyD;AAC5D,SAAK,KAAL,CAAW,IAAX,CACE,UAAU,IAAI,EADhB,EAEE,KAAK,CAAC,KAAK,CAAC,OAAN,CAAc,KAAd,IAAuB,KAAvB,GAAgC,KAA6B,CAAC,KAA/D,EAAsE,IAAtE,CAA2E,MAA3E,CAAkF,EAFzF,EAGE,aAHF;AAKD;;AAED,EAAA,IAAI,CAAC,UAAD,EAA4B,IAA5B,EAAwC;AAC1C,SAAK,KAAL,CAAW,IAAX,CAAgB,OAAO,UAAU,IAAI,IAAd,GAAqB,EAArB,GAA0B,YAAY,UAAU,GAAG,KAAK,IAAI,GAAnF;AACD;;AAED,EAAA,WAAW,CAAC,IAAD,EAAe,UAAf,EAAiC;AAC1C,SAAK,KAAL,CAAW,IAAX,CAAgB,gBAAgB,IAAI,IAAI,UAAU,EAAlD;AACD,GA7B6B,CA+B9B;;;AACA,EAAA,KAAK,CAAC,KAAD,EAAqB;AACxB,SAAK,MAAM,QAAX,IAAuB,KAAvB,EAA8B;AAC5B,YAAM,YAAY,GAAG,iBAAiB,CAAC,QAAD,CAAjB,CAClB,OADkB,CACV,aADU,EACK,CAAC,CAAD,EAAI,EAAJ,KAAW,EAAE,CAAC,WAAH,EADhB,CAArB;AAEA,WAAK,KAAL,CAAW,IAAX,CAAgB,WAAW,YAAY;oCACT,QAAQ;;;UAGlC,YAAY,SAAS,YAAY;CAJrC;AAMD;AACF;;AAED,EAAA,KAAK,GAAA;AACH,WAAO,KAAK,KAAL,CAAW,IAAX,CAAgB,IAAhB,IAAwB,IAA/B;AACD;;AA/C6B;;;;AAkDhC,SAAS,iBAAT,CAA2B,QAA3B,EAA2C;AACzC,MAAI,QAAQ,KAAK,UAAjB,EAA6B;AAC3B,WAAO,eAAP;AACD;;AACD,MAAI,QAAQ,KAAK,aAAjB,EAAgC;AAC9B,WAAO,kBAAP;AACD;;AACD,SAAO,OAAO,QAAQ,CAAC,CAAD,CAAR,CAAY,WAAZ,EAAP,GAAmC,QAAQ,CAAC,SAAT,CAAmB,CAAnB,CAA1C;AACD,C", "sourcesContent": ["export class NsisScriptGenerator {\n  private readonly lines: Array<string> = []\n\n  addIncludeDir(file: string) {\n    this.lines.push(`!addincludedir \"${file}\"`)\n  }\n\n  addPluginDir(pluginArch: string, dir: string) {\n    this.lines.push(`!addplugindir /${pluginArch} \"${dir}\"`)\n  }\n\n  include(file: string) {\n    this.lines.push(`!include \"${file}\"`)\n  }\n\n  macro(name: string, lines: Array<string> | NsisScriptGenerator) {\n    this.lines.push(\n      `!macro ${name}`,\n      `  ${(Array.isArray(lines) ? lines : (lines as NsisScriptGenerator).lines).join(\"\\n  \")}`,\n      `!macroend\\n`\n    )\n  }\n\n  file(outputName: string | null, file: string) {\n    this.lines.push(`File${outputName == null ? \"\" : ` \"/oname=${outputName}\"`} \"${file}\"`)\n  }\n\n  insertMacro(name: string, parameters: string) {\n    this.lines.push(`!insertmacro ${name} ${parameters}`)\n  }\n\n  // without -- !!!\n  flags(flags: Array<string>) {\n    for (const flagName of flags) {\n      const variableName = getVarNameForFlag(flagName)\n        .replace(/[-]+(\\w|$)/g, (m, p1) => p1.toUpperCase())\n      this.lines.push(`!macro _${variableName} _a _b _t _f\n  $\\{StdUtils.TestParameter} $R9 \"${flagName}\"\n  StrCmp \"$R9\" \"true\" \\`$\\{_t}\\` \\`$\\{_f}\\`\n!macroend\n!define ${variableName} \\`\"\" ${variableName} \"\"\\`\n`)\n    }\n  }\n\n  build() {\n    return this.lines.join(\"\\n\") + \"\\n\"\n  }\n}\n\nfunction getVarNameForFlag(flagName: string): string {\n  if (flagName === \"allusers\") {\n    return \"isForAllUsers\"\n  }\n  if (flagName === \"currentuser\") {\n    return \"isForCurrentUser\"\n  }\n  return \"is\" + flagName[0].toUpperCase() + flagName.substring(1)\n}"], "sourceRoot": ""}