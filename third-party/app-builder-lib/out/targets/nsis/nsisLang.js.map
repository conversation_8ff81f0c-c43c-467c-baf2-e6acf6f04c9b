{"version": 3, "sources": ["../../../src/targets/nsis/nsisLang.ts"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AAIA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;;;AAEA,MAAM,KAAK,GAAG,qBAAO,uBAAP,CAAd;;AAEM,MAAO,gBAAP,CAAuB;AAI3B,EAAA,WAAA,CAAY,OAAZ,EAAgC;AAC9B,UAAM,OAAO,GAAG,OAAO,CAAC,kBAAxB;;AAEA,QAAI,OAAO,CAAC,OAAR,KAAoB,KAApB,IAA6B,OAAO,KAAK,IAAzC,IAAkD,KAAK,CAAC,OAAN,CAAc,OAAd,KAA0B,OAAO,CAAC,MAAR,KAAmB,CAAnG,EAAuG;AACrG,WAAK,WAAL,GAAmB,KAAnB;AACD,KAFD,MAGK;AACH,WAAK,WAAL,GAAmB,OAAO,CAAC,sBAAR,KAAmC,KAAtD;AACD;;AAED,QAAI,KAAK,WAAT,EAAsB;AACpB,WAAK,KAAL,GAAa,OAAO,IAAI,IAAX,GAAkB,0BAAiB,KAAjB,EAAlB,GAA6C,4BAAQ,OAAR,EACvD,GADuD,CACnD,EAAE,IAAI,+BAAiB,EAAE,CAAC,OAAH,CAAW,GAAX,EAAgB,GAAhB,CAAjB,CAD6C,CAA1D;AAED,KAHD,MAIK;AACH,WAAK,KAAL,GAAa,CAAC,OAAD,CAAb;AACD;AACF;;AArB0B;;;;AAwBvB,SAAU,mBAAV,CAA8B,eAA9B,EAAoE,gBAApE,EAAsG;AAC1G,QAAM,MAAM,GAAkB,EAA9B;;AACA,OAAK,MAAM,cAAX,IAA6B,gBAAgB,CAAC,KAA9C,EAAqD;AACnD,QAAI,IAAJ;;AACA,QAAI,cAAc,KAAK,OAAvB,EAAgC;AAC9B,MAAA,IAAI,GAAG,aAAP;AACD,KAFD,MAGK,IAAI,cAAc,KAAK,OAAvB,EAAgC;AACnC,MAAA,IAAI,GAAG,aAAP;AACD,KAFI,MAGA,IAAI,cAAc,KAAK,OAAvB,EAAgC;AACnC,MAAA,IAAI,GAAG,WAAP;AACD,KAFI,MAGA,IAAI,cAAc,KAAK,OAAvB,EAAgC;AACnC,MAAA,IAAI,GAAG,cAAP;AACD,KAFI,MAGA;AACH,YAAM,IAAI,GAAG,cAAc,CAAC,SAAf,CAAyB,CAAzB,EAA4B,cAAc,CAAC,OAAf,CAAuB,GAAvB,CAA5B,CAAb;AACA,MAAA,IAAI,GAAI,sBAAqB,IAArB,CAAR;;AACA,UAAI,IAAI,IAAI,IAAZ,EAAkB;AAChB,cAAM,IAAI,KAAJ,CAAU,gCAAgC,IAAI,EAA9C,CAAN;AACD;;AAED,UAAI,IAAI,KAAK,SAAb,EAAwB;AACtB,QAAA,IAAI,GAAG,sBAAP;AACD;AACF;;AACD,IAAA,MAAM,CAAC,IAAP,CAAY,8BAA8B,IAAI,GAA9C;AACD;;AAED,EAAA,eAAe,CAAC,KAAhB,CAAsB,UAAtB,EAAkC,MAAlC;AACD;;AAED,eAAe,mBAAf,CAAmC,IAAnC,EAAiD,QAAjD,EAAgF;AAC9E,QAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,WAAT,CAAqB,cAArB,CAAnB;AACA,QAAM,2BAAW,IAAX,EAAiB,IAAjB,CAAN;AACA,SAAO,IAAP;AACD;;AAEM,eAAe,2BAAf,CAA2C,KAA3C,EAA0D,QAA1D,EAA2F,eAA3F,EAAiI,gBAAjI,EAAmK;AACxK,QAAM,IAAI,GAAG,wBAAS,MAAM,yBAAS,IAAI,CAAC,IAAL,CAAU,4BAAV,EAA4B,KAA5B,CAAT,EAA6C,OAA7C,CAAf,CAAb;AACA,QAAM,YAAY,GAAG,gCAAgC,CAAC,IAAD,EAAO,gBAAP,CAAhC,CAAyD,IAAzD,CAA8D,IAA9D,CAArB;AACA,EAAA,KAAK,CAAC,YAAD,CAAL;AACA,EAAA,eAAe,CAAC,OAAhB,CAAwB,MAAM,mBAAmB,CAAC,YAAD,EAAe,QAAf,CAAjD;AACD;;AAED,SAAS,gCAAT,CAA0C,QAA1C,EAAyD,gBAAzD,EAA2F;AACzF,QAAM,MAAM,GAAkB,EAA9B;AACA,QAAM,aAAa,GAAG,IAAI,GAAJ,CAAQ,gBAAgB,CAAC,KAAzB,CAAtB;;AACA,OAAK,MAAM,SAAX,IAAwB,MAAM,CAAC,IAAP,CAAY,QAAZ,CAAxB,EAA+C;AAC7C,UAAM,kBAAkB,GAAG,QAAQ,CAAC,SAAD,CAAnC;AACA,UAAM,gBAAgB,GAAG,IAAI,GAAJ,CAAQ,gBAAgB,CAAC,KAAzB,CAAzB;;AACA,SAAK,MAAM,IAAX,IAAmB,MAAM,CAAC,IAAP,CAAY,kBAAZ,CAAnB,EAAoD;AAClD,YAAM,cAAc,GAAG,+BAAiB,IAAjB,CAAvB;;AAEA,UAAI,CAAC,aAAa,CAAC,GAAd,CAAkB,cAAlB,CAAL,EAAwC;AACtC;AACD;;AAED,YAAM,KAAK,GAAG,kBAAkB,CAAC,IAAD,CAAhC;;AACA,UAAI,KAAK,IAAI,IAAb,EAAmB;AACjB,cAAM,IAAI,KAAJ,CAAU,GAAG,SAAS,sBAAsB,IAAI,EAAhD,CAAN;AACD;;AAED,MAAA,MAAM,CAAC,IAAP,CAAY,cAAc,SAAS,IAAI,cAAK,cAAL,CAAoB,KAAK,KAAK,CAAC,OAAN,CAAc,KAAd,EAAqB,UAArB,CAAgC,GAAhG;AACA,MAAA,gBAAgB,CAAC,MAAjB,CAAwB,cAAxB;AACD;;AAED,QAAI,gBAAgB,CAAC,WAArB,EAAkC;AAChC,YAAM,kBAAkB,GAAG,kBAAkB,CAAC,EAAnB,CAAsB,OAAtB,CAA8B,KAA9B,EAAqC,UAArC,CAA3B;;AACA,WAAK,MAAM,cAAX,IAA6B,gBAA7B,EAA+C;AAC7C,QAAA,MAAM,CAAC,IAAP,CAAY,cAAc,SAAS,IAAI,cAAK,cAAL,CAAoB,KAAK,kBAAkB,GAAlF;AACD;AACF;AACF;;AACD,SAAO,MAAP;AACD,C", "sourcesContent": ["import { asArray } from \"builder-util\"\nimport { bundledLanguages, langIdToName, lcid, toLangWithRegion } from \"../../util/langs\"\nimport _debug from \"debug\"\nimport { outputFile, readFile } from \"fs-extra\"\nimport { safeLoad } from \"js-yaml\"\nimport * as path from \"path\"\nimport { PlatformPackager } from \"../../platformPackager\"\nimport { NsisOptions } from \"./nsisOptions\"\nimport { NsisScriptGenerator } from \"./nsisScriptGenerator\"\nimport { nsisTemplatesDir } from \"./nsisUtil\"\n\nconst debug = _debug(\"electron-builder:nsis\")\n\nexport class LangConfigurator {\n  readonly isMultiLang: boolean\n  readonly langs: Array<string>\n\n  constructor(options: NsisOptions) {\n    const rawList = options.installerLanguages\n\n    if (options.unicode === false || rawList === null || (Array.isArray(rawList) && rawList.length === 0)) {\n      this.isMultiLang = false\n    }\n    else {\n      this.isMultiLang = options.multiLanguageInstaller !== false\n    }\n\n    if (this.isMultiLang) {\n      this.langs = rawList == null ? bundledLanguages.slice() : asArray(rawList)\n        .map(it => toLangWithRegion(it.replace(\"-\", \"_\")))\n    }\n    else {\n      this.langs = [\"en_US\"]\n    }\n  }\n}\n\nexport function createAddLangsMacro(scriptGenerator: NsisScriptGenerator, langConfigurator: LangConfigurator) {\n  const result: Array<string> = []\n  for (const langWithRegion of langConfigurator.langs) {\n    let name: string\n    if (langWithRegion === \"zh_CN\") {\n      name = \"SimpChinese\"\n    }\n    else if (langWithRegion === \"zh_TW\") {\n      name = \"TradChinese\"\n    }\n    else if (langWithRegion === \"nb_NO\") {\n      name = \"Norwegian\"\n    }\n    else if (langWithRegion === \"pt_BR\") {\n      name = \"PortugueseBR\"\n    }\n    else {\n      const lang = langWithRegion.substring(0, langWithRegion.indexOf(\"_\"))\n      name = (langIdToName as any)[lang]\n      if (name == null) {\n        throw new Error(`Language name is unknown for ${lang}`)\n      }\n\n      if (name === \"Spanish\") {\n        name = \"SpanishInternational\"\n      }\n    }\n    result.push(`!insertmacro MUI_LANGUAGE \"${name}\"`)\n  }\n\n  scriptGenerator.macro(\"addLangs\", result)\n}\n\nasync function writeCustomLangFile(data: string, packager: PlatformPackager<any>) {\n  const file = await packager.getTempFile(\"messages.nsh\")\n  await outputFile(file, data)\n  return file\n}\n\nexport async function addCustomMessageFileInclude(input: string, packager: PlatformPackager<any>, scriptGenerator: NsisScriptGenerator, langConfigurator: LangConfigurator) {\n  const data = safeLoad(await readFile(path.join(nsisTemplatesDir, input), \"utf-8\"))\n  const instructions = computeCustomMessageTranslations(data, langConfigurator).join(\"\\n\")\n  debug(instructions)\n  scriptGenerator.include(await writeCustomLangFile(instructions, packager))\n}\n\nfunction computeCustomMessageTranslations(messages: any, langConfigurator: LangConfigurator): Array<string> {\n  const result: Array<string> = []\n  const includedLangs = new Set(langConfigurator.langs)\n  for (const messageId of Object.keys(messages)) {\n    const langToTranslations = messages[messageId]\n    const unspecifiedLangs = new Set(langConfigurator.langs)\n    for (const lang of Object.keys(langToTranslations)) {\n      const langWithRegion = toLangWithRegion(lang)\n\n      if (!includedLangs.has(langWithRegion)) {\n        continue\n      }\n\n      const value = langToTranslations[lang]\n      if (value == null) {\n        throw new Error(`${messageId} not specified for ${lang}`)\n      }\n\n      result.push(`LangString ${messageId} ${lcid[langWithRegion]} \"${value.replace(/\\n/g, \"$\\\\r$\\\\n\")}\"`)\n      unspecifiedLangs.delete(langWithRegion)\n    }\n\n    if (langConfigurator.isMultiLang) {\n      const defaultTranslation = langToTranslations.en.replace(/\\n/g, \"$\\\\r$\\\\n\")\n      for (const langWithRegion of unspecifiedLangs) {\n        result.push(`LangString ${messageId} ${lcid[langWithRegion]} \"${defaultTranslation}\"`)\n      }\n    }\n  }\n  return result\n}"], "sourceRoot": ""}