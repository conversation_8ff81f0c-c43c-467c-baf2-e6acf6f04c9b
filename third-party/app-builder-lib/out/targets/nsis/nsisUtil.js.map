{"version": 3, "sources": ["../../../src/targets/nsis/nsisUtil.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;;;AAEO,MAAM,gBAAgB,GAAG,oCAAgB,MAAhB,CAAzB;;;AAEA,MAAM,SAAS,GAAG,MAAK;AAC5B,QAAM,MAAM,GAAG,OAAO,CAAC,GAAR,CAAY,yBAA3B;;AACA,MAAI,MAAM,IAAI,IAAV,IAAkB,MAAM,CAAC,MAAP,GAAgB,CAAtC,EAAyC;AACvC,WAAO,OAAO,CAAC,OAAR,CAAgB,MAAM,CAAC,IAAP,EAAhB,CAAP;AACD,GAJ2B,CAK5B;;;AACA,SAAO,kCAAc,MAAd,EAAsB,SAAtB,EAAiC,0FAAjC,CAAP;AACD,CAPM;;;;AASD,MAAO,gBAAP,CAAuB;AAO3B,EAAA,WAAA,CAA6B,aAA7B,EAA6D;AAAhC,SAAA,aAAA,GAAA,aAAA;AANZ,SAAA,cAAA,GAAiB,IAAI,GAAJ,EAAjB;AACA,SAAA,cAAA,GAAiB,IAAI,GAAJ,EAAjB;AAEjB;;AACA,SAAA,QAAA,GAAW,CAAX;AAGC;;AAED,QAAM,QAAN,CAAe,IAAf,EAA2B,MAA3B,EAA6C;AAC3C,QAAI,WAAW,GAAG,KAAK,cAAL,CAAoB,GAApB,CAAwB,IAAxB,CAAlB;;AACA,QAAI,WAAW,IAAI,IAAnB,EAAyB;AACvB,YAAM,SAAS,GAAG,MAAM,CAAC,KAAP,CAAa,GAAb,CAAiB,IAAjB,CAAlB;AACA,MAAA,WAAW,GAAG,KAAK,aAAL,CAAmB,IAAnB,CAAwB,SAAxB,EAAmC,MAAnC,EACX,IADW,CACN,MAAM,MAAM,CAAC,eAAP,CAAuB,SAAvB,EAAkC,IAAlC,CADA,CAAd;AAEA,WAAK,cAAL,CAAoB,GAApB,CAAwB,IAAxB,EAA8B,WAA9B;AACD;;AAED,UAAM,IAAI,GAAG,MAAM,WAAnB;;AACA,QAAI,MAAM,CAAC,cAAX,EAA2B;AACzB,WAAK,cAAL,CAAoB,GAApB,CAAwB,IAAxB,EAA8B,KAA9B;AACD,KAFD,MAGK,IAAI,CAAC,KAAK,cAAL,CAAoB,GAApB,CAAwB,IAAxB,CAAL,EAAoC;AACvC,WAAK,cAAL,CAAoB,GAApB,CAAwB,IAAxB,EAA8B,IAA9B;AACD;;AACD,WAAO,IAAP;AACD;;AAED,QAAM,WAAN,GAAiB;AACf,QAAI,EAAE,KAAK,QAAP,GAAkB,CAAtB,EAAyB;AACvB;AACD;;AAED,UAAM,aAAa,GAAkB,EAArC;;AACA,SAAK,MAAM,CAAC,IAAD,EAAO,QAAP,CAAX,IAA+B,KAAK,cAAL,CAAoB,OAApB,EAA/B,EAA8D;AAC5D,UAAI,QAAJ,EAAc;AACZ,QAAA,aAAa,CAAC,IAAd,CAAmB,IAAI,CAAC,IAAxB;AACD;AACF;;AAED,UAAM,uBAAgB,GAAhB,CAAoB,aAApB,EAAmC,EAAE,IAAI,uBAAO,EAAP,CAAzC,CAAN;AACD;;AA1C0B;;;;AA6CvB,MAAO,iBAAP,CAAwB;AAA9B,EAAA,WAAA,GAAA;AACmB,SAAA,MAAA,GAAS,IAAI,GAAJ,EAAT;AAkClB;;AAhCC,EAAA,IAAI,CAAC,SAAD,EAAoB,MAApB,EAAsC;AACxC,QAAI,CAAC,MAAM,CAAC,QAAP,CAAgB,IAAhB,CAAqB,SAArB,CAA+B,mBAApC,EAAyD;AACvD,aAAO,OAAO,CAAC,OAAR,EAAP;AACD;;AAED,QAAI,mBAAmB,GAAG,MAAM,CAAC,OAAP,CAAe,iBAAzC;;AACA,QAAI,mBAAmB,KAAK,KAAxB,IAAiC,MAAM,CAAC,OAAP,CAAe,UAAf,KAA8B,IAAnE,EAAyE;AACvE,MAAA,mBAAmB,GAAG,IAAtB;;AACA,yBAAI,IAAJ,CAAS,+EAAT;AACD;;AAED,QAAI,mBAAmB,KAAK,KAA5B,EAAmC;AACjC,aAAO,OAAO,CAAC,OAAR,EAAP;AACD;;AAED,QAAI,OAAO,GAAG,KAAK,MAAL,CAAY,GAAZ,CAAgB,SAAhB,CAAd;;AACA,QAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,aAAO,OAAP;AACD;;AAED,IAAA,OAAO,GAAG,SAAS,GAChB,IADO,CACF,EAAE,IAAG;AACT,YAAM,OAAO,GAAG,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,WAArB,EAAkC,aAAlC,CAAhB;AACA,YAAM,OAAO,GAAG,oBAAS,IAAI,CAAC,IAAL,CAAU,EAAV,EAAc,aAAd,CAAT,EAAuC,OAAvC,EAAgD,KAAhD,CAAhB;;AACA,UAAI,MAAM,CAAC,QAAP,CAAgB,4BAAhB,CAA6C,qBAA7C,KAAuE,KAA3E,EAAkF;AAChF,eAAO,OAAO,CAAC,IAAR,CAAa,MAAM,MAAM,CAAC,QAAP,CAAgB,IAAhB,CAAqB,OAArB,CAAnB,CAAP;AACD;;AACD,aAAO,OAAP;AACD,KARO,CAAV;AASA,SAAK,MAAL,CAAY,GAAZ,CAAgB,SAAhB,EAA2B,OAA3B;AACA,WAAO,OAAP;AACD;;AAlC2B;;;;AAqC9B,MAAM,YAAN,CAAkB;AAIhB,EAAA,WAAA,CAAY,MAAZ,EAA0B;AACxB,SAAK,OAAL,GAAe,MAAf;AACA,SAAK,SAAL,GAAiB,CAAjB;AACD;;AAED,MAAI,MAAJ,GAAU;AACR,WAAO,KAAK,OAAL,CAAa,MAApB;AACD;;AAED,MAAI,QAAJ,GAAY;AACV,WAAO,KAAK,SAAZ;AACD;;AAED,EAAA,KAAK,CAAC,SAAD,EAAyB;AAC5B,QAAI,SAAS,CAAC,KAAV,CAAgB,CAAC,CAAD,EAAI,CAAJ,KAAU,KAAK,OAAL,CAAa,KAAK,SAAL,GAAiB,CAA9B,MAAqC,CAA/D,CAAJ,EAAuE;AACrE,WAAK,SAAL,IAAkB,SAAS,CAAC,MAA5B;AACA,aAAO,IAAP;AACD;;AACD,WAAO,KAAP;AACD;;AAED,EAAA,IAAI,CAAC,MAAD,EAAe;AACjB,SAAK,SAAL,IAAkB,MAAlB;AACD;;AAED,EAAA,KAAK,CAAC,IAAD,EAAa;AAChB,UAAM,KAAK,GAAG,KAAK,OAAL,CAAa,QAAb,CAAsB,KAAK,SAA3B,EAAsC,KAAK,SAAL,GAAiB,IAAvD,CAAd;;AACA,SAAK,SAAL,IAAkB,IAAlB;AACA,WAAO,KAAP;AACD;;AAED,EAAA,MAAM,GAAA;AACJ,UAAM,KAAK,GAAG,KAAK,OAAL,CAAa,KAAK,SAAlB,IAAgC,KAAK,OAAL,CAAa,KAAK,SAAL,GAAiB,CAA9B,KAAoC,CAAlF;AACA,SAAK,SAAL,IAAkB,CAAlB;AACA,WAAO,KAAP;AACD;;AAED,EAAA,MAAM,GAAA;AACJ,WAAO,KAAK,MAAL,KAAiB,KAAK,MAAL,MAAiB,EAAzC;AACD;;AAED,EAAA,MAAM,CAAC,MAAD,EAAe;AACnB,QAAI,KAAK,GAAG,EAAZ;;AACA,SAAK,IAAI,CAAC,GAAG,CAAb,EAAgB,CAAC,GAAG,MAApB,EAA4B,CAAC,EAA7B,EAAiC;AAC/B,YAAM,CAAC,GAAG,KAAK,OAAL,CAAa,KAAK,SAAL,GAAiB,CAA9B,CAAV;;AACA,UAAI,CAAC,KAAK,IAAV,EAAgB;AACd;AACD;;AACD,MAAA,KAAK,IAAI,MAAM,CAAC,YAAP,CAAoB,CAApB,CAAT;AACD;;AACD,SAAK,SAAL,IAAkB,MAAlB;AACA,WAAO,KAAP;AACD;;AAxDe;;AA2DZ,MAAO,iBAAP,CAAwB;AAC5B;AACA,SAAO,IAAP,CAAY,aAAZ,EAAmC,eAAnC,EAA0D;AACxD,UAAM,MAAM,GAAG,aAAG,YAAH,CAAgB,aAAhB,CAAf;;AACA,UAAM,MAAM,GAAG,IAAI,YAAJ,CAAiB,MAAjB,CAAf,CAFwD,CAGxD;;AACA,QAAI,CAAC,MAAM,CAAC,KAAP,CAAa,CAAE,IAAF,EAAQ,IAAR,CAAb,CAAL,EAAmC;AACjC,YAAM,IAAI,KAAJ,CAAU,yBAAV,CAAN;AACD;;AACD,IAAA,MAAM,CAAC,IAAP,CAAY,EAAZ,EAPwD,CAQxD;;AACA,IAAA,MAAM,CAAC,IAAP,CAAY,MAAM,CAAC,MAAP,KAAkB,MAAM,CAAC,QAArC,EATwD,CAUxD;;AACA,QAAI,CAAC,MAAM,CAAC,KAAP,CAAa,CAAE,IAAF,EAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB,CAAb,CAAL,EAA+C;AAC7C,YAAM,IAAI,KAAJ,CAAU,yBAAV,CAAN;AACD;;AACD,IAAA,MAAM,CAAC,IAAP,CAAY,CAAZ;AACA,UAAM,gBAAgB,GAAG,MAAM,CAAC,MAAP,EAAzB;AACA,IAAA,MAAM,CAAC,IAAP,CAAY,EAAZ;AACA,UAAM,oBAAoB,GAAG,MAAM,CAAC,MAAP,EAA7B;AACA,IAAA,MAAM,CAAC,IAAP,CAAY,CAAZ;AACA,IAAA,MAAM,CAAC,IAAP,CAAY,oBAAZ,EAnBwD,CAoBxD;;AACA,QAAI,UAAU,GAAG,CAAjB;;AACA,SAAK,IAAI,CAAC,GAAG,CAAb,EAAgB,CAAC,GAAG,gBAApB,EAAsC,CAAC,EAAvC,EAA2C;AACzC,YAAM,IAAI,GAAG,MAAM,CAAC,MAAP,CAAc,CAAd,CAAb;AACA,MAAA,MAAM,CAAC,IAAP,CAAY,CAAZ;AACA,YAAM,OAAO,GAAG,MAAM,CAAC,MAAP,EAAhB;AACA,YAAM,UAAU,GAAG,MAAM,CAAC,MAAP,EAAnB;AACA,MAAA,MAAM,CAAC,IAAP,CAAY,EAAZ;;AACA,cAAQ,IAAR;AACE,aAAK,OAAL;AACA,aAAK,QAAL;AACA,aAAK,OAAL;AACA,aAAK,OAAL;AAAc;AACZ,YAAA,UAAU,GAAG,IAAI,CAAC,GAAL,CAAS,UAAU,GAAG,OAAtB,EAA+B,UAA/B,CAAb;AACA;AACD;;AACD;AAAS;AACP,gBAAI,UAAU,KAAK,CAAf,IAAoB,OAAO,KAAK,CAApC,EAAuC;AACrC,oBAAM,IAAI,KAAJ,CAAU,0BAA0B,IAA1B,GAAiC,IAA3C,CAAN;AACD;;AACD;AACD;AAbH;AAeD;;AACD,UAAM,UAAU,GAAG,MAAM,CAAC,QAAP,CAAgB,CAAhB,EAAmB,UAAnB,CAAnB;AACA,UAAM,QAAQ,GAAG,MAAM,CAAC,MAAP,GAAgB,UAAjC;AACA,UAAM,UAAU,GAAG,IAAI,YAAJ,CAAiB,MAAM,CAAC,QAAP,CAAgB,UAAhB,EAA4B,UAAU,GAAG,QAAzC,CAAjB,CAAnB;AACA,UAAM,aAAa,GAAG,CAAE,IAAF,EAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB,EAA0B,IAA1B,EAAgC,IAAhC,EAAsC,IAAtC,EAA4C,IAA5C,EAAkD,IAAlD,EAAwD,IAAxD,EAA8D,IAA9D,EAAoE,IAApE,EAA0E,IAA1E,EAAgF,IAAhF,EAAsF,IAAtF,EAA4F,IAA5F,CAAtB;AACA,IAAA,UAAU,CAAC,MAAX,GAhDwD,CAgDpC;;AACpB,QAAI,CAAC,UAAU,CAAC,KAAX,CAAiB,aAAjB,CAAL,EAAsC;AACpC,YAAM,IAAI,KAAJ,CAAU,oBAAV,CAAN;AACD;;AACD,IAAA,UAAU,CAAC,MAAX,GApDwD,CAoDpC;;AACpB,QAAI,QAAQ,KAAK,UAAU,CAAC,MAAX,EAAjB,EAAsC;AACpC,YAAM,IAAI,KAAJ,CAAU,gBAAV,CAAN;AACD;;AAED,QAAI,WAAW,GAAG,IAAlB;;AACA,WAAO,IAAP,EAAa;AACX,UAAI,IAAI,GAAG,UAAU,CAAC,MAAX,EAAX;AACA,YAAM,UAAU,GAAG,CAAC,IAAI,GAAG,UAAR,MAAwB,CAA3C;AACA,MAAA,IAAI,GAAG,IAAI,GAAG,UAAd;;AACA,UAAI,IAAI,KAAK,CAAT,IAAe,UAAU,CAAC,QAAX,GAAsB,IAAvB,GAA+B,UAAU,CAAC,MAAxD,IAAkE,UAAU,CAAC,QAAX,IAAuB,UAAU,CAAC,MAAxG,EAAgH;AAC9G;AACD;;AACD,UAAI,MAAM,GAAG,UAAU,CAAC,KAAX,CAAiB,IAAjB,CAAb;;AACA,UAAI,UAAJ,EAAgB;AACd,QAAA,MAAM,GAAG,gBAAK,cAAL,CAAoB,MAApB,CAAT;AACD;;AACD,YAAM,WAAW,GAAG,IAAI,YAAJ,CAAiB,MAAjB,CAApB;AACA,MAAA,WAAW,CAAC,MAAZ,GAZW,CAYU;;AACrB,UAAI,WAAW,CAAC,KAAZ,CAAkB,aAAlB,CAAJ,EAAsC;AACpC,YAAI,WAAJ,EAAiB;AACf,gBAAM,IAAI,KAAJ,CAAU,wBAAV,CAAN;AACD;;AACD,QAAA,WAAW,GAAG,MAAd;AACD;AACF;;AACD,QAAI,CAAC,WAAL,EAAkB;AAChB,YAAM,IAAI,KAAJ,CAAU,wBAAV,CAAN;AACD;;AACD,iBAAG,aAAH,CAAiB,eAAjB,EAAkC,UAAlC;;AACA,iBAAG,cAAH,CAAkB,eAAlB,EAAmC,WAAnC;AACD;;AArF2B,C", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { Arch, log } from \"builder-util\"\nimport { PackageFileInfo } from \"builder-util-runtime\"\nimport { getBinFromUrl } from \"../../binDownload\"\nimport { copyFile } from \"builder-util/out/fs\"\nimport { unlink } from \"fs-extra\"\nimport * as path from \"path\"\nimport { getTemplatePath } from \"../../util/pathManager\"\nimport { NsisTarget } from \"./NsisTarget\"\nimport fs from \"fs\"\nimport zlib from \"zlib\"\n\nexport const nsisTemplatesDir = getTemplatePath(\"nsis\")\n\nexport const NSIS_PATH = () => {\n  const custom = process.env.ELECTRON_BUILDER_NSIS_DIR\n  if (custom != null && custom.length > 0) {\n    return Promise.resolve(custom.trim())\n  }\n  // noinspection SpellCheckingInspection\n  return getBinFromUrl(\"nsis\", \"3.0.4.1\", \"VKMiizYdmNdJOWpRGz4trl4lD++BvYP2irAXpMilheUP0pc93iKlWAoP843Vlraj8YG19CVn0j+dCo/hURz9+Q==\")\n}\n\nexport class AppPackageHelper {\n  private readonly archToFileInfo = new Map<Arch, Promise<PackageFileInfo>>()\n  private readonly infoToIsDelete = new Map<PackageFileInfo, boolean>()\n\n  /** @private */\n  refCount = 0\n\n  constructor(private readonly elevateHelper: CopyElevateHelper) {\n  }\n\n  async packArch(arch: Arch, target: NsisTarget): Promise<PackageFileInfo> {\n    let infoPromise = this.archToFileInfo.get(arch)\n    if (infoPromise == null) {\n      const appOutDir = target.archs.get(arch)!\n      infoPromise = this.elevateHelper.copy(appOutDir, target)\n        .then(() => target.buildAppPackage(appOutDir, arch))\n      this.archToFileInfo.set(arch, infoPromise)\n    }\n\n    const info = await infoPromise\n    if (target.isWebInstaller) {\n      this.infoToIsDelete.set(info, false)\n    }\n    else if (!this.infoToIsDelete.has(info)) {\n      this.infoToIsDelete.set(info, true)\n    }\n    return info\n  }\n\n  async finishBuild(): Promise<any> {\n    if (--this.refCount > 0) {\n      return\n    }\n\n    const filesToDelete: Array<string> = []\n    for (const [info, isDelete] of this.infoToIsDelete.entries()) {\n      if (isDelete) {\n        filesToDelete.push(info.path)\n      }\n    }\n\n    await BluebirdPromise.map(filesToDelete, it => unlink(it))\n  }\n}\n\nexport class CopyElevateHelper {\n  private readonly copied = new Map<string, Promise<any>>()\n\n  copy(appOutDir: string, target: NsisTarget): Promise<any> {\n    if (!target.packager.info.framework.isCopyElevateHelper) {\n      return Promise.resolve()\n    }\n\n    let isPackElevateHelper = target.options.packElevateHelper\n    if (isPackElevateHelper === false && target.options.perMachine === true) {\n      isPackElevateHelper = true\n      log.warn(\"`packElevateHelper = false` is ignored, because `perMachine` is set to `true`\")\n    }\n\n    if (isPackElevateHelper === false) {\n      return Promise.resolve()\n    }\n\n    let promise = this.copied.get(appOutDir)\n    if (promise != null) {\n      return promise\n    }\n\n    promise = NSIS_PATH()\n      .then(it => {\n        const outFile = path.join(appOutDir, \"resources\", \"elevate.exe\")\n        const promise = copyFile(path.join(it, \"elevate.exe\"), outFile, false)\n        if (target.packager.platformSpecificBuildOptions.signAndEditExecutable !== false) {\n          return promise.then(() => target.packager.sign(outFile))\n        }\n        return promise\n      })\n    this.copied.set(appOutDir, promise)\n    return promise\n  }\n}\n\nclass BinaryReader {\n  private readonly _buffer: Buffer\n  private _position: number\n\n  constructor(buffer: Buffer) {\n    this._buffer = buffer\n    this._position = 0\n  }\n\n  get length(): number {\n    return this._buffer.length\n  }\n\n  get position(): number {\n    return this._position\n  }\n\n  match(signature: Array<number>): boolean {\n    if (signature.every((v, i) => this._buffer[this._position + i] === v)) {\n      this._position += signature.length\n      return true\n    }\n    return false\n  }\n\n  skip(offset: number) {\n    this._position += offset\n  }\n\n  bytes(size: number): Buffer {\n    const value = this._buffer.subarray(this._position, this._position + size)\n    this._position += size\n    return value\n  }\n\n  uint16(): number {\n    const value = this._buffer[this._position] | (this._buffer[this._position + 1] << 8)\n    this._position += 2\n    return value\n  }\n\n  uint32(): number {\n    return this.uint16() | (this.uint16() << 16)\n  }\n\n  string(length: number): string {\n    let value = \"\"\n    for (let i = 0; i < length; i++) {\n      const c = this._buffer[this._position + i]\n      if (c === 0x00) {\n        break\n      }\n      value += String.fromCharCode(c)\n    }\n    this._position += length\n    return value\n  }\n}\n\nexport class UninstallerReader {\n  // noinspection SpellCheckingInspection\n  static exec(installerPath: string, uninstallerPath: string) {\n    const buffer = fs.readFileSync(installerPath)\n    const reader = new BinaryReader(buffer)\n    // IMAGE_DOS_HEADER\n    if (!reader.match([ 0x4D, 0x5A ])) {\n      throw new Error(\"Invalid 'MZ' signature.\")\n    }\n    reader.skip(58)\n    // e_lfanew\n    reader.skip(reader.uint32() - reader.position)\n    // IMAGE_FILE_HEADER\n    if (!reader.match([ 0x50, 0x45, 0x00, 0x00 ])) {\n      throw new Error(\"Invalid 'PE' signature.\")\n    }\n    reader.skip(2)\n    const numberOfSections = reader.uint16()\n    reader.skip(12)\n    const sizeOfOptionalHeader = reader.uint16()\n    reader.skip(2)\n    reader.skip(sizeOfOptionalHeader)\n    // IMAGE_SECTION_HEADER\n    let nsisOffset = 0\n    for (let i = 0; i < numberOfSections; i++) {\n      const name = reader.string(8)\n      reader.skip(8)\n      const rawSize = reader.uint32()\n      const rawPointer = reader.uint32()\n      reader.skip(16)\n      switch (name) {\n        case \".text\":\n        case \".rdata\":\n        case \".data\":\n        case \".rsrc\": {\n          nsisOffset = Math.max(rawPointer + rawSize, nsisOffset)\n          break\n        }\n        default: {\n          if (rawPointer !== 0 && rawSize !== 0) {\n            throw new Error(\"Unsupported section '\" + name + \"'.\")\n          }\n          break\n        }\n      }\n    }\n    const executable = buffer.subarray(0, nsisOffset)\n    const nsisSize = buffer.length - nsisOffset\n    const nsisReader = new BinaryReader(buffer.subarray(nsisOffset, nsisOffset + nsisSize))\n    const nsisSignature = [ 0xEF, 0xBE, 0xAD, 0xDE, 0x4E, 0x75, 0x6C, 0x6C, 0x73, 0x6F, 0x66, 0x74, 0x49, 0x6E, 0x73, 0x74 ]\n    nsisReader.uint32() // ?\n    if (!nsisReader.match(nsisSignature)) {\n      throw new Error(\"Invalid signature.\")\n    }\n    nsisReader.uint32() // ?\n    if (nsisSize !== nsisReader.uint32()) {\n      throw new Error(\"Size mismatch.\")\n    }\n\n    let innerBuffer = null\n    while (true) {\n      let size = nsisReader.uint32()\n      const compressed = (size & 0x80000000) !== 0\n      size = size & 0x7FFFFFFF\n      if (size === 0 || (nsisReader.position + size) > nsisReader.length || nsisReader.position >= nsisReader.length) {\n        break\n      }\n      let buffer = nsisReader.bytes(size)\n      if (compressed) {\n        buffer = zlib.inflateRawSync(buffer)\n      }\n      const innerReader = new BinaryReader(buffer)\n      innerReader.uint32() // ?\n      if (innerReader.match(nsisSignature)) {\n        if (innerBuffer) {\n          throw new Error(\"Multiple inner blocks.\")\n        }\n        innerBuffer = buffer\n      }\n    }\n    if (!innerBuffer) {\n      throw new Error(\"Inner block not found.\")\n    }\n    fs.writeFileSync(uninstallerPath, executable)\n    fs.appendFileSync(uninstallerPath, innerBuffer)\n  }\n}\n"], "sourceRoot": ""}