"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.WebInstallerTarget = void 0;

function _PublishManager() {
  const data = require("../../publish/PublishManager");

  _PublishManager = function () {
    return data;
  };

  return data;
}

function _NsisTarget() {
  const data = require("./NsisTarget");

  _NsisTarget = function () {
    return data;
  };

  return data;
}

/** @private */
class WebInstallerTarget extends _NsisTarget().NsisTarget {
  constructor(packager, outDir, targetName, packageHelper) {
    super(packager, outDir, targetName, packageHelper);
  }

  get isWebInstaller() {
    return true;
  }

  async configureDefines(oneClick, defines) {
    //noinspection ES6MissingAwait
    await _NsisTarget().NsisTarget.prototype.configureDefines.call(this, oneClick, defines);
    const packager = this.packager;
    const options = this.options;
    let appPackageUrl = options.appPackageUrl;

    if (appPackageUrl == null) {
      const publishConfigs = await (0, _PublishManager().getPublishConfigsForUpdateInfo)(packager, await (0, _PublishManager().getPublishConfigs)(packager, packager.info.config, null, false), null);

      if (publishConfigs == null || publishConfigs.length === 0) {
        throw new Error("Cannot compute app package download URL");
      }

      appPackageUrl = (0, _PublishManager().computeDownloadUrl)(publishConfigs[0], null, packager);
      defines.APP_PACKAGE_URL_IS_INCOMLETE = null;
    }

    defines.APP_PACKAGE_URL = appPackageUrl;
  }

  get installerFilenamePattern() {
    // tslint:disable:no-invalid-template-strings
    return "${productName} Web Setup ${version}.${ext}";
  }

  generateGitHubInstallerName() {
    const appInfo = this.packager.appInfo;
    const classifier = appInfo.name.toLowerCase() === appInfo.name ? "web-setup" : "WebSetup";
    return `${appInfo.name}-${classifier}-${appInfo.version}.exe`;
  }

} exports.WebInstallerTarget = WebInstallerTarget;
// __ts-babel@6.0.4
//# sourceMappingURL=WebInstallerTarget.js.map