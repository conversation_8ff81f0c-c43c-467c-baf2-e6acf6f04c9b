{"version": 3, "sources": ["../../../src/targets/nsis/NsisTarget.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;;;AAEA,MAAM,KAAK,GAAG,qBAAO,uBAAP,CAAd,C,CAEA;;AACA,MAAM,wBAAwB,GAAG,2BAAK,KAAL,CAAW,sCAAX,CAAjC,C,CAEA;;;AACA,MAAM,uBAAuB,GAAG,MAAM,kCAAc,gBAAd,EAAgC,OAAhC,EAAyC,0FAAzC,CAAtC;;AAEA,MAAM,4BAA4B,GAAG,KAArC;;AAEM,MAAO,UAAP,SAA0B,cAA1B,CAAgC;AAMpC,EAAA,WAAA,CAAqB,QAArB,EAAqD,MAArD,EAAqE,UAArE,EAA4G,aAA5G,EAA2I;AACzI,UAAM,UAAN;AADmB,SAAA,QAAA,GAAA,QAAA;AAAgC,SAAA,MAAA,GAAA,MAAA;AAAuD,SAAA,aAAA,GAAA,aAAA;AAH5G;;AACS,SAAA,KAAA,GAA2B,IAAI,GAAJ,EAA3B;AAKP,SAAK,aAAL,CAAmB,QAAnB;AAEA,SAAK,OAAL,GAAe,UAAU,KAAK,UAAf,GAA4B,MAAM,CAAC,MAAP,CAAc,IAAd,CAA5B,GAAkD;AAC/D,MAAA,2BAA2B,EAAE,CAAC,MAAD,EAAS,MAAT,EAAiB,MAAjB,EAAyB,MAAzB,EAAiC,MAAjC,EAAyC,KAAzC,EAAgD,MAAhD,EAAwD,OAAxD,EAAiE,OAAjE,CADkC;AAE/D,SAAG,KAAK,QAAL,CAAc,MAAd,CAAqB;AAFuC,KAAjE;;AAKA,QAAI,UAAU,KAAK,MAAnB,EAA2B;AACzB,MAAA,MAAM,CAAC,MAAP,CAAc,KAAK,OAAnB,EAA6B,KAAK,QAAL,CAAc,MAAd,CAA6B,UAAU,KAAK,UAAf,GAA4B,SAA5B,GAAwC,UAArE,CAA7B;AACD;;AAED,UAAM,IAAI,GAAG,QAAQ,CAAC,IAAT,CAAc,QAAd,CAAuB,YAApC;;AACA,QAAI,IAAI,IAAI,IAAR,IAAgB,IAAI,CAAC,2BAAD,CAAJ,IAAqC,IAAzD,EAA+D;AAC7D,yBAAI,IAAJ,CAAS,iEAAT;AACD;AACF;;AAED,QAAM,KAAN,CAAY,SAAZ,EAA+B,IAA/B,EAAyC;AACvC,SAAK,KAAL,CAAW,GAAX,CAAe,IAAf,EAAqB,SAArB;AACD;;AAED,MAAI,wBAAJ,GAA4B;AAC1B,WAAO,CAAC,KAAK,UAAN,IAAoB,KAAK,OAAL,CAAa,mBAAb,KAAqC,KAAhE;AACD;;AAEO,EAAA,8BAA8B,GAAA;AACpC,UAAM,MAAM,GAAG,KAAK,cAAL,GAAsB,IAAtB,GAA6B,KAAK,OAAL,CAAa,2BAAzD;AACA,WAAO,MAAM,IAAI,IAAV,GAAiB,IAAjB,GAAwB,4BAAQ,MAAR,EAAgB,GAAhB,CAAoB,EAAE,IAAI,EAAE,CAAC,UAAH,CAAc,GAAd,IAAqB,EAArB,GAA0B,IAAI,EAAE,EAA1D,CAA/B;AACD;AAED;;;AACA,QAAM,eAAN,CAAsB,SAAtB,EAAyC,IAAzC,EAAmD;AACjD,UAAM,OAAO,GAAG,KAAK,OAArB;AACA,UAAM,QAAQ,GAAG,KAAK,QAAtB;AAEA,UAAM,wBAAwB,GAAG,KAAK,wBAAtC;AACA,UAAM,MAAM,GAAG,CAAC,wBAAD,IAA6B,OAAO,CAAC,MAArC,GAA8C,KAA9C,GAAsD,IAArE;AACA,UAAM,WAAW,GAAG,IAAI,CAAC,IAAL,CAAU,KAAK,MAAf,EAAuB,GAAG,QAAQ,CAAC,OAAT,CAAiB,aAAa,IAAI,QAAQ,CAAC,OAAT,CAAiB,OAAO,IAAI,oBAAK,IAAL,CAAU,SAAS,MAAM,EAAjH,CAApB;AACA,UAAM,2BAA2B,GAAG,KAAK,8BAAL,EAApC;AACA,UAAM,cAAc,GAAmB;AACrC,MAAA,UAAU,EAAE,IADyB;AAErC,MAAA,WAAW,EAAE,QAAQ,CAAC,WAFe;AAGrC,MAAA,QAAQ,EAAE,2BAA2B,IAAI,IAA/B,GAAsC,IAAtC,GAA6C,2BAA2B,CAAC,GAA5B,CAAgC,EAAE,IAAI,IAAI,EAAE,EAA5C;AAHlB,KAAvC;AAMA,UAAM,KAAK,GAAG,mBAAK,iBAAiB,oBAAK,IAAL,CAAU,EAAhC,CAAd;AACA,UAAM,wBAAQ,MAAR,EAAgB,WAAhB,EAA6B,SAA7B,EAAwC,wBAAwB,GAAG,+EAAyC,cAAzC,CAAH,GAA8D,cAA9H,CAAN;AACA,IAAA,KAAK,CAAC,GAAN;;AAEA,QAAI,wBAAwB,IAAI,KAAK,cAArC,EAAqD;AACnD,YAAM,IAAI,GAAG,MAAM,qDAAe,WAAf,CAAnB;AACA,aAAO,EACL,GAAG,IADE;AAEL,QAAA,IAAI,EAAE;AAFD,OAAP;AAID,KAND,MAOK;AACH,aAAO,MAAM,qBAAqB,CAAC,WAAD,CAAlC;AACD;AACF;;AAED,QAAM,WAAN,GAAiB;AACf,QAAI;AACF,YAAM,KAAK,cAAL,EAAN;AACD,KAFD,SAGQ;AACN,YAAM,KAAK,aAAL,CAAmB,WAAnB,EAAN;AACD;AACF;;AAED,MAAc,wBAAd,GAAsC;AACpC;AACA,WAAO,qBAAqB,KAAK,UAAL,GAAkB,EAAlB,GAAuB,QAA5C,IAAwD,mBAA/D;AACD;;AAED,MAAY,UAAZ,GAAsB;AACpB,WAAO,KAAK,IAAL,KAAc,UAArB;AACD;;AAEO,QAAM,cAAN,GAAoB;AAC1B,UAAM,QAAQ,GAAG,KAAK,QAAtB;AACA,UAAM,OAAO,GAAG,QAAQ,CAAC,OAAzB;AACA,UAAM,OAAO,GAAG,KAAK,OAArB;AACA,UAAM,iBAAiB,GAAG,QAAQ,CAAC,yBAAT,CAAmC,OAAnC,EAA4C,KAA5C,EAAmD,IAAnD,EAAyD,KAAK,wBAA9D,CAA1B;AACA,UAAM,QAAQ,GAAG,OAAO,CAAC,QAAR,KAAqB,KAAtC;AACA,UAAM,aAAa,GAAG,IAAI,CAAC,IAAL,CAAU,KAAK,MAAf,EAAuB,iBAAvB,CAAtB;AAEA,UAAM,SAAS,GAAQ;AACrB,MAAA,MAAM,EAAE,KAAK,IADQ;AAErB,MAAA,IAAI,EAAE,mBAAI,QAAJ,CAAa,aAAb,CAFe;AAGrB,MAAA,KAAK,EAAE,KAAK,CAAC,IAAN,CAAW,KAAK,KAAL,CAAW,IAAX,EAAX,EAA8B,GAA9B,CAAkC,EAAE,IAAI,oBAAK,EAAL,CAAxC,EAAkD,IAAlD,CAAuD,IAAvD;AAHc,KAAvB;AAKA,UAAM,YAAY,GAAG,OAAO,CAAC,UAAR,KAAuB,IAA5C;;AACA,QAAI,CAAC,KAAK,UAAV,EAAsB;AACpB,MAAA,SAAS,CAAC,QAAV,GAAqB,QAArB;AACA,MAAA,SAAS,CAAC,UAAV,GAAuB,YAAvB;AACD;;AAED,UAAM,QAAQ,CAAC,IAAT,CAAc,wBAAd,CAAuC;AAC3C,MAAA,qBAAqB,EAAE,KAAK,IADe;AAE3C,MAAA,IAAI,EAAE,aAFqC;AAG3C,MAAA,IAAI,EAAE;AAHqC,KAAvC,EAIH,SAJG,CAAN;;AAMA,UAAM,IAAI,GAAG,OAAO,CAAC,IAAR,IAAgB,2BAAK,EAAL,CAAQ,OAAO,CAAC,EAAhB,EAAoB,wBAApB,CAA7B;;AACA,UAAM,eAAe,GAAG,IAAI,CAAC,OAAL,CAAa,KAAb,EAAoB,KAApB,CAAxB;AACA,UAAM,OAAO,GAAQ;AACnB,MAAA,MAAM,EAAE,OAAO,CAAC,EADG;AAEnB,MAAA,QAAQ,EAAE,IAFS;AAGnB;AACA,MAAA,iBAAiB,EAAE,eAJA;AAKnB,MAAA,YAAY,EAAE,OAAO,CAAC,WALH;AAMnB,MAAA,gBAAgB,EAAE,OAAO,CAAC,eANP;AAOnB,MAAA,YAAY,EAAE,iDAA8B,OAA9B,EAAuC,CAAC,QAAD,IAAa,YAApD,CAPK;AAQnB,MAAA,eAAe,EAAE,OAAO,CAAC,WARN;AASnB,MAAA,OAAO,EAAE,OAAO,CAAC,OATE;AAWnB,MAAA,WAAW,EAAE,QAAQ,CAAC,UAXH;AAYnB,MAAA,mBAAmB,EAAE,QAAQ,CAAC,IAAT,CAAc,iBAZhB;AAcnB,MAAA,gBAAgB,EAAE,OAAO,CAAC;AAdP,KAArB;;AAgBA,QAAI,eAAe,KAAK,IAAxB,EAA8B;AAC5B,MAAA,OAAO,CAAC,wBAAR,GAAmC,4DAA4D,IAAI,EAAnG;AACD;;AAED,UAAM,QAAQ,GAAQ;AACpB,MAAA,OAAO,EAAE,IAAI,aAAa,GADN;AAEpB,MAAA,gBAAgB,EAAE,OAAO,CAAC,4BAAR,EAFE;AAGpB,MAAA,eAAe,EAAE,KAAK,iBAAL,EAHG;AAIpB,MAAA,OAAO,EAAE,KAAK;AAJM,KAAtB;AAOA,UAAM,UAAU,GAAG,KAAK,UAAxB;AACA,UAAM,QAAQ,GAAG,CAAC,UAAU,GAAG,IAAH,GAAU,MAAM,QAAQ,CAAC,WAAT,CAAqB,OAAO,CAAC,aAA7B,EAA4C,mBAA5C,CAA3B,MAAgG,MAAM,QAAQ,CAAC,WAAT,EAAtG,CAAjB;;AACA,QAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,UAAI,UAAJ,EAAgB;AACd,QAAA,QAAQ,CAAC,IAAT,GAAgB,IAAI,QAAQ,GAA5B;AACD,OAFD,MAGK;AACH,QAAA,OAAO,CAAC,QAAR,GAAmB,QAAnB;AACA,QAAA,OAAO,CAAC,UAAR,GAAqB,QAArB;AACD;AACF;;AAED,UAAM,YAAY,GAAwC,EAA1D;AACA,QAAI,aAAa,GAAG,CAApB;;AACA,QAAI,KAAK,UAAL,IAAmB,OAAO,CAAC,MAA/B,EAAuC;AACrC,WAAK,MAAM,CAAC,IAAD,EAAO,GAAP,CAAX,IAA0B,KAAK,KAAL,CAAW,OAAX,EAA1B,EAAgD;AAC9C,QAAA,OAAO,CAAC,IAAI,KAAK,oBAAK,GAAd,GAAoB,YAApB,GAAoC,IAAI,KAAK,oBAAK,KAAd,GAAsB,eAAtB,GAAwC,YAA7E,CAAP,GAAqG,GAArG;AACD;AACF,KAJD,MAKK,IAAI,4BAA4B,IAAI,KAAK,KAAL,CAAW,IAAX,KAAoB,CAAxD,EAA2D;AAC9D,MAAA,OAAO,CAAC,aAAR,GAAwB,KAAK,KAAL,CAAW,GAAX,CAAe,KAAK,KAAL,CAAW,IAAX,GAAkB,IAAlB,GAAyB,KAAxC,CAAxB;AACD,KAFI,MAGA;AACH,YAAM,uBAAgB,GAAhB,CAAoB,KAAK,KAAL,CAAW,IAAX,EAApB,EAAuC,MAAM,IAAN,IAAa;AACxD,cAAM,QAAQ,GAAG,MAAM,KAAK,aAAL,CAAmB,QAAnB,CAA4B,IAA5B,EAAkC,IAAlC,CAAvB;AACA,cAAM,IAAI,GAAG,QAAQ,CAAC,IAAtB;AACA,cAAM,SAAS,GAAG,IAAI,KAAK,oBAAK,GAAd,GAAoB,QAApB,GAAgC,IAAI,KAAK,oBAAK,KAAd,GAAsB,WAAtB,GAAoC,QAAtF;AACA,QAAA,OAAO,CAAC,SAAD,CAAP,GAAqB,IAArB;AACA,QAAA,OAAO,CAAC,GAAG,SAAS,OAAb,CAAP,GAA+B,IAAI,CAAC,QAAL,CAAc,IAAd,CAA/B,CALwD,CAMxD;;AACA,QAAA,OAAO,CAAC,GAAG,SAAS,OAAb,CAAP,GAA+B,MAAM,CAAC,IAAP,CAAY,QAAQ,CAAC,MAArB,EAA6B,QAA7B,EAAuC,QAAvC,CAAgD,KAAhD,EAAuD,WAAvD,EAA/B;;AAEA,YAAI,KAAK,cAAT,EAAyB;AACvB,gBAAM,QAAQ,CAAC,uBAAT,CAAiC,IAAjC,EAAuC,IAAvC,EAA6C,IAA7C,CAAN;AACA,UAAA,YAAY,CAAC,oBAAK,IAAL,CAAD,CAAZ,GAA2B,QAA3B;AACD;;AAED,cAAM,WAAW,GAAG,CAAC,MAAM,yBAAK,iBAAL,EAAc,CAAC,GAAD,EAAM,IAAN,CAAd,CAAP,EAAmC,IAAnC,EAApB,CAdwD,CAexD;;AACA,cAAM,KAAK,GAAG,WAAW,CAAC,KAAZ,CAAkB,2BAAlB,CAAd;;AACA,YAAI,KAAK,IAAI,IAAb,EAAmB;AACjB,6BAAI,IAAJ,CAAS;AAAC,YAAA,MAAM,EAAE;AAAT,WAAT,EAAgC,oCAAhC;AACD,SAFD,MAGK;AACH,UAAA,aAAa,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAD,CAAN,EAAW,EAAX,CAAzB;AACD;AACF,OAvBK,CAAN;AAwBD;;AAED,SAAK,qCAAL,CAA2C,OAA3C;;AACA,QAAI,UAAJ,EAAgB;AACd,YAAM,eAAe,GAAG,OAAxB;AACA,MAAA,OAAO,CAAC,uBAAR,GAAkC,eAAe,CAAC,qBAAhB,IAAyC,MAA3E;AACA,MAAA,OAAO,CAAC,eAAR,GAA0B,eAAe,CAAC,aAAhB,KAAkC,MAAM,sCAAkB,CAAC,OAAD,CAAlB,CAAxC,CAA1B;;AAEA,UAAI,eAAe,CAAC,WAAhB,IAA+B,IAAnC,EAAyC;AACvC,QAAA,OAAO,CAAC,YAAR,GAAuB,IAAI,CAAC,OAAL,CAAa,QAAQ,CAAC,UAAtB,EAAkC,eAAe,CAAC,WAAlD,CAAvB;AACD;AACF,KARD,MASK;AACH,YAAM,KAAK,gBAAL,CAAsB,QAAtB,EAAgC,OAAhC,CAAN;AACD;;AAED,QAAI,aAAa,KAAK,CAAtB,EAAyB;AACvB;AACA,MAAA,OAAO,CAAC,cAAR,GAAyB,IAAI,CAAC,KAAL,CAAW,aAAa,GAAG,IAA3B,CAAzB;AACD;;AAED,QAAI,QAAQ,CAAC,WAAT,KAAyB,OAA7B,EAAsC;AACpC,MAAA,QAAQ,CAAC,WAAT,GAAuB,KAAvB;AACD,KAFD,MAGK;AACH;AACA;AACA;AACA,MAAA,QAAQ,CAAC,aAAT,GAAyB,MAAzB;;AACA,UAAI,CAAC,KAAK,cAAV,EAA0B;AACxB,QAAA,OAAO,CAAC,QAAR,GAAmB,MAAnB;AACD;AACF;;AAED,IAAA,KAAK,CAAC,OAAD,CAAL;AACA,IAAA,KAAK,CAAC,QAAD,CAAL;;AAEA,QAAI,QAAQ,CAAC,eAAT,CAAyB,uBAAzB,IAAoD,IAApD,KAA4D,MAAM,QAAQ,CAAC,eAAT,CAAyB,uBAAzB,CAAiD,CAAC,OAAD,EAAU,QAAV,CAAjD,CAAlE,CAAJ,EAA6I;AAC3I;AACD,KA5IyB,CA8I1B;;;AACA,UAAM,kBAAkB,GAAQ,EAAE,GAAG;AAAL,KAAhC;AACA,UAAM,mBAAmB,GAAQ,EAAE,GAAG;AAAL,KAAjC;;AACA,QAAI,OAAO,CAAC,YAAR,IAAwB,IAA5B,EAAkC;AAChC,MAAA,kBAAkB,CAAC,OAAnB,GAA6B,OAAO,CAAC,YAArC;AACA,MAAA,mBAAmB,CAAC,gBAApB,GAAuC,OAAO,CAAC,mBAA/C;AACA,MAAA,mBAAmB,CAAC,eAApB,GAAsC,KAAK,iBAAL,CAAuB,IAAvB,CAAtC;AACD;;AAED,UAAM,YAAY,GAAG,MAAM,KAAK,kCAAL,EAA3B;AACA,UAAM,MAAM,GAAG,UAAU,GAAG,MAAM,yBAAS,IAAI,CAAC,IAAL,CAAU,4BAAV,EAA4B,cAA5B,CAAT,EAAsD,MAAtD,CAAT,GAAyE,MAAM,KAAK,+BAAL,CAAqC,kBAArC,EAAyD,mBAAzD,EAA8E,aAA9E,EAA6F,YAA7F,CAAxG,CAxJ0B,CA0J1B;;AACA,IAAA,OAAO,CAAC,oBAAR,GAA+B,kBAAkB,CAAC,oBAAlD;AAEA,UAAM,KAAK,eAAL,CAAqB,OAArB,EAA8B,QAA9B,EAAwC,YAAY,IAAG,MAAM,KAAK,kBAAL,CAAwB,MAAxB,EAAgC,IAAhC,CAAT,CAApD,CAAN;AACA,UAAM,OAAO,CAAC,GAAR,CAAiB,CAAC,QAAQ,CAAC,IAAT,CAAc,aAAd,CAAD,EAA+B,OAAO,CAAC,oBAAR,IAAgC,IAAhC,GAAuC,OAAO,CAAC,OAAR,EAAvC,GAA2D,uBAAO,OAAO,CAAC,oBAAf,CAA1F,CAAjB,CAAN;AAEA,UAAM,gBAAgB,GAAG,yDAAgC,iBAAhC,EAAmD,MAAM,KAAK,2BAAL,EAAzD,CAAzB;AACA,QAAI,UAAJ;;AACA,QAAI,KAAK,cAAT,EAAyB;AACvB,MAAA,UAAU,GAAG,0EAAoC,aAApC,EAAmD,YAAnD,CAAb;AACD,KAFD,MAGK,IAAI,KAAK,wBAAT,EAAmC;AACtC,MAAA,UAAU,GAAG,MAAM,qDAAe,aAAf,EAA8B,IAA9B,EAAoC,QAApC,EAA8C,gBAA9C,CAAnB;AACD;;AAED,QAAI,UAAU,IAAI,IAAd,IAAsB,YAAtB,IAAsC,QAA1C,EAAoD;AAClD,MAAA,UAAU,CAAC,qBAAX,GAAmC,IAAnC;AACD;;AAED,UAAM,QAAQ,CAAC,IAAT,CAAc,0BAAd,CAAyC;AAC7C,MAAA,IAAI,EAAE,aADuC;AAE7C,MAAA,UAF6C;AAG7C,MAAA,MAAM,EAAE,IAHqC;AAI7C,MAAA,QAJ6C;AAK7C,MAAA,IAAI,EAAE,KAAK,KAAL,CAAW,IAAX,KAAoB,CAApB,GAAwB,KAAK,KAAL,CAAW,IAAX,GAAkB,IAAlB,GAAyB,KAAjD,GAAyD,IALlB;AAM7C,MAAA,gBAN6C;AAO7C,MAAA,iBAAiB,EAAE,CAAC,KAAK;AAPoB,KAAzC,CAAN;AASD;;AAES,EAAA,2BAA2B,GAAA;AACnC,UAAM,OAAO,GAAG,KAAK,QAAL,CAAc,OAA9B;AACA,UAAM,UAAU,GAAG,OAAO,CAAC,IAAR,CAAa,WAAb,OAA+B,OAAO,CAAC,IAAvC,GAA8C,QAA9C,GAAyD,QAA5E;AACA,WAAO,GAAG,OAAO,CAAC,IAAI,IAAI,KAAK,UAAL,GAAkB,EAAlB,GAAuB,UAAU,GAAG,OAAO,CAAC,OAAO,MAA7E;AACD;;AAED,MAAY,gBAAZ,GAA4B;AAC1B,WAAO,KAAK,OAAL,CAAa,OAAb,KAAyB,KAAhC;AACD;;AAED,MAAI,cAAJ,GAAkB;AAChB,WAAO,KAAP;AACD;;AAEO,QAAM,+BAAN,CAAsC,OAAtC,EAAoD,QAApD,EAAmE,aAAnE,EAA0F,YAA1F,EAA8G;AACpH,UAAM,QAAQ,GAAG,KAAK,QAAtB;AACA,UAAM,gBAAgB,GAAG,MAAM,QAAQ,CAAC,WAAT,CAAqB,KAAK,OAAL,CAAa,MAAlC,EAA0C,eAA1C,CAA/B;AACA,UAAM,MAAM,GAAG,MAAM,yBAAS,gBAAgB,IAAI,IAAI,CAAC,IAAL,CAAU,4BAAV,EAA4B,eAA5B,CAA7B,EAA2E,MAA3E,CAArB;;AAEA,QAAI,gBAAgB,IAAI,IAAxB,EAA8B;AAC5B,yBAAI,IAAJ,CAAS;AAAC,QAAA,MAAM,EAAE;AAAT,OAAT,EAAiD,+CAAjD;;AACA,aAAO,MAAP;AACD,KARmH,CAUpH;AACA;;;AACA,UAAM,eAAe,GAAG,IAAI,CAAC,IAAL,CAAU,KAAK,MAAf,EAAuB,iBAAiB,KAAK,IAAI,IAAI,KAAK,QAAL,CAAc,OAAd,CAAsB,aAAa,MAAxF,CAAxB;AACA,UAAM,KAAK,GAAG,OAAO,CAAC,QAAR,KAAqB,OAAnC;AACA,IAAA,OAAO,CAAC,iBAAR,GAA4B,IAA5B;AACA,IAAA,OAAO,CAAC,oBAAR,GAA+B,KAAK,GAAG,eAAH,GAAqB,IAAI,CAAC,KAAL,CAAW,IAAX,CAAgB,IAAhB,EAAsB,eAAtB,CAAzD;AACA,UAAM,KAAK,eAAL,CAAqB,OAArB,EAA8B,QAA9B,EAAwC,YAAY,IAAG,MAAM,KAAK,kBAAL,CAAwB,MAAxB,EAAgC,KAAhC,CAAT,CAApD,CAAN,CAhBoH,CAkBpH;;AACA,QAAI,sCAAJ,EAAuB;AACrB,UAAI;AACF,sCAAkB,IAAlB,CAAuB,aAAvB,EAAsC,eAAtC;AACD,OAFD,CAGA,OAAO,KAAP,EAAc;AACZ,2BAAI,IAAJ,CAAS,0BAA0B,KAAK,CAAC,OAAzC;;AAEA,cAAM,EAAE,GAAG,MAAM,QAAQ,CAAC,EAAT,CAAY,KAA7B;AACA,cAAM,EAAE,CAAC,IAAH,CAAQ,aAAR,EAAuB,EAAvB,CAAN,CAJY,CAKZ;;AACA,YAAI,CAAC,GAAG,CAAR;;AACA,eAAO,EAAE,MAAM,kBAAO,eAAP,CAAR,KAAoC,CAAC,KAAK,GAAjD,EAAsD;AACpD;AACA;AACA,gBAAM,IAAI,OAAJ,CAAY,CAAC,OAAD,EAAU,OAAV,KAAsB,UAAU,CAAC,OAAD,EAAU,GAAV,CAA5C,CAAN;AACD;AACF;AACF,KAjBD,MAkBK;AACH,YAAM,sBAAS,aAAT,CAAN;AACD;;AACD,UAAM,QAAQ,CAAC,IAAT,CAAc,eAAd,EAA+B,4BAA/B,CAAN;AAEA,WAAO,OAAO,CAAC,iBAAf,CA1CoH,CA2CpH;;AACA,IAAA,OAAO,CAAC,oBAAR,GAA+B,eAA/B;AACA,WAAO,MAAP;AACD;;AAEO,EAAA,iBAAiB,CAAC,KAAA,GAAiB,KAAlB,EAAuB;AAC9C;AACA;AACA,UAAM,QAAQ,GAAG,KAAK,OAAL,CAAa,QAAb,IAAyB,MAA1C;AACA,UAAM,OAAO,GAAG,KAAK,QAAL,CAAc,OAA9B;AACA,UAAM,UAAU,GAAG,CACjB,SAAS,QAAQ,iBAAiB,OAAO,CAAC,WAAW,GADpC,EAEjB,SAAS,QAAQ,oBAAoB,OAAO,CAAC,OAAO,GAFnC,EAGjB,SAAS,QAAQ,oBAAoB,OAAO,CAAC,SAAS,GAHrC,EAIjB,SAAS,QAAQ,qBAAqB,OAAO,CAAC,WAAW,GAJxC,EAKjB,SAAS,QAAQ,iBAAiB,OAAO,CAAC,YAAY,GALrC,CAAnB;;AAOA,QAAI,KAAJ,EAAW;AACT,MAAA,UAAU,CAAC,CAAD,CAAV,GAAgB,SAAS,QAAQ,oBAAoB,OAAO,CAAC,YAAY,GAAzE;AACA,MAAA,UAAU,CAAC,CAAD,CAAV,GAAgB,SAAS,QAAQ,iBAAiB,OAAO,CAAC,YAAY,GAAtE;AACD;;AACD,4BAAI,KAAK,QAAL,CAAc,4BAAd,CAA2C,eAA/C,EAAgE,EAAE,IAAI,UAAU,CAAC,IAAX,CAAgB,SAAS,QAAQ,qBAAqB,EAAE,GAAxD,CAAtE;AACA,4BAAI,OAAO,CAAC,WAAZ,EAAyB,EAAE,IAAI,UAAU,CAAC,IAAX,CAAgB,SAAS,QAAQ,iBAAiB,EAAE,GAApD,CAA/B;AACA,WAAO,UAAP;AACD;;AAES,EAAA,gBAAgB,CAAC,QAAD,EAAoB,OAApB,EAAgC;AACxD,UAAM,QAAQ,GAAG,KAAK,QAAtB;AACA,UAAM,OAAO,GAAG,KAAK,OAArB;AAEA,UAAM,gBAAgB,GAAG,KAAI,+BAAJ,EAAqB,QAAQ,CAAC,IAAT,CAAc,iBAAnC,CAAzB;;AAEA,QAAI,QAAJ,EAAc;AACZ,MAAA,OAAO,CAAC,SAAR,GAAoB,IAApB;;AAEA,UAAI,OAAO,CAAC,cAAR,KAA2B,KAA/B,EAAsC;AACpC,QAAA,OAAO,CAAC,gBAAR,GAA2B,IAA3B;AACD;;AAED,MAAA,gBAAgB,CAAC,GAAjB,CAAqB,YAAW;AAC9B,cAAM,mBAAmB,GAAG,MAAM,QAAQ,CAAC,WAAT,CAAqB,OAAO,CAAC,mBAA7B,EAAkD,yBAAlD,CAAlC;;AACA,YAAI,mBAAmB,IAAI,IAA3B,EAAiC;AAC/B,UAAA,OAAO,CAAC,UAAR,GAAqB,mBAArB;AACD;AACF,OALD;AAMD,KAbD,MAcK;AACH,UAAI,OAAO,CAAC,cAAR,KAA2B,KAA/B,EAAsC;AACpC,QAAA,OAAO,CAAC,qBAAR,GAAgC,IAAhC;AACD;;AAED,MAAA,gBAAgB,CAAC,GAAjB,CAAqB,YAAW;AAC9B,cAAM,eAAe,GAAG,MAAM,QAAQ,CAAC,WAAT,CAAqB,OAAO,CAAC,eAA7B,EAA8C,qBAA9C,CAA9B;;AACA,YAAI,eAAe,IAAI,IAAvB,EAA6B;AAC3B,UAAA,OAAO,CAAC,eAAR,GAA0B,IAA1B;AACA,UAAA,OAAO,CAAC,qBAAR,GAAgC,IAAhC;AACA,UAAA,OAAO,CAAC,sBAAR,GAAiC,eAAjC;AACD;AACF,OAPD;AASA,MAAA,gBAAgB,CAAC,GAAjB,CAAqB,YAAW;AAC9B,cAAM,MAAM,GAAG,CAAC,MAAM,QAAQ,CAAC,WAAT,CAAqB,OAAO,CAAC,gBAA7B,EAA+C,sBAA/C,CAAP,KAAkF,wDAAjG;AACA,QAAA,OAAO,CAAC,4BAAR,GAAuC,MAAvC;AACA,QAAA,OAAO,CAAC,8BAAR,GAAyC,CAAC,MAAM,QAAQ,CAAC,WAAT,CAAqB,OAAO,CAAC,kBAA7B,EAAiD,wBAAjD,CAAP,KAAsF,MAA/H;AACD,OAJD;;AAMA,UAAI,OAAO,CAAC,cAAR,KAA2B,KAA/B,EAAsC;AACpC,QAAA,OAAO,CAAC,qCAAR,GAAgD,IAAhD;AACD;AACF;;AAED,QAAI,OAAO,CAAC,UAAR,KAAuB,IAA3B,EAAiC;AAC/B,MAAA,OAAO,CAAC,0BAAR,GAAqC,IAArC;AACD;;AAED,QAAI,CAAC,QAAD,IAAa,OAAO,CAAC,UAAR,KAAuB,IAAxC,EAA8C;AAC5C,MAAA,OAAO,CAAC,mCAAR,GAA8C,IAA9C;AACD;;AAED,QAAI,OAAO,CAAC,kCAAZ,EAAgD;AAC9C,UAAI,QAAJ,EAAc;AACZ,cAAM,KAAI,wCAAJ,EAA8B,2GAA9B,CAAN;AACD;;AACD,MAAA,OAAO,CAAC,kCAAR,GAA6C,IAA7C;AACD;;AAED,UAAM,aAAa,GAAG,gEAAoB,OAApB,EAA6B,QAA7B,CAAtB;;AAEA,QAAI,aAAa,CAAC,YAAd,IAA8B,IAAlC,EAAwC;AACtC,MAAA,OAAO,CAAC,aAAR,GAAwB,aAAa,CAAC,YAAtC;AACD;;AAED,IAAA,OAAO,CAAC,aAAR,GAAwB,aAAa,CAAC,YAAtC;;AAEA,QAAI,OAAO,CAAC,wBAAZ,EAAsC;AACpC,MAAA,OAAO,CAAC,4BAAR,GAAuC,IAAvC;AACD;;AAED,IAAA,gBAAgB,CAAC,GAAjB,CAAqB,YAAW;AAC9B,YAAM,eAAe,GAAG,MAAM,QAAQ,CAAC,WAAT,CAAqB,OAAO,CAAC,eAA7B,EAA8C,qBAA9C,CAA9B;;AACA,UAAI,eAAe,IAAI,IAAvB,EAA6B;AAC3B;AACA,QAAA,OAAO,CAAC,gBAAR,GAA2B,eAA3B;AACA,QAAA,OAAO,CAAC,UAAR,GAAqB,eAArB;AACD;AACF,KAPD;AASA,IAAA,OAAO,CAAC,sBAAR,GAAiC,QAAQ,CAAC,WAAT,CAAqB,OAAO,CAAC,oBAAR,IAAgC,2BAArD,EAAkF,IAAlF,EAAwF,EAAxF,EAA4F,KAA5F,CAAjC;;AACA,QAAI,aAAa,CAAC,uBAAd,KAA0C,qEAA8B,KAA5E,EAAmF;AACjF,MAAA,OAAO,CAAC,8BAAR,GAAyC,IAAzC;AACD;;AACD,QAAI,aAAa,CAAC,uBAAd,KAA0C,qEAA8B,MAA5E,EAAoF;AAClF,MAAA,OAAO,CAAC,yBAAR,GAAoC,IAApC;AACD;;AACD,QAAI,CAAC,aAAa,CAAC,yBAAnB,EAA8C;AAC5C,MAAA,OAAO,CAAC,iCAAR,GAA4C,IAA5C;AACD;;AAED,QAAI,OAAO,CAAC,uBAAR,KAAoC,IAAxC,EAA8C;AAC5C,MAAA,OAAO,CAAC,qBAAR,GAAgC,IAAhC;AACD;;AAED,WAAO,gBAAgB,CAAC,UAAjB,EAAP;AACD;;AAEO,EAAA,qCAAqC,CAAC,OAAD,EAAa;AACxD,UAAM,OAAO,GAAG,KAAK,QAAL,CAAc,OAA9B;AACA,UAAM,WAAW,GAAG,OAAO,CAAC,WAA5B;;AACA,QAAI,WAAW,IAAI,IAAnB,EAAyB;AACvB,MAAA,OAAO,CAAC,YAAR,GAAuB,WAAvB;AACD,KALuD,CAOxD;;;AACA,QAAI,OAAO,CAAC,YAAR,KAAyB,OAAO,CAAC,eAArC,EAAsD;AACpD,MAAA,OAAO,CAAC,oBAAR,GAA+B,OAAO,CAAC,eAAvC;AACD;;AAED,QAAI,KAAK,cAAT,EAAyB;AACvB,MAAA,OAAO,CAAC,sBAAR,GAAiC,GAAG,OAAO,CAAC,mBAAmB,KAAK,mDAA6B,EAAjG;AACD,KAFD,MAGK;AACH,MAAA,OAAO,CAAC,wBAAR,GAAmC,GAAG,OAAO,CAAC,mBAAmB,KAAK,qDAA+B,EAArG;AACD;;AAED,QAAI,CAAC,KAAK,cAAN,IAAwB,OAAO,CAAC,aAAR,IAAyB,IAArD,EAA2D;AACzD,YAAM,OAAO,GAAG,KAAK,OAArB;;AACA,UAAI,OAAO,CAAC,MAAZ,EAAoB;AAClB,QAAA,OAAO,CAAC,eAAR,GAA0B,IAA1B;AACD;;AAED,MAAA,OAAO,CAAC,kBAAR,GAA6B,OAAO,CAAC,MAAR,GAAiB,KAAjB,GAAyB,IAAtD;AACD;AACF;;AAEO,QAAM,eAAN,CAAsB,OAAtB,EAAoC,QAApC,EAAmD,MAAnD,EAAiE;AACvE,UAAM,IAAI,GAAmB,KAAK,OAAL,CAAa,gBAAb,KAAkC,KAAnC,GAA4C,EAA5C,GAAiD,CAAC,KAAD,CAA7E;;AACA,SAAK,MAAM,IAAX,IAAmB,MAAM,CAAC,IAAP,CAAY,OAAZ,CAAnB,EAAyC;AACvC,YAAM,KAAK,GAAG,OAAO,CAAC,IAAD,CAArB;;AACA,UAAI,KAAK,IAAI,IAAb,EAAmB;AACjB,QAAA,IAAI,CAAC,IAAL,CAAU,KAAK,IAAI,EAAnB;AACD,OAFD,MAGK;AACH,QAAA,IAAI,CAAC,IAAL,CAAU,KAAK,IAAI,IAAI,KAAK,EAA5B;AACD;AACF;;AAED,SAAK,MAAM,IAAX,IAAmB,MAAM,CAAC,IAAP,CAAY,QAAZ,CAAnB,EAA0C;AACxC,YAAM,KAAK,GAAG,QAAQ,CAAC,IAAD,CAAtB;;AACA,UAAI,KAAK,CAAC,OAAN,CAAc,KAAd,CAAJ,EAA0B;AACxB,aAAK,MAAM,CAAX,IAAgB,KAAhB,EAAuB;AACrB,UAAA,IAAI,CAAC,IAAL,CAAU,KAAK,IAAI,IAAI,CAAC,EAAxB;AACD;AACF,OAJD,MAKK;AACH,QAAA,IAAI,CAAC,IAAL,CAAU,KAAK,IAAI,IAAI,KAAK,EAA5B;AACD;AACF;;AAED,IAAA,IAAI,CAAC,IAAL,CAAU,GAAV;;AAEA,QAAI,KAAK,QAAL,CAAc,WAAd,CAA0B,SAA9B,EAAyC;AACvC,WAAK,QAAL,CAAc,WAAd,CAA0B,GAA1B,CAA8B,aAA9B,EAA6C,MAA7C;AACD;;AAED,UAAM,QAAQ,GAAG,MAAM,4BAAvB;AACA,UAAM,OAAO,GAAG,IAAI,CAAC,IAAL,CAAU,QAAV,EAAoB,OAAO,CAAC,QAAR,KAAqB,QAArB,GAAgC,KAAhC,GAAyC,OAAO,CAAC,QAAR,KAAqB,OAArB,GAA+B,KAA/B,GAAuC,OAApG,EAA8G,OAAO,CAAC,QAAR,KAAqB,OAArB,GAA+B,cAA/B,GAAgD,UAA9J,CAAhB,CA/BuE,CAiCvE;AACE;AACA;;AACA,UAAM,aAAa,CAAC,QAAQ,CAAC,SAAD,CAAR,CAAoB,OAApB,CAA4B,IAA5B,EAAkC,EAAlC,CAAD,CAAnB,CApCqE,CAqCvE;;AAEA,UAAM,kCAAc,OAAd,EAAuB,IAAvB,EAA6B,MAA7B,EAAqC;AACzC;AACA,MAAA,GAAG,EAAE,EAAC,GAAG,OAAO,CAAC,GAAZ;AAAiB,QAAA,OAAO,EAAE;AAA1B,OAFoC;AAGzC,MAAA,GAAG,EAAE;AAHoC,KAArC,CAAN;AAKD;;AAEO,QAAM,kCAAN,GAAwC;AAC9C,UAAM,QAAQ,GAAG,KAAK,QAAtB;AACA,UAAM,OAAO,GAAG,KAAK,OAArB;AACA,UAAM,eAAe,GAAG,KAAI,0CAAJ,GAAxB;AACA,UAAM,gBAAgB,GAAG,KAAI,4BAAJ,EAAqB,OAArB,CAAzB;AAEA,IAAA,eAAe,CAAC,OAAhB,CAAwB,IAAI,CAAC,IAAL,CAAU,4BAAV,EAA4B,SAA5B,EAAuC,cAAvC,CAAxB;AAEA,UAAM,UAAU,GAAG,IAAI,CAAC,IAAL,CAAU,4BAAV,EAA4B,SAA5B,CAAnB;AACA,IAAA,eAAe,CAAC,aAAhB,CAA8B,UAA9B;AACA,IAAA,eAAe,CAAC,KAAhB,CAAsB,CAAC,SAAD,EAAY,WAAZ,EAAyB,gBAAzB,EAA2C,qBAA3C,EAAkE,iBAAlE,EAAqF,UAArF,EAAiG,aAAjG,CAAtB;AAEA,yCAAoB,eAApB,EAAqC,gBAArC;AAEA,UAAM,WAAW,GAAG,KAAI,+BAAJ,EAAqB,QAAQ,CAAC,IAAT,CAAc,iBAAnC,CAApB;AAEA,UAAM,UAAU,GAAG,KAAK,gBAAL,GAAwB,aAAxB,GAAwC,UAA3D;AACA,IAAA,WAAW,CAAC,GAAZ,CAAgB,YAAW;AACzB,MAAA,eAAe,CAAC,YAAhB,CAA6B,UAA7B,EAAyC,IAAI,CAAC,IAAL,CAAU,MAAM,uBAAuB,EAAvC,EAA2C,SAA3C,EAAsD,UAAtD,CAAzC;AACD,KAFD;AAIA,IAAA,WAAW,CAAC,GAAZ,CAAgB,YAAW;AACzB,YAAM,aAAa,GAAG,IAAI,CAAC,IAAL,CAAU,QAAQ,CAAC,IAAT,CAAc,iBAAxB,EAA2C,UAA3C,CAAtB;AACA,YAAM,IAAI,GAAG,MAAM,sBAAW,aAAX,CAAnB;;AACA,UAAI,IAAI,IAAI,IAAR,IAAgB,IAAI,CAAC,WAAL,EAApB,EAAwC;AACtC,QAAA,eAAe,CAAC,YAAhB,CAA6B,UAA7B,EAAyC,aAAzC;AACD;AACF,KAND;AAQA,IAAA,WAAW,CAAC,OAAZ,CAAoB,6CAA4B,cAA5B,EAA4C,QAA5C,EAAsD,eAAtD,EAAuE,gBAAvE,CAApB;;AAEA,QAAI,CAAC,KAAK,UAAV,EAAsB;AACpB,UAAI,OAAO,CAAC,QAAR,KAAqB,KAAzB,EAAgC;AAC9B,QAAA,WAAW,CAAC,OAAZ,CAAoB,6CAA4B,sBAA5B,EAAoD,QAApD,EAA8D,eAA9D,EAA+E,gBAA/E,CAApB;AACD;;AAED,MAAA,WAAW,CAAC,GAAZ,CAAgB,YAAW;AACzB,cAAM,aAAa,GAAG,MAAM,QAAQ,CAAC,WAAT,CAAqB,KAAK,OAAL,CAAa,OAAlC,EAA2C,eAA3C,CAA5B;;AACA,YAAI,aAAa,IAAI,IAArB,EAA2B;AACzB,UAAA,eAAe,CAAC,aAAhB,CAA8B,QAAQ,CAAC,IAAT,CAAc,iBAA5C;AACA,UAAA,eAAe,CAAC,OAAhB,CAAwB,aAAxB;AACD;AACF,OAND;AAOD;;AAED,UAAM,WAAW,CAAC,UAAZ,EAAN;AACA,WAAO,eAAe,CAAC,KAAhB,EAAP;AACD;;AAEO,QAAM,kBAAN,CAAyB,cAAzB,EAAiD,WAAjD,EAAqE;AAC3E,UAAM,QAAQ,GAAG,KAAK,QAAtB;AACA,UAAM,OAAO,GAAG,KAAK,OAArB;AACA,UAAM,gBAAgB,GAAG,KAAI,4BAAJ,EAAqB,OAArB,CAAzB;AAEA,UAAM,eAAe,GAAG,KAAI,0CAAJ,GAAxB;AACA,UAAM,WAAW,GAAG,KAAI,+BAAJ,EAAqB,QAAQ,CAAC,IAAT,CAAc,iBAAnC,CAApB;;AAEA,QAAI,WAAJ,EAAiB;AACf;AACA,MAAA,WAAW,CAAC,GAAZ,CAAgB,MAAM,uCAAmB,QAAnB,EAA6B,OAA7B,EAAsC,eAAtC,EAAuD,gBAAgB,CAAC,KAAxE,CAAtB;AACD;;AAED,UAAM,WAAW,CAAC,UAAZ,EAAN;;AAEA,QAAI,KAAK,UAAT,EAAqB;AACnB,aAAO,eAAe,CAAC,KAAhB,KAA0B,cAAjC;AACD;;AAED,UAAM,2BAA2B,GAAG,KAAK,8BAAL,EAApC;;AACA,QAAI,2BAA2B,IAAI,IAA/B,IAAuC,2BAA2B,CAAC,MAA5B,KAAuC,CAAlF,EAAqF;AACnF,WAAK,MAAM,CAAC,IAAD,EAAO,GAAP,CAAX,IAA0B,KAAK,KAAL,CAAW,OAAX,EAA1B,EAAgD;AAC9C,cAAM,wBAAwB,CAAC,2BAAD,EAA8B,GAA9B,EAAmC,IAAnC,EAAyC,eAAzC,CAA9B;AACD;AACF;;AAED,UAAM,gBAAgB,GAAG,QAAQ,CAAC,gBAAlC;;AACA,QAAI,gBAAgB,CAAC,MAAjB,KAA4B,CAAhC,EAAmC;AAEjC,MAAA,eAAe,CAAC,OAAhB,CAAwB,IAAI,CAAC,IAAL,CAAU,IAAI,CAAC,IAAL,CAAU,4BAAV,EAA4B,SAA5B,CAAV,EAAkD,qBAAlD,CAAxB;;AACA,UAAI,WAAJ,EAAiB;AACf,cAAM,8BAA8B,GAAG,KAAI,0CAAJ,GAAvC;;AACA,aAAK,MAAM,IAAX,IAAmB,gBAAnB,EAAqC;AACnC,gBAAM,UAAU,GAAG,4BAAQ,IAAI,CAAC,GAAb,EAAkB,GAAlB,CAAsB,gCAAtB,CAAnB;;AACA,eAAK,MAAM,GAAX,IAAkB,UAAlB,EAA8B;AAC5B,kBAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,WAAT,CAAqB,4CAAwB,IAAI,CAAC,IAA7B,EAAmC,KAAnC,CAArB,EAAgE,GAAG,UAAU,CAAC,CAAD,CAAG,MAAhF,CAAzB;AACA,gBAAI,iBAAiB,GAAG,WAAxB;;AACA,gBAAI,UAAU,IAAI,IAAlB,EAAwB;AACtB,cAAA,iBAAiB,GAAG,wBAAwB,IAAI,CAAC,QAAL,CAAc,UAAd,CAAyB,EAArE;AACA,cAAA,8BAA8B,CAAC,IAA/B,CAAoC,iBAApC,EAAuD,UAAvD;AACD;;AAED,kBAAM,IAAI,GAAG,IAAI,iBAAiB,GAAlC;AACA,kBAAM,WAAW,GAAG,cAAc,QAAQ,CAAC,OAAT,CAAiB,WAAW,GAA9D;AACA,kBAAM,OAAO,GAAG,sBAAhB;AACA,YAAA,8BAA8B,CAAC,WAA/B,CAA2C,eAA3C,EAA4D,IAAI,GAAG,MAAM,IAAI,CAAC,IAAL,IAAa,GAAG,MAAM,IAAI,CAAC,WAAL,IAAoB,EAAE,KAAK,IAAI,IAAI,WAAW,IAAI,OAAO,EAAxJ;AACD;AACF;;AACD,QAAA,eAAe,CAAC,KAAhB,CAAsB,0BAAtB,EAAkD,8BAAlD;AACD,OAnBD,MAoBK;AACH,cAAM,gCAAgC,GAAG,KAAI,0CAAJ,GAAzC;;AACA,aAAK,MAAM,IAAX,IAAmB,gBAAnB,EAAqC;AACnC,eAAK,MAAM,GAAX,IAAkB,4BAAQ,IAAI,CAAC,GAAb,CAAlB,EAAqC;AACnC,YAAA,gCAAgC,CAAC,WAAjC,CAA6C,iBAA7C,EAAgE,IAAI,sCAAa,GAAb,CAAiB,MAAM,IAAI,CAAC,IAAL,IAAa,GAAG,GAA3G;AACD;AACF;;AACD,QAAA,eAAe,CAAC,KAAhB,CAAsB,4BAAtB,EAAoD,gCAApD;AACD;AACF;;AAED,WAAO,eAAe,CAAC,KAAhB,KAA0B,cAAjC;AACD;;AAhoBmC;;;;AAmoBtC,eAAe,wBAAf,CAAwC,2BAAxC,EAAoF,GAApF,EAAiG,IAAjG,EAA6G,eAA7G,EAAiJ;AAC/I,QAAM,YAAY,GAAG,IAAI,CAAC,IAAL,CAAU,GAAV,EAAe,WAAf,CAArB;AACA,QAAM,OAAO,GAAG,MAAM,sBAAW,YAAX,CAAtB;;AACA,MAAI,OAAO,IAAI,IAAX,IAAmB,CAAC,OAAO,CAAC,WAAR,EAAxB,EAA+C;AAC7C;AACD;;AAED,QAAM,WAAW,GAAG,GAAG,IAAI,CAAC,GAAG,cAA/B;AACA,QAAM,mBAAmB,GAAG,MAAM,gBAAK,YAAL,EAAmB,CAAC,IAAD,EAAO,IAAP,KAAe;AAClE,QAAI,IAAI,CAAC,WAAL,EAAJ,EAAwB;AACtB,aAAO,CAAC,IAAI,CAAC,QAAL,CAAc,WAAd,CAAR;AACD,KAFD,MAGK;AACH,aAAO,2BAA2B,CAAC,IAA5B,CAAiC,EAAE,IAAI,IAAI,CAAC,QAAL,CAAc,EAAd,CAAvC,CAAP;AACD;AACF,GAPiC,CAAlC;;AASA,MAAI,mBAAmB,CAAC,MAApB,KAA+B,CAAnC,EAAsC;AACpC,UAAM,KAAK,GAAG,KAAI,0CAAJ,GAAd;;AACA,SAAK,MAAM,IAAX,IAAmB,mBAAnB,EAAwC;AACtC,MAAA,KAAK,CAAC,IAAN,CAAW,aAAa,IAAI,CAAC,QAAL,CAAc,GAAd,EAAmB,IAAnB,EAAyB,OAAzB,CAAiC,KAAjC,EAAwC,IAAxC,CAA6C,EAArE,EAAyE,IAAzE;AACD;;AACD,IAAA,eAAe,CAAC,KAAhB,CAAsB,eAAe,oBAAK,IAAL,CAAU,EAA/C,EAAmD,KAAnD;AACD;AACF;;AAED,eAAe,aAAf,CAA6B,OAA7B,EAA4C;AAC1C,WAAS,MAAT,CAAgB,aAAhB,EAAsC;AACpC,WAAO,IAAI,OAAJ,CAAY,CAAC,OAAD,EAAU,MAAV,KAAoB;AACrC,MAAA,EAAE,CAAC,IAAH,CAAQ,OAAR,EAAiB,IAAjB,EAAuB,CAAC,KAAD,EAAQ,EAAR,KAAc;AACnC,YAAI;AACF,cAAI,KAAK,IAAI,IAAT,IAAiB,KAAK,CAAC,IAAN,KAAe,OAApC,EAA6C;AAC3C,gBAAI,CAAC,aAAL,EAAoB;AAClB,iCAAI,IAAJ,CAAS,EAAT,EAAa,qFAAb;AACD;;AACD,YAAA,OAAO,CAAC,KAAD,CAAP;AACD,WALD,MAMK,IAAI,EAAE,IAAI,IAAV,EAAgB;AACnB,YAAA,OAAO,CAAC,IAAD,CAAP;AACD,WAFI,MAGA;AACH,YAAA,EAAE,CAAC,KAAH,CAAS,EAAT,EAAa,MAAM,OAAO,CAAC,IAAD,CAA1B;AACD;AACF,SAbD,CAcA,OAAO,KAAP,EAAc;AACZ,UAAA,MAAM,CAAC,KAAD,CAAN;AACD;AACF,OAlBD;AAmBD,KApBM,EAqBJ,IArBI,CAqBC,MAAM,IAAG;AACb,UAAI,MAAJ,EAAY;AACV,eAAO,IAAP;AACD,OAFD,MAGK;AACH,eAAO,IAAI,OAAJ,CAAa,OAAD,IAAa,UAAU,CAAC,OAAD,EAAU,IAAV,CAAnC,EACJ,IADI,CACC,MAAM,MAAM,CAAC,IAAD,CADb,CAAP;AAED;AACF,KA7BI,CAAP;AA8BD;;AAED,QAAM,MAAM,CAAC,KAAD,CAAZ;AACD;;AAED,eAAe,qBAAf,CAAqC,IAArC,EAAiD;AAC/C,SAAO;AACL,IAAA,IAAI,EAAE,IADD;AAEL,IAAA,IAAI,EAAE,CAAC,MAAM,qBAAK,IAAL,CAAP,EAAmB,IAFpB;AAGL,IAAA,MAAM,EAAE,MAAM,sBAAS,IAAT;AAHT,GAAP;AAKD,C", "sourcesContent": ["import { path7za } from \"7zip-bin\"\nimport BluebirdPromise from \"bluebird-lst\"\nimport { executeApp<PERSON><PERSON>er, Arch, asArray, AsyncTaskManager, getPlatformIconFileName, InvalidConfigurationError, log, spawnAndWrite, use, exec } from \"builder-util\"\nimport { PackageFileInfo, UUID, CURRENT_APP_PACKAGE_FILE_NAME, CURRENT_APP_INSTALLER_FILE_NAME } from \"builder-util-runtime\"\nimport { getBinFromUrl } from \"../../binDownload\"\nimport { statOrNull, walk, exists } from \"builder-util/out/fs\"\nimport { hashFile } from \"../../util/hash\"\nimport _debug from \"debug\"\nimport { readFile, stat, unlink } from \"fs-extra\"\nimport * as path from \"path\"\nimport * as fs from \"fs\"\nimport { Target } from \"../../core\"\nimport { DesktopShortcutCreationPolicy, getEffectiveOptions } from \"../../options/CommonWindowsInstallerConfiguration\"\nimport { computeSafeArtifactNameIfNeeded, normalizeExt } from \"../../platformPackager\"\nimport { isMacOsCatalina } from \"../../util/macosVersion\"\nimport { time } from \"../../util/timer\"\nimport { execWine } from \"../../wine\"\nimport { WinPackager } from \"../../winPackager\"\nimport { archive, ArchiveOptions } from \"../archive\"\nimport { appendBlockmap, configureDifferentialAwareArchiveOptions, createBlockmap, createNsisWebDifferentialUpdateInfo } from \"../differentialUpdateInfoBuilder\"\nimport { getWindowsInstallationDirName } from \"../targetUtil\"\nimport { addCustomMessageFileInclude, createAddLangsMacro, LangConfigurator } from \"./nsisLang\"\nimport { computeLicensePage } from \"./nsisLicense\"\nimport { NsisOptions, PortableOptions } from \"./nsisOptions\"\nimport { NsisScriptGenerator } from \"./nsisScriptGenerator\"\nimport { AppPackageHelper, NSIS_PATH, nsisTemplatesDir, UninstallerReader } from \"./nsisUtil\"\n\nconst debug = _debug(\"electron-builder:nsis\")\n\n// noinspection SpellCheckingInspection\nconst ELECTRON_BUILDER_NS_UUID = UUID.parse(\"50e065bc-3134-11e6-9bab-38c9862bdaf3\")\n\n// noinspection SpellCheckingInspection\nconst nsisResourcePathPromise = () => getBinFromUrl(\"nsis-resources\", \"3.4.1\", \"Dqd6g+2buwwvoG1Vyf6BHR1b+25QMmPcwZx40atOT57gH27rkjOei1L0JTldxZu4NFoEmW4kJgZ3DlSWVON3+Q==\")\n\nconst USE_NSIS_BUILT_IN_COMPRESSOR = false\n\nexport class NsisTarget extends Target {\n  readonly options: NsisOptions\n\n  /** @private */\n  readonly archs: Map<Arch, string> = new Map()\n\n  constructor(readonly packager: WinPackager, readonly outDir: string, targetName: string, protected readonly packageHelper: AppPackageHelper) {\n    super(targetName)\n\n    this.packageHelper.refCount++\n\n    this.options = targetName === \"portable\" ? Object.create(null) : {\n      preCompressedFileExtensions: [\".avi\", \".mov\", \".m4v\", \".mp4\", \".m4p\", \".qt\", \".mkv\", \".webm\", \".vmdk\"],\n      ...this.packager.config.nsis,\n    }\n\n    if (targetName !== \"nsis\") {\n      Object.assign(this.options, (this.packager.config as any)[targetName === \"nsis-web\" ? \"nsisWeb\" : targetName])\n    }\n\n    const deps = packager.info.metadata.dependencies\n    if (deps != null && deps[\"electron-squirrel-startup\"] != null) {\n      log.warn('\"electron-squirrel-startup\" dependency is not required for NSIS')\n    }\n  }\n\n  async build(appOutDir: string, arch: Arch) {\n    this.archs.set(arch, appOutDir)\n  }\n\n  get isBuildDifferentialAware() {\n    return !this.isPortable && this.options.differentialPackage !== false\n  }\n\n  private getPreCompressedFileExtensions(): Array<string> | null {\n    const result = this.isWebInstaller ? null : this.options.preCompressedFileExtensions\n    return result == null ? null : asArray(result).map(it => it.startsWith(\".\") ? it : `.${it}`)\n  }\n\n  /** @private */\n  async buildAppPackage(appOutDir: string, arch: Arch): Promise<PackageFileInfo> {\n    const options = this.options\n    const packager = this.packager\n\n    const isBuildDifferentialAware = this.isBuildDifferentialAware\n    const format = !isBuildDifferentialAware && options.useZip ? \"zip\" : \"7z\"\n    const archiveFile = path.join(this.outDir, `${packager.appInfo.sanitizedName}-${packager.appInfo.version}-${Arch[arch]}.nsis.${format}`)\n    const preCompressedFileExtensions = this.getPreCompressedFileExtensions()\n    const archiveOptions: ArchiveOptions = {\n      withoutDir: true,\n      compression: packager.compression,\n      excluded: preCompressedFileExtensions == null ? null : preCompressedFileExtensions.map(it => `*${it}`)\n    }\n\n    const timer = time(`nsis package, ${Arch[arch]}`)\n    await archive(format, archiveFile, appOutDir, isBuildDifferentialAware ? configureDifferentialAwareArchiveOptions(archiveOptions) : archiveOptions)\n    timer.end()\n\n    if (isBuildDifferentialAware && this.isWebInstaller) {\n      const data = await appendBlockmap(archiveFile)\n      return {\n        ...data,\n        path: archiveFile,\n      }\n    }\n    else {\n      return await createPackageFileInfo(archiveFile)\n    }\n  }\n\n  async finishBuild(): Promise<any> {\n    try {\n      await this.buildInstaller()\n    }\n    finally {\n      await this.packageHelper.finishBuild()\n    }\n  }\n\n  protected get installerFilenamePattern(): string {\n    // tslint:disable:no-invalid-template-strings\n    return \"${productName} \" + (this.isPortable ? \"\" : \"Setup \") + \"${version}.${ext}\"\n  }\n\n  private get isPortable() {\n    return this.name === \"portable\"\n  }\n\n  private async buildInstaller(): Promise<any> {\n    const packager = this.packager\n    const appInfo = packager.appInfo\n    const options = this.options\n    const installerFilename = packager.expandArtifactNamePattern(options, \"exe\", null, this.installerFilenamePattern)\n    const oneClick = options.oneClick !== false\n    const installerPath = path.join(this.outDir, installerFilename)\n\n    const logFields: any = {\n      target: this.name,\n      file: log.filePath(installerPath),\n      archs: Array.from(this.archs.keys()).map(it => Arch[it]).join(\", \"),\n    }\n    const isPerMachine = options.perMachine === true\n    if (!this.isPortable) {\n      logFields.oneClick = oneClick\n      logFields.perMachine = isPerMachine\n    }\n\n    await packager.info.callArtifactBuildStarted({\n      targetPresentableName: this.name,\n      file: installerPath,\n      arch: null,\n    }, logFields)\n\n    const guid = options.guid || UUID.v5(appInfo.id, ELECTRON_BUILDER_NS_UUID)\n    const uninstallAppKey = guid.replace(/\\\\/g, \" - \")\n    const defines: any = {\n      APP_ID: appInfo.id,\n      APP_GUID: guid,\n      // Windows bug - entry in Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall cannot have \\ symbols (dir)\n      UNINSTALL_APP_KEY: uninstallAppKey,\n      PRODUCT_NAME: appInfo.productName,\n      PRODUCT_FILENAME: appInfo.productFilename,\n      APP_FILENAME: getWindowsInstallationDirName(appInfo, !oneClick || isPerMachine),\n      APP_DESCRIPTION: appInfo.description,\n      VERSION: appInfo.version,\n\n      PROJECT_DIR: packager.projectDir,\n      BUILD_RESOURCES_DIR: packager.info.buildResourcesDir,\n\n      APP_PACKAGE_NAME: appInfo.name\n    }\n    if (uninstallAppKey !== guid) {\n      defines.UNINSTALL_REGISTRY_KEY_2 = `Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Uninstall\\\\${guid}`\n    }\n\n    const commands: any = {\n      OutFile: `\"${installerPath}\"`,\n      VIProductVersion: appInfo.getVersionInWeirdWindowsForm(),\n      VIAddVersionKey: this.computeVersionKey(),\n      Unicode: this.isUnicodeEnabled,\n    }\n\n    const isPortable = this.isPortable\n    const iconPath = (isPortable ? null : await packager.getResource(options.installerIcon, \"installerIcon.ico\")) || await packager.getIconPath()\n    if (iconPath != null) {\n      if (isPortable) {\n        commands.Icon = `\"${iconPath}\"`\n      }\n      else {\n        defines.MUI_ICON = iconPath\n        defines.MUI_UNICON = iconPath\n      }\n    }\n\n    const packageFiles: { [arch: string]: PackageFileInfo } = {}\n    let estimatedSize = 0\n    if (this.isPortable && options.useZip) {\n      for (const [arch, dir] of this.archs.entries()) {\n        defines[arch === Arch.x64 ? \"APP_DIR_64\" : (arch === Arch.arm64 ? \"APP_DIR_ARM64\" : \"APP_DIR_32\")] = dir\n      }\n    }\n    else if (USE_NSIS_BUILT_IN_COMPRESSOR && this.archs.size === 1) {\n      defines.APP_BUILD_DIR = this.archs.get(this.archs.keys().next().value)\n    }\n    else {\n      await BluebirdPromise.map(this.archs.keys(), async arch => {\n        const fileInfo = await this.packageHelper.packArch(arch, this)\n        const file = fileInfo.path\n        const defineKey = arch === Arch.x64 ? \"APP_64\" : (arch === Arch.arm64 ? \"APP_ARM64\" : \"APP_32\")\n        defines[defineKey] = file\n        defines[`${defineKey}_NAME`] = path.basename(file)\n        // nsis expect a hexadecimal string\n        defines[`${defineKey}_HASH`] = Buffer.from(fileInfo.sha512, \"base64\").toString(\"hex\").toUpperCase()\n\n        if (this.isWebInstaller) {\n          await packager.dispatchArtifactCreated(file, this, arch)\n          packageFiles[Arch[arch]] = fileInfo\n        }\n\n        const archiveInfo = (await exec(path7za, [\"l\", file])).trim()\n        // after adding blockmap data will be \"Warnings: 1\" in the end of output\n        const match = archiveInfo.match(/(\\d+)\\s+\\d+\\s+\\d+\\s+files/)\n        if (match == null) {\n          log.warn({output: archiveInfo}, \"cannot compute size of app package\")\n        }\n        else {\n          estimatedSize += parseInt(match[1], 10)\n        }\n      })\n    }\n\n    this.configureDefinesForAllTypeOfInstaller(defines)\n    if (isPortable) {\n      const portableOptions = options as PortableOptions\n      defines.REQUEST_EXECUTION_LEVEL = portableOptions.requestExecutionLevel || \"user\"\n      defines.UNPACK_DIR_NAME = portableOptions.unpackDirName || (await executeAppBuilder([\"ksuid\"]))\n\n      if (portableOptions.splashImage != null) {\n        defines.SPLASH_IMAGE = path.resolve(packager.projectDir, portableOptions.splashImage)\n      }\n    }\n    else {\n      await this.configureDefines(oneClick, defines)\n    }\n\n    if (estimatedSize !== 0) {\n      // in kb\n      defines.ESTIMATED_SIZE = Math.round(estimatedSize / 1024)\n    }\n\n    if (packager.compression === \"store\") {\n      commands.SetCompress = \"off\"\n    }\n    else {\n      // difference - 33.540 vs 33.601, only 61 KB (but zip is faster to decompress)\n      // do not use /SOLID - \"With solid compression, files are uncompressed to temporary file before they are copied to their final destination\",\n      // it is not good for portable installer (where built-in NSIS compression is used). http://forums.winamp.com/showpost.php?p=2982902&postcount=6\n      commands.SetCompressor = \"zlib\"\n      if (!this.isWebInstaller) {\n        defines.COMPRESS = \"auto\"\n      }\n    }\n\n    debug(defines)\n    debug(commands)\n\n    if (packager.packagerOptions.effectiveOptionComputed != null && await packager.packagerOptions.effectiveOptionComputed([defines, commands])) {\n      return\n    }\n\n    // prepare short-version variants of defines and commands, to make an uninstaller that doesn't differ much from the previous one\n    const definesUninstaller: any = { ...defines }\n    const commandsUninstaller: any = { ...commands}\n    if (appInfo.shortVersion != null) {\n      definesUninstaller.VERSION = appInfo.shortVersion\n      commandsUninstaller.VIProductVersion = appInfo.shortVersionWindows\n      commandsUninstaller.VIAddVersionKey = this.computeVersionKey(true)\n    }\n\n    const sharedHeader = await this.computeCommonInstallerScriptHeader()\n    const script = isPortable ? await readFile(path.join(nsisTemplatesDir, \"portable.nsi\"), \"utf8\") : await this.computeScriptAndSignUninstaller(definesUninstaller, commandsUninstaller, installerPath, sharedHeader)\n\n    // copy outfile name into main options, as the computeScriptAndSignUninstaller function was kind enough to add important data to temporary defines.\n    defines.UNINSTALLER_OUT_FILE = definesUninstaller.UNINSTALLER_OUT_FILE\n\n    await this.executeMakensis(defines, commands, sharedHeader + await this.computeFinalScript(script, true))\n    await Promise.all<any>([packager.sign(installerPath), defines.UNINSTALLER_OUT_FILE == null ? Promise.resolve() : unlink(defines.UNINSTALLER_OUT_FILE)])\n\n    const safeArtifactName = computeSafeArtifactNameIfNeeded(installerFilename, () => this.generateGitHubInstallerName())\n    let updateInfo: any\n    if (this.isWebInstaller) {\n      updateInfo = createNsisWebDifferentialUpdateInfo(installerPath, packageFiles)\n    }\n    else if (this.isBuildDifferentialAware) {\n      updateInfo = await createBlockmap(installerPath, this, packager, safeArtifactName)\n    }\n\n    if (updateInfo != null && isPerMachine && oneClick) {\n      updateInfo.isAdminRightsRequired = true\n    }\n\n    await packager.info.callArtifactBuildCompleted({\n      file: installerPath,\n      updateInfo,\n      target: this,\n      packager,\n      arch: this.archs.size === 1 ? this.archs.keys().next().value : null,\n      safeArtifactName,\n      isWriteUpdateInfo: !this.isPortable,\n    })\n  }\n\n  protected generateGitHubInstallerName() {\n    const appInfo = this.packager.appInfo\n    const classifier = appInfo.name.toLowerCase() === appInfo.name ? \"setup-\" : \"Setup-\"\n    return `${appInfo.name}-${this.isPortable ? \"\" : classifier}${appInfo.version}.exe`\n  }\n\n  private get isUnicodeEnabled() {\n    return this.options.unicode !== false\n  }\n\n  get isWebInstaller(): boolean {\n    return false\n  }\n\n  private async computeScriptAndSignUninstaller(defines: any, commands: any, installerPath: string, sharedHeader: string) {\n    const packager = this.packager\n    const customScriptPath = await packager.getResource(this.options.script, \"installer.nsi\")\n    const script = await readFile(customScriptPath || path.join(nsisTemplatesDir, \"installer.nsi\"), \"utf8\")\n\n    if (customScriptPath != null) {\n      log.info({reason: \"custom NSIS script is used\"}, \"uninstaller is not signed by electron-builder\")\n      return script\n    }\n\n    // https://github.com/electron-userland/electron-builder/issues/2103\n    // it is more safe and reliable to write uninstaller to our out dir\n    const uninstallerPath = path.join(this.outDir, `__uninstaller-${this.name}-${this.packager.appInfo.sanitizedName}.exe`)\n    const isWin = process.platform === \"win32\"\n    defines.BUILD_UNINSTALLER = null\n    defines.UNINSTALLER_OUT_FILE = isWin ? uninstallerPath : path.win32.join(\"Z:\", uninstallerPath)\n    await this.executeMakensis(defines, commands, sharedHeader + await this.computeFinalScript(script, false))\n\n    // http://forums.winamp.com/showthread.php?p=3078545\n    if (isMacOsCatalina()) {\n      try {\n        UninstallerReader.exec(installerPath, uninstallerPath)\n      }\n      catch (error) {\n        log.warn(\"packager.vm is used: \" + error.message)\n\n        const vm = await packager.vm.value\n        await vm.exec(installerPath, [])\n        // Parallels VM can exit after command execution, but NSIS continue to be running\n        let i = 0\n        while (!(await exists(uninstallerPath)) && i++ < 100) {\n          // noinspection JSUnusedLocalSymbols\n          // eslint-disable-next-line @typescript-eslint/no-unused-vars\n          await new Promise((resolve, _reject) => setTimeout(resolve, 300))\n        }\n      }\n    }\n    else {\n      await execWine(installerPath)\n    }\n    await packager.sign(uninstallerPath, \"  Signing NSIS uninstaller\")\n\n    delete defines.BUILD_UNINSTALLER\n    // platform-specific path, not wine\n    defines.UNINSTALLER_OUT_FILE = uninstallerPath\n    return script\n  }\n\n  private computeVersionKey(short: boolean = false) {\n    // Error: invalid VIProductVersion format, should be X.X.X.X\n    // so, we must strip beta\n    const localeId = this.options.language || \"1033\"\n    const appInfo = this.packager.appInfo\n    const versionKey = [\n      `/LANG=${localeId} ProductName \"${appInfo.productName}\"`,\n      `/LANG=${localeId} ProductVersion \"${appInfo.version}\"`,\n      `/LANG=${localeId} LegalCopyright \"${appInfo.copyright}\"`,\n      `/LANG=${localeId} FileDescription \"${appInfo.description}\"`,\n      `/LANG=${localeId} FileVersion \"${appInfo.buildVersion}\"`,\n    ]\n    if (short) {\n      versionKey[1] = `/LANG=${localeId} ProductVersion \"${appInfo.shortVersion}\"`\n      versionKey[4] = `/LANG=${localeId} FileVersion \"${appInfo.shortVersion}\"`\n    }\n    use(this.packager.platformSpecificBuildOptions.legalTrademarks, it => versionKey.push(`/LANG=${localeId} LegalTrademarks \"${it}\"`))\n    use(appInfo.companyName, it => versionKey.push(`/LANG=${localeId} CompanyName \"${it}\"`))\n    return versionKey\n  }\n\n  protected configureDefines(oneClick: boolean, defines: any): Promise<any> {\n    const packager = this.packager\n    const options = this.options\n\n    const asyncTaskManager = new AsyncTaskManager(packager.info.cancellationToken)\n\n    if (oneClick) {\n      defines.ONE_CLICK = null\n\n      if (options.runAfterFinish !== false) {\n        defines.RUN_AFTER_FINISH = null\n      }\n\n      asyncTaskManager.add(async () => {\n        const installerHeaderIcon = await packager.getResource(options.installerHeaderIcon, \"installerHeaderIcon.ico\")\n        if (installerHeaderIcon != null) {\n          defines.HEADER_ICO = installerHeaderIcon\n        }\n      })\n    }\n    else {\n      if (options.runAfterFinish === false) {\n        defines.HIDE_RUN_AFTER_FINISH = null\n      }\n\n      asyncTaskManager.add(async () => {\n        const installerHeader = await packager.getResource(options.installerHeader, \"installerHeader.bmp\")\n        if (installerHeader != null) {\n          defines.MUI_HEADERIMAGE = null\n          defines.MUI_HEADERIMAGE_RIGHT = null\n          defines.MUI_HEADERIMAGE_BITMAP = installerHeader\n        }\n      })\n\n      asyncTaskManager.add(async () => {\n        const bitmap = (await packager.getResource(options.installerSidebar, \"installerSidebar.bmp\")) || \"${NSISDIR}\\\\Contrib\\\\Graphics\\\\Wizard\\\\nsis3-metro.bmp\"\n        defines.MUI_WELCOMEFINISHPAGE_BITMAP = bitmap\n        defines.MUI_UNWELCOMEFINISHPAGE_BITMAP = (await packager.getResource(options.uninstallerSidebar, \"uninstallerSidebar.bmp\")) || bitmap\n      })\n\n      if (options.allowElevation !== false) {\n        defines.MULTIUSER_INSTALLMODE_ALLOW_ELEVATION = null\n      }\n    }\n\n    if (options.perMachine === true) {\n      defines.INSTALL_MODE_PER_ALL_USERS = null\n    }\n\n    if (!oneClick || options.perMachine === true) {\n      defines.INSTALL_MODE_PER_ALL_USERS_REQUIRED = null\n    }\n\n    if (options.allowToChangeInstallationDirectory) {\n      if (oneClick) {\n        throw new InvalidConfigurationError(\"allowToChangeInstallationDirectory makes sense only for assisted installer (please set oneClick to false)\")\n      }\n      defines.allowToChangeInstallationDirectory = null\n    }\n\n    const commonOptions = getEffectiveOptions(options, packager)\n\n    if (commonOptions.menuCategory != null) {\n      defines.MENU_FILENAME = commonOptions.menuCategory\n    }\n\n    defines.SHORTCUT_NAME = commonOptions.shortcutName\n\n    if (options.deleteAppDataOnUninstall) {\n      defines.DELETE_APP_DATA_ON_UNINSTALL = null\n    }\n\n    asyncTaskManager.add(async () => {\n      const uninstallerIcon = await packager.getResource(options.uninstallerIcon, \"uninstallerIcon.ico\")\n      if (uninstallerIcon != null) {\n        // we don't need to copy MUI_UNICON (defaults to app icon), so, we have 2 defines\n        defines.UNINSTALLER_ICON = uninstallerIcon\n        defines.MUI_UNICON = uninstallerIcon\n      }\n    })\n\n    defines.UNINSTALL_DISPLAY_NAME = packager.expandMacro(options.uninstallDisplayName || \"${productName} ${version}\", null, {}, false)\n    if (commonOptions.isCreateDesktopShortcut === DesktopShortcutCreationPolicy.NEVER) {\n      defines.DO_NOT_CREATE_DESKTOP_SHORTCUT = null\n    }\n    if (commonOptions.isCreateDesktopShortcut === DesktopShortcutCreationPolicy.ALWAYS) {\n      defines.RECREATE_DESKTOP_SHORTCUT = null\n    }\n    if (!commonOptions.isCreateStartMenuShortcut) {\n      defines.DO_NOT_CREATE_START_MENU_SHORTCUT = null\n    }\n\n    if (options.displayLanguageSelector === true) {\n      defines.DISPLAY_LANG_SELECTOR = null\n    }\n\n    return asyncTaskManager.awaitTasks()\n  }\n\n  private configureDefinesForAllTypeOfInstaller(defines: any) {\n    const appInfo = this.packager.appInfo\n    const companyName = appInfo.companyName\n    if (companyName != null) {\n      defines.COMPANY_NAME = companyName\n    }\n\n    // electron uses product file name as app data, define it as well to remove on uninstall\n    if (defines.APP_FILENAME !== appInfo.productFilename) {\n      defines.APP_PRODUCT_FILENAME = appInfo.productFilename\n    }\n\n    if (this.isWebInstaller) {\n      defines.APP_PACKAGE_STORE_FILE = `${appInfo.updaterCacheDirName}\\\\${CURRENT_APP_PACKAGE_FILE_NAME}`\n    }\n    else {\n      defines.APP_INSTALLER_STORE_FILE = `${appInfo.updaterCacheDirName}\\\\${CURRENT_APP_INSTALLER_FILE_NAME}`\n    }\n\n    if (!this.isWebInstaller && defines.APP_BUILD_DIR == null) {\n      const options = this.options\n      if (options.useZip) {\n        defines.ZIP_COMPRESSION = null\n      }\n\n      defines.COMPRESSION_METHOD = options.useZip ? \"zip\" : \"7z\"\n    }\n  }\n\n  private async executeMakensis(defines: any, commands: any, script: string) {\n    const args: Array<string> = (this.options.warningsAsErrors === false) ? [] : [\"-WX\"]\n    for (const name of Object.keys(defines)) {\n      const value = defines[name]\n      if (value == null) {\n        args.push(`-D${name}`)\n      }\n      else {\n        args.push(`-D${name}=${value}`)\n      }\n    }\n\n    for (const name of Object.keys(commands)) {\n      const value = commands[name]\n      if (Array.isArray(value)) {\n        for (const c of value) {\n          args.push(`-X${name} ${c}`)\n        }\n      }\n      else {\n        args.push(`-X${name} ${value}`)\n      }\n    }\n\n    args.push(\"-\")\n\n    if (this.packager.debugLogger.isEnabled) {\n      this.packager.debugLogger.add(\"nsis.script\", script)\n    }\n\n    const nsisPath = await NSIS_PATH()\n    const command = path.join(nsisPath, process.platform === \"darwin\" ? \"mac\" : (process.platform === \"win32\" ? \"Bin\" : \"linux\"), process.platform === \"win32\" ? \"makensis.exe\" : \"makensis\")\n\n    // if (process.platform === \"win32\") {\n      // fix for an issue caused by virus scanners, locking the file during write\n      // https://github.com/electron-userland/electron-builder/issues/5005\n      await ensureNotBusy(commands[\"OutFile\"].replace(/\"/g, \"\"))\n    // }\n\n    await spawnAndWrite(command, args, script, {\n      // we use NSIS_CONFIG_CONST_DATA_PATH=no to build makensis on Linux, but in any case it doesn't use stubs as MacOS/Windows version, so, we explicitly set NSISDIR\n      env: {...process.env, NSISDIR: nsisPath},\n      cwd: nsisTemplatesDir,\n    })\n  }\n\n  private async computeCommonInstallerScriptHeader() {\n    const packager = this.packager\n    const options = this.options\n    const scriptGenerator = new NsisScriptGenerator()\n    const langConfigurator = new LangConfigurator(options)\n\n    scriptGenerator.include(path.join(nsisTemplatesDir, \"include\", \"StdUtils.nsh\"))\n\n    const includeDir = path.join(nsisTemplatesDir, \"include\")\n    scriptGenerator.addIncludeDir(includeDir)\n    scriptGenerator.flags([\"updated\", \"force-run\", \"keep-shortcuts\", \"no-desktop-shortcut\", \"delete-app-data\", \"allusers\", \"currentuser\"])\n\n    createAddLangsMacro(scriptGenerator, langConfigurator)\n\n    const taskManager = new AsyncTaskManager(packager.info.cancellationToken)\n\n    const pluginArch = this.isUnicodeEnabled ? \"x86-unicode\" : \"x86-ansi\"\n    taskManager.add(async () => {\n      scriptGenerator.addPluginDir(pluginArch, path.join(await nsisResourcePathPromise(), \"plugins\", pluginArch))\n    })\n\n    taskManager.add(async () => {\n      const userPluginDir = path.join(packager.info.buildResourcesDir, pluginArch)\n      const stat = await statOrNull(userPluginDir)\n      if (stat != null && stat.isDirectory()) {\n        scriptGenerator.addPluginDir(pluginArch, userPluginDir)\n      }\n    })\n\n    taskManager.addTask(addCustomMessageFileInclude(\"messages.yml\", packager, scriptGenerator, langConfigurator))\n\n    if (!this.isPortable) {\n      if (options.oneClick === false) {\n        taskManager.addTask(addCustomMessageFileInclude(\"assistedMessages.yml\", packager, scriptGenerator, langConfigurator))\n      }\n\n      taskManager.add(async () => {\n        const customInclude = await packager.getResource(this.options.include, \"installer.nsh\")\n        if (customInclude != null) {\n          scriptGenerator.addIncludeDir(packager.info.buildResourcesDir)\n          scriptGenerator.include(customInclude)\n        }\n      })\n    }\n\n    await taskManager.awaitTasks()\n    return scriptGenerator.build()\n  }\n\n  private async computeFinalScript(originalScript: string, isInstaller: boolean) {\n    const packager = this.packager\n    const options = this.options\n    const langConfigurator = new LangConfigurator(options)\n\n    const scriptGenerator = new NsisScriptGenerator()\n    const taskManager = new AsyncTaskManager(packager.info.cancellationToken)\n\n    if (isInstaller) {\n      // http://stackoverflow.com/questions/997456/nsis-license-file-based-on-language-selection\n      taskManager.add(() => computeLicensePage(packager, options, scriptGenerator, langConfigurator.langs))\n    }\n\n    await taskManager.awaitTasks()\n\n    if (this.isPortable) {\n      return scriptGenerator.build() + originalScript\n    }\n\n    const preCompressedFileExtensions = this.getPreCompressedFileExtensions()\n    if (preCompressedFileExtensions != null && preCompressedFileExtensions.length !== 0) {\n      for (const [arch, dir] of this.archs.entries()) {\n        await generateForPreCompressed(preCompressedFileExtensions, dir, arch, scriptGenerator)\n      }\n    }\n\n    const fileAssociations = packager.fileAssociations\n    if (fileAssociations.length !== 0) {\n\n      scriptGenerator.include(path.join(path.join(nsisTemplatesDir, \"include\"), \"FileAssociation.nsh\"))\n      if (isInstaller) {\n        const registerFileAssociationsScript = new NsisScriptGenerator()\n        for (const item of fileAssociations) {\n          const extensions = asArray(item.ext).map(normalizeExt)\n          for (const ext of extensions) {\n            const customIcon = await packager.getResource(getPlatformIconFileName(item.icon, false), `${extensions[0]}.ico`)\n            let installedIconPath = \"$appExe,0\"\n            if (customIcon != null) {\n              installedIconPath = `$INSTDIR\\\\resources\\\\${path.basename(customIcon)}`\n              registerFileAssociationsScript.file(installedIconPath, customIcon)\n            }\n\n            const icon = `\"${installedIconPath}\"`\n            const commandText = `\"Open with ${packager.appInfo.productName}\"`\n            const command = '\"$appExe $\\\\\"%1$\\\\\"\"'\n            registerFileAssociationsScript.insertMacro(\"APP_ASSOCIATE\", `\"${ext}\" \"${item.name || ext}\" \"${item.description || \"\"}\" ${icon} ${commandText} ${command}`)\n          }\n        }\n        scriptGenerator.macro(\"registerFileAssociations\", registerFileAssociationsScript)\n      }\n      else {\n        const unregisterFileAssociationsScript = new NsisScriptGenerator()\n        for (const item of fileAssociations) {\n          for (const ext of asArray(item.ext)) {\n            unregisterFileAssociationsScript.insertMacro(\"APP_UNASSOCIATE\", `\"${normalizeExt(ext)}\" \"${item.name || ext}\"`)\n          }\n        }\n        scriptGenerator.macro(\"unregisterFileAssociations\", unregisterFileAssociationsScript)\n      }\n    }\n\n    return scriptGenerator.build() + originalScript\n  }\n}\n\nasync function generateForPreCompressed(preCompressedFileExtensions: Array<string>, dir: string, arch: Arch, scriptGenerator: NsisScriptGenerator) {\n  const resourcesDir = path.join(dir, \"resources\")\n  const dirInfo = await statOrNull(resourcesDir)\n  if (dirInfo == null || !dirInfo.isDirectory()) {\n    return\n  }\n\n  const nodeModules = `${path.sep}node_modules`\n  const preCompressedAssets = await walk(resourcesDir, (file, stat) => {\n    if (stat.isDirectory()) {\n      return !file.endsWith(nodeModules)\n    }\n    else {\n      return preCompressedFileExtensions.some(it => file.endsWith(it))\n    }\n  })\n\n  if (preCompressedAssets.length !== 0) {\n    const macro = new NsisScriptGenerator()\n    for (const file of preCompressedAssets) {\n      macro.file(`$INSTDIR\\\\${path.relative(dir, file).replace(/\\//g, \"\\\\\")}`, file)\n    }\n    scriptGenerator.macro(`customFiles_${Arch[arch]}`, macro)\n  }\n}\n\nasync function ensureNotBusy(outFile: string) {\n  function isBusy(wasBusyBefore: boolean): Promise<boolean> {\n    return new Promise((resolve, reject) => {\n      fs.open(outFile, \"r+\", (error, fd) => {\n        try {\n          if (error != null && error.code === \"EBUSY\") {\n            if (!wasBusyBefore) {\n              log.info({}, \"output file is locked for writing (maybe by virus scanner) => waiting for unlock...\")\n            }\n            resolve(false)\n          }\n          else if (fd == null) {\n            resolve(true)\n          }\n          else {\n            fs.close(fd, () => resolve(true))\n          }\n        }\n        catch (error) {\n          reject(error)\n        }\n      })\n    })\n      .then(result => {\n        if (result) {\n          return true\n        }\n        else {\n          return new Promise((resolve) => setTimeout(resolve, 2000))\n            .then(() => isBusy(true))\n        }\n      })\n  }\n\n  await isBusy(false)\n}\n\nasync function createPackageFileInfo(file: string): Promise<PackageFileInfo> {\n  return {\n    path: file,\n    size: (await stat(file)).size,\n    sha512: await hashFile(file),\n  }\n}\n"], "sourceRoot": ""}