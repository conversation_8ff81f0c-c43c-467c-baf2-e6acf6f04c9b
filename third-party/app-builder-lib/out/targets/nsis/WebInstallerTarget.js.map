{"version": 3, "sources": ["../../../src/targets/nsis/WebInstallerTarget.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAGA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAGA;AACM,MAAO,kBAAP,SAAkC,wBAAlC,CAA4C;AAChD,EAAA,WAAA,CAAY,QAAZ,EAAmC,MAAnC,EAAmD,UAAnD,EAAuE,aAAvE,EAAsG;AACpG,UAAM,QAAN,EAAgB,MAAhB,EAAwB,UAAxB,EAAoC,aAApC;AACD;;AAED,MAAI,cAAJ,GAAkB;AAChB,WAAO,IAAP;AACD;;AAES,QAAM,gBAAN,CAAuB,QAAvB,EAA0C,OAA1C,EAAsD;AAC9D;AACA,UAAO,yBAAW,SAAX,CAA4C,gBAA5C,CAA6D,IAA7D,CAAkE,IAAlE,EAAwE,QAAxE,EAAkF,OAAlF,CAAP;AAEA,UAAM,QAAQ,GAAG,KAAK,QAAtB;AACA,UAAM,OAAO,GAAG,KAAK,OAArB;AAEA,QAAI,aAAa,GAAG,OAAO,CAAC,aAA5B;;AACA,QAAI,aAAa,IAAI,IAArB,EAA2B;AACzB,YAAM,cAAc,GAAG,MAAM,sDAA+B,QAA/B,EAAyC,MAAM,yCAAkB,QAAlB,EAA4B,QAAQ,CAAC,IAAT,CAAc,MAA1C,EAAkD,IAAlD,EAAwD,KAAxD,CAA/C,EAA+G,IAA/G,CAA7B;;AACA,UAAI,cAAc,IAAI,IAAlB,IAA0B,cAAc,CAAC,MAAf,KAA0B,CAAxD,EAA2D;AACzD,cAAM,IAAI,KAAJ,CAAU,yCAAV,CAAN;AACD;;AAED,MAAA,aAAa,GAAG,0CAAmB,cAAc,CAAC,CAAD,CAAjC,EAAsC,IAAtC,EAA4C,QAA5C,CAAhB;AAEA,MAAA,OAAO,CAAC,4BAAR,GAAuC,IAAvC;AACD;;AAED,IAAA,OAAO,CAAC,eAAR,GAA0B,aAA1B;AACD;;AAED,MAAc,wBAAd,GAAsC;AACpC;AACA,WAAO,4CAAP;AACD;;AAES,EAAA,2BAA2B,GAAA;AACnC,UAAM,OAAO,GAAG,KAAK,QAAL,CAAc,OAA9B;AACA,UAAM,UAAU,GAAG,OAAO,CAAC,IAAR,CAAa,WAAb,OAA+B,OAAO,CAAC,IAAvC,GAA8C,WAA9C,GAA4D,UAA/E;AACA,WAAO,GAAG,OAAO,CAAC,IAAI,IAAI,UAAU,IAAI,OAAO,CAAC,OAAO,MAAvD;AACD;;AAxC+C,C", "sourcesContent": ["import { computeDownloadUrl, getPublishConfigs, getPublishConfigsForUpdateInfo } from \"../../publish/PublishManager\"\nimport { WinPackager } from \"../../winPackager\"\nimport { NsisWebOptions } from \"./nsisOptions\"\nimport { NsisTarget } from \"./NsisTarget\"\nimport { AppPackageHelper } from \"./nsisUtil\"\n\n/** @private */\nexport class WebInstallerTarget extends NsisTarget {\n  constructor(packager: WinPackager, outDir: string, targetName: string, packageHelper: AppPackageHelper) {\n    super(packager, outDir, targetName, packageHelper)\n  }\n\n  get isWebInstaller(): boolean {\n    return true\n  }\n\n  protected async configureDefines(oneClick: boolean, defines: any): Promise<any> {\n    //noinspection ES6MissingAwait\n    await (NsisTarget.prototype as WebInstallerTarget).configureDefines.call(this, oneClick, defines)\n\n    const packager = this.packager\n    const options = this.options as NsisWebOptions\n\n    let appPackageUrl = options.appPackageUrl\n    if (appPackageUrl == null) {\n      const publishConfigs = await getPublishConfigsForUpdateInfo(packager, await getPublishConfigs(packager, packager.info.config, null, false), null)\n      if (publishConfigs == null || publishConfigs.length === 0) {\n        throw new Error(\"Cannot compute app package download URL\")\n      }\n\n      appPackageUrl = computeDownloadUrl(publishConfigs[0], null, packager)\n\n      defines.APP_PACKAGE_URL_IS_INCOMLETE = null\n    }\n\n    defines.APP_PACKAGE_URL = appPackageUrl\n  }\n\n  protected get installerFilenamePattern(): string {\n    // tslint:disable:no-invalid-template-strings\n    return \"${productName} Web Setup ${version}.${ext}\"\n  }\n\n  protected generateGitHubInstallerName(): string {\n    const appInfo = this.packager.appInfo\n    const classifier = appInfo.name.toLowerCase() === appInfo.name ? \"web-setup\" : \"WebSetup\"\n    return `${appInfo.name}-${classifier}-${appInfo.version}.exe`\n  }\n}"], "sourceRoot": ""}