"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.computeLicensePage = computeLicensePage;

function _langs() {
  const data = require("../../util/langs");

  _langs = function () {
    return data;
  };

  return data;
}

function _license() {
  const data = require("../../util/license");

  _license = function () {
    return data;
  };

  return data;
}

var path = _interopRequireWildcard(require("path"));

function _nsisUtil() {
  const data = require("./nsisUtil");

  _nsisUtil = function () {
    return data;
  };

  return data;
}

function _getRequireWildcardCache() { if (typeof WeakMap !== "function") return null; var cache = new WeakMap(); _getRequireWildcardCache = function () { return cache; }; return cache; }

function _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }

async function computeLicensePage(packager, options, scriptGenerator, languages) {
  const license = await (0, _license().getNotLocalizedLicenseFile)(options.license, packager);

  if (license != null) {
    let licensePage;

    if (license.endsWith(".html")) {
      licensePage = ["!define MUI_PAGE_CUSTOMFUNCTION_SHOW LicenseShow", "Function LicenseShow", "  FindWindow $R0 `#32770` `` $HWNDPARENT", "  GetDlgItem $R0 $R0 1000", "EmbedHTML::Load /replace $R0 file://$PLUGINSDIR\\license.html", "FunctionEnd", `!insertmacro MUI_PAGE_LICENSE "${path.join(_nsisUtil().nsisTemplatesDir, "empty-license.txt")}"`];
    } else {
      licensePage = [`!insertmacro MUI_PAGE_LICENSE "${license}"`];
    }

    scriptGenerator.macro("licensePage", licensePage);

    if (license.endsWith(".html")) {
      scriptGenerator.macro("addLicenseFiles", [`File /oname=$PLUGINSDIR\\license.html "${license}"`]);
    }

    return;
  }

  const licenseFiles = await (0, _license().getLicenseFiles)(packager);

  if (licenseFiles.length === 0) {
    return;
  }

  const licensePage = [];
  const unspecifiedLangs = new Set(languages);
  let defaultFile = null;

  for (const item of licenseFiles) {
    unspecifiedLangs.delete(item.langWithRegion);

    if (defaultFile == null) {
      defaultFile = item.file;
    }

    licensePage.push(`LicenseLangString MUILicense ${_langs().lcid[item.langWithRegion] || item.lang} "${item.file}"`);
  }

  for (const l of unspecifiedLangs) {
    licensePage.push(`LicenseLangString MUILicense ${_langs().lcid[l]} "${defaultFile}"`);
  }

  licensePage.push('!insertmacro MUI_PAGE_LICENSE "$(MUILicense)"');
  scriptGenerator.macro("licensePage", licensePage);
} 
// __ts-babel@6.0.4
//# sourceMappingURL=nsisLicense.js.map