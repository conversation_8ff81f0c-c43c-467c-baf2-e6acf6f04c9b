{"version": 3, "sources": ["../../src/targets/tools.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEM,SAAU,iBAAV,GAA2B;AAC/B;AACA,SAAO,kCAAc,aAAd,EAA6B,aAA7B,EAA4C,0FAA5C,CAAP;AACD,C", "sourcesContent": ["import { getBinFromUrl } from \"../binDownload\"\n\nexport function getLinuxToolsPath() {\n  //noinspection SpellCheckingInspection\n  return getBinFromUrl(\"linux-tools\", \"mac-10.12.3\", \"SQ8fqIRVXuQVWnVgaMTDWyf2TLAJjJYw3tRSqQJECmgF6qdM7Kogfa6KD49RbGzzMYIFca9Uw3MdsxzOPRWcYw==\")\n}"], "sourceRoot": ""}