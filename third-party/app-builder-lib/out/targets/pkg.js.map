{"version": 3, "sources": ["../../src/targets/pkg.ts"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;AAGA,MAAM,QAAQ,GAAG,wBAAjB,C,CAEA;AACA;AACA;AACA;;AACM,MAAO,SAAP,SAAyB,cAAzB,CAA+B;AAQnC,EAAA,WAAA,CAA6B,QAA7B,EAA6D,MAA7D,EAA2E;AACzE,UAAM,KAAN;AAD2B,SAAA,QAAA,GAAA,QAAA;AAAgC,SAAA,MAAA,GAAA,MAAA;AAPpD,SAAA,OAAA,GAAsB;AAC7B,MAAA,aAAa,EAAE,IADc;AAE7B,MAAA,oBAAoB,EAAE,IAFO;AAG7B,MAAA,kBAAkB,EAAE,IAHS;AAI7B,SAAG,KAAK,QAAL,CAAc,MAAd,CAAqB;AAJK,KAAtB;AASR;;AAED,QAAM,KAAN,CAAY,OAAZ,EAA6B,IAA7B,EAAuC;AACrC,UAAM,QAAQ,GAAG,KAAK,QAAtB;AACA,UAAM,OAAO,GAAG,KAAK,OAArB;AACA,UAAM,OAAO,GAAG,QAAQ,CAAC,OAAzB,CAHqC,CAKrC;;AACA,UAAM,YAAY,GAAG,QAAQ,CAAC,yBAAT,CAAmC,OAAnC,EAA4C,KAA5C,CAArB;AACA,UAAM,YAAY,GAAG,IAAI,CAAC,IAAL,CAAU,KAAK,MAAf,EAAuB,YAAvB,CAArB;AAEA,UAAM,QAAQ,CAAC,IAAT,CAAc,wBAAd,CAAuC;AAC3C,MAAA,qBAAqB,EAAE,KADoB;AAE3C,MAAA,IAAI,EAAE,YAFqC;AAG3C,MAAA;AAH2C,KAAvC,CAAN;AAMA,UAAM,YAAY,GAAG,CAAC,MAAM,QAAQ,CAAC,eAAT,CAAyB,KAAhC,EAAuC,YAA5D;AAEA,UAAM,SAAS,GAAG,KAAK,MAAvB,CAjBqC,CAkBrC;;AACA,UAAM,YAAY,GAAG,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,kBAArB,CAArB;AAEA,UAAM,gBAAgB,GAAG,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,GAAG,yCAAyB,OAAO,CAAC,EAAjC,CAAoC,MAA5D,CAAzB;AACA,UAAM,yBAAyB,GAAG,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,GAAG,yCAAyB,OAAO,CAAC,EAAjC,CAAoC,QAA5D,CAAlC;AACA,UAAM,QAAQ,GAAG,CAAC,MAAM,OAAO,CAAC,GAAR,CAAY,CAClC,iCAAa,QAAb,EAAuB,OAAO,CAAC,QAAR,IAAoB,QAAQ,CAAC,4BAAT,CAAsC,QAAjF,EAA2F,YAA3F,CADkC,EAElC,KAAK,kCAAL,CAAwC,YAAxC,EAAsD,OAAtD,CAFkC,EAGlC,KAAK,qBAAL,CAA2B,OAA3B,EAAoC,yBAApC,EAA+D,gBAA/D,CAHkC,CAAZ,CAAP,EAIb,CAJa,CAAjB;;AAMA,QAAI,QAAQ,IAAI,IAAZ,IAAoB,QAAQ,CAAC,gBAAjC,EAAmD;AACjD,YAAM,IAAI,KAAJ,CAAU,sBAAsB,QAAQ,gFAAxC,CAAN;AACD;;AAED,UAAM,IAAI,GAAG,uBAAuB,CAAC,QAAD,EAAW,YAAX,CAApC;AACA,IAAA,IAAI,CAAC,IAAL,CAAU,gBAAV,EAA4B,YAA5B;AACA,IAAA,IAAI,CAAC,IAAL,CAAU,YAAV;AACA,4BAAI,OAAO,CAAC,YAAZ,EAA0B,EAAE,IAAI,IAAI,CAAC,IAAL,CAAU,GAAG,EAAb,CAAhC;AACA,UAAM,yBAAK,cAAL,EAAqB,IAArB,EAA2B;AAC/B,MAAA,GAAG,EAAE;AAD0B,KAA3B,CAAN;AAGA,UAAM,OAAO,CAAC,GAAR,CAAY,CAAC,uBAAO,gBAAP,CAAD,EAA2B,uBAAO,YAAP,CAA3B,CAAZ,CAAN;AAEA,UAAM,QAAQ,CAAC,uBAAT,CAAiC,YAAjC,EAA+C,IAA/C,EAAqD,IAArD,EAA2D,QAAQ,CAAC,uBAAT,CAAiC,YAAjC,EAA+C,KAA/C,EAAsD,IAAtD,CAA3D,CAAN;AACD;;AAEO,QAAM,kCAAN,CAAyC,YAAzC,EAA+D,OAA/D,EAA8E;AACpF,UAAM,yBAAK,cAAL,EAAqB,CAAC,cAAD,EAAiB,aAAjB,EAAgC,OAAhC,EAAyC,YAAzC,CAArB,EAA6E;AACjF,MAAA,GAAG,EAAE,KAAK;AADuE,KAA7E,CAAN;AAIA,UAAM,OAAO,GAAG,KAAK,OAArB;AACA,QAAI,QAAQ,GAAG,MAAM,yBAAS,YAAT,EAAuB,OAAvB,CAArB;;AAEA,QAAI,OAAO,CAAC,SAAR,IAAqB,IAArB,IAA6B,OAAO,CAAC,SAAR,CAAkB,MAAlB,KAA6B,CAA9D,EAAiE;AAC/D,YAAM,YAAY,GAAG,oBAAoB,KAAK,QAAL,CAAc,OAAd,CAAsB,EAAE,4BAAjE;AACA,YAAM,UAAU,GAAG,gEAAnB;AACA,UAAI,gBAAgB,GAAG,EAAvB;AACA,MAAA,OAAO,CAAC,SAAR,CAAkB,OAAlB,CAA0B,KAAK,IAAG;AAChC,QAAA,gBAAgB,IAAI,wBAAwB,KAAK,OAAjD;AACD,OAFD;AAGA,MAAA,QAAQ,GAAG,QAAQ,CAAC,OAAT,CAAiB,yBAAjB,EAA4C,GAAG,YAAY,GAAG,gBAAgB,GAAG,UAAU,EAA3F,CAAX;AACD;;AAED,UAAM,WAAW,GAAG,QAAQ,CAAC,WAAT,CAAqB,yBAArB,CAApB;AACA,IAAA,QAAQ,GAAG,QAAQ,CAAC,SAAT,CAAmB,CAAnB,EAAsB,WAAtB,IAAqC,iCAAiC,OAAO,CAAC,aAAa,6BAA6B,OAAO,CAAC,oBAAoB,yBAAyB,OAAO,CAAC,kBAAkB,QAAvM,GAAkN,QAAQ,CAAC,SAAT,CAAmB,WAAnB,CAA7N;;AAEA,QAAI,OAAO,CAAC,UAAR,IAAsB,IAA1B,EAAgC;AAC9B,YAAM,UAAU,GAAG,MAAM,KAAK,QAAL,CAAc,WAAd,CAA0B,OAAO,CAAC,UAAR,CAAmB,IAA7C,CAAzB;;AACA,UAAI,UAAU,IAAI,IAAlB,EAAwB;AACtB,cAAM,SAAS,GAAG,OAAO,CAAC,UAAR,CAAmB,SAAnB,IAAgC,QAAlD,CADsB,CAEtB;;AACA,cAAM,OAAO,GAAG,OAAO,CAAC,UAAR,CAAmB,OAAnB,IAA8B,OAA9C;AACA,QAAA,QAAQ,GAAG,QAAQ,CAAC,SAAT,CAAmB,CAAnB,EAAsB,WAAtB,IAAqC,yBAAyB,UAAU,gBAAgB,SAAS,cAAc,OAAO,OAAtH,GAAgI,QAAQ,CAAC,SAAT,CAAmB,WAAnB,CAA3I;AACA,QAAA,QAAQ,GAAG,QAAQ,CAAC,SAAT,CAAmB,CAAnB,EAAsB,WAAtB,IAAqC,kCAAkC,UAAU,gBAAgB,SAAS,cAAc,OAAO,OAA/H,GAAyI,QAAQ,CAAC,SAAT,CAAmB,WAAnB,CAApJ;AACD;AACF;;AAED,UAAM,OAAO,GAAG,MAAM,KAAK,QAAL,CAAc,WAAd,CAA0B,OAAO,CAAC,OAAlC,CAAtB;;AACA,QAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,MAAA,QAAQ,GAAG,QAAQ,CAAC,SAAT,CAAmB,CAAnB,EAAsB,WAAtB,IAAqC,sBAAsB,OAAO,OAAlE,GAA4E,QAAQ,CAAC,SAAT,CAAmB,WAAnB,CAAvF;AACD;;AAED,UAAM,OAAO,GAAG,MAAM,2CAA2B,OAAO,CAAC,OAAnC,EAA4C,KAAK,QAAjD,CAAtB;;AACA,QAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,MAAA,QAAQ,GAAG,QAAQ,CAAC,SAAT,CAAmB,CAAnB,EAAsB,WAAtB,IAAqC,sBAAsB,OAAO,OAAlE,GAA4E,QAAQ,CAAC,SAAT,CAAmB,WAAnB,CAAvF;AACD;;AAED,UAAM,UAAU,GAAG,MAAM,KAAK,QAAL,CAAc,WAAd,CAA0B,OAAO,CAAC,UAAlC,CAAzB;;AACA,QAAI,UAAU,IAAI,IAAlB,EAAwB;AACtB,MAAA,QAAQ,GAAG,QAAQ,CAAC,SAAT,CAAmB,CAAnB,EAAsB,WAAtB,IAAqC,yBAAyB,UAAU,OAAxE,GAAkF,QAAQ,CAAC,SAAT,CAAmB,WAAnB,CAA7F;AACD;;AAED,8BAAM,QAAN;AACA,UAAM,0BAAU,YAAV,EAAwB,QAAxB,CAAN;AACD;;AAEO,QAAM,qBAAN,CAA4B,OAA5B,EAA6C,sBAA7C,EAA6E,iBAA7E,EAAsG;AAC5G,UAAM,OAAO,GAAG,KAAK,OAArB;AACA,UAAM,QAAQ,GAAG,IAAI,CAAC,OAAL,CAAa,OAAb,CAAjB,CAF4G,CAI5G;;AACA,UAAM,yBAAK,UAAL,EAAiB,CACrB,WADqB,EAErB,QAFqB,EAEX,QAFW,EAGrB,sBAHqB,CAAjB,CAAN,CAL4G,CAW5G;;AACA,UAAM,SAAS,GAAG,CAAC,MAAM,2CAAoC,CAAC,cAAD,EAAiB,IAAjB,EAAuB,sBAAvB,CAApC,CAAP,EAA4F,CAA5F,EAA+F,MAA/F,CAAuG,EAAD,IAAa,EAAE,CAAC,sBAAH,KAA8B,eAAjJ,CAAlB;;AACA,QAAI,SAAS,CAAC,MAAV,GAAmB,CAAvB,EAA0B;AACxB,YAAM,WAAW,GAAG,SAAS,CAAC,CAAD,CAA7B,CADwB,CAGxB;AACA;AACA;;AACA,aAAO,WAAW,CAAC,YAAnB;;AAEA,UAAI,OAAO,CAAC,aAAR,IAAyB,IAA7B,EAAmC;AACjC,QAAA,WAAW,CAAC,mBAAZ,GAAkC,OAAO,CAAC,aAA1C;AACD;;AAED,UAAI,OAAO,CAAC,gBAAR,IAA4B,IAAhC,EAAsC;AACpC,QAAA,WAAW,CAAC,sBAAZ,GAAqC,OAAO,CAAC,gBAA7C;AACD;;AAED,UAAI,OAAO,CAAC,mBAAR,IAA+B,IAAnC,EAAyC;AACvC,QAAA,WAAW,CAAC,yBAAZ,GAAwC,OAAO,CAAC,mBAAhD;AACD;;AAED,UAAI,OAAO,CAAC,eAAR,IAA2B,IAA/B,EAAqC;AACnC,QAAA,WAAW,CAAC,qBAAZ,GAAoC,OAAO,CAAC,eAA5C;AACD;;AAED,YAAM,iDAA8B,CAAC,cAAD,CAA9B,EAAgD;AAAC,SAAC,sBAAD,GAA0B;AAA3B,OAAhD,CAAN;AACD,KAtC2G,CAwC5G;;;AACA,UAAM,IAAI,GAAG,CACX,QADW,EACD,QADC,EAEX;AACA,uBAHW,EAGU,sBAHV,CAAb;AAMA,4BAAI,KAAK,OAAL,CAAa,eAAb,IAAgC,eAApC,EAAqD,EAAE,IAAI,IAAI,CAAC,IAAL,CAAU,oBAAV,EAAgC,EAAhC,CAA3D;;AACA,QAAI,OAAO,CAAC,OAAR,IAAmB,IAAvB,EAA6B;AAC3B,MAAA,IAAI,CAAC,IAAL,CAAU,WAAV,EAAuB,IAAI,CAAC,OAAL,CAAa,KAAK,QAAL,CAAc,IAAd,CAAmB,iBAAhC,EAAmD,OAAO,CAAC,OAA3D,CAAvB;AACD,KAFD,MAGK,IAAI,OAAO,CAAC,OAAR,KAAoB,IAAxB,EAA8B;AACjC,YAAM,GAAG,GAAG,IAAI,CAAC,IAAL,CAAU,KAAK,QAAL,CAAc,IAAd,CAAmB,iBAA7B,EAAgD,aAAhD,CAAZ;AACA,YAAM,IAAI,GAAG,MAAM,sBAAW,GAAX,CAAnB;;AACA,UAAI,IAAI,IAAI,IAAR,IAAgB,IAAI,CAAC,WAAL,EAApB,EAAwC;AACtC,QAAA,IAAI,CAAC,IAAL,CAAU,WAAV,EAAuB,GAAvB;AACD;AACF;;AAED,IAAA,IAAI,CAAC,IAAL,CAAU,iBAAV;AAEA,UAAM,yBAAK,UAAL,EAAiB,IAAjB,CAAN;AACD;;AA1KkC;;;;AA6K/B,SAAU,uBAAV,CAAkC,QAAlC,EAA6D,QAA7D,EAAgG;AACpG,QAAM,IAAI,GAAkB,EAA5B;;AACA,MAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,IAAA,IAAI,CAAC,IAAL,CAAU,QAAV,EAAoB,QAAQ,CAAC,IAA7B;;AACA,QAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,MAAA,IAAI,CAAC,IAAL,CAAU,YAAV,EAAwB,QAAxB;AACD;AACF;;AACD,SAAO,IAAP;AACD,C", "sourcesContent": ["import { Arch, debug, exec, use } from \"builder-util\"\nimport { statOrNull } from \"builder-util/out/fs\"\nimport { executeAppBuilderAndWrite<PERSON>son, executeAppBuilderAsJson } from \"../util/appBuilder\"\nimport { getNotLocalizedLicenseFile } from \"../util/license\"\nimport { readFile, unlink, writeFile } from \"fs-extra\"\nimport * as path from \"path\"\nimport { PkgOptions } from \"..\"\nimport { filterCFBundleIdentifier } from \"../appInfo\"\nimport { findIdentity, Identity } from \"../codeSign/macCodeSign\"\nimport { Target } from \"../core\"\nimport MacPackager from \"../macPackager\"\n\nconst certType = \"Developer ID Installer\"\n\n// http://www.shanekirk.com/2013/10/creating-flat-packages-in-osx/\n// to use --scripts, we must build .app bundle separately using pkgbuild\n// productbuild --scripts doesn't work (because scripts in this case not added to our package)\n// https://github.com/electron-userland/electron-osx-sign/issues/96#issuecomment-274986942\nexport class PkgTarget extends Target {\n  readonly options: PkgOptions = {\n    allowAnywhere: true,\n    allowCurrentUserHome: true,\n    allowRootDirectory: true,\n    ...this.packager.config.pkg,\n  }\n\n  constructor(private readonly packager: MacPackager, readonly outDir: string) {\n    super(\"pkg\")\n  }\n\n  async build(appPath: string, arch: Arch): Promise<any> {\n    const packager = this.packager\n    const options = this.options\n    const appInfo = packager.appInfo\n\n    // pkg doesn't like not ASCII symbols (Could not open package to list files: /Volumes/test/t-gIjdGK/test-project-0/dist/Test App ßW-1.1.0.pkg)\n    const artifactName = packager.expandArtifactNamePattern(options, \"pkg\")\n    const artifactPath = path.join(this.outDir, artifactName)\n\n    await packager.info.callArtifactBuildStarted({\n      targetPresentableName: \"pkg\",\n      file: artifactPath,\n      arch,\n    })\n\n    const keychainFile = (await packager.codeSigningInfo.value).keychainFile\n\n    const appOutDir = this.outDir\n    // https://developer.apple.com/library/content/documentation/DeveloperTools/Reference/DistributionDefinitionRef/Chapters/Distribution_XML_Ref.html\n    const distInfoFile = path.join(appOutDir, \"distribution.xml\")\n\n    const innerPackageFile = path.join(appOutDir, `${filterCFBundleIdentifier(appInfo.id)}.pkg`)\n    const componentPropertyListFile = path.join(appOutDir, `${filterCFBundleIdentifier(appInfo.id)}.plist`)\n    const identity = (await Promise.all([\n      findIdentity(certType, options.identity || packager.platformSpecificBuildOptions.identity, keychainFile),\n      this.customizeDistributionConfiguration(distInfoFile, appPath),\n      this.buildComponentPackage(appPath, componentPropertyListFile, innerPackageFile),\n    ]))[0]\n\n    if (identity == null && packager.forceCodeSigning) {\n      throw new Error(`Cannot find valid \"${certType}\" to sign standalone installer, please see https://electron.build/code-signing`)\n    }\n\n    const args = prepareProductBuildArgs(identity, keychainFile)\n    args.push(\"--distribution\", distInfoFile)\n    args.push(artifactPath)\n    use(options.productbuild, it => args.push(...it as any))\n    await exec(\"productbuild\", args, {\n      cwd: appOutDir,\n    })\n    await Promise.all([unlink(innerPackageFile), unlink(distInfoFile)])\n\n    await packager.dispatchArtifactCreated(artifactPath, this, arch, packager.computeSafeArtifactName(artifactName, \"pkg\", arch))\n  }\n\n  private async customizeDistributionConfiguration(distInfoFile: string, appPath: string) {\n    await exec(\"productbuild\", [\"--synthesize\", \"--component\", appPath, distInfoFile], {\n      cwd: this.outDir,\n    })\n\n    const options = this.options\n    let distInfo = await readFile(distInfoFile, \"utf-8\")\n\n    if (options.mustClose != null && options.mustClose.length !== 0) {\n      const startContent = `    <pkg-ref id=\"${this.packager.appInfo.id}\">\\n        <must-close>\\n`\n      const endContent = \"        </must-close>\\n    </pkg-ref>\\n</installer-gui-script>\"\n      let mustCloseContent = \"\"\n      options.mustClose.forEach(appId => {\n        mustCloseContent += `            <app id=\"${appId}\"/>\\n`\n      })\n      distInfo = distInfo.replace(\"</installer-gui-script>\", `${startContent}${mustCloseContent}${endContent}`)\n    }\n\n    const insertIndex = distInfo.lastIndexOf(\"</installer-gui-script>\")\n    distInfo = distInfo.substring(0, insertIndex) + `    <domains enable_anywhere=\"${options.allowAnywhere}\" enable_currentUserHome=\"${options.allowCurrentUserHome}\" enable_localSystem=\"${options.allowRootDirectory}\" />\\n` + distInfo.substring(insertIndex)\n\n    if (options.background != null) {\n      const background = await this.packager.getResource(options.background.file)\n      if (background != null) {\n        const alignment = options.background.alignment || \"center\"\n        // noinspection SpellCheckingInspection\n        const scaling = options.background.scaling || \"tofit\"\n        distInfo = distInfo.substring(0, insertIndex) + `    <background file=\"${background}\" alignment=\"${alignment}\" scaling=\"${scaling}\"/>\\n` + distInfo.substring(insertIndex)\n        distInfo = distInfo.substring(0, insertIndex) + `    <background-darkAqua file=\"${background}\" alignment=\"${alignment}\" scaling=\"${scaling}\"/>\\n` + distInfo.substring(insertIndex)\n      }\n    }\n\n    const welcome = await this.packager.getResource(options.welcome)\n    if (welcome != null) {\n      distInfo = distInfo.substring(0, insertIndex) + `    <welcome file=\"${welcome}\"/>\\n` + distInfo.substring(insertIndex)\n    }\n\n    const license = await getNotLocalizedLicenseFile(options.license, this.packager)\n    if (license != null) {\n      distInfo = distInfo.substring(0, insertIndex) + `    <license file=\"${license}\"/>\\n` + distInfo.substring(insertIndex)\n    }\n\n    const conclusion = await this.packager.getResource(options.conclusion)\n    if (conclusion != null) {\n      distInfo = distInfo.substring(0, insertIndex) + `    <conclusion file=\"${conclusion}\"/>\\n` + distInfo.substring(insertIndex)\n    }\n\n    debug(distInfo)\n    await writeFile(distInfoFile, distInfo)\n  }\n\n  private async buildComponentPackage(appPath: string, propertyListOutputFile: string, packageOutputFile: string) {\n    const options = this.options\n    const rootPath = path.dirname(appPath)\n\n    // first produce a component plist template\n    await exec(\"pkgbuild\", [\n      \"--analyze\",\n      \"--root\", rootPath,\n      propertyListOutputFile,\n    ])\n\n    // process the template plist\n    const plistInfo = (await executeAppBuilderAsJson<Array<any>>([\"decode-plist\", \"-f\", propertyListOutputFile]))[0].filter((it: any) => it.RootRelativeBundlePath !== \"Electron.dSYM\")\n    if (plistInfo.length > 0) {\n      const packageInfo = plistInfo[0]\n\n      // ChildBundles lists all of electron binaries within the .app.\n      // There is no particular reason for removing that key, except to be as close as possible to\n      // the PackageInfo generated by previous versions of electron-builder.\n      delete packageInfo.ChildBundles\n\n      if (options.isRelocatable != null) {\n        packageInfo.BundleIsRelocatable = options.isRelocatable\n      }\n\n      if (options.isVersionChecked != null) {\n        packageInfo.BundleIsVersionChecked = options.isVersionChecked\n      }\n\n      if (options.hasStrictIdentifier != null) {\n        packageInfo.BundleHasStrictIdentifier = options.hasStrictIdentifier\n      }\n\n      if (options.overwriteAction != null) {\n        packageInfo.BundleOverwriteAction = options.overwriteAction\n      }\n\n      await executeAppBuilderAndWriteJson([\"encode-plist\"], {[propertyListOutputFile]: plistInfo})\n    }\n\n    // now build the package\n    const args = [\n      \"--root\", rootPath,\n      // \"--identifier\", this.packager.appInfo.id,\n      \"--component-plist\", propertyListOutputFile,\n    ]\n\n    use(this.options.installLocation || \"/Applications\", it => args.push(\"--install-location\", it!))\n    if (options.scripts != null) {\n      args.push(\"--scripts\", path.resolve(this.packager.info.buildResourcesDir, options.scripts))\n    }\n    else if (options.scripts !== null) {\n      const dir = path.join(this.packager.info.buildResourcesDir, \"pkg-scripts\")\n      const stat = await statOrNull(dir)\n      if (stat != null && stat.isDirectory()) {\n        args.push(\"--scripts\", dir)\n      }\n    }\n\n    args.push(packageOutputFile)\n\n    await exec(\"pkgbuild\", args)\n  }\n}\n\nexport function prepareProductBuildArgs(identity: Identity | null, keychain: string | null | undefined): Array<string> {\n  const args: Array<string> = []\n  if (identity != null) {\n    args.push(\"--sign\", identity.hash)\n    if (keychain != null) {\n      args.push(\"--keychain\", keychain)\n    }\n  }\n  return args\n}\n"], "sourceRoot": ""}