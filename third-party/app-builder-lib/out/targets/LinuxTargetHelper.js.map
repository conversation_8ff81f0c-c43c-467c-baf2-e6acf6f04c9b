{"version": 3, "sources": ["../../src/targets/LinuxTargetHelper.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAKO,MAAM,aAAa,GAAG,MAAtB;;;AAED,MAAO,iBAAP,CAAwB;AAO5B,EAAA,WAAA,CAAoB,QAApB,EAA2C;AAAvB,SAAA,QAAA,GAAA,QAAA;AANH,SAAA,WAAA,GAAc,KAAI,eAAJ,EAAS,MAAM,KAAK,mBAAL,EAAf,CAAd;AAEA,SAAA,oBAAA,GAAuB,KAAI,eAAJ,EAAS,MAAM,KAAK,oBAAL,EAAf,CAAvB;AAEjB,SAAA,WAAA,GAA6B,IAA7B;AAGC;;AAED,MAAI,KAAJ,GAAS;AACP,WAAO,KAAK,WAAL,CAAiB,KAAxB;AACD;;AAED,MAAI,aAAJ,GAAiB;AACf,WAAO,KAAK,oBAAL,CAA0B,KAAjC;AACD;;AAEO,QAAM,oBAAN,GAA0B;AAChC,UAAM,KAAK,GAAkB,EAA7B;;AACA,SAAK,MAAM,eAAX,IAA8B,KAAK,QAAL,CAAc,gBAA5C,EAA8D;AAC5D,UAAI,CAAC,eAAe,CAAC,QAArB,EAA+B;AAC7B;AACD;;AAED,YAAM,IAAI,GAAG,oBAAoB,eAAe,CAAC,QAAQ;qBAC1C,eAAe,CAAC,GAAG;MAClC,eAAe,CAAC,WAAhB,GAA8B,YAAY,eAAe,CAAC,WAAW,YAArE,GAAoF,EAAE;;aAFtF;AAKA,MAAA,KAAK,CAAC,IAAN,CAAW,IAAX;AACD;;AAED,QAAI,KAAK,CAAC,MAAN,KAAiB,CAArB,EAAwB;AACtB,aAAO,IAAP;AACD;;AAED,UAAM,IAAI,GAAG,MAAM,KAAK,QAAL,CAAc,WAAd,CAA0B,MAA1B,CAAnB;AACA,UAAM,2BAAW,IAAX,EAAiB,wHAAwH,KAAK,CAAC,IAAN,CAAW,IAAX,CAAxH,GAA2I,gBAA5J,CAAN;AACA,WAAO,IAAP;AACD,GAxC2B,CA0C5B;;;AACQ,QAAM,mBAAN,GAAyB;;;AAC/B,UAAM,QAAQ,GAAG,KAAK,QAAtB;AACA,UAAM;AAAE,MAAA,4BAAF;AAAgC,MAAA;AAAhC,QAA2C,QAAjD;AAEA,UAAM,OAAO,GAAG,CACZ,4BAA4B,CAAC,IADjB,E,YAEZ,MAAM,CAAC,G,MAAG,I,IAAA,EAAA,KAAA,KAAA,C,GAAA,KAAA,C,GAAA,EAAA,CAAE,I,MAAI,I,IAAA,EAAA,KAAA,KAAA,C,GAAA,E,GAAI,MAAM,CAAC,IAFf,EAGZ,MAHY,CAGL,GAAG,IAAI,CAAC,CAAC,GAHJ,CAAhB,CAJ+B,CAS/B;;AACA,UAAM,eAAe,GAAG,C,MACpB,MAAM,CAAC,W,MAAW,I,IAAA,EAAA,KAAA,KAAA,C,GAAA,KAAA,C,GAAA,EAAA,CAAE,cADA,EAEpB,GAAG,4BAAQ,QAAQ,CAAC,uBAAT,EAAR,CAFiB,EAGpB,MAHoB,CAGb,MAAM,QAAN,IAAkB,QAAQ,KAAI,MAAM,2BAAO,QAAP,CAAV,CAHb,CAAxB,CAV+B,CAe/B;;AACA,UAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,WAAT,CAAqB,OAArB,EAA8B,eAA9B,EAA+C,KAA/C,CAArB;AACA,SAAK,WAAL,GAAmB,MAAM,CAAC,MAAM,CAAC,MAAP,GAAgB,CAAjB,CAAN,CAA0B,IAA7C;AACA,WAAO,MAAP;AACD;;AAED,EAAA,cAAc,CAAC,OAAD,EAAoC;AAChD,WAAO,OAAO,CAAC,WAAR,IAAuB,KAAK,QAAL,CAAc,OAAd,CAAsB,WAApD;AACD;;AAED,QAAM,iBAAN,CAAwB,qBAAxB,EAA2E,IAA3E,EAA0F,WAA1F,EAAuH,KAAvH,EAAwJ;AACtJ,UAAM,IAAI,GAAG,MAAM,KAAK,mBAAL,CAAyB,qBAAzB,EAAgD,IAAhD,EAAsD,KAAtD,CAAnB;AACA,UAAM,IAAI,GAAG,WAAW,KAAI,MAAM,KAAK,QAAL,CAAc,WAAd,CAA0B,GAAG,KAAK,QAAL,CAAc,OAAd,CAAsB,eAAe,UAAlE,CAAV,CAAxB;AACA,UAAM,2BAAW,IAAX,EAAiB,IAAjB,CAAN;AACA,WAAO,IAAP;AACD;;AAED,QAAM,mBAAN,CAA0B,qBAA1B,EAA6E,IAA7E,EAA4F,KAA5F,EAA6H;AAC3H,QAAI,IAAI,IAAI,IAAR,IAAgB,IAAI,CAAC,MAAL,KAAgB,CAApC,EAAuC;AACrC,YAAM,IAAI,KAAJ,CAAU,yBAAV,CAAN;AACD,KAH0H,CAI3H;;;AACA,QAAI,qBAAqB,CAAC,OAAtB,IAAiC,IAAjC,IAAyC,qBAAqB,CAAC,OAAtB,CAA8B,IAA9B,IAAsC,IAAnF,EAAyF;AACvF,YAAM,IAAI,KAAJ,CAAU,sFAAV,CAAN;AACD;;AAED,UAAM,QAAQ,GAAG,KAAK,QAAtB;AACA,UAAM,OAAO,GAAG,QAAQ,CAAC,OAAzB;AAEA,UAAM,eAAe,GAAG,OAAO,CAAC,eAAhC;AACA,UAAM,cAAc,GAAG,qBAAqB,CAAC,cAA7C;;AACA,QAAI,IAAI,IAAI,IAAZ,EAAkB;AAChB,MAAA,IAAI,GAAG,GAAG,aAAa,IAAI,eAAe,IAAI,QAAQ,CAAC,cAAc,EAArE;;AACA,UAAI,CAAC,qBAAqB,IAArB,CAA0B,IAA1B,CAAL,EAAsC;AACpC,QAAA,IAAI,GAAG,IAAI,IAAI,GAAf;AACD;;AACD,UAAI,cAAJ,EAAoB;AAClB,QAAA,IAAI,IAAI,GAAR;AACA,QAAA,IAAI,IAAI,cAAc,CAAC,IAAf,CAAoB,GAApB,CAAR;AACD;;AACD,MAAA,IAAI,IAAI,KAAR;AACD;;AAED,UAAM,WAAW,GAAQ;AACvB,MAAA,IAAI,EAAE,OAAO,CAAC,WADS;AAEvB,MAAA,IAAI,EAAE,IAFiB;AAGvB,MAAA,QAAQ,EAAE,OAHa;AAIvB,MAAA,IAAI,EAAE,aAJiB;AAKvB,MAAA,IAAI,EAAE,QAAQ,CAAC,cALQ;AAMvB;AACA;AACA;AACA;AACA;AACA,MAAA,cAAc,EAAE,OAAO,CAAC,WAXD;AAYvB,SAAG,KAZoB;AAavB,SAAG,qBAAqB,CAAC;AAbF,KAAzB;AAgBA,UAAM,WAAW,GAAG,KAAK,cAAL,CAAoB,qBAApB,CAApB;;AACA,QAAI,CAAC,oCAAgB,WAAhB,CAAL,EAAmC;AACjC,MAAA,WAAW,CAAC,OAAZ,GAAsB,WAAtB;AACD;;AAED,UAAM,SAAS,GAAkB,4BAAQ,qBAAqB,CAAC,SAA9B,CAAjC;;AACA,SAAK,MAAM,eAAX,IAA8B,QAAQ,CAAC,gBAAvC,EAAyD;AACvD,UAAI,eAAe,CAAC,QAAhB,IAA4B,IAAhC,EAAsC;AACpC,QAAA,SAAS,CAAC,IAAV,CAAe,eAAe,CAAC,QAA/B;AACD;AACF;;AAED,SAAK,MAAM,QAAX,IAAuB,4BAAQ,QAAQ,CAAC,MAAT,CAAgB,SAAxB,EAAmC,MAAnC,CAA0C,4BAAQ,QAAQ,CAAC,4BAAT,CAAsC,SAA9C,CAA1C,CAAvB,EAA4H;AAC1H,WAAK,MAAM,MAAX,IAAqB,4BAAQ,QAAQ,CAAC,OAAjB,CAArB,EAAgD;AAC9C,QAAA,SAAS,CAAC,IAAV,CAAe,oBAAoB,MAAM,EAAzC;AACD;AACF;;AAED,QAAI,SAAS,CAAC,MAAV,KAAqB,CAAzB,EAA4B;AAC1B,MAAA,WAAW,CAAC,QAAZ,GAAuB,SAAS,CAAC,IAAV,CAAe,GAAf,IAAsB,GAA7C;AACD;;AAED,QAAI,QAAQ,GAAG,qBAAqB,CAAC,QAArC;;AACA,QAAI,oCAAgB,QAAhB,CAAJ,EAA+B;AAC7B,YAAM,WAAW,GAAG,CAAC,QAAQ,CAAC,MAAT,CAAgB,GAAhB,IAAuB,EAAxB,EAA4B,QAAhD;;AACA,UAAI,WAAW,IAAI,IAAnB,EAAyB;AACvB,QAAA,QAAQ,GAAG,kBAAkB,CAAC,WAAD,CAA7B;AACD;;AAED,UAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB;AACA,YAAI,WAAW,IAAI,IAAnB,EAAyB;AACvB,6BAAI,IAAJ,CAAS;AAAC,YAAA;AAAD,WAAT,EAAwB,wGAAxB;AACD;;AACD,2BAAI,IAAJ,CAAS;AACP,UAAA,MAAM,EAAE,qDADD;AAEP,UAAA,IAAI,EAAE;AAFC,SAAT,EAGG,0DAHH;;AAIA,QAAA,QAAQ,GAAG,SAAX;AACD;AACF;;AACD,IAAA,WAAW,CAAC,UAAZ,GAAyB,GAAG,QAAQ,GAAG,QAAQ,CAAC,QAAT,CAAkB,GAAlB,IAAyB,EAAzB,GAA8B,GAAG,EAAxE;AAEA,QAAI,IAAI,GAAG,iBAAX;;AACA,SAAK,MAAM,IAAX,IAAmB,MAAM,CAAC,IAAP,CAAY,WAAZ,CAAnB,EAA6C;AAC3C,MAAA,IAAI,IAAI,KAAK,IAAI,IAAI,WAAW,CAAC,IAAD,CAAM,EAAtC;AACD;;AACD,IAAA,IAAI,IAAI,IAAR;AACA,WAAO,IAAP;AACD;;AAtK2B;;;AAyK9B,MAAM,kBAAkB,GAAQ;AAC9B,yCAAuC,UADT;AAE9B,yCAAuC,aAFT;AAG9B,mCAAiC,WAHH;AAI9B,+BAA6B,MAJC;AAK9B,+BAA6B,kBALC;AAM9B,mCAAiC,SANH;AAO9B,2CAAyC,cAPX;AAQ9B,iCAA+B;AARD,CAAhC,C", "sourcesContent": ["import { asArray, isEmptyOrSpaces, log, exists } from \"builder-util\"\nimport { outputFile } from \"fs-extra\"\nimport { Lazy } from \"lazy-val\"\nimport { LinuxTargetSpecificOptions } from \"..\"\nimport { LinuxPackager } from \"../linuxPackager\"\nimport { IconInfo } from \"../platformPackager\"\n\nexport const installPrefix = \"/opt\"\n\nexport class LinuxTargetHelper {\n  private readonly iconPromise = new Lazy(() => this.computeDesktopIcons())\n\n  private readonly mimeTypeFilesPromise = new Lazy(() => this.computeMimeTypeFiles())\n\n  maxIconPath: string | null = null\n\n  constructor(private packager: LinuxPackager) {\n  }\n\n  get icons(): Promise<Array<IconInfo>> {\n    return this.iconPromise.value\n  }\n\n  get mimeTypeFiles(): Promise<string | null> {\n    return this.mimeTypeFilesPromise.value\n  }\n\n  private async computeMimeTypeFiles(): Promise<string | null> {\n    const items: Array<string> = []\n    for (const fileAssociation of this.packager.fileAssociations) {\n      if (!fileAssociation.mimeType) {\n        continue\n      }\n\n      const data = `<mime-type type=\"${fileAssociation.mimeType}\">\n  <glob pattern=\"*.${fileAssociation.ext}\"/>\n    ${fileAssociation.description ? `<comment>${fileAssociation.description}</comment>` : \"\"}\n  <icon name=\"x-office-document\" />\n</mime-type>`\n      items.push(data)\n    }\n\n    if (items.length === 0) {\n      return null\n    }\n\n    const file = await this.packager.getTempFile(\".xml\")\n    await outputFile(file, '<?xml version=\"1.0\" encoding=\"utf-8\"?>\\n<mime-info xmlns=\"http://www.freedesktop.org/standards/shared-mime-info\">\\n' + items.join(\"\\n\") + \"\\n</mime-info>\")\n    return file\n  }\n\n  // must be name without spaces and other special characters, but not product name used\n  private async computeDesktopIcons(): Promise<Array<IconInfo>> {\n    const packager = this.packager\n    const { platformSpecificBuildOptions, config } = packager\n\n    const sources = [\n        platformSpecificBuildOptions.icon,\n        config.mac?.icon ?? config.icon\n      ].filter(str => !!str) as string[]\n    \n    // If no explicit sources are defined, fallback to buildResources directory, then default framework icon\n    const fallbackSources = [\n        config.directories?.buildResources,\n        ...asArray(packager.getDefaultFrameworkIcon())\n      ].filter(async filepath => filepath && await exists(filepath)) as string[]\n\n    // need to put here and not as default because need to resolve image size\n    const result = await packager.resolveIcon(sources, fallbackSources, \"set\")\n    this.maxIconPath = result[result.length - 1].file\n    return result\n  }\n\n  getDescription(options: LinuxTargetSpecificOptions) {\n    return options.description || this.packager.appInfo.description\n  }\n\n  async writeDesktopEntry(targetSpecificOptions: LinuxTargetSpecificOptions, exec?: string, destination?: string | null, extra?: { [key: string]: string }): Promise<string> {\n    const data = await this.computeDesktopEntry(targetSpecificOptions, exec, extra)\n    const file = destination || await this.packager.getTempFile(`${this.packager.appInfo.productFilename}.desktop`)\n    await outputFile(file, data)\n    return file\n  }\n\n  async computeDesktopEntry(targetSpecificOptions: LinuxTargetSpecificOptions, exec?: string, extra?: { [key: string]: string }): Promise<string> {\n    if (exec != null && exec.length === 0) {\n      throw new Error(\"Specified exec is empty\")\n    }\n    // https://github.com/electron-userland/electron-builder/issues/3418\n    if (targetSpecificOptions.desktop != null && targetSpecificOptions.desktop.Exec != null) {\n      throw new Error(\"Please specify executable name as linux.executableName instead of linux.desktop.Exec\")\n    }\n\n    const packager = this.packager\n    const appInfo = packager.appInfo\n\n    const productFilename = appInfo.productFilename\n    const executableArgs = targetSpecificOptions.executableArgs\n    if (exec == null) {\n      exec = `${installPrefix}/${productFilename}/${packager.executableName}`\n      if (!/^[/0-9A-Za-z._-]+$/.test(exec)) {\n        exec = `\"${exec}\"`\n      }\n      if (executableArgs) {\n        exec += \" \"\n        exec += executableArgs.join(\" \")\n      }\n      exec += \" %U\"\n    }\n\n    const desktopMeta: any = {\n      Name: appInfo.productName,\n      Exec: exec,\n      Terminal: \"false\",\n      Type: \"Application\",\n      Icon: packager.executableName,\n      // https://askubuntu.com/questions/367396/what-represent-the-startupwmclass-field-of-a-desktop-file\n      // must be set to package.json name (because it is Electron set WM_CLASS)\n      // to get WM_CLASS of running window: xprop WM_CLASS\n      // StartupWMClass doesn't work for unicode\n      // https://github.com/electron/electron/blob/2-0-x/atom/browser/native_window_views.cc#L226\n      StartupWMClass: appInfo.productName,\n      ...extra,\n      ...targetSpecificOptions.desktop,\n    }\n\n    const description = this.getDescription(targetSpecificOptions)\n    if (!isEmptyOrSpaces(description)) {\n      desktopMeta.Comment = description\n    }\n\n    const mimeTypes: Array<string> = asArray(targetSpecificOptions.mimeTypes)\n    for (const fileAssociation of packager.fileAssociations) {\n      if (fileAssociation.mimeType != null) {\n        mimeTypes.push(fileAssociation.mimeType)\n      }\n    }\n\n    for (const protocol of asArray(packager.config.protocols).concat(asArray(packager.platformSpecificBuildOptions.protocols))) {\n      for (const scheme of asArray(protocol.schemes)) {\n        mimeTypes.push(`x-scheme-handler/${scheme}`)\n      }\n    }\n\n    if (mimeTypes.length !== 0) {\n      desktopMeta.MimeType = mimeTypes.join(\";\") + \";\"\n    }\n\n    let category = targetSpecificOptions.category\n    if (isEmptyOrSpaces(category)) {\n      const macCategory = (packager.config.mac || {}).category\n      if (macCategory != null) {\n        category = macToLinuxCategory[macCategory]\n      }\n\n      if (category == null) {\n        // https://github.com/develar/onshape-desktop-shell/issues/48\n        if (macCategory != null) {\n          log.warn({macCategory}, \"cannot map macOS category to Linux. If possible mapping is known for you, please file issue to add it.\")\n        }\n        log.warn({\n          reason: \"linux.category is not set and cannot map from macOS\",\n          docs: \"https://www.electron.build/configuration/linux\",\n        }, \"application Linux category is set to default \\\"Utility\\\"\")\n        category = \"Utility\"\n      }\n    }\n    desktopMeta.Categories = `${category}${category.endsWith(\";\") ? \"\" : \";\"}`\n\n    let data = `[Desktop Entry]`\n    for (const name of Object.keys(desktopMeta)) {\n      data += `\\n${name}=${desktopMeta[name]}`\n    }\n    data += \"\\n\"\n    return data\n  }\n}\n\nconst macToLinuxCategory: any = {\n  \"public.app-category.graphics-design\": \"Graphics\",\n  \"public.app-category.developer-tools\": \"Development\",\n  \"public.app-category.education\": \"Education\",\n  \"public.app-category.games\": \"Game\",\n  \"public.app-category.video\": \"Video;AudioVideo\",\n  \"public.app-category.utilities\": \"Utility\",\n  \"public.app-category.social-networking\": \"Network;Chat\",\n  \"public.app-category.finance\": \"Office;Finance\",\n}\n"], "sourceRoot": ""}