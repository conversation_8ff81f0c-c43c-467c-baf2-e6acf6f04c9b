{"version": 3, "sources": ["../../src/targets/ArchiveTarget.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;AAEM,MAAO,aAAP,SAA6B,cAA7B,CAAmC;AAGvC,EAAA,WAAA,CAAY,IAAZ,EAAmC,MAAnC,EAAoE,QAApE,EAAsH,iBAAA,GAAoB,KAA1I,EAA+I;AAC7I,UAAM,IAAN;AADiC,SAAA,MAAA,GAAA,MAAA;AAAiC,SAAA,QAAA,GAAA,QAAA;AAAkD,SAAA,iBAAA,GAAA,iBAAA;AAF7G,SAAA,OAAA,GAAkC,KAAK,QAAL,CAAc,MAAd,CAA6B,KAAK,IAAlC,CAAlC;AAIR;;AAED,QAAM,KAAN,CAAY,SAAZ,EAA+B,IAA/B,EAAyC;AACvC,UAAM,QAAQ,GAAG,KAAK,QAAtB;;AACA,UAAM,KAAK,GAAG,QAAQ,CAAC,QAAT,KAAsB,iBAAS,GAA7C;;AACA,UAAM,MAAM,GAAG,KAAK,IAApB;AAEA,QAAI,cAAJ;;AACA,QAAI,QAAQ,CAAC,QAAT,KAAsB,iBAAS,KAAnC,EAA0C;AACxC;AACA,MAAA,cAAc,GAAG,wBAAwB,IAAI,KAAK,oBAAK,GAAd,GAAoB,EAApB,GAAyB,UAAjD,IAA+D,SAAhF;AACD,KAHD,MAIK;AACH;AACA,MAAA,cAAc,GAAG,+BAA+B,IAAI,KAAK,oBAAK,GAAd,GAAoB,EAApB,GAAyB,UAAxD,IAAsE,eAAvF;AACD;;AAED,UAAM,YAAY,GAAG,QAAQ,CAAC,yBAAT,CAAmC,KAAK,OAAxC,EAAiD,MAAjD,EAAyD,IAAzD,EAA+D,cAA/D,EAA+E,KAA/E,CAArB;AACA,UAAM,YAAY,GAAG,IAAI,CAAC,IAAL,CAAU,KAAK,MAAf,EAAuB,YAAvB,CAArB;AAEA,UAAM,QAAQ,CAAC,IAAT,CAAc,wBAAd,CAAuC;AAC3C,MAAA,qBAAqB,EAAE,GAAG,KAAK,GAAG,QAAH,GAAc,EAAE,GAAG,MAAM,EADb;AAE3C,MAAA,IAAI,EAAE,YAFqC;AAG3C,MAAA;AAH2C,KAAvC,CAAN;AAKA,QAAI,UAAU,GAAQ,IAAtB;;AACA,QAAI,MAAM,CAAC,UAAP,CAAkB,MAAlB,CAAJ,EAA+B;AAC7B,YAAM,oBAAI,QAAQ,CAAC,WAAb,EAA0B,MAA1B,EAAkC,YAAlC,EAAgD,SAAhD,EAA2D,KAA3D,EAAkE,QAAQ,CAAC,IAAT,CAAc,cAAhF,CAAN;AACD,KAFD,MAGK;AACH,UAAI,UAAU,GAAG,CAAC,KAAlB;AACA,UAAI,YAAY,GAAG,SAAnB;;AACA,UAAI,KAAJ,EAAW;AACT,QAAA,YAAY,GAAG,IAAI,CAAC,OAAL,CAAa,SAAb,CAAf;AACA,cAAM,YAAY,GAAG,oCAAgB,QAAQ,CAAC,MAAzB,EAAiC,gBAAjC,EAAmD,YAAnD,EAAiE,QAAQ,CAAC,4BAAT,CAAsC,KAAK,MAA3C,EAAmD,IAAnD,EAAyD,QAAQ,CAAC,4BAAlE,CAAjE,CAArB;;AACA,YAAI,YAAY,IAAI,IAApB,EAA0B;AACxB,UAAA,YAAY,GAAG,SAAf;AACD,SAFD,MAGK;AACH,gBAAM,8BAAU,YAAV,EAAwB,IAAxB,EAA8B,IAA9B,CAAN;AACA,UAAA,UAAU,GAAG,IAAb;AACD;AACF;;AAED,YAAM,cAAc,GAAG;AACrB,QAAA,WAAW,EAAE,QAAQ,CAAC,WADD;AAErB,QAAA;AAFqB,OAAvB;AAIA,YAAM,wBAAQ,MAAR,EAAgB,YAAhB,EAA8B,YAA9B,EAA4C,cAA5C,CAAN;;AAEA,UAAI,KAAK,iBAAL,IAA0B,MAAM,KAAK,KAAzC,EAAgD;AAC9C,QAAA,UAAU,GAAG,MAAM,qDAAe,YAAf,CAAnB;AACD;AACF;;AAED,UAAM,QAAQ,CAAC,IAAT,CAAc,0BAAd,CAAyC;AAC7C,MAAA,UAD6C;AAE7C,MAAA,IAAI,EAAE,YAFuC;AAG7C;AACA,MAAA,gBAAgB,EAAE,QAAQ,CAAC,uBAAT,CAAiC,YAAjC,EAA+C,MAA/C,EAAuD,IAAvD,EAA6D,KAA7D,EAAoE,cAAc,CAAC,OAAf,CAAuB,gBAAvB,EAAyC,SAAzC,CAApE,CAJ2B;AAK7C,MAAA,MAAM,EAAE,IALqC;AAM7C,MAAA,IAN6C;AAO7C,MAAA,QAP6C;AAQ7C,MAAA,iBAAiB,EAAE,KAAK;AARqB,KAAzC,CAAN;AAUD;;AAtEsC,C", "sourcesContent": ["import { Arch } from \"builder-util\"\nimport * as path from \"path\"\nimport { Platform, Target, TargetSpecificOptions } from \"../core\"\nimport { copyFiles, getFileMatchers } from \"../fileMatcher\"\nimport { PlatformPackager } from \"../platformPackager\"\nimport { archive, tar } from \"./archive\"\nimport { appendBlockmap } from \"./differentialUpdateInfoBuilder\"\n\nexport class ArchiveTarget extends Target {\n  readonly options: TargetSpecificOptions = (this.packager.config as any)[this.name]\n\n  constructor(name: string, readonly outDir: string, private readonly packager: PlatformPackager<any>, private readonly isWriteUpdateInfo = false) {\n    super(name)\n  }\n\n  async build(appOutDir: string, arch: Arch): Promise<any> {\n    const packager = this.packager\n    const isMac = packager.platform === Platform.MAC\n    const format = this.name\n\n    let defaultPattern: string\n    if (packager.platform === Platform.LINUX) {\n      // tslint:disable-next-line:no-invalid-template-strings\n      defaultPattern = \"${name}-${version}\" + (arch === Arch.x64 ? \"\" : \"-${arch}\") + \".${ext}\"\n    }\n    else {\n      // tslint:disable-next-line:no-invalid-template-strings\n      defaultPattern = \"${productName}-${version}\" + (arch === Arch.x64 ? \"\" : \"-${arch}\") + \"-${os}.${ext}\"\n    }\n\n    const artifactName = packager.expandArtifactNamePattern(this.options, format, arch, defaultPattern, false)\n    const artifactPath = path.join(this.outDir, artifactName)\n\n    await packager.info.callArtifactBuildStarted({\n      targetPresentableName: `${isMac ? \"macOS \" : \"\"}${format}`,\n      file: artifactPath,\n      arch,\n    })\n    let updateInfo: any = null\n    if (format.startsWith(\"tar.\")) {\n      await tar(packager.compression, format, artifactPath, appOutDir, isMac, packager.info.tempDirManager)\n    }\n    else {\n      let withoutDir = !isMac\n      let dirToArchive = appOutDir\n      if (isMac) {\n        dirToArchive = path.dirname(appOutDir)\n        const fileMatchers = getFileMatchers(packager.config, \"extraDistFiles\", dirToArchive, packager.createGetFileMatchersOptions(this.outDir, arch, packager.platformSpecificBuildOptions))\n        if (fileMatchers == null) {\n          dirToArchive = appOutDir\n        }\n        else {\n          await copyFiles(fileMatchers, null, true)\n          withoutDir = true\n        }\n      }\n\n      const archiveOptions = {\n        compression: packager.compression,\n        withoutDir,\n      }\n      await archive(format, artifactPath, dirToArchive, archiveOptions)\n\n      if (this.isWriteUpdateInfo && format === \"zip\") {\n        updateInfo = await appendBlockmap(artifactPath)\n      }\n    }\n\n    await packager.info.callArtifactBuildCompleted({\n      updateInfo,\n      file: artifactPath,\n      // tslint:disable-next-line:no-invalid-template-strings\n      safeArtifactName: packager.computeSafeArtifactName(artifactName, format, arch, false, defaultPattern.replace(\"${productName}\", \"${name}\")),\n      target: this,\n      arch,\n      packager,\n      isWriteUpdateInfo: this.isWriteUpdateInfo,\n    })\n  }\n}"], "sourceRoot": ""}