{"version": 3, "sources": ["../../src/targets/archive.ts"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AAGA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;AAEA;AACO,eAAe,GAAf,CAAmB,WAAnB,EAA8D,MAA9D,EAA8E,OAA9E,EAA+F,YAA/F,EAAqH,QAArH,EAAwI,cAAxI,EAA8J;AACnK,QAAM,OAAO,GAAG,MAAM,cAAc,CAAC,WAAf,CAA2B;AAAC,IAAA,MAAM,EAAE;AAAT,GAA3B,CAAtB;AACA,QAAM,OAAO,GAAG,WAAW,CAAC,GAAD,CAA3B;AACA,EAAA,OAAO,CAAC,IAAR,CAAa,OAAb;AACA,EAAA,OAAO,CAAC,IAAR,CAAa,IAAI,CAAC,QAAL,CAAc,YAAd,CAAb;AAEA,QAAM,OAAO,CAAC,GAAR,CAAY,CAChB,yBAAK,iBAAL,EAAc,OAAd,EAAuB;AAAC,IAAA,GAAG,EAAE,IAAI,CAAC,OAAL,CAAa,YAAb;AAAN,GAAvB,CADgB,EAEhB;AACA,4BAAe,OAAf,CAHgB,CAAZ,CAAN;;AAMA,MAAI,CAAC,QAAL,EAAe;AACb,UAAM,yBAAK,iBAAL,EAAc,CAAC,IAAD,EAAO,OAAP,EAAgB,IAAI,CAAC,QAAL,CAAc,YAAd,CAAhB,EAA6C,IAAI,CAAC,QAAL,CAAc,OAAd,EAAuB,IAAI,MAAM,EAAjC,CAA7C,CAAd,CAAN;AACD;;AAED,MAAI,MAAM,KAAK,QAAf,EAAyB;AACvB;AACA,QAAI,QAAQ,GAAG,MAAf;;AACA,QAAI,OAAO,CAAC,QAAR,KAAqB,QAAzB,EAAmC;AACjC,MAAA,QAAQ,GAAG,IAAI,CAAC,IAAL,CAAU,MAAM,iCAAhB,EAAqC,KAArC,EAA4C,QAA5C,CAAX;AACD;;AACD,UAAM,yBAAK,QAAL,EAAe,CAAC,WAAW,KAAK,OAAhB,GAA0B,IAA1B,GAAiC,IAAlC,EAAwC;AAAS;AAAjD,MAAwF,OAAxF,CAAf,CAAN,CANuB,CAOvB;;AACA,UAAM,qBAAK,GAAG,OAAO,KAAf,EAAsB,OAAtB,CAAN;AACA;AACD;;AAED,QAAM,IAAI,GAAG,qBAAqB,CAAC,MAAM,KAAK,QAAX,GAAsB,IAAtB,GAA8B,MAAM,KAAK,SAAX,GAAuB,OAAvB,GAAiC,MAAhE,EAAyE;AACzG,IAAA,aAAa,EAAE,IAD0F;AAEzG,IAAA,MAAM,EAAE,SAFiG;AAGzG,IAAA;AAHyG,GAAzE,CAAlC;AAKA,EAAA,IAAI,CAAC,IAAL,CAAU,OAAV,EAAmB,OAAnB;AACA,QAAM,yBAAK,iBAAL,EAAc,IAAd,EAAoB;AACxB,IAAA,GAAG,EAAE,IAAI,CAAC,OAAL,CAAa,YAAb;AADmB,GAApB,EAEH,uBAAQ,OAFL,CAAN;AAGD;;AA6BK,SAAU,qBAAV,CAAgC,MAAhC,EAAgD,OAAA,GAA0B,EAA1E,EAA4E;AAChF,MAAI,SAAS,GAAG,OAAO,CAAC,WAAR,KAAwB,OAAxC;AACA,QAAM,IAAI,GAAG,WAAW,CAAC,GAAD,CAAxB;AAEA,MAAI,UAAU,GAAG,KAAjB;;AACA,MAAI,OAAO,CAAC,GAAR,CAAY,kCAAZ,IAAkD,IAAtD,EAA4D;AAC1D,IAAA,SAAS,GAAG,KAAZ;AACA,IAAA,IAAI,CAAC,IAAL,CAAU,OAAO,OAAO,CAAC,GAAR,CAAY,kCAAkC,EAA/D;AACA,IAAA,UAAU,GAAG,IAAb;AACD;;AAED,QAAM,KAAK,GAAG,MAAM,KAAK,KAAzB;;AACA,MAAI,CAAC,SAAL,EAAgB;AACd,QAAI,KAAK,IAAI,OAAO,CAAC,WAAR,KAAwB,SAArC,EAAgD;AAC9C;AACA,MAAA,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,WAAtB;AACD;;AAED,QAAI,CAAC,UAAL,EAAiB;AACf;AACA,MAAA,IAAI,CAAC,IAAL,CAAU,UAAW,CAAC,KAAD,IAAU,OAAO,CAAC,WAAR,KAAwB,SAAnC,GAAgD,GAAhD,GAAsD,GAAhE,CAAV;AACD;AACF;;AAED,MAAI,OAAO,CAAC,QAAR,IAAoB,IAAxB,EAA8B;AAC5B,IAAA,IAAI,CAAC,IAAL,CAAU,OAAO,OAAO,CAAC,QAAQ,GAAjC;AACD,GA1B+E,CA4BhF;AACA;AACA;AACA;;;AACA,MAAI,CAAC,OAAO,CAAC,aAAb,EAA4B;AAC1B,IAAA,IAAI,CAAC,IAAL,CAAU,UAAV;AACD;;AAED,MAAI,MAAM,KAAK,IAAX,IAAmB,MAAM,CAAC,QAAP,CAAgB,KAAhB,CAAvB,EAA+C;AAC7C,QAAI,OAAO,CAAC,KAAR,KAAkB,KAAtB,EAA6B;AAC3B,MAAA,IAAI,CAAC,IAAL,CAAU,SAAV;AACD;;AAED,QAAI,OAAO,CAAC,yBAAR,KAAsC,KAA1C,EAAiD;AAC/C,MAAA,IAAI,CAAC,IAAL,CAAU,UAAV;AACD,KAP4C,CAS7C;AACA;;;AACA,IAAA,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,UAAtB;AACD;;AAED,MAAI,OAAO,CAAC,MAAR,IAAkB,IAAtB,EAA4B;AAC1B,QAAI,OAAO,CAAC,MAAR,KAAmB,SAAvB,EAAkC;AAChC,MAAA,IAAI,CAAC,IAAL,CAAU,OAAO,OAAO,CAAC,MAAM,EAA/B;AACD;AACF,GAJD,MAKK,IAAI,KAAK,IAAI,SAAb,EAAwB;AAC3B,IAAA,IAAI,CAAC,IAAL,CAAU,OAAO,SAAS,GAAG,MAAH,GAAY,SAAS,EAA/C;AACD;;AAED,MAAI,KAAJ,EAAW;AACT;AACA;AACA;AACA,IAAA,IAAI,CAAC,IAAL,CAAU,MAAV;AACD;;AACD,SAAO,IAAP;AACD,C,CAED;;AACA;;;AACO,eAAe,OAAf,CAAuB,MAAvB,EAAuC,OAAvC,EAAwD,YAAxD,EAA8E,OAAA,GAA0B,EAAxG,EAA0G;AAC/G,QAAM,IAAI,GAAG,qBAAqB,CAAC,MAAD,EAAS,OAAT,CAAlC,CAD+G,CAE/G;;AACA,QAAM,0BAAe,OAAf,CAAN;AAEA,EAAA,IAAI,CAAC,IAAL,CAAU,OAAV,EAAmB,OAAO,CAAC,UAAR,GAAqB,GAArB,GAA2B,IAAI,CAAC,QAAL,CAAc,YAAd,CAA9C;;AACA,MAAI,OAAO,CAAC,QAAR,IAAoB,IAAxB,EAA8B;AAC5B,SAAK,MAAM,IAAX,IAAmB,OAAO,CAAC,QAA3B,EAAqC;AACnC,MAAA,IAAI,CAAC,IAAL,CAAU,OAAO,IAAI,EAArB;AACD;AACF;;AAED,MAAI;AACF,UAAM,yBAAK,iBAAL,EAAc,IAAd,EAAoB;AACxB,MAAA,GAAG,EAAE,OAAO,CAAC,UAAR,GAAqB,YAArB,GAAoC,IAAI,CAAC,OAAL,CAAa,YAAb;AADjB,KAApB,EAEH,uBAAQ,OAFL,CAAN;AAGD,GAJD,CAKA,OAAO,CAAP,EAAU;AACR,QAAI,CAAC,CAAC,IAAF,KAAW,QAAX,IAAuB,EAAE,MAAM,kBAAO,YAAP,CAAR,CAA3B,EAA0D;AACxD,YAAM,IAAI,KAAJ,CAAU,2BAA2B,YAAY,iBAAjD,CAAN;AACD,KAFD,MAGK;AACH,YAAM,CAAN;AACD;AACF;;AAED,SAAO,OAAP;AACD;;AAED,SAAS,WAAT,CAAqB,OAArB,EAAuC;AACrC,QAAM,IAAI,GAAG,CAAC,OAAD,EAAU,KAAV,CAAb;;AACA,MAAI,uBAAQ,OAAZ,EAAqB;AACnB,IAAA,IAAI,CAAC,IAAL,CAAU,KAAV;AACD;;AACD,SAAO,IAAP;AACD,C", "sourcesContent": ["import { path7za } from \"7zip-bin\"\nimport { debug7z, exec } from \"builder-util\"\nimport { exists, unlinkIfExists } from \"builder-util/out/fs\"\nimport { move } from \"fs-extra\"\nimport * as path from \"path\"\nimport { TmpDir } from \"temp-file\"\nimport { CompressionLevel } from \"../core\"\nimport { getLinuxToolsPath } from \"./tools\"\n\n/** @internal */\nexport async function tar(compression: CompressionLevel | any | any, format: string, outFile: string, dirToArchive: string, isMacApp: boolean, tempDirManager: TmpDir): Promise<void> {\n  const tarFile = await tempDirManager.getTempFile({suffix: \".tar\"})\n  const tarArgs = debug7zArgs(\"a\")\n  tarArgs.push(tarFile)\n  tarArgs.push(path.basename(dirToArchive))\n\n  await Promise.all([\n    exec(path7za, tarArgs, {cwd: path.dirname(dirToArchive)}),\n    // remove file before - 7z doesn't overwrite file, but update\n    unlinkIfExists(outFile),\n  ])\n\n  if (!isMacApp) {\n    await exec(path7za, [\"rn\", tarFile, path.basename(dirToArchive), path.basename(outFile, `.${format}`)])\n  }\n\n  if (format === \"tar.lz\") {\n    // noinspection SpellCheckingInspection\n    let lzipPath = \"lzip\"\n    if (process.platform === \"darwin\") {\n      lzipPath = path.join(await getLinuxToolsPath(), \"bin\", lzipPath)\n    }\n    await exec(lzipPath, [compression === \"store\" ? \"-1\" : \"-9\", \"--keep\" /* keep (don't delete) input files */, tarFile])\n    // bloody lzip creates file in the same dir where input file with postfix `.lz`, option --output doesn't work\n    await move(`${tarFile}.lz`, outFile)\n    return\n  }\n\n  const args = compute7zCompressArgs(format === \"tar.xz\" ? \"xz\" : (format === \"tar.bz2\" ? \"bzip2\" : \"gzip\"), {\n    isRegularFile: true,\n    method: \"DEFAULT\",\n    compression,\n  })\n  args.push(outFile, tarFile)\n  await exec(path7za, args, {\n    cwd: path.dirname(dirToArchive),\n  }, debug7z.enabled)\n}\n\nexport interface ArchiveOptions {\n  compression?: CompressionLevel | null\n\n  /**\n   * @default false\n   */\n  withoutDir?: boolean\n\n  /**\n   * @default true\n   */\n  solid?: boolean\n\n  /**\n   * @default true\n   */\n  isArchiveHeaderCompressed?: boolean\n\n  dictSize?: number\n  excluded?: Array<string> | null\n\n  // DEFAULT allows to disable custom logic and do not pass method switch at all\n  method?: \"Copy\" | \"LZMA\" | \"Deflate\" | \"DEFAULT\"\n\n  isRegularFile?: boolean\n}\n\nexport function compute7zCompressArgs(format: string, options: ArchiveOptions = {}) {\n  let storeOnly = options.compression === \"store\"\n  const args = debug7zArgs(\"a\")\n\n  let isLevelSet = false\n  if (process.env.ELECTRON_BUILDER_COMPRESSION_LEVEL != null) {\n    storeOnly = false\n    args.push(`-mx=${process.env.ELECTRON_BUILDER_COMPRESSION_LEVEL}`)\n    isLevelSet = true\n  }\n\n  const isZip = format === \"zip\"\n  if (!storeOnly) {\n    if (isZip && options.compression === \"maximum\") {\n      // http://superuser.com/a/742034\n      args.push(\"-mfb=258\", \"-mpass=15\")\n    }\n\n    if (!isLevelSet) {\n      // https://github.com/electron-userland/electron-builder/pull/3032\n      args.push(\"-mx=\" + ((!isZip || options.compression === \"maximum\") ? \"9\" : \"7\"))\n    }\n  }\n\n  if (options.dictSize != null) {\n    args.push(`-md=${options.dictSize}m`)\n  }\n\n  // https://sevenzip.osdn.jp/chm/cmdline/switches/method.htm#7Z\n  // https://stackoverflow.com/questions/27136783/7zip-produces-different-output-from-identical-input\n  // tc and ta are off by default, but to be sure, we explicitly set it to off\n  // disable \"Stores NTFS timestamps for files: Modification time, Creation time, Last access time.\" to produce the same archive for the same data\n  if (!options.isRegularFile) {\n    args.push(\"-mtc=off\")\n  }\n\n  if (format === \"7z\" || format.endsWith(\".7z\")) {\n    if (options.solid === false) {\n      args.push(\"-ms=off\")\n    }\n\n    if (options.isArchiveHeaderCompressed === false) {\n      args.push(\"-mhc=off\")\n    }\n\n    // args valid only for 7z\n    // -mtm=off disable \"Stores last Modified timestamps for files.\"\n    args.push(\"-mtm=off\", \"-mta=off\")\n  }\n\n  if (options.method != null) {\n    if (options.method !== \"DEFAULT\") {\n      args.push(`-mm=${options.method}`)\n    }\n  }\n  else if (isZip || storeOnly) {\n    args.push(`-mm=${storeOnly ? \"Copy\" : \"Deflate\"}`)\n  }\n\n  if (isZip) {\n    // -mcu switch:  7-Zip uses UTF-8, if there are non-ASCII symbols.\n    // because default mode: 7-Zip uses UTF-8, if the local code page doesn't contain required symbols.\n    // but archive should be the same regardless where produced\n    args.push(\"-mcu\")\n  }\n  return args\n}\n\n// 7z is very fast, so, use ultra compression\n/** @internal */\nexport async function archive(format: string, outFile: string, dirToArchive: string, options: ArchiveOptions = {}): Promise<string> {\n  const args = compute7zCompressArgs(format, options)\n  // remove file before - 7z doesn't overwrite file, but update\n  await unlinkIfExists(outFile)\n\n  args.push(outFile, options.withoutDir ? \".\" : path.basename(dirToArchive))\n  if (options.excluded != null) {\n    for (const mask of options.excluded) {\n      args.push(`-xr!${mask}`)\n    }\n  }\n\n  try {\n    await exec(path7za, args, {\n      cwd: options.withoutDir ? dirToArchive : path.dirname(dirToArchive),\n    }, debug7z.enabled)\n  }\n  catch (e) {\n    if (e.code === \"ENOENT\" && !(await exists(dirToArchive))) {\n      throw new Error(`Cannot create archive: \"${dirToArchive}\" doesn't exist`)\n    }\n    else {\n      throw e\n    }\n  }\n\n  return outFile\n}\n\nfunction debug7zArgs(command: \"a\" | \"x\"): Array<string> {\n  const args = [command, \"-bd\"]\n  if (debug7z.enabled) {\n    args.push(\"-bb\")\n  }\n  return args\n}"], "sourceRoot": ""}