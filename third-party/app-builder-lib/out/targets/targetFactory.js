"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.computeArchToTargetNamesMap = computeArchToTargetNamesMap;
exports.createTargets = createTargets;
exports.createCommonTarget = createCommonTarget;
exports.NoOpTarget = void 0;

function _builderUtil() {
  const data = require("builder-util");

  _builderUtil = function () {
    return data;
  };

  return data;
}

function _() {
  const data = require("..");

  _ = function () {
    return data;
  };

  return data;
}

function _ArchiveTarget() {
  const data = require("./ArchiveTarget");

  _ArchiveTarget = function () {
    return data;
  };

  return data;
}

const archiveTargets = new Set(["zip", "7z", "tar.xz", "tar.lz", "tar.gz", "tar.bz2"]);

function computeArchToTargetNamesMap(raw, platformPackager, platform) {
  for (const targetNames of raw.values()) {
    if (targetNames.length > 0) {
      // https://github.com/electron-userland/electron-builder/issues/1355
      return raw;
    }
  }

  const defaultArchs = raw.size === 0 ? [process.arch] : Array.from(raw.keys()).map(it => _builderUtil().Arch[it]);
  const result = new Map(raw);

  for (const target of (0, _builderUtil().asArray)(platformPackager.platformSpecificBuildOptions.target).map(it => typeof it === "string" ? {
    target: it
  } : it)) {
    let name = target.target;
    let archs = target.arch;
    const suffixPos = name.lastIndexOf(":");

    if (suffixPos > 0) {
      name = target.target.substring(0, suffixPos);

      if (archs == null) {
        archs = target.target.substring(suffixPos + 1);
      }
    }

    for (const arch of archs == null ? defaultArchs : (0, _builderUtil().asArray)(archs)) {
      (0, _builderUtil().addValue)(result, (0, _builderUtil().archFromString)(arch), name);
    }
  }

  if (result.size === 0) {
    const defaultTarget = platformPackager.defaultTarget;

    if (raw.size === 0 && platform === _().Platform.LINUX && (process.platform === "darwin" || process.platform === "win32")) {
      result.set(_builderUtil().Arch.x64, defaultTarget); // cannot enable arm because of native dependencies - e.g. keytar doesn't provide pre-builds for arm
      // result.set(Arch.armv7l, ["snap"])
    } else {
      for (const arch of defaultArchs) {
        result.set((0, _builderUtil().archFromString)(arch), defaultTarget);
      }
    }
  }

  return result;
}

function createTargets(nameToTarget, rawList, outDir, packager) {
  const result = [];

  const mapper = (name, factory) => {
    let target = nameToTarget.get(name);

    if (target == null) {
      target = factory(outDir);
      nameToTarget.set(name, target);
    }

    result.push(target);
  };

  const targets = normalizeTargets(rawList, packager.defaultTarget);
  packager.createTargets(targets, mapper);
  return result;
}

function normalizeTargets(targets, defaultTarget) {
  const list = [];

  for (const t of targets) {
    const name = t.toLowerCase().trim();

    if (name === _().DEFAULT_TARGET) {
      list.push(...defaultTarget);
    } else {
      list.push(name);
    }
  }

  return list;
}

function createCommonTarget(target, outDir, packager) {
  if (archiveTargets.has(target)) {
    return new (_ArchiveTarget().ArchiveTarget)(target, outDir, packager);
  } else if (target === _().DIR_TARGET) {
    return new NoOpTarget(_().DIR_TARGET);
  } else {
    throw new Error(`Unknown target: ${target}`);
  }
}

class NoOpTarget extends _().Target {
  constructor(name) {
    super(name);
    this.options = null;
  }

  get outDir() {
    throw new Error("NoOpTarget");
  } // eslint-disable-next-line


  async build(appOutDir, arch) {// no build
  }

} exports.NoOpTarget = NoOpTarget;
// __ts-babel@6.0.4
//# sourceMappingURL=targetFactory.js.map