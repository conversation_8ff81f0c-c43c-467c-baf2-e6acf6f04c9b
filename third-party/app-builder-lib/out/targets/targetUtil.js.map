{"version": 3, "sources": ["../../src/targets/targetUtil.ts"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;AAGM,MAAO,QAAP,CAAe;AACnB,EAAA,WAAA,CAAqB,GAArB,EAAgC;AAAX,SAAA,GAAA,GAAA,GAAA;AACpB;;AAED,EAAA,WAAW,CAAC,IAAD,EAAa;AACtB,WAAO,KAAK,GAAL,GAAW,IAAI,CAAC,GAAhB,GAAsB,IAA7B;AACD;;AAED,EAAA,OAAO,GAAA;AACL,QAAI,CAAC,qBAAM,OAAP,IAAkB,OAAO,CAAC,GAAR,CAAY,2CAAZ,KAA4D,MAAlF,EAA0F;AACxF,aAAO,uBAAO,KAAK,GAAZ,CAAP;AACD;;AACD,WAAO,OAAO,CAAC,OAAR,EAAP;AACD;;AAED,EAAA,QAAQ,GAAA;AACN,WAAO,KAAK,GAAZ;AACD;;AAjBkB;;;;AAoBd,eAAe,cAAf,CAA8B,MAA9B,EAA8C,QAA9C,EAA+E,IAA/E,EAAyF;AAC9F,SAAO,IAAI,QAAJ,CAAa,MAAM,kBAAkB,CAAC,MAAD,EAAS,QAAT,EAAmB,IAAnB,CAArC,CAAP;AACD;;AAEM,eAAe,kBAAf,CAAkC,MAAlC,EAAkD,QAAlD,EAAmF,IAAnF,EAA6F;AAClG,QAAM,OAAO,GAAG,QAAQ,CAAC,IAAT,CAAc,sBAAd,CAAqC,MAArC,EAA6C,QAA7C,EAAuD,IAAvD,CAAhB;AACA,QAAM,yBAAS,OAAT,CAAN;AACA,SAAO,OAAP;AACD,C,CAED;AACA;;;AACM,SAAU,6BAAV,CAAwC,OAAxC,EAA0D,qBAA1D,EAAwF;AAC5F,SAAO,qBAAqB,IAAI,sBAAsB,IAAtB,CAA2B,OAAO,CAAC,eAAnC,CAAzB,GAA+E,OAAO,CAAC,eAAvF,GAAyG,OAAO,CAAC,aAAxH;AACD,C", "sourcesContent": ["import { emptyDir, remove } from \"fs-extra\"\nimport * as path from \"path\"\nimport { Target, AppInfo } from \"../\"\nimport { Arch, debug } from \"builder-util\"\nimport { PlatformPackager } from \"../platformPackager\"\n\nexport class StageDir {\n  constructor(readonly dir: string) {\n  }\n\n  getTempFile(name: string) {\n    return this.dir + path.sep + name\n  }\n\n  cleanup() {\n    if (!debug.enabled || process.env.ELECTRON_BUILDER_REMOVE_STAGE_EVEN_IF_DEBUG === \"true\") {\n      return remove(this.dir)\n    }\n    return Promise.resolve()\n  }\n\n  toString() {\n    return this.dir\n  }\n}\n\nexport async function createStageDir(target: Target, packager: PlatformPackager<any>, arch: Arch): Promise<StageDir> {\n  return new StageDir(await createStageDirPath(target, packager, arch))\n}\n\nexport async function createStageDirPath(target: Target, packager: PlatformPackager<any>, arch: Arch): Promise<string> {\n  const tempDir = packager.info.stageDirPathCustomizer(target, packager, arch)\n  await emptyDir(tempDir)\n  return tempDir\n}\n\n// https://github.com/electron-userland/electron-builder/issues/3100\n// https://github.com/electron-userland/electron-builder/commit/2539cfba20dc639128e75c5b786651b652bb4b78\nexport function getWindowsInstallationDirName(appInfo: AppInfo, isTryToUseProductName: boolean): string {\n  return isTryToUseProductName && /^[-_+0-9a-zA-Z .]+$/.test(appInfo.productFilename) ? appInfo.productFilename : appInfo.sanitizedName\n}"], "sourceRoot": ""}