{"version": 3, "sources": ["../../src/targets/fpm.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;AAQc,MAAO,SAAP,SAAyB,cAAzB,CAA+B;AAK3C,EAAA,WAAA,CAAY,IAAZ,EAA2C,QAA3C,EAAqF,MAArF,EAAyH,MAAzH,EAAuI;AACrI,UAAM,IAAN,EAAY,KAAZ;AADyC,SAAA,QAAA,GAAA,QAAA;AAA0C,SAAA,MAAA,GAAA,MAAA;AAAoC,SAAA,MAAA,GAAA,MAAA;AAJhH,SAAA,OAAA,GAAsC,EAAC,GAAG,KAAK,QAAL,CAAc,4BAAlB;AAAgD,SAAI,KAAK,QAAL,CAAc,MAAd,CAA6B,KAAK,IAAlC;AAApD,KAAtC;AAOP,SAAK,WAAL,GAAmB,KAAK,aAAL,EAAnB;AACD;;AAEO,QAAM,aAAN,GAAmB;AACzB,UAAM,mBAAmB,GAAG,oCAAgB,OAAhB,CAA5B;AAEA,UAAM,QAAQ,GAAG,KAAK,QAAtB;AACA,UAAM,eAAe,GAAG;AACtB;AACA,MAAA,UAAU,EAAE,QAAQ,CAAC,cAFC;AAGtB,MAAA,eAAe,EAAE,QAAQ,CAAC,OAAT,CAAiB,eAHZ;AAG6B,SAAG,QAAQ,CAAC;AAHzC,KAAxB;;AAKA,aAAS,WAAT,CAAqB,KAArB,EAAuD,WAAvD,EAA0E;AACxE,UAAI,KAAK,IAAI,IAAb,EAAmB;AACjB,eAAO,IAAI,CAAC,IAAL,CAAU,mBAAV,EAA+B,WAA/B,CAAP;AACD;;AACD,aAAO,IAAI,CAAC,OAAL,CAAa,QAAQ,CAAC,UAAtB,EAAkC,KAAlC,CAAP;AACD;;AAED,WAAO,MAAM,OAAO,CAAC,GAAR,CAAoB,CAC/B,eAAe,CAAC,QAAQ,CAAC,IAAT,CAAc,cAAf,EAA+B,WAAW,CAAC,KAAK,OAAL,CAAa,YAAd,EAA4B,mBAA5B,CAA1C,EAA4F,eAA5F,CADgB,EAE/B,eAAe,CAAC,QAAQ,CAAC,IAAT,CAAc,cAAf,EAA+B,WAAW,CAAC,KAAK,OAAL,CAAa,WAAd,EAA2B,kBAA3B,CAA1C,EAA0F,eAA1F,CAFgB,CAApB,CAAb;AAID;;AAED,EAAA,YAAY,GAAA;AACV,WAAO,KAAK,yBAAL,EAAP;AACD;;AAEO,QAAM,yBAAN,GAA+B;AACrC,UAAM,QAAQ,GAAG,KAAK,QAAtB;AACA,UAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,OAAT,CAAiB,iBAAjB,EAAzB;AACA,UAAM,MAAM,GAAkB,EAA9B;;AACA,QAAI,UAAU,IAAI,IAAlB,EAAwB;AACtB,MAAA,MAAM,CAAC,IAAP,CAAY,2GAAZ;AACD;;AAED,UAAM,OAAO,GAAG,KAAK,OAArB;AACA,QAAI,MAAM,GAAG,OAAO,CAAC,UAArB;;AACA,QAAI,MAAM,IAAI,IAAd,EAAoB;AAClB,YAAM,CAAC,GAAG,QAAQ,CAAC,IAAT,CAAc,QAAd,CAAuB,MAAjC;;AACA,UAAI,CAAC,IAAI,IAAL,IAAa,CAAC,CAAC,KAAF,IAAW,IAA5B,EAAkC;AAChC,QAAA,MAAM,CAAC,IAAP,CAAY,aAAa,GAAC,mBAA1B;AACD,OAFD,MAGK;AACH,QAAA,MAAM,GAAG,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,GAA9B;AACD;AACF;;AAED,QAAI,MAAM,CAAC,MAAP,GAAgB,CAApB,EAAuB;AACrB,YAAM,IAAI,KAAJ,CAAU,MAAM,CAAC,IAAP,CAAY,MAAZ,CAAV,CAAN;AACD;;AAED,WAAO;AACL,MAAA,UAAU,EAAE,MADP;AAEL,MAAA,GAAG,EAAE,UAFA;AAGL,MAAA,MAAM,EAAE,OAAO,CAAC,MAAR,IAAkB;AAHrB,KAAP;AAKD;;AAED,QAAM,KAAN,CAAY,SAAZ,EAA+B,IAA/B,EAAyC;AACvC,UAAM,MAAM,GAAG,KAAK,IAApB,CADuC,CAGvC;;AACA,QAAI,UAAU,GAAG,mCAAjB;AACA,QAAI,cAAc,GAAG,KAArB;;AACA,QAAI,MAAM,KAAK,KAAf,EAAsB;AACpB,MAAA,UAAU,GAAG,mCAAb;AACA,MAAA,cAAc,GAAG,IAAjB;AACD,KAHD,MAIK,IAAI,MAAM,KAAK,KAAf,EAAsB;AACzB,MAAA,UAAU,GAAG,mCAAb;AACA,MAAA,cAAc,GAAG,IAAjB;AACD;;AAED,UAAM,QAAQ,GAAG,KAAK,QAAtB;AACA,UAAM,YAAY,GAAG,IAAI,CAAC,IAAL,CAAU,KAAK,MAAf,EAAuB,QAAQ,CAAC,yBAAT,CAAmC,KAAK,OAAxC,EAAiD,MAAjD,EAAyD,IAAzD,EAA+D,UAA/D,EAA2E,CAAC,cAA5E,CAAvB,CAArB;AAEA,UAAM,QAAQ,CAAC,IAAT,CAAc,wBAAd,CAAuC;AAC3C,MAAA,qBAAqB,EAAE,MADoB;AAE3C,MAAA,IAAI,EAAE,YAFqC;AAG3C,MAAA;AAH2C,KAAvC,CAAN;AAMA,UAAM,0BAAe,YAAf,CAAN;;AACA,QAAI,QAAQ,CAAC,eAAT,CAAyB,WAAzB,IAAwC,IAA5C,EAAkD;AAChD,YAAM,0BAAU,KAAK,MAAf,CAAN;AACD;;AAED,UAAM,OAAO,GAAG,MAAM,KAAK,WAA3B;AACA,UAAM,OAAO,GAAG,QAAQ,CAAC,OAAzB;AACA,UAAM,OAAO,GAAG,KAAK,OAArB;AACA,UAAM,QAAQ,GAAG,OAAO,CAAC,QAAzB;AACA,UAAM,IAAI,GAAG,CACX,gBADW,EACO,sCAAkB,IAAlB,EAAwB,MAAxB,CADP,EAEX,QAFW,EAED,OAAO,CAAC,gBAFP,EAGX,iBAHW,EAGQ,OAAO,CAAC,CAAD,CAHf,EAIX,gBAJW,EAIO,OAAO,CAAC,CAAD,CAJd,EAKX,eALW,EAKM,wBAAQ,MAAM,KAAK,KAAX,GAAmB,KAAK,MAAL,CAAY,cAAZ,CAA2B,OAA3B,CAAnB,GAA0D,GAAG,QAAQ,IAAI,EAAE,MAAM,KAAK,MAAL,CAAY,cAAZ,CAA2B,OAA3B,CAAmC,EAA5H,CALN,EAMX,WANW,EAME,OAAO,CAAC,OANV,EAOX,WAPW,EAOE,YAPF,CAAb;AAUA,oCAAa,IAAb,EAAmB,MAAM,KAAK,yBAAL,EAAzB;AAEA,UAAM,eAAe,GAAG,OAAO,CAAC,eAAhC;;AACA,QAAI,eAAe,IAAI,IAAvB,EAA6B;AAC3B,MAAA,IAAI,CAAC,IAAL,CAAU,YAAV,EAAwB,eAAxB;AACD;;AAED,QAAI,MAAM,KAAK,KAAf,EAAsB;AACpB,8BAAK,OAAsB,CAAC,QAA5B,EAAsC,EAAE,IAAI,IAAI,CAAC,IAAL,CAAU,gBAAV,EAA4B,EAA5B,CAA5C;AACD,KAFD,MAGK,IAAI,MAAM,KAAK,KAAf,EAAsB;AACzB,UAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,QAAA,IAAI,CAAC,IAAL,CAAU,eAAV,EAA2B,wBAAQ,QAAR,CAA3B;AACD;AACF;;AAED,UAAM,gBAAgB,GAAqB;AACzC,MAAA,IADyC;AACnC,MAAA;AADmC,KAA3C;;AAIA,QAAI,OAAO,CAAC,WAAR,IAAuB,IAA3B,EAAiC;AAC/B,MAAA,gBAAgB,CAAC,WAAjB,GAA+B,OAAO,CAAC,WAAvC;AACD,KAjEsC,CAmEvC;;;AACA,UAAM,OAAO,GAAG,OAAO,CAAC,OAAxB;;AACA,QAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,UAAI,KAAK,CAAC,OAAN,CAAc,OAAd,CAAJ,EAA4B;AAC1B,QAAA,gBAAgB,CAAC,aAAjB,GAAiC,OAAjC;AACD,OAFD,MAGK;AACH;AACA,YAAI,OAAO,OAAP,KAAmB,QAAvB,EAAiC;AAC/B,UAAA,gBAAgB,CAAC,aAAjB,GAAiC,CAAC,OAAD,CAAjC;AACD,SAFD,MAGK;AACH,gBAAM,IAAI,KAAJ,CAAU,sDAAsD,OAAO,EAAvE,CAAN;AACD;AACF;AACF;;AAED,4BAAI,QAAQ,CAAC,IAAT,CAAc,QAAd,CAAuB,OAA3B,EAAoC,EAAE,IAAI,IAAI,CAAC,IAAL,CAAU,WAAV,EAAuB,EAAvB,CAA1C;AACA,4BAAI,OAAO,CAAC,WAAZ,EAAyB,EAAE,IAAI,IAAI,CAAC,IAAL,CAAU,aAAV,EAAyB,EAAzB,CAA/B;AAEA,4BAAI,OAAO,CAAC,GAAZ,EAAiB,EAAE,IAAI,IAAI,CAAC,IAAL,CAAU,GAAG,EAAb,CAAvB;AAEA,IAAA,IAAI,CAAC,IAAL,CAAU,GAAG,SAAS,KAAK,kCAAa,IAAI,OAAO,CAAC,eAAe,EAAnE;;AACA,SAAK,MAAM,IAAX,IAAoB,MAAM,KAAK,MAAL,CAAY,KAAtC,EAA8C;AAC5C,YAAM,UAAU,GAAG,IAAI,CAAC,OAAL,CAAa,IAAI,CAAC,IAAlB,CAAnB;AACA,YAAM,QAAQ,GAAG,UAAU,KAAK,MAAf,GAAwB,UAAxB,GAAqC,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAA/E;AACA,MAAA,IAAI,CAAC,IAAL,CAAU,GAAG,IAAI,CAAC,IAAI,6BAA6B,QAAQ,SAAS,QAAQ,CAAC,cAAc,GAAG,UAAU,EAAxG;AACD;;AAED,UAAM,gBAAgB,GAAG,MAAM,KAAK,MAAL,CAAY,aAA3C;;AACA,QAAI,gBAAgB,IAAI,IAAxB,EAA8B;AAC5B,MAAA,IAAI,CAAC,IAAL,CAAU,GAAG,gBAAgB,6BAA6B,QAAQ,CAAC,cAAc,MAAjF;AACD;;AAED,UAAM,eAAe,GAAG,MAAM,KAAK,MAAL,CAAY,iBAAZ,CAA8B,KAAK,OAAnC,CAA9B;AACA,IAAA,IAAI,CAAC,IAAL,CAAU,GAAG,eAAe,4BAA4B,QAAQ,CAAC,cAAc,UAA/E;;AAEA,QAAI,QAAQ,CAAC,eAAT,CAAyB,uBAAzB,IAAoD,IAApD,KAA4D,MAAM,QAAQ,CAAC,eAAT,CAAyB,uBAAzB,CAAiD,CAAC,IAAD,EAAO,eAAP,CAAjD,CAAlE,CAAJ,EAAiJ;AAC/I;AACD;;AAED,UAAM,GAAG,GAAG,EACV,GAAG,OAAO,CAAC,GADD;AAEV,MAAA,QAAQ,EAAE,iBAFA;AAGV,MAAA,qBAAqB,EAAE,QAAQ,CAAC,WAAT,KAAyB,OAAzB,GAAmC,GAAnC,GAAyC;AAHtD,KAAZ,CA5GuC,CAkHvC;AACA;;AACA,QAAI,MAAM,KAAK,KAAX,KAAoB,MAAM,oCAA1B,CAAJ,EAA+C;AAC7C,YAAM,cAAc,GAAG,MAAM,iCAA7B;AACA,MAAA,MAAM,CAAC,MAAP,CAAc,GAAd,EAAmB;AACjB,QAAA,IAAI,EAAE,+BAAW,OAAO,CAAC,GAAR,CAAY,IAAvB,EAA6B,CAAC,IAAI,CAAC,IAAL,CAAU,cAAV,EAA0B,KAA1B,CAAD,CAA7B,CADW;AAEjB,QAAA,iBAAiB,EAAE,+BAAW,OAAO,CAAC,GAAR,CAAY,iBAAvB,EAA0C,CAAC,IAAI,CAAC,IAAL,CAAU,cAAV,EAA0B,KAA1B,CAAD,CAA1C;AAFF,OAAnB;AAID;;AAED,UAAM,sCAAkB,CAAC,KAAD,EAAQ,iBAAR,EAA2B,IAAI,CAAC,SAAL,CAAe,gBAAf,CAA3B,CAAlB,EAAgF,SAAhF,EAA2F;AAAC,MAAA;AAAD,KAA3F,CAAN;AAEA,UAAM,QAAQ,CAAC,uBAAT,CAAiC,YAAjC,EAA+C,IAA/C,EAAqD,IAArD,CAAN;AACD;;AAnM0C;;;;AA6M7C,eAAe,eAAf,CAA+B,MAA/B,EAA+C,YAA/C,EAAqE,OAArE,EAAiF;AAC/E;AACA,WAAS,QAAT,CAAkB,KAAlB,EAAiC,EAAjC,EAA2C;AACzC,QAAI,EAAE,IAAI,OAAV,EAAmB;AACjB,aAAO,OAAO,CAAC,EAAD,CAAd;AACD,KAFD,MAGK;AACH,YAAM,IAAI,KAAJ,CAAU,SAAS,EAAE,iBAArB,CAAN;AACD;AACF;;AACD,QAAM,MAAM,GAAG,CAAC,MAAM,yBAAS,YAAT,EAAuB,MAAvB,CAAP,EACZ,OADY,CACJ,kBADI,EACgB,QADhB,EAEZ,OAFY,CAEJ,mBAFI,EAEiB,CAAC,KAAD,EAAQ,EAAR,KAAc;AAC1C,uBAAI,IAAJ,CAAS,6DAAT;;AACA,WAAO,QAAQ,CAAC,KAAD,EAAQ,EAAE,CAAC,IAAH,EAAR,CAAf;AACD,GALY,CAAf;AAOA,QAAM,UAAU,GAAG,MAAM,MAAM,CAAC,WAAP,CAAmB;AAAC,IAAA,MAAM,EAAE,IAAI,CAAC,QAAL,CAAc,YAAd,EAA4B,MAA5B;AAAT,GAAnB,CAAzB;AACA,QAAM,2BAAW,UAAX,EAAuB,MAAvB,CAAN;AACA,SAAO,UAAP;AACD,C", "sourcesContent": ["import { path7za } from \"7zip-bin\"\nimport { Arch, executeAppBuilder, log, TmpDir, toLinuxArchString, use } from \"builder-util\"\nimport { unlinkIfExists } from \"builder-util/out/fs\"\nimport { ensureDir, outputFile, readFile } from \"fs-extra\"\nimport * as path from \"path\"\nimport { DebOptions, LinuxTargetSpecificOptions } from \"..\"\nimport { smarten } from \"../appInfo\"\nimport { Target } from \"../core\"\nimport * as errorMessages from \"../errorMessages\"\nimport { LinuxPackager } from \"../linuxPackager\"\nimport { objectToArgs } from \"../util/appBuilder\"\nimport { computeEnv } from \"../util/bundledTool\"\nimport { isMacOsSierra } from \"../util/macosVersion\"\nimport { getTemplatePath } from \"../util/pathManager\"\nimport { installPrefix, LinuxTargetHelper } from \"./LinuxTargetHelper\"\nimport { getLinuxToolsPath } from \"./tools\"\n\ninterface FpmOptions {\n  maintainer: string | undefined\n  vendor: string\n  url: string\n}\n\nexport default class FpmTarget extends Target {\n  readonly options: LinuxTargetSpecificOptions = {...this.packager.platformSpecificBuildOptions, ...(this.packager.config as any)[this.name]}\n\n  private readonly scriptFiles: Promise<Array<string>>\n\n  constructor(name: string, private readonly packager: LinuxPackager, private readonly helper: LinuxTargetHelper, readonly outDir: string) {\n    super(name, false)\n\n    this.scriptFiles = this.createScripts()\n  }\n\n  private async createScripts(): Promise<Array<string>> {\n    const defaultTemplatesDir = getTemplatePath(\"linux\")\n\n    const packager = this.packager\n    const templateOptions = {\n      // old API compatibility\n      executable: packager.executableName,\n      productFilename: packager.appInfo.productFilename, ...packager.platformSpecificBuildOptions}\n\n    function getResource(value: string | null | undefined, defaultFile: string) {\n      if (value == null) {\n        return path.join(defaultTemplatesDir, defaultFile)\n      }\n      return path.resolve(packager.projectDir, value)\n    }\n\n    return await Promise.all<string>([\n      writeConfigFile(packager.info.tempDirManager, getResource(this.options.afterInstall, \"after-install.tpl\"), templateOptions),\n      writeConfigFile(packager.info.tempDirManager, getResource(this.options.afterRemove, \"after-remove.tpl\"), templateOptions)\n    ])\n  }\n\n  checkOptions(): Promise<any> {\n    return this.computeFpmMetaInfoOptions()\n  }\n\n  private async computeFpmMetaInfoOptions(): Promise<FpmOptions> {\n    const packager = this.packager\n    const projectUrl = await packager.appInfo.computePackageUrl()\n    const errors: Array<string> = []\n    if (projectUrl == null) {\n      errors.push(\"Please specify project homepage, see https://electron.build/configuration/configuration#Metadata-homepage\")\n    }\n\n    const options = this.options\n    let author = options.maintainer\n    if (author == null) {\n      const a = packager.info.metadata.author\n      if (a == null || a.email == null) {\n        errors.push(errorMessages.authorEmailIsMissed)\n      }\n      else {\n        author = `${a.name} <${a.email}>`\n      }\n    }\n\n    if (errors.length > 0) {\n      throw new Error(errors.join(\"\\n\\n\"))\n    }\n\n    return {\n      maintainer: author!!,\n      url: projectUrl!!,\n      vendor: options.vendor || author!!,\n    }\n  }\n\n  async build(appOutDir: string, arch: Arch): Promise<any> {\n    const target = this.name\n\n    // tslint:disable:no-invalid-template-strings\n    let nameFormat = \"${name}-${version}-${arch}.${ext}\"\n    let isUseArchIfX64 = false\n    if (target === \"deb\") {\n      nameFormat = \"${name}_${version}_${arch}.${ext}\"\n      isUseArchIfX64 = true\n    }\n    else if (target === \"rpm\") {\n      nameFormat = \"${name}-${version}.${arch}.${ext}\"\n      isUseArchIfX64 = true\n    }\n\n    const packager = this.packager\n    const artifactPath = path.join(this.outDir, packager.expandArtifactNamePattern(this.options, target, arch, nameFormat, !isUseArchIfX64))\n\n    await packager.info.callArtifactBuildStarted({\n      targetPresentableName: target,\n      file: artifactPath,\n      arch,\n    })\n\n    await unlinkIfExists(artifactPath)\n    if (packager.packagerOptions.prepackaged != null) {\n      await ensureDir(this.outDir)\n    }\n\n    const scripts = await this.scriptFiles\n    const appInfo = packager.appInfo\n    const options = this.options\n    const synopsis = options.synopsis\n    const args = [\n      \"--architecture\", toLinuxArchString(arch, target),\n      \"--name\", appInfo.linuxPackageName,\n      \"--after-install\", scripts[0],\n      \"--after-remove\", scripts[1],\n      \"--description\", smarten(target === \"rpm\" ? this.helper.getDescription(options)! : `${synopsis || \"\"}\\n ${this.helper.getDescription(options)}`),\n      \"--version\", appInfo.version,\n      \"--package\", artifactPath,\n    ]\n\n    objectToArgs(args, await this.computeFpmMetaInfoOptions() as any)\n\n    const packageCategory = options.packageCategory\n    if (packageCategory != null) {\n      args.push(\"--category\", packageCategory)\n    }\n\n    if (target === \"deb\") {\n      use((options as DebOptions).priority, it => args.push(\"--deb-priority\", it!))\n    }\n    else if (target === \"rpm\") {\n      if (synopsis != null) {\n        args.push(\"--rpm-summary\", smarten(synopsis))\n      }\n    }\n\n    const fpmConfiguration: FpmConfiguration = {\n      args, target,\n    }\n\n    if (options.compression != null) {\n      fpmConfiguration.compression = options.compression\n    }\n\n    // noinspection JSDeprecatedSymbols\n    const depends = options.depends\n    if (depends != null) {\n      if (Array.isArray(depends)) {\n        fpmConfiguration.customDepends = depends\n      }\n      else {\n        // noinspection SuspiciousTypeOfGuard\n        if (typeof depends === \"string\") {\n          fpmConfiguration.customDepends = [depends as string]\n        }\n        else {\n          throw new Error(`depends must be Array or String, but specified as: ${depends}`)\n        }\n      }\n    }\n\n    use(packager.info.metadata.license, it => args.push(\"--license\", it!))\n    use(appInfo.buildNumber, it => args.push(\"--iteration\", it!))\n\n    use(options.fpm, it => args.push(...it as any))\n\n    args.push(`${appOutDir}/=${installPrefix}/${appInfo.productFilename}`)\n    for (const icon of (await this.helper.icons)) {\n      const extWithDot = path.extname(icon.file)\n      const sizeName = extWithDot === \".svg\" ? \"scalable\" : `${icon.size}x${icon.size}`\n      args.push(`${icon.file}=/usr/share/icons/hicolor/${sizeName}/apps/${packager.executableName}${extWithDot}`)\n    }\n\n    const mimeTypeFilePath = await this.helper.mimeTypeFiles\n    if (mimeTypeFilePath != null) {\n      args.push(`${mimeTypeFilePath}=/usr/share/mime/packages/${packager.executableName}.xml`)\n    }\n\n    const desktopFilePath = await this.helper.writeDesktopEntry(this.options)\n    args.push(`${desktopFilePath}=/usr/share/applications/${packager.executableName}.desktop`)\n\n    if (packager.packagerOptions.effectiveOptionComputed != null && await packager.packagerOptions.effectiveOptionComputed([args, desktopFilePath])) {\n      return\n    }\n\n    const env = {\n      ...process.env,\n      SZA_PATH: path7za,\n      SZA_COMPRESSION_LEVEL: packager.compression === \"store\" ? \"0\" : \"9\",\n    }\n\n    // rpmbuild wants directory rpm with some default config files. Even if we can use dylibbundler, path to such config files are not changed (we need to replace in the binary)\n    // so, for now, brew install rpm is still required.\n    if (target !== \"rpm\" && await isMacOsSierra()) {\n      const linuxToolsPath = await getLinuxToolsPath()\n      Object.assign(env, {\n        PATH: computeEnv(process.env.PATH, [path.join(linuxToolsPath, \"bin\")]),\n        DYLD_LIBRARY_PATH: computeEnv(process.env.DYLD_LIBRARY_PATH, [path.join(linuxToolsPath, \"lib\")]),\n      })\n    }\n\n    await executeAppBuilder([\"fpm\", \"--configuration\", JSON.stringify(fpmConfiguration)], undefined, {env})\n\n    await packager.dispatchArtifactCreated(artifactPath, this, arch)\n  }\n}\n\ninterface FpmConfiguration {\n  target: string\n  args: Array<string>\n  customDepends?: Array<string>\n  compression?: string | null\n}\n\nasync function writeConfigFile(tmpDir: TmpDir, templatePath: string, options: any): Promise<string> {\n  //noinspection JSUnusedLocalSymbols\n  function replacer(match: string, p1: string) {\n    if (p1 in options) {\n      return options[p1]\n    }\n    else {\n      throw new Error(`Macro ${p1} is not defined`)\n    }\n  }\n  const config = (await readFile(templatePath, \"utf8\"))\n    .replace(/\\${([a-zA-Z]+)}/g, replacer)\n    .replace(/<%=([a-zA-Z]+)%>/g, (match, p1) => {\n      log.warn(\"<%= varName %> is deprecated, please use ${varName} instead\")\n      return replacer(match, p1.trim())\n    })\n\n  const outputPath = await tmpDir.getTempFile({suffix: path.basename(templatePath, \".tpl\")})\n  await outputFile(outputPath, config)\n  return outputPath\n}\n"], "sourceRoot": ""}