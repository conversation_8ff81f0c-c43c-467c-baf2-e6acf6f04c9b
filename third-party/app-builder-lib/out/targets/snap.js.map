{"version": 3, "sources": ["../../src/targets/snap.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAGA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;AAEA,MAAM,YAAY,GAAG,CAAC,SAAD,EAAY,gBAAZ,EAA8B,MAA9B,EAAsC,KAAtC,EAA6C,SAA7C,EAAwD,QAAxD,EAAkE,iBAAlE,EAAqF,SAArF,EAAgG,WAAhG,EAA6G,gBAA7G,EAA+H,YAA/H,EAA6I,QAA7I,CAArB;;AAEc,MAAO,UAAP,SAA0B,cAA1B,CAAgC;AAK5C,EAAA,WAAA,CAAY,IAAZ,EAA2C,QAA3C,EAAqF,MAArF,EAAyH,MAAzH,EAAuI;AACrI,UAAM,IAAN;AADyC,SAAA,QAAA,GAAA,QAAA;AAA0C,SAAA,MAAA,GAAA,MAAA;AAAoC,SAAA,MAAA,GAAA,MAAA;AAJhH,SAAA,OAAA,GAAuB,EAAC,GAAG,KAAK,QAAL,CAAc,4BAAlB;AAAgD,SAAI,KAAK,QAAL,CAAc,MAAd,CAA6B,KAAK,IAAlC;AAApD,KAAvB;AAEF,SAAA,gBAAA,GAAmB,KAAnB;AAIN;;AAEO,EAAA,cAAc,CAAC,MAAD,EAA2C,WAA3C,EAAqE;AACzF,UAAM,MAAM,GAAG,mCAAgB,MAAhB,EAAwB,WAAxB,CAAf;;AACA,QAAI,MAAM,KAAK,WAAf,EAA4B;AAC1B,WAAK,gBAAL,GAAwB,KAAxB;AACD;;AACD,WAAO,MAAP;AACD;;AAEO,QAAM,gBAAN,CAAuB,IAAvB,EAAiC;AACvC,QAAI,CAAC,KAAK,mCAAL,CAAyC,OAAzC,CAAL,EAAwD;AACtD,UAAI,CAAC,KAAK,mCAAL,CAAyC,cAAzC,CAAL,EAA+D;AAC7D,cAAM,KAAI,wCAAJ,EAA8B,iDAA9B,CAAN;AACD;;AAED,yBAAI,IAAJ,CAAS,sDAAT;AACD;;AAED,UAAM,OAAO,GAAG,KAAK,QAAL,CAAc,OAA9B;AACA,UAAM,QAAQ,GAAG,KAAK,QAAL,CAAc,cAAd,CAA6B,WAA7B,EAAjB;AACA,UAAM,OAAO,GAAG,KAAK,OAArB;AAEA,UAAM,KAAK,GAAG,0BAA0B,CAAC,KAAK,OAAL,CAAa,KAAd,CAAxC;AAEA,UAAM,SAAS,GAAG,KAAK,cAAL,CAAoB,KAAK,IAAI,IAAT,GAAgB,IAAhB,GAAuB,MAAM,CAAC,mBAAP,CAA2B,KAA3B,CAA3C,EAA8E,YAA9E,CAAlB;AAEA,UAAM,KAAK,GAAG,0BAA0B,CAAC,KAAK,OAAL,CAAa,KAAd,CAAxC;AAEA,UAAM,aAAa,GAAG,mCAAQ,OAAO,CAAC,aAAhB,CAAtB;AACA,UAAM,oBAAoB,GAAG,uBAAuB,EAApD;AACA,UAAM,aAAa,GAAG,KAAK,cAAL,CAAoB,OAAO,CAAC,aAA5B,EAA2C,oBAA3C,CAAtB;AAEA,SAAK,gBAAL,GAAwB,KAAK,OAAL,CAAa,cAAb,KAAgC,KAAhC,KACrB,IAAI,KAAK,oBAAK,GAAd,IAAqB,IAAI,KAAK,oBAAK,MADd,KAEtB,aAAa,CAAC,MAAd,KAAyB,CAFH,IAGtB,4BAA4B,CAAC,aAAD,EAAgB,oBAAhB,CAH9B;AAKA,UAAM,aAAa,GAAQ;AACzB,MAAA,OAAO,EAAE,YADgB;AAEzB,MAAA,KAAK,EAAE,SAFkB;AAGzB,MAAA,OAAO,EAAE;AAHgB,KAA3B;AAMA,UAAM,IAAI,GAAQ,wBAAS,MAAM,yBAAS,IAAI,CAAC,IAAL,CAAU,oCAAgB,MAAhB,CAAV,EAAmC,gBAAnC,CAAT,EAA+D,OAA/D,CAAf,CAAlB;;AACA,QAAI,KAAK,gBAAT,EAA2B;AACzB,aAAO,aAAa,CAAC,OAArB;AACD;;AACD,QAAI,OAAO,CAAC,KAAR,IAAiB,IAArB,EAA2B;AACzB,MAAA,IAAI,CAAC,KAAL,GAAa,OAAO,CAAC,KAArB;AACD;;AACD,QAAI,OAAO,CAAC,WAAR,IAAuB,IAA3B,EAAiC;AAC/B,MAAA,IAAI,CAAC,WAAL,GAAmB,OAAO,CAAC,WAA3B;AACD;;AACD,QAAI,OAAO,CAAC,YAAR,IAAwB,IAA5B,EAAkC;AAChC,MAAA,IAAI,CAAC,KAAL,CAAW,GAAX,CAAe,KAAf,GAAuB,OAAO,CAAC,YAA/B;AACD;;AACD,QAAI,OAAO,CAAC,MAAR,IAAkB,IAAtB,EAA4B;AAC1B,MAAA,IAAI,CAAC,MAAL,GAAc,OAAO,CAAC,MAAtB;AACD;;AACD,QAAI,KAAK,IAAI,IAAb,EAAmB;AACjB,MAAA,aAAa,CAAC,KAAd,GAAsB,MAAM,CAAC,mBAAP,CAA2B,KAA3B,CAAtB;;AACA,WAAK,MAAM,QAAX,IAAuB,aAAa,CAAC,KAArC,EAA4C;AAC1C,cAAM,WAAW,GAAG,KAAK,CAAC,QAAD,CAAzB;;AACA,YAAI,WAAW,IAAI,IAAnB,EAAyB;AACvB;AACD;;AACD,YAAI,CAAC,IAAI,CAAC,KAAV,EAAiB;AACf,UAAA,IAAI,CAAC,KAAL,GAAa,EAAb;AACD;;AACD,QAAA,IAAI,CAAC,KAAL,CAAW,QAAX,IAAuB,WAAvB;AACD;AACF;;AAED,mCAAW,IAAX,EAAiB;AACf,MAAA,IAAI,EAAE,QADS;AAEf,MAAA,OAAO,EAAE,OAAO,CAAC,OAFF;AAGf,MAAA,KAAK,EAAE,OAAO,CAAC,KAAR,IAAiB,OAAO,CAAC,WAHjB;AAIf,MAAA,OAAO,EAAE,OAAO,CAAC,OAAR,IAAmB,OAAO,CAAC,WAJrB;AAKf,MAAA,WAAW,EAAE,KAAK,MAAL,CAAY,cAAZ,CAA2B,OAA3B,CALE;AAMf,MAAA,aAAa,EAAE,CAAC,sCAAkB,IAAlB,EAAwB,MAAxB,CAAD,CANA;AAOf,MAAA,IAAI,EAAE;AACJ,SAAC,QAAD,GAAY;AADR,OAPS;AAUf,MAAA,KAAK,EAAE;AACL,QAAA,GAAG,EAAE;AACH,4BAAkB;AADf;AADA;AAVQ,KAAjB;;AAiBA,QAAI,OAAO,CAAC,SAAZ,EAAuB;AACrB,MAAA,aAAa,CAAC,SAAd,GAA0B,GAAG,IAAI,CAAC,IAAI,UAAtC;AACD;;AAED,QAAI,OAAO,CAAC,WAAR,KAAwB,SAA5B,EAAuC;AACrC,aAAO,aAAa,CAAC,KAArB;AACA,aAAO,IAAI,CAAC,KAAZ;AACD,KAHD,MAIK;AACH,YAAM,WAAW,GAAG,iBAAiB,CAAC,IAAD,CAArC;AACA,MAAA,aAAa,CAAC,WAAd,GAA4B;AAC1B;AACA;AACA,QAAA,eAAe,EAAE,GAHS;AAI1B,QAAA,MAAM,EAAE,kBAJkB;AAK1B,QAAA,IAAI,EAAE,yDALoB;AAM1B,QAAA,oBAAoB,EAAE,sBANI;AAO1B,QAAA,eAAe,EAAE,CACf,oBADe,EAEf,uCAAuC,WAAvC,GAAqD,iBAArD,GAAyE,WAF1D,EAGf,0CAHe,EAIf,eAAe,WAAf,GAA6B,iBAA7B,GAAiD,WAJlC,EAKf,IALe,CAKV,GALU,CAPS;AAa1B,WAAG,OAAO,CAAC;AAbe,OAA5B;;AAgBA,UAAI,KAAK,IAAI,IAAb,EAAmB;AACjB,aAAK,MAAM,QAAX,IAAuB,SAAvB,EAAkC;AAChC,gBAAM,WAAW,GAAG,KAAK,CAAC,QAAD,CAAzB;;AACA,cAAI,WAAW,IAAI,IAAnB,EAAyB;AACvB;AACD;;AAED,UAAA,IAAI,CAAC,KAAL,CAAW,QAAX,IAAuB,WAAvB;AACD;AACF;AACF;;AAED,QAAI,aAAa,CAAC,MAAd,GAAuB,CAA3B,EAA8B;AAC5B,MAAA,IAAI,CAAC,KAAL,CAAW,GAAX,CAAe,gBAAf,IAAmC,aAAnC;AACD;;AACD,QAAI,OAAO,CAAC,KAAR,IAAiB,IAArB,EAA2B;AACzB,MAAA,IAAI,CAAC,KAAL,CAAW,GAAX,CAAe,KAAf,GAAuB,OAAO,CAAC,KAA/B;AACD;;AAED,QAAI,OAAO,CAAC,OAAR,IAAmB,IAAvB,EAA6B;AAC3B,MAAA,IAAI,CAAC,OAAL,GAAe,mCAAQ,OAAO,CAAC,OAAhB,CAAf;AACD;;AAED,WAAO,IAAP;AACD;;AAED,QAAM,KAAN,CAAY,SAAZ,EAA+B,IAA/B,EAAyC;AACvC,UAAM,QAAQ,GAAG,KAAK,QAAtB;AACA,UAAM,OAAO,GAAG,KAAK,OAArB,CAFuC,CAGvC;;AACA,UAAM,YAAY,GAAG,QAAQ,CAAC,yBAAT,CAAmC,KAAK,OAAxC,EAAiD,MAAjD,EAAyD,IAAzD,EAA+D,mCAA/D,EAAoG,KAApG,CAArB;AACA,UAAM,YAAY,GAAG,IAAI,CAAC,IAAL,CAAU,KAAK,MAAf,EAAuB,YAAvB,CAArB;AACA,UAAM,QAAQ,CAAC,IAAT,CAAc,wBAAd,CAAuC;AAC3C,MAAA,qBAAqB,EAAE,MADoB;AAE3C,MAAA,IAAI,EAAE,YAFqC;AAG3C,MAAA;AAH2C,KAAvC,CAAN;AAMA,UAAM,IAAI,GAAG,MAAM,KAAK,gBAAL,CAAsB,IAAtB,CAAnB;;AACA,QAAI,KAAK,gBAAT,EAA2B;AACzB,aAAO,IAAI,CAAC,KAAZ;AACD;;AAED,UAAM,QAAQ,GAAG,MAAM,sCAAmB,IAAnB,EAAyB,QAAzB,EAAmC,IAAnC,CAAvB;AACA,UAAM,QAAQ,GAAG,sCAAkB,IAAlB,EAAwB,MAAxB,CAAjB;AACA,UAAM,IAAI,GAAG,CACX,MADW,EAEX,OAFW,EAEF,SAFE,EAGX,SAHW,EAGA,QAHA,EAIX,QAJW,EAID,QAJC,EAKX,UALW,EAKC,YALD,EAMX,cANW,EAMK,KAAK,QAAL,CAAc,cANnB,CAAb;AASA,UAAM,KAAK,MAAL,CAAY,KAAlB;;AACA,QAAI,KAAK,MAAL,CAAY,WAAZ,IAA2B,IAA/B,EAAqC;AACnC,UAAI,CAAC,KAAK,gBAAV,EAA4B;AAC1B,QAAA,IAAI,CAAC,IAAL,GAAY,mBAAZ;AACD;;AACD,MAAA,IAAI,CAAC,IAAL,CAAU,QAAV,EAAoB,KAAK,MAAL,CAAY,WAAhC;AACD,KAlCsC,CAoCvC;;;AACA,UAAM,WAAW,GAAG,IAAI,CAAC,IAAL,CAAU,QAAV,EAAoB,KAAK,gBAAL,GAAwB,MAAxB,GAAiC,MAArD,CAApB;AACA,UAAM,WAAW,GAAG,IAAI,CAAC,IAAL,CAAU,WAAV,EAAuB,KAAvB,EAA8B,GAAG,IAAI,CAAC,IAAI,UAA1C,CAApB;AACA,UAAM,KAAK,MAAL,CAAY,iBAAZ,CAA8B,KAAK,OAAnC,EAA4C,QAAQ,CAAC,cAAT,GAA0B,KAAtE,EAA6E,WAA7E,EAA0F;AAC9F;AACA,MAAA,IAAI,EAAE;AAFwF,KAA1F,CAAN;;AAKA,QAAI,KAAK,mCAAL,CAAyC,OAAzC,KAAqD,CAAC,uBAAuB,CAAC,IAAD,CAAjF,EAAyF;AACvF,MAAA,IAAI,CAAC,IAAL,CAAU,6BAAV;;AACA,UAAI,KAAK,gBAAT,EAA2B;AACzB,QAAA,IAAI,CAAC,IAAL,CAAU,WAAV,EAAuB,gBAAvB;AACD;AACF;;AAED,QAAI,QAAQ,CAAC,eAAT,CAAyB,uBAAzB,IAAoD,IAApD,KAA4D,MAAM,QAAQ,CAAC,eAAT,CAAyB,uBAAzB,CAAiD;AAAC,MAAA,IAAD;AAAO,MAAA,WAAP;AAAoB,MAAA;AAApB,KAAjD,CAAlE,CAAJ,EAAmJ;AACjJ;AACD;;AAED,UAAM,2BAAW,IAAI,CAAC,IAAL,CAAU,WAAV,EAAuB,KAAK,gBAAL,GAAwB,WAAxB,GAAsC,gBAA7D,CAAX,EAA2F,oCAAgB,IAAhB,CAA3F,CAAN;AAEA,UAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,WAAT,CAAqB,OAAO,CAAC,KAA7B,EAAoC,YAApC,CAAvB;;AACA,QAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,MAAA,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,QAArB;AACD;;AAED,QAAI,KAAK,gBAAT,EAA2B;AACzB,MAAA,IAAI,CAAC,IAAL,CAAU,gBAAV,EAA4B,aAAa,QAAQ,EAAjD;AACD;;AACD,UAAM,sCAAkB,IAAlB,CAAN;AAEA,UAAM,QAAQ,CAAC,IAAT,CAAc,0BAAd,CAAyC;AAC7C,MAAA,IAAI,EAAE,YADuC;AAE7C,MAAA,gBAAgB,EAAE,QAAQ,CAAC,uBAAT,CAAiC,YAAjC,EAA+C,MAA/C,EAAuD,IAAvD,EAA6D,KAA7D,CAF2B;AAG7C,MAAA,MAAM,EAAE,IAHqC;AAI7C,MAAA,IAJ6C;AAK7C,MAAA,QAL6C;AAM7C,MAAA,aAAa,EAAE,OAAO,CAAC,OAAR,IAAmB,IAAnB,GAA0B;AAAC,QAAA,QAAQ,EAAE;AAAX,OAA1B,GAAoD;AANtB,KAAzC,CAAN;AAQD;;AAEO,EAAA,mCAAmC,CAAC,OAAD,EAAgB;AACzD,WAAO,MAAM,GAAC,GAAP,CAAW,KAAK,QAAL,CAAc,MAAd,CAAqB,eAArB,IAAwC,OAAnD,EAA4D,OAA5D,CAAP;AACD;;AArO2C;;;;AAwO9C,SAAS,iBAAT,CAA2B,IAA3B,EAAqC;AACnC,UAAQ,IAAR;AACE,SAAK,oBAAK,GAAV;AACE,aAAO,kBAAP;;AACF,SAAK,oBAAK,IAAV;AACE,aAAO,gBAAP;;AACF,SAAK,oBAAK,MAAV;AACE;AACA,aAAO,qBAAP;;AACF,SAAK,oBAAK,KAAV;AACE,aAAO,mBAAP;;AAEF;AACE,YAAM,IAAI,KAAJ,CAAU,oBAAoB,IAAI,EAAlC,CAAN;AAZJ;AAcD;;AAED,SAAS,4BAAT,CAAsC,CAAtC,EAAwD,CAAxD,EAAwE;AACtE,EAAA,CAAC,GAAG,CAAC,CAAC,KAAF,EAAJ;AACA,EAAA,CAAC,GAAG,CAAC,CAAC,KAAF,EAAJ;AACA,EAAA,CAAC,CAAC,IAAF;AACA,EAAA,CAAC,CAAC,IAAF;AACA,SAAO,CAAC,CAAC,MAAF,KAAa,CAAC,CAAC,MAAf,IAAyB,CAAC,CAAC,KAAF,CAAQ,CAAC,KAAD,EAAQ,KAAR,KAAkB,KAAK,KAAK,CAAC,CAAC,KAAD,CAArC,CAAhC;AACD;;AAED,SAAS,0BAAT,CAAoC,GAApC,EAA2G;AACzG,MAAI,GAAG,IAAI,IAAX,EAAiB;AACf,WAAO,IAAP;AACD;;AAED,QAAM,MAAM,GAAQ,EAApB;;AACA,OAAK,MAAM,IAAX,IAAoB,KAAK,CAAC,OAAN,CAAc,GAAd,IAAqB,GAArB,GAA2B,CAAC,GAAD,CAA/C,EAAuD;AACrD,QAAI,OAAO,IAAP,KAAgB,QAApB,EAA8B;AAC5B,MAAA,MAAM,CAAC,IAAD,CAAN,GAAe,IAAf;AACD,KAFD,MAGK;AACH,MAAA,MAAM,CAAC,MAAP,CAAc,MAAd,EAAsB,IAAtB;AACD;AACF;;AACD,SAAO,MAAP;AACD;;AAED,SAAS,uBAAT,CAAiC,IAAjC,EAA0C;AACxC,MAAI,IAAI,CAAC,KAAL,IAAc,IAAlB,EAAwB;AACtB,SAAK,MAAM,QAAX,IAAuB,MAAM,CAAC,IAAP,CAAY,IAAI,CAAC,KAAjB,CAAvB,EAAgD;AAC9C,YAAM,IAAI,GAAG,IAAI,CAAC,KAAL,CAAW,QAAX,CAAb;;AACA,UAAI,IAAI,CAAC,SAAL,KAAmB,iBAAnB,IAAwC,IAAI,CAAC,eAAD,CAAJ,KAA0B,IAAtE,EAA4E;AAC1E,eAAO,IAAP;AACD;AACF;AACF;;AACD,SAAO,KAAP;AACD;;AAED,SAAS,uBAAT,GAAgC;AAC9B;AACA;AACA,SAAO,CAAC,UAAD,EAAa,SAAb,EAAwB,SAAxB,EAAmC,oBAAnC,EAAyD,eAAzD,CAAP;AACD,C", "sourcesContent": ["import { Arch, deepAssign, executeApp<PERSON>uilder, InvalidConfigurationError, log, replaceDefault as _replaceDefault, serializeToYaml, toLinuxArchString } from \"builder-util\"\nimport { asArray } from \"builder-util-runtime\"\nimport { outputFile, readFile } from \"fs-extra\"\nimport { safeLoad } from \"js-yaml\"\nimport * as path from \"path\"\nimport * as semver from \"semver\"\nimport { SnapOptions } from \"..\"\nimport { Target } from \"../core\"\nimport { LinuxPackager } from \"../linuxPackager\"\nimport { PlugDescriptor } from \"../options/SnapOptions\"\nimport { getTemplatePath } from \"../util/pathManager\"\nimport { LinuxTargetHelper } from \"./LinuxTargetHelper\"\nimport { createStageDirPath } from \"./targetUtil\"\n\nconst defaultPlugs = [\"desktop\", \"desktop-legacy\", \"home\", \"x11\", \"wayland\", \"unity7\", \"browser-support\", \"network\", \"gsettings\", \"audio-playback\", \"pulseaudio\", \"opengl\"]\n\nexport default class SnapTarget extends Target {\n  readonly options: SnapOptions = {...this.packager.platformSpecificBuildOptions, ...(this.packager.config as any)[this.name]}\n\n  public isUseTemplateApp = false\n\n  constructor(name: string, private readonly packager: LinuxPackager, private readonly helper: LinuxTargetHelper, readonly outDir: string) {\n    super(name)\n  }\n\n  private replaceDefault(inList: Array<string> | null | undefined, defaultList: Array<string>) {\n    const result = _replaceDefault(inList, defaultList)\n    if (result !== defaultList) {\n      this.isUseTemplateApp = false\n    }\n    return result\n  }\n\n  private async createDescriptor(arch: Arch): Promise<any> {\n    if (!this.isElectronVersionGreaterOrEqualThan(\"4.0.0\")) {\n      if (!this.isElectronVersionGreaterOrEqualThan(\"2.0.0-beta.1\")) {\n        throw new InvalidConfigurationError(\"Electron 2 and higher is required to build Snap\")\n      }\n\n      log.warn(\"Electron 4 and higher is highly recommended for Snap\")\n    }\n\n    const appInfo = this.packager.appInfo\n    const snapName = this.packager.executableName.toLowerCase()\n    const options = this.options\n\n    const plugs = normalizePlugConfiguration(this.options.plugs)\n\n    const plugNames = this.replaceDefault(plugs == null ? null : Object.getOwnPropertyNames(plugs), defaultPlugs)\n\n    const slots = normalizePlugConfiguration(this.options.slots)\n\n    const buildPackages = asArray(options.buildPackages)\n    const defaultStagePackages = getDefaultStagePackages()\n    const stagePackages = this.replaceDefault(options.stagePackages, defaultStagePackages)\n\n    this.isUseTemplateApp = this.options.useTemplateApp !== false &&\n      (arch === Arch.x64 || arch === Arch.armv7l) &&\n      buildPackages.length === 0 &&\n      isArrayEqualRegardlessOfSort(stagePackages, defaultStagePackages)\n\n    const appDescriptor: any = {\n      command: \"command.sh\",\n      plugs: plugNames,\n      adapter: \"none\",\n    }\n\n    const snap: any = safeLoad(await readFile(path.join(getTemplatePath(\"snap\"), \"snapcraft.yaml\"), \"utf-8\"))\n    if (this.isUseTemplateApp) {\n      delete appDescriptor.adapter\n    }\n    if (options.grade != null) {\n      snap.grade = options.grade\n    }\n    if (options.confinement != null) {\n      snap.confinement = options.confinement\n    }\n    if (options.appPartStage != null) {\n      snap.parts.app.stage = options.appPartStage\n    }\n    if (options.layout != null) {\n      snap.layout = options.layout\n    }\n    if (slots != null) {\n      appDescriptor.slots = Object.getOwnPropertyNames(slots)\n      for (const slotName of appDescriptor.slots) {\n        const slotOptions = slots[slotName]\n        if (slotOptions == null) {\n          continue\n        }\n        if (!snap.slots) {\n          snap.slots = {}\n        }\n        snap.slots[slotName] = slotOptions\n      }\n    }\n    \n    deepAssign(snap, {\n      name: snapName,\n      version: appInfo.version,\n      title: options.title || appInfo.productName,\n      summary: options.summary || appInfo.productName,\n      description: this.helper.getDescription(options),\n      architectures: [toLinuxArchString(arch, \"snap\")],\n      apps: {\n        [snapName]: appDescriptor\n      },\n      parts: {\n        app: {\n          \"stage-packages\": stagePackages,\n        }\n      },\n    })\n\n    if (options.autoStart) {\n      appDescriptor.autostart = `${snap.name}.desktop`\n    }\n\n    if (options.confinement === \"classic\") {\n      delete appDescriptor.plugs\n      delete snap.plugs\n    }\n    else {\n      const archTriplet = archNameToTriplet(arch)\n      appDescriptor.environment = {\n        // https://github.com/electron-userland/electron-builder/issues/4007\n        // https://github.com/electron/electron/issues/9056\n        DISABLE_WAYLAND: \"1\",\n        TMPDIR: \"$XDG_RUNTIME_DIR\",\n        PATH: \"$SNAP/usr/sbin:$SNAP/usr/bin:$SNAP/sbin:$SNAP/bin:$PATH\",\n        SNAP_DESKTOP_RUNTIME: \"$SNAP/gnome-platform\",\n        LD_LIBRARY_PATH: [\n          \"$SNAP_LIBRARY_PATH\",\n          \"$SNAP/lib:$SNAP/usr/lib:$SNAP/lib/\" + archTriplet + \":$SNAP/usr/lib/\" + archTriplet,\n          \"$LD_LIBRARY_PATH:$SNAP/lib:$SNAP/usr/lib\",\n          \"$SNAP/lib/\" + archTriplet + \":$SNAP/usr/lib/\" + archTriplet\n        ].join(\":\"),\n        ...options.environment,\n      }\n\n      if (plugs != null) {\n        for (const plugName of plugNames) {\n          const plugOptions = plugs[plugName]\n          if (plugOptions == null) {\n            continue\n          }\n\n          snap.plugs[plugName] = plugOptions\n        }\n      }\n    }\n\n    if (buildPackages.length > 0) {\n      snap.parts.app[\"build-packages\"] = buildPackages\n    }\n    if (options.after != null) {\n      snap.parts.app.after = options.after\n    }\n\n    if (options.assumes != null) {\n      snap.assumes = asArray(options.assumes)\n    }\n\n    return snap\n  }\n\n  async build(appOutDir: string, arch: Arch): Promise<any> {\n    const packager = this.packager\n    const options = this.options\n    // tslint:disable-next-line:no-invalid-template-strings\n    const artifactName = packager.expandArtifactNamePattern(this.options, \"snap\", arch, \"${name}_${version}_${arch}.${ext}\", false)\n    const artifactPath = path.join(this.outDir, artifactName)\n    await packager.info.callArtifactBuildStarted({\n      targetPresentableName: \"snap\",\n      file: artifactPath,\n      arch,\n    })\n\n    const snap = await this.createDescriptor(arch)\n    if (this.isUseTemplateApp) {\n      delete snap.parts\n    }\n\n    const stageDir = await createStageDirPath(this, packager, arch)\n    const snapArch = toLinuxArchString(arch, \"snap\")\n    const args = [\n      \"snap\",\n      \"--app\", appOutDir,\n      \"--stage\", stageDir,\n      \"--arch\", snapArch,\n      \"--output\", artifactPath,\n      \"--executable\", this.packager.executableName,\n    ]\n\n    await this.helper.icons\n    if (this.helper.maxIconPath != null) {\n      if (!this.isUseTemplateApp) {\n        snap.icon = \"snap/gui/icon.png\"\n      }\n      args.push(\"--icon\", this.helper.maxIconPath)\n    }\n\n    // snapcraft.yaml inside a snap directory\n    const snapMetaDir = path.join(stageDir, this.isUseTemplateApp ? \"meta\" : \"snap\")\n    const desktopFile = path.join(snapMetaDir, \"gui\", `${snap.name}.desktop`)\n    await this.helper.writeDesktopEntry(this.options, packager.executableName + \" %U\", desktopFile, {\n      // tslint:disable:no-invalid-template-strings\n      Icon: \"${SNAP}/meta/gui/icon.png\"\n    })\n\n    if (this.isElectronVersionGreaterOrEqualThan(\"5.0.0\") && !isBrowserSandboxAllowed(snap)) {\n      args.push(\"--extraAppArgs=--no-sandbox\")\n      if (this.isUseTemplateApp) {\n        args.push(\"--exclude\", \"chrome-sandbox\")\n      }\n    }\n\n    if (packager.packagerOptions.effectiveOptionComputed != null && await packager.packagerOptions.effectiveOptionComputed({snap, desktopFile, args})) {\n      return\n    }\n\n    await outputFile(path.join(snapMetaDir, this.isUseTemplateApp ? \"snap.yaml\" : \"snapcraft.yaml\"), serializeToYaml(snap))\n\n    const hooksDir = await packager.getResource(options.hooks, \"snap-hooks\")\n    if (hooksDir != null) {\n      args.push(\"--hooks\", hooksDir)\n    }\n\n    if (this.isUseTemplateApp) {\n      args.push(\"--template-url\", `electron4:${snapArch}`)\n    }\n    await executeAppBuilder(args)\n\n    await packager.info.callArtifactBuildCompleted({\n      file: artifactPath,\n      safeArtifactName: packager.computeSafeArtifactName(artifactName, \"snap\", arch, false),\n      target: this,\n      arch,\n      packager,\n      publishConfig: options.publish == null ? {provider: \"snapStore\"} : null,\n    })\n  }\n\n  private isElectronVersionGreaterOrEqualThan(version: string) {\n    return semver.gte(this.packager.config.electronVersion || \"7.0.0\", version)\n  }\n}\n\nfunction archNameToTriplet(arch: Arch): string {\n  switch (arch) {\n    case Arch.x64:\n      return \"x86_64-linux-gnu\"\n    case Arch.ia32:\n      return \"i386-linux-gnu\"\n    case Arch.armv7l:\n      // noinspection SpellCheckingInspection\n      return \"arm-linux-gnueabihf\"\n    case Arch.arm64:\n      return \"aarch64-linux-gnu\"\n\n    default:\n      throw new Error(`Unsupported arch ${arch}`)\n  }\n}\n\nfunction isArrayEqualRegardlessOfSort(a: Array<string>, b: Array<string>) {\n  a = a.slice()\n  b = b.slice()\n  a.sort()\n  b.sort()\n  return a.length === b.length && a.every((value, index) => value === b[index])\n}\n\nfunction normalizePlugConfiguration(raw: Array<string | PlugDescriptor> | PlugDescriptor | null | undefined): { [key: string]: {[name: string]: any } | null } | null {\n  if (raw == null) {\n    return null\n  }\n\n  const result: any = {}\n  for (const item of (Array.isArray(raw) ? raw : [raw])) {\n    if (typeof item === \"string\") {\n      result[item] = null\n    }\n    else {\n      Object.assign(result, item)\n    }\n  }\n  return result\n}\n\nfunction isBrowserSandboxAllowed(snap: any): boolean {\n  if (snap.plugs != null) {\n    for (const plugName of Object.keys(snap.plugs)) {\n      const plug = snap.plugs[plugName]\n      if (plug.interface === \"browser-support\" && plug[\"allow-sandbox\"] === true) {\n        return true\n      }\n    }\n  }\n  return false\n}\n\nfunction getDefaultStagePackages() {\n  // libxss1 - was \"error while loading shared libraries: libXss.so.1\" on Xubuntu 16.04\n  // noinspection SpellCheckingInspection\n  return [\"libnspr4\", \"libnss3\", \"libxss1\", \"libappindicator3-1\", \"libsecret-1-0\"]\n}\n"], "sourceRoot": ""}