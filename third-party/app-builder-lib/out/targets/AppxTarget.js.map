{"version": 3, "sources": ["../../src/targets/AppxTarget.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAGA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;;;AAEA,MAAM,oBAAoB,GAAG,MAA7B;AAEA,MAAM,4BAA4B,GAA8B;AAC9D,mBAAiB,sBAD6C;AAE9D,2BAAyB,wBAFqC;AAG9D,yBAAuB,sBAHuC;AAI9D,yBAAuB;AAJuC,CAAhE;AAOA,MAAM,qBAAqB,GAAG,OAA9B;;AAEc,MAAO,UAAP,SAA0B,cAA1B,CAAgC;AAG5C,EAAA,WAAA,CAA6B,QAA7B,EAA6D,MAA7D,EAA2E;AACzE,UAAM,MAAN;AAD2B,SAAA,QAAA,GAAA,QAAA;AAAgC,SAAA,MAAA,GAAA,MAAA;AAFpD,SAAA,OAAA,GAAuB,+BAAW,EAAX,EAAe,KAAK,QAAL,CAAc,4BAA7B,EAA2D,KAAK,QAAL,CAAc,MAAd,CAAqB,IAAhF,CAAvB;;AAKP,QAAI,OAAO,CAAC,QAAR,KAAqB,QAArB,KAAkC,OAAO,CAAC,QAAR,KAAqB,OAArB,IAAgC,mCAAlE,CAAJ,EAAoF;AAClF,YAAM,IAAI,KAAJ,CAAU,sFAAV,CAAN;AACD;AACF,GAT2C,CAW5C;;;AACA,QAAM,KAAN,CAAY,SAAZ,EAA+B,IAA/B,EAAyC;AACvC,UAAM,QAAQ,GAAG,KAAK,QAAtB;AACA,UAAM,YAAY,GAAG,QAAQ,CAAC,+BAAT,CAAyC,KAAK,OAA9C,EAAuD,MAAvD,EAA+D,IAA/D,CAArB;AACA,UAAM,YAAY,GAAG,IAAI,CAAC,IAAL,CAAU,KAAK,MAAf,EAAuB,YAAvB,CAArB;AACA,UAAM,QAAQ,CAAC,IAAT,CAAc,wBAAd,CAAuC;AAC3C,MAAA,qBAAqB,EAAE,MADoB;AAE3C,MAAA,IAAI,EAAE,YAFqC;AAG3C,MAAA;AAH2C,KAAvC,CAAN;AAMA,UAAM,UAAU,GAAG,MAAM,2CAAzB;AACA,UAAM,EAAE,GAAG,MAAM,QAAQ,CAAC,EAAT,CAAY,KAA7B;AAEA,UAAM,QAAQ,GAAG,MAAM,kCAAe,IAAf,EAAqB,QAArB,EAA+B,IAA/B,CAAvB;AAEA,UAAM,WAAW,GAAG,QAAQ,CAAC,WAAT,CAAqB,aAArB,CAApB;AACA,UAAM,YAAY,GAAG,CAAC,MAAD,EAAS;AAAK;AAAd,MACnB,IADmB,EACb,EAAE,CAAC,QAAH,CAAY,WAAZ,CADa,EAEnB,IAFmB,EAEb,EAAE,CAAC,QAAH,CAAY,YAAZ,CAFa,CAArB;;AAIA,QAAI,QAAQ,CAAC,WAAT,KAAyB,OAA7B,EAAsC;AACpC,MAAA,YAAY,CAAC,IAAb,CAAkB,KAAlB;AACD;;AAED,UAAM,WAAW,GAAyB,EAA1C;AACA,IAAA,WAAW,CAAC,IAAZ,CAAiB,MAAM,uBAAgB,GAAhB,CAAoB,gBAAK,SAAL,CAApB,EAAqC,IAAI,IAAG;AACjE,UAAI,QAAQ,GAAG,IAAI,CAAC,SAAL,CAAe,SAAS,CAAC,MAAV,GAAmB,CAAlC,CAAf;;AACA,UAAI,IAAI,CAAC,GAAL,KAAa,IAAjB,EAAuB;AACrB,QAAA,QAAQ,GAAG,QAAQ,CAAC,OAAT,CAAiB,KAAjB,EAAwB,IAAxB,CAAX;AACD;;AACD,aAAO,IAAI,EAAE,CAAC,QAAH,CAAY,IAAZ,CAAiB,WAAW,QAAQ,GAA/C;AACD,KANsB,CAAvB;AAQA,UAAM,YAAY,GAAG,MAAM,KAAK,QAAL,CAAc,WAAd,CAA0B,SAA1B,EAAqC,oBAArC,CAA3B;AACA,UAAM,SAAS,GAAG,MAAM,UAAU,CAAC,iBAAX,CAA6B,EAA7B,EAAiC,UAAjC,EAA6C,YAA7C,CAAxB;AACA,UAAM,UAAU,GAAG,SAAS,CAAC,UAA7B;AAEA,UAAM,YAAY,GAAG,QAAQ,CAAC,WAAT,CAAqB,kBAArB,CAArB;AACA,UAAM,KAAK,aAAL,CAAmB,YAAnB,EAAiC,IAAjC,EAAuC,MAAM,KAAK,oBAAL,EAA7C,EAA0E,UAA1E,CAAN;AACA,UAAM,QAAQ,CAAC,IAAT,CAAc,uBAAd,CAAsC,YAAtC,CAAN;AACA,IAAA,WAAW,CAAC,IAAZ,CAAiB,SAAS,CAAC,QAA3B;AACA,IAAA,WAAW,CAAC,IAAZ,CAAiB,CAAC,IAAI,EAAE,CAAC,QAAH,CAAY,YAAZ,CAAyB,sBAA9B,CAAjB;AACA,UAAM,YAAY,GAAI,IAAI,KAAK,oBAAK,KAAd,GAAsB,KAAtB,GAA8B,oBAAK,IAAL,CAApD;;AAEA,QAAI,sBAAsB,CAAC,UAAD,CAA1B,EAAwC;AACtC,YAAM,OAAO,GAAG,EAAE,CAAC,QAAH,CAAY,QAAQ,CAAC,WAAT,CAAqB,eAArB,CAAZ,CAAhB;AACA,YAAM,WAAW,GAAG,EAAE,CAAC,QAAH,CAAY,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,YAAtB,EAAoC,YAApC,EAAkD,aAAlD,CAAZ,CAApB;AAEA,YAAM,SAAS,GAAG,QAAQ,CAAC,WAAT,CAAqB,aAArB,CAAlB;AACA,YAAM,yBAAS,SAAT,CAAN;AACA,YAAM,uBAAgB,GAAhB,CAAoB,SAAS,CAAC,SAA9B,EAAyC,EAAE,IAAI,0BAAe,EAAf,EAAmB,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,IAAI,CAAC,QAAL,CAAc,EAAd,CAArB,CAAnB,CAA/C,CAAN;AAEA,YAAM,EAAE,CAAC,IAAH,CAAQ,WAAR,EAAqB,CAAC,KAAD,EACzB,YADyB,EAEzB,WAFyB,EAEZ,EAAE,CAAC,QAAH,CAAY,YAAZ,CAFY,EAGzB,cAHyB,EAGT,EAAE,CAAC,QAAH,CAAY,IAAI,CAAC,OAAL,CAAa,SAAb,CAAZ,CAHS,EAIzB,YAJyB,EAIX,EAAE,CAAC,QAAH,CAAY,IAAI,CAAC,IAAL,CAAU,oCAAgB,MAAhB,CAAV,EAAmC,eAAnC,CAAZ,CAJW,EAKzB,aALyB,EAKV,OALU,CAArB,CAAN,CARsC,CAgBtC;;AACA,WAAK,MAAM,YAAX,IAA2B,CAAC,MAAM,wBAAQ,QAAQ,CAAC,GAAjB,CAAP,EAA8B,MAA9B,CAAqC,EAAE,IAAI,EAAE,CAAC,UAAH,CAAc,YAAd,CAA3C,EAAwE,IAAxE,EAA3B,EAA2G;AACzG,QAAA,WAAW,CAAC,IAAZ,CAAiB,CAAC,IAAI,EAAE,CAAC,QAAH,CAAY,QAAQ,CAAC,WAAT,CAAqB,YAArB,CAAZ,CAA+C,MAAM,YAAY,GAAtE,CAAjB;AACD;;AACD,MAAA,YAAY,CAAC,IAAb,CAAkB,IAAlB;AACD;;AAED,QAAI,OAAO,GAAG,SAAd;;AACA,SAAK,MAAM,IAAX,IAAmB,WAAnB,EAAgC;AAC9B,MAAA,OAAO,IAAI,SAAS,IAAI,CAAC,IAAL,CAAU,MAAV,CAApB;AACD;;AACD,UAAM,0BAAU,WAAV,EAAuB,OAAvB,CAAN;AACA,IAAA,QAAQ,CAAC,WAAT,CAAqB,GAArB,CAAyB,cAAzB,EAAyC,OAAzC;;AAEA,QAAI,KAAK,OAAL,CAAa,YAAb,IAA6B,IAAjC,EAAuC;AACrC,MAAA,YAAY,CAAC,IAAb,CAAkB,GAAG,KAAK,OAAL,CAAa,YAAlC;AACD;;AACD,UAAM,EAAE,CAAC,IAAH,CAAQ,EAAE,CAAC,QAAH,CAAY,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,YAAtB,EAAoC,YAApC,EAAkD,cAAlD,CAAZ,CAAR,EAAwF,YAAxF,CAAN;AACA,UAAM,QAAQ,CAAC,IAAT,CAAc,YAAd,CAAN;AAEA,UAAM,QAAQ,CAAC,OAAT,EAAN;AAEA,UAAM,QAAQ,CAAC,IAAT,CAAc,0BAAd,CAAyC;AAC7C,MAAA,IAAI,EAAE,YADuC;AAE7C,MAAA,QAF6C;AAG7C,MAAA,IAH6C;AAI7C,MAAA,gBAAgB,EAAE,QAAQ,CAAC,uBAAT,CAAiC,YAAjC,EAA+C,MAA/C,CAJ2B;AAK7C,MAAA,MAAM,EAAE,IALqC;AAM7C,MAAA,iBAAiB,EAAE,KAAK,OAAL,CAAa;AANa,KAAzC,CAAN;AAQD;;AAEO,eAAa,iBAAb,CAA+B,EAA/B,EAA8C,UAA9C,EAAkE,YAAlE,EAA6F;AACnG,UAAM,QAAQ,GAAkB,EAAhC;AACA,QAAI,UAAJ;AACA,UAAM,SAAS,GAAkB,EAAjC;;AACA,QAAI,YAAY,IAAI,IAApB,EAA0B;AACxB,MAAA,UAAU,GAAG,EAAb;AACD,KAFD,MAGK;AACH,MAAA,UAAU,GAAG,CAAC,MAAM,wBAAQ,YAAR,CAAP,EAA8B,MAA9B,CAAqC,EAAE,IAAI,CAAC,EAAE,CAAC,UAAH,CAAc,GAAd,CAAD,IAAuB,CAAC,EAAE,CAAC,QAAH,CAAY,KAAZ,CAAxB,IAA8C,EAAE,CAAC,QAAH,CAAY,GAAZ,CAAzF,CAAb;;AACA,WAAK,MAAM,IAAX,IAAmB,UAAnB,EAA+B;AAC7B,QAAA,QAAQ,CAAC,IAAT,CAAc,IAAI,EAAE,CAAC,QAAH,CAAY,YAAZ,CAAyB,GAAG,EAAE,CAAC,OAAO,GAAG,IAAI,cAAc,IAAI,GAAjF;AACA,QAAA,SAAS,CAAC,IAAV,CAAe,IAAI,CAAC,IAAL,CAAU,YAAV,EAAwB,IAAxB,CAAf;AACD;AACF;;AAED,SAAK,MAAM,YAAX,IAA2B,MAAM,CAAC,IAAP,CAAY,4BAAZ,CAA3B,EAAsE;AACpE,UAAI,UAAU,CAAC,MAAX,KAAsB,CAAtB,IAA2B,CAAC,sBAAsB,CAAC,UAAD,EAAa,YAAb,CAAtD,EAAkF;AAChF,cAAM,IAAI,GAAG,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,YAAtB,EAAoC,4BAA4B,CAAC,YAAD,CAAhE,CAAb;AACA,QAAA,QAAQ,CAAC,IAAT,CAAc,IAAI,EAAE,CAAC,QAAH,CAAY,IAAZ,CAAiB,cAAc,YAAY,GAA7D;AACA,QAAA,SAAS,CAAC,IAAV,CAAe,IAAf;AACD;AACF,KArBkG,CAuBnG;;;AACA,WAAO;AAAC,MAAA,UAAD;AAAa,MAAA,QAAb;AAAuB,MAAA;AAAvB,KAAP;AACD,GAjI2C,CAmI5C;;;AACQ,QAAM,oBAAN,GAA0B;AAChC,QAAI,OAAM,KAAK,QAAL,CAAc,OAAd,CAAsB,KAA5B,KAAqC,IAAzC,EAA+C;AAC7C,yBAAI,IAAJ,CAAS;AAAC,QAAA,MAAM,EAAE;AAAT,OAAT,EAA+C,oBAA/C;;AACA,aAAO,KAAK,OAAL,CAAa,SAAb,IAA0B,OAAjC;AACD;;AAED,UAAM,QAAQ,GAAG,MAAM,KAAK,QAAL,CAAc,YAAd,CAA2B,KAAlD;AACA,UAAM,SAAS,GAAG,KAAK,OAAL,CAAa,SAAb,KAA2B,QAAQ,IAAI,IAAZ,GAAmB,IAAnB,GAA0B,QAAQ,CAAC,wBAA9D,CAAlB;;AACA,QAAI,SAAS,IAAI,IAAjB,EAAuB;AACrB,YAAM,IAAI,KAAJ,CAAU,+DAAV,CAAN;AACD;;AACD,WAAO,SAAP;AACD;;AAEO,QAAM,aAAN,CAAoB,OAApB,EAAqC,IAArC,EAAiD,SAAjD,EAAoE,UAApE,EAA6F;AACnG,UAAM,OAAO,GAAG,KAAK,QAAL,CAAc,OAA9B;AACA,UAAM,OAAO,GAAG,KAAK,OAArB;AACA,UAAM,UAAU,GAAG,QAAQ,OAAO,CAAC,eAAe,MAAlD;AACA,UAAM,WAAW,GAAG,OAAO,CAAC,WAAR,IAAuB,OAAO,CAAC,WAAnD;AACA,UAAM,UAAU,GAAG,MAAM,KAAK,aAAL,CAAmB,UAAnB,EAA+B,WAA/B,CAAzB;AAEA,UAAM,QAAQ,GAAG,CAAC,MAAM,yBAAS,IAAI,CAAC,IAAL,CAAU,oCAAgB,MAAhB,CAAV,EAAmC,kBAAnC,CAAT,EAAiE,MAAjE,CAAP,EACd,OADc,CACN,qBADM,EACiB,CAAC,KAAD,EAAQ,EAAR,KAAsB;AACpD,cAAQ,EAAR;AACE,aAAK,WAAL;AACE,iBAAO,SAAP;;AAEF,aAAK,sBAAL;AAA6B;AAC3B,kBAAM,IAAI,GAAG,OAAO,CAAC,oBAAR,IAAgC,OAAO,CAAC,WAArD;;AACA,gBAAI,IAAI,IAAI,IAAZ,EAAkB;AAChB,oBAAM,KAAI,wCAAJ,EAA8B,0HAA9B,CAAN;AACD;;AACD,mBAAO,IAAP;AACD;;AAED,aAAK,SAAL;AACE,iBAAO,OAAO,CAAC,4BAAR,CAAqC,OAAO,CAAC,cAAR,KAA2B,IAAhE,CAAP;;AAEF,aAAK,eAAL;AAAsB;AACpB,kBAAM,MAAM,GAAG,OAAO,CAAC,aAAR,IAAyB,OAAO,CAAC,YAAjC,IAAiD,OAAO,CAAC,IAAxE;;AACA,gBAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAD,CAAP,EAAY,EAAZ,CAAT,CAAV,EAAqC;AACnC,kBAAI,OAAO,GAAG,kDAAkD,MAAM,GAAtE;;AACA,kBAAI,OAAO,CAAC,aAAR,IAAyB,IAA7B,EAAmC;AACjC,gBAAA,OAAO,IAAI,wEAAX;AACD;;AACD,oBAAM,KAAI,wCAAJ,EAA8B,OAA9B,CAAN;AACD;;AACD,mBAAO,MAAP;AACD;;AAED,aAAK,cAAL;AACE,iBAAO,OAAO,CAAC,YAAR,IAAwB,OAAO,CAAC,IAAvC;;AAEF,aAAK,YAAL;AACE,iBAAO,UAAP;;AAEF,aAAK,aAAL;AACE,iBAAO,WAAP;;AAEF,aAAK,aAAL;AACE,iBAAO,OAAO,CAAC,WAAR,IAAuB,OAAO,CAAC,WAAtC;;AAEF,aAAK,iBAAL;AACE,iBAAO,OAAO,CAAC,eAAR,IAA2B,SAAlC;;AAEF,aAAK,MAAL;AACE,iBAAO,uBAAP;;AAEF,aAAK,mBAAL;AACE,iBAAO,+BAAP;;AAEF,aAAK,iBAAL;AACE,iBAAO,6BAAP;;AAEF,aAAK,YAAL;AACE,iBAAO,aAAa,CAAC,UAAD,CAApB;;AAEF,aAAK,aAAL;AACE,iBAAO,cAAc,CAAC,UAAD,EAAa,OAAO,CAAC,eAAR,IAA2B,KAAxC,CAArB;;AAEF,aAAK,cAAL;AACE,iBAAO,eAAe,CAAC,UAAD,CAAtB;;AAEF,aAAK,MAAL;AACE,iBAAO,IAAI,KAAK,oBAAK,IAAd,GAAqB,KAArB,GAA8B,IAAI,KAAK,oBAAK,KAAd,GAAsB,OAAtB,GAAgC,KAArE;;AAEF,aAAK,mBAAL;AACE,iBAAO,mBAAmB,CAAC,4BAAQ,OAAO,CAAC,SAAhB,CAAD,CAA1B;;AAEF,aAAK,YAAL;AACE,iBAAO,UAAP;;AAEF,aAAK,YAAL;AACE,iBAAO,IAAI,KAAK,oBAAK,KAAd,GAAsB,cAAtB,GAAuC,cAA9C;;AAEF,aAAK,kBAAL;AACE,iBAAO,IAAI,KAAK,oBAAK,KAAd,GAAsB,cAAtB,GAAuC,cAA9C;;AAEF;AACE,gBAAM,IAAI,KAAJ,CAAU,SAAS,EAAE,iBAArB,CAAN;AA5EJ;AA8ED,KAhFc,CAAjB;AAiFA,UAAM,0BAAU,OAAV,EAAmB,QAAnB,CAAN;AACD;;AAEO,QAAM,aAAN,CAAoB,UAApB,EAAwC,WAAxC,EAA2D;AACjE,UAAM,UAAU,GAAG,4BAAQ,KAAK,QAAL,CAAc,MAAd,CAAqB,SAA7B,EAChB,MADgB,CACT,4BAAQ,KAAK,QAAL,CAAc,4BAAd,CAA2C,SAAnD,CADS,CAAnB;AAGA,UAAM,gBAAgB,GAAG,4BAAQ,KAAK,QAAL,CAAc,MAAd,CAAqB,gBAA7B,EACtB,MADsB,CACf,4BAAQ,KAAK,QAAL,CAAc,4BAAd,CAA2C,gBAAnD,CADe,CAAzB;AAGA,QAAI,wBAAwB,GAAG,KAAK,OAAL,CAAa,sBAA5C;;AACA,QAAI,wBAAwB,KAAK,SAAjC,EAA4C;AAC1C,YAAM,IAAI,GAAG,KAAK,QAAL,CAAc,IAAd,CAAmB,QAAnB,CAA4B,YAAzC;AACA,MAAA,wBAAwB,GAAG,IAAI,IAAI,IAAR,IAAgB,IAAI,CAAC,+BAAD,CAAJ,IAAyC,IAApF;AACD;;AAED,QAAI,CAAC,wBAAD,IACC,UAAU,CAAC,MAAX,KAAsB,CADvB,IAEC,gBAAgB,CAAC,MAAjB,KAA4B,CAF7B,IAGC,KAAK,OAAL,CAAa,oBAAb,KAAsC,SAH3C,EAGsD;AACpD,aAAO,EAAP;AACD;;AAED,QAAI,UAAU,GAAG,cAAjB;;AAEA,QAAI,wBAAJ,EAA8B;AAC5B,MAAA,UAAU,IAAI;wEACoD,UAAU;mFACC,WAAW;6BAFxF;AAID;;AAED,SAAK,MAAM,QAAX,IAAuB,UAAvB,EAAmC;AACjC,WAAK,MAAM,MAAX,IAAqB,4BAAQ,QAAQ,CAAC,OAAjB,CAArB,EAAgD;AAC9C,QAAA,UAAU,IAAI;;kCAEY,MAAM;kCACN,QAAQ,CAAC,IAAI;;2BAHvC;AAMD;AACF;;AAED,SAAK,MAAM,eAAX,IAA8B,gBAA9B,EAAgD;AAC9C,WAAK,MAAM,GAAX,IAAkB,4BAAQ,eAAe,CAAC,GAAxB,CAAlB,EAAgD;AAC9C,QAAA,UAAU,IAAI;;6CAEuB,GAAG;;iCAEf,GAAG;;;2BAJ5B;AAQD;AACF;;AAED,QAAI,KAAK,OAAL,CAAa,oBAAb,KAAsC,SAA1C,EAAqD;AACnD,YAAM,cAAc,GAAG,IAAI,CAAC,OAAL,CAAa,KAAK,QAAL,CAAc,IAAd,CAAmB,MAAhC,EAAwC,KAAK,OAAL,CAAa,oBAArD,CAAvB;AACA,MAAA,UAAU,IAAI,MAAM,yBAAS,cAAT,EAAyB,MAAzB,CAApB;AACD;;AAED,IAAA,UAAU,IAAI,eAAd;AACA,WAAO,UAAP;AACD;;AAzS2C,C,CA4S9C;;;;;AACA,SAAS,mBAAT,CAA6B,aAA7B,EAA4E;AAC1E,MAAI,aAAa,IAAI,IAAjB,IAAyB,aAAa,CAAC,MAAd,KAAyB,CAAtD,EAAyD;AACvD,IAAA,aAAa,GAAG,CAAC,qBAAD,CAAhB;AACD;;AACD,SAAO,aAAa,CAAC,GAAd,CAAkB,EAAE,IAAI,uBAAuB,EAAE,CAAC,OAAH,CAAW,IAAX,EAAiB,GAAjB,CAAqB,MAApE,EAA4E,IAA5E,CAAiF,IAAjF,CAAP;AACD;;AAED,SAAS,aAAT,CAAuB,UAAvB,EAAgD;AAC9C,MAAI,sBAAsB,CAAC,UAAD,EAAa,eAAb,CAA1B,EAAyD;AACvD,WAAO,sFAAP;AACD,GAFD,MAGK;AACH,WAAO,EAAP;AACD;AACF;;AAED,SAAS,cAAT,CAAwB,UAAxB,EAAmD,eAAnD,EAA2E;AACzE,QAAM,YAAY,GAAkB,CAAC,kBAAD,EAAqB,+CAArB,CAApC;;AAEA,MAAI,sBAAsB,CAAC,UAAD,EAAa,eAAb,CAA1B,EAAyD;AACvD,IAAA,YAAY,CAAC,IAAb,CAAkB,2CAAlB;AACD;;AACD,MAAI,sBAAsB,CAAC,UAAD,EAAa,eAAb,CAA1B,EAAyD;AACvD,IAAA,YAAY,CAAC,IAAb,CAAkB,yCAAlB;AACD;;AAED,MAAI,eAAJ,EAAqB;AACnB,IAAA,YAAY,CAAC,IAAb,CAAkB,GAAlB;AACA,IAAA,YAAY,CAAC,IAAb,CAAkB,uBAAlB;AACA,IAAA,YAAY,CAAC,IAAb,CAAkB,aAAlB,EAAiC,wBAAjC,EAA2D,IAA3D;AACA,IAAA,YAAY,CAAC,IAAb,CAAkB,aAAlB,EAAiC,0BAAjC,EAA6D,IAA7D;AACA,IAAA,YAAY,CAAC,IAAb,CAAkB,wBAAlB;AACA,IAAA,YAAY,CAAC,IAAb,CAAkB,oBAAlB;AACD,GAPD,MAOO;AACL,IAAA,YAAY,CAAC,IAAb,CAAkB,IAAlB;AACD;;AACD,SAAO,YAAY,CAAC,IAAb,CAAkB,GAAlB,CAAP;AACD;;AAED,SAAS,eAAT,CAAyB,UAAzB,EAAkD;AAChD,MAAI,sBAAsB,CAAC,UAAD,EAAa,kBAAb,CAA1B,EAA4D;AAC1D,WAAO,uDAAP;AACD,GAFD,MAGK;AACH,WAAO,EAAP;AACD;AACF;;AAED,SAAS,sBAAT,CAAgC,UAAhC,EAA2D,YAA3D,EAA+E;AAC7E,QAAM,gBAAgB,GAAG,YAAY,CAAC,SAAb,CAAuB,CAAvB,EAA0B,YAAY,CAAC,OAAb,CAAqB,GAArB,CAA1B,CAAzB;AACA,SAAO,UAAU,CAAC,IAAX,CAAgB,EAAE,IAAI,EAAE,CAAC,QAAH,CAAY,gBAAZ,CAAtB,CAAP;AACD;;AAED,SAAS,sBAAT,CAAgC,UAAhC,EAAyD;AACvD,SAAO,UAAU,CAAC,IAAX,CAAgB,EAAE,IAAI,EAAE,CAAC,QAAH,CAAY,SAAZ,KAA0B,EAAE,CAAC,QAAH,CAAY,cAAZ,CAAhD,CAAP;AACD,C", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { Arch, asArray, deepAssign, InvalidConfigurationError, log } from \"builder-util\"\nimport { copyOrLinkFile, walk } from \"builder-util/out/fs\"\nimport { emptyDir, readdir, readFile, writeFile } from \"fs-extra\"\nimport * as path from \"path\"\nimport { AppXOptions } from \"../\"\nimport { getSignVendorPath, isOldWin6 } from \"../codeSign/windowsCodeSign\"\nimport { Target } from \"../core\"\nimport { getTemplatePath } from \"../util/pathManager\"\nimport { VmManager } from \"../vm/vm\"\nimport { WinPackager } from \"../winPackager\"\nimport { createStageDir } from \"./targetUtil\"\n\nconst APPX_ASSETS_DIR_NAME = \"appx\"\n\nconst vendorAssetsForDefaultAssets: { [key: string]: string } = {\n  \"StoreLogo.png\": \"SampleAppx.50x50.png\",\n  \"Square150x150Logo.png\": \"SampleAppx.150x150.png\",\n  \"Square44x44Logo.png\": \"SampleAppx.44x44.png\",\n  \"Wide310x150Logo.png\": \"SampleAppx.310x150.png\",\n}\n\nconst DEFAULT_RESOURCE_LANG = \"en-US\"\n\nexport default class AppXTarget extends Target {\n  readonly options: AppXOptions = deepAssign({}, this.packager.platformSpecificBuildOptions, this.packager.config.appx)\n\n  constructor(private readonly packager: WinPackager, readonly outDir: string) {\n    super(\"appx\")\n\n    if (process.platform !== \"darwin\" && (process.platform !== \"win32\" || isOldWin6())) {\n      throw new Error(\"AppX is supported only on Windows 10 or Windows Server 2012 R2 (version number 6.3+)\")\n    }\n  }\n\n  // https://docs.microsoft.com/en-us/windows/uwp/packaging/create-app-package-with-makeappx-tool#mapping-files\n  async build(appOutDir: string, arch: Arch): Promise<any> {\n    const packager = this.packager\n    const artifactName = packager.expandArtifactBeautyNamePattern(this.options, \"appx\", arch)\n    const artifactPath = path.join(this.outDir, artifactName)\n    await packager.info.callArtifactBuildStarted({\n      targetPresentableName: \"AppX\",\n      file: artifactPath,\n      arch,\n    })\n\n    const vendorPath = await getSignVendorPath()\n    const vm = await packager.vm.value\n\n    const stageDir = await createStageDir(this, packager, arch)\n\n    const mappingFile = stageDir.getTempFile(\"mapping.txt\")\n    const makeAppXArgs = [\"pack\", \"/o\" /* overwrite the output file if it exists */,\n      \"/f\", vm.toVmFile(mappingFile),\n      \"/p\", vm.toVmFile(artifactPath),\n    ]\n    if (packager.compression === \"store\") {\n      makeAppXArgs.push(\"/nc\")\n    }\n\n    const mappingList: Array<Array<string>> = []\n    mappingList.push(await BluebirdPromise.map(walk(appOutDir), file => {\n      let appxPath = file.substring(appOutDir.length + 1)\n      if (path.sep !== \"\\\\\") {\n        appxPath = appxPath.replace(/\\//g, \"\\\\\")\n      }\n      return `\"${vm.toVmFile(file)}\" \"app\\\\${appxPath}\"`\n    }))\n\n    const userAssetDir = await this.packager.getResource(undefined, APPX_ASSETS_DIR_NAME)\n    const assetInfo = await AppXTarget.computeUserAssets(vm, vendorPath, userAssetDir)\n    const userAssets = assetInfo.userAssets\n\n    const manifestFile = stageDir.getTempFile(\"AppxManifest.xml\")\n    await this.writeManifest(manifestFile, arch, await this.computePublisherName(), userAssets)\n    await packager.info.callAppxManifestCreated(manifestFile)\n    mappingList.push(assetInfo.mappings)\n    mappingList.push([`\"${vm.toVmFile(manifestFile)}\" \"AppxManifest.xml\"`])\n    const signToolArch = (arch === Arch.arm64 ? \"x64\" : Arch[arch])\n\n    if (isScaledAssetsProvided(userAssets)) {\n      const outFile = vm.toVmFile(stageDir.getTempFile(\"resources.pri\"))\n      const makePriPath = vm.toVmFile(path.join(vendorPath, \"windows-10\", signToolArch, \"makepri.exe\"))\n\n      const assetRoot = stageDir.getTempFile(\"appx/assets\")\n      await emptyDir(assetRoot)\n      await BluebirdPromise.map(assetInfo.allAssets, it => copyOrLinkFile(it, path.join(assetRoot, path.basename(it))))\n\n      await vm.exec(makePriPath, [\"new\",\n        \"/Overwrite\",\n        \"/Manifest\", vm.toVmFile(manifestFile),\n        \"/ProjectRoot\", vm.toVmFile(path.dirname(assetRoot)),\n        \"/ConfigXml\", vm.toVmFile(path.join(getTemplatePath(\"appx\"), \"priconfig.xml\")),\n        \"/OutputFile\", outFile,\n      ])\n\n      // in addition to resources.pri, resources.scale-140.pri and other such files will be generated\n      for (const resourceFile of (await readdir(stageDir.dir)).filter(it => it.startsWith(\"resources.\")).sort()) {\n        mappingList.push([`\"${vm.toVmFile(stageDir.getTempFile(resourceFile))}\" \"${resourceFile}\"`])\n      }\n      makeAppXArgs.push(\"/l\")\n    }\n\n    let mapping = \"[Files]\"\n    for (const list of mappingList) {\n      mapping += \"\\r\\n\" + list.join(\"\\r\\n\")\n    }\n    await writeFile(mappingFile, mapping)\n    packager.debugLogger.add(\"appx.mapping\", mapping)\n\n    if (this.options.makeappxArgs != null) {\n      makeAppXArgs.push(...this.options.makeappxArgs)\n    }\n    await vm.exec(vm.toVmFile(path.join(vendorPath, \"windows-10\", signToolArch, \"makeappx.exe\")), makeAppXArgs)\n    await packager.sign(artifactPath)\n\n    await stageDir.cleanup()\n\n    await packager.info.callArtifactBuildCompleted({\n      file: artifactPath,\n      packager,\n      arch,\n      safeArtifactName: packager.computeSafeArtifactName(artifactName, \"appx\"),\n      target: this,\n      isWriteUpdateInfo: this.options.electronUpdaterAware,\n    })\n  }\n\n  private static async computeUserAssets(vm: VmManager, vendorPath: string, userAssetDir: string | null) {\n    const mappings: Array<string> = []\n    let userAssets: Array<string>\n    const allAssets: Array<string> = []\n    if (userAssetDir == null) {\n      userAssets = []\n    }\n    else {\n      userAssets = (await readdir(userAssetDir)).filter(it => !it.startsWith(\".\") && !it.endsWith(\".db\") && it.includes(\".\"))\n      for (const name of userAssets) {\n        mappings.push(`\"${vm.toVmFile(userAssetDir)}${vm.pathSep}${name}\" \"assets\\\\${name}\"`)\n        allAssets.push(path.join(userAssetDir, name))\n      }\n    }\n\n    for (const defaultAsset of Object.keys(vendorAssetsForDefaultAssets)) {\n      if (userAssets.length === 0 || !isDefaultAssetIncluded(userAssets, defaultAsset)) {\n        const file = path.join(vendorPath, \"appxAssets\", vendorAssetsForDefaultAssets[defaultAsset])\n        mappings.push(`\"${vm.toVmFile(file)}\" \"assets\\\\${defaultAsset}\"`)\n        allAssets.push(file)\n      }\n    }\n\n    // we do not use process.arch to build path to tools, because even if you are on x64, ia32 appx tool must be used if you build appx for ia32\n    return {userAssets, mappings, allAssets}\n  }\n\n  // https://github.com/electron-userland/electron-builder/issues/2108#issuecomment-333200711\n  private async computePublisherName() {\n    if (await this.packager.cscInfo.value == null) {\n      log.info({reason: \"Windows Store only build\"}, \"AppX is not signed\")\n      return this.options.publisher || \"CN=ms\"\n    }\n\n    const certInfo = await this.packager.lazyCertInfo.value\n    const publisher = this.options.publisher || (certInfo == null ? null : certInfo.bloodyMicrosoftSubjectDn)\n    if (publisher == null) {\n      throw new Error(\"Internal error: cannot compute subject using certificate info\")\n    }\n    return publisher\n  }\n\n  private async writeManifest(outFile: string, arch: Arch, publisher: string, userAssets: Array<string>) {\n    const appInfo = this.packager.appInfo\n    const options = this.options\n    const executable = `app\\\\${appInfo.productFilename}.exe`\n    const displayName = options.displayName || appInfo.productName\n    const extensions = await this.getExtensions(executable, displayName)\n\n    const manifest = (await readFile(path.join(getTemplatePath(\"appx\"), \"appxmanifest.xml\"), \"utf8\"))\n      .replace(/\\${([a-zA-Z0-9]+)}/g, (match, p1): string => {\n        switch (p1) {\n          case \"publisher\":\n            return publisher\n\n          case \"publisherDisplayName\": {\n            const name = options.publisherDisplayName || appInfo.companyName\n            if (name == null) {\n              throw new InvalidConfigurationError(`Please specify \"author\" in the application package.json — it is required because \"appx.publisherDisplayName\" is not set.`)\n            }\n            return name\n          }\n\n          case \"version\":\n            return appInfo.getVersionInWeirdWindowsForm(options.setBuildNumber === true)\n\n          case \"applicationId\": {\n            const result = options.applicationId || options.identityName || appInfo.name\n            if (!isNaN(parseInt(result[0], 10))) {\n              let message = `AppX Application.Id can’t start with numbers: \"${result}\"`\n              if (options.applicationId == null) {\n                message += `\\nPlease set appx.applicationId (or correct appx.identityName or name)`\n              }\n              throw new InvalidConfigurationError(message)\n            }\n            return result\n          }\n\n          case \"identityName\":\n            return options.identityName || appInfo.name\n\n          case \"executable\":\n            return executable\n\n          case \"displayName\":\n            return displayName\n\n          case \"description\":\n            return appInfo.description || appInfo.productName\n\n          case \"backgroundColor\":\n            return options.backgroundColor || \"#464646\"\n\n          case \"logo\":\n            return \"assets\\\\StoreLogo.png\"\n\n          case \"square150x150Logo\":\n            return \"assets\\\\Square150x150Logo.png\"\n\n          case \"square44x44Logo\":\n            return \"assets\\\\Square44x44Logo.png\"\n\n          case \"lockScreen\":\n            return lockScreenTag(userAssets)\n\n          case \"defaultTile\":\n            return defaultTileTag(userAssets, options.showNameOnTiles || false)\n\n          case \"splashScreen\":\n            return splashScreenTag(userAssets)\n\n          case \"arch\":\n            return arch === Arch.ia32 ? \"x86\" : (arch === Arch.arm64 ? \"arm64\" : \"x64\")\n\n          case \"resourceLanguages\":\n            return resourceLanguageTag(asArray(options.languages))\n\n          case \"extensions\":\n            return extensions\n\n          case \"minVersion\":\n            return arch === Arch.arm64 ? \"10.0.16299.0\" : \"10.0.14316.0\"\n\n          case \"maxVersionTested\":\n            return arch === Arch.arm64 ? \"10.0.16299.0\" : \"10.0.14316.0\"\n\n          default:\n            throw new Error(`Macro ${p1} is not defined`)\n        }\n      })\n    await writeFile(outFile, manifest)\n  }\n\n  private async getExtensions(executable: string, displayName: string): Promise<string> {\n    const uriSchemes = asArray(this.packager.config.protocols)\n      .concat(asArray(this.packager.platformSpecificBuildOptions.protocols))\n\n    const fileAssociations = asArray(this.packager.config.fileAssociations)\n      .concat(asArray(this.packager.platformSpecificBuildOptions.fileAssociations))\n\n    let isAddAutoLaunchExtension = this.options.addAutoLaunchExtension\n    if (isAddAutoLaunchExtension === undefined) {\n      const deps = this.packager.info.metadata.dependencies\n      isAddAutoLaunchExtension = deps != null && deps[\"electron-winstore-auto-launch\"] != null\n    }\n\n    if (!isAddAutoLaunchExtension\n      && uriSchemes.length === 0\n      && fileAssociations.length === 0\n      && this.options.customExtensionsPath === undefined) {\n      return \"\"\n    }\n\n    let extensions = \"<Extensions>\"\n\n    if (isAddAutoLaunchExtension) {\n      extensions += `\n        <desktop:Extension Category=\"windows.startupTask\" Executable=\"${executable}\" EntryPoint=\"Windows.FullTrustApplication\">\n          <desktop:StartupTask TaskId=\"SlackStartup\" Enabled=\"true\" DisplayName=\"${displayName}\" />\n        </desktop:Extension>`\n    }\n\n    for (const protocol of uriSchemes) {\n      for (const scheme of asArray(protocol.schemes)) {\n        extensions += `\n          <uap:Extension Category=\"windows.protocol\">\n            <uap:Protocol Name=\"${scheme}\">\n               <uap:DisplayName>${protocol.name}</uap:DisplayName>\n             </uap:Protocol>\n          </uap:Extension>`\n      }\n    }\n\n    for (const fileAssociation of fileAssociations) {\n      for (const ext of asArray(fileAssociation.ext)) {\n        extensions += `\n          <uap:Extension Category=\"windows.fileTypeAssociation\">\n            <uap:FileTypeAssociation Name=\"${ext}\">\n              <uap:SupportedFileTypes>\n                <uap:FileType>.${ext}</uap:FileType>\n              </uap:SupportedFileTypes>\n            </uap:FileTypeAssociation>\n          </uap:Extension>`\n      }\n    }\n\n    if (this.options.customExtensionsPath !== undefined) {\n      const extensionsPath = path.resolve(this.packager.info.appDir, this.options.customExtensionsPath)\n      extensions += await readFile(extensionsPath, \"utf8\")\n    }\n\n    extensions += \"</Extensions>\"\n    return extensions\n  }\n}\n\n// get the resource - language tag, see https://docs.microsoft.com/en-us/windows/uwp/globalizing/manage-language-and-region#specify-the-supported-languages-in-the-apps-manifest\nfunction resourceLanguageTag(userLanguages: Array<string> | null | undefined): string {\n  if (userLanguages == null || userLanguages.length === 0) {\n    userLanguages = [DEFAULT_RESOURCE_LANG]\n  }\n  return userLanguages.map(it => `<Resource Language=\"${it.replace(/_/g, \"-\")}\" />`).join(\"\\n\")\n}\n\nfunction lockScreenTag(userAssets: Array<string>): string {\n  if (isDefaultAssetIncluded(userAssets, \"BadgeLogo.png\")) {\n    return '<uap:LockScreen Notification=\"badgeAndTileText\" BadgeLogo=\"assets\\\\BadgeLogo.png\" />'\n  }\n  else {\n    return \"\"\n  }\n}\n\nfunction defaultTileTag(userAssets: Array<string>, showNameOnTiles: boolean): string {\n  const defaultTiles: Array<string> = [\"<uap:DefaultTile\", 'Wide310x150Logo=\"assets\\\\Wide310x150Logo.png\"']\n\n  if (isDefaultAssetIncluded(userAssets, \"LargeTile.png\")) {\n    defaultTiles.push('Square310x310Logo=\"assets\\\\LargeTile.png\"')\n  }\n  if (isDefaultAssetIncluded(userAssets, \"SmallTile.png\")) {\n    defaultTiles.push('Square71x71Logo=\"assets\\\\SmallTile.png\"')\n  }\n\n  if (showNameOnTiles) {\n    defaultTiles.push(\">\")\n    defaultTiles.push(\"<uap:ShowNameOnTiles>\")\n    defaultTiles.push(\"<uap:ShowOn\", 'Tile=\"wide310x150Logo\"', \"/>\")\n    defaultTiles.push(\"<uap:ShowOn\", 'Tile=\"square150x150Logo\"', \"/>\")\n    defaultTiles.push(\"</uap:ShowNameOnTiles>\")\n    defaultTiles.push(\"</uap:DefaultTile>\")\n  } else {\n    defaultTiles.push(\"/>\")\n  }\n  return defaultTiles.join(\" \")\n}\n\nfunction splashScreenTag(userAssets: Array<string>): string {\n  if (isDefaultAssetIncluded(userAssets, \"SplashScreen.png\")) {\n    return '<uap:SplashScreen Image=\"assets\\\\SplashScreen.png\" />'\n  }\n  else {\n    return \"\"\n  }\n}\n\nfunction isDefaultAssetIncluded(userAssets: Array<string>, defaultAsset: string) {\n  const defaultAssetName = defaultAsset.substring(0, defaultAsset.indexOf(\".\"))\n  return userAssets.some(it => it.includes(defaultAssetName))\n}\n\nfunction isScaledAssetsProvided(userAssets: Array<string>) {\n  return userAssets.some(it => it.includes(\".scale-\") || it.includes(\".targetsize-\"))\n}\n"], "sourceRoot": ""}