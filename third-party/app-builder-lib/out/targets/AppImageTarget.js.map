{"version": 3, "sources": ["../../src/targets/AppImageTarget.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;AAEA;AACc,MAAO,cAAP,SAA8B,cAA9B,CAAoC;AAIhD,EAAA,WAAA,CAAY,OAAZ,EAA8C,QAA9C,EAAwF,MAAxF,EAA4H,MAA5H,EAA0I;AACxI,UAAM,UAAN;AAD4C,SAAA,QAAA,GAAA,QAAA;AAA0C,SAAA,MAAA,GAAA,MAAA;AAAoC,SAAA,MAAA,GAAA,MAAA;AAHnH,SAAA,OAAA,GAA2B,EAAC,GAAG,KAAK,QAAL,CAAc,4BAAlB;AAAgD,SAAI,KAAK,QAAL,CAAc,MAAd,CAA6B,KAAK,IAAlC;AAApD,KAA3B;AAMP,SAAK,YAAL,GAAoB,KAAI,eAAJ,EAAiB,MAAM,MAAM,CAAC,mBAAP,CAA2B,KAAK,OAAhC,EAAyC,wBAAzC,EAAmE;AAC5G,4BAAsB,GAAG,QAAQ,CAAC,OAAT,CAAiB,YAAY;AADsD,KAAnE,CAAvB,CAApB;AAGD;;AAED,QAAM,KAAN,CAAY,SAAZ,EAA+B,IAA/B,EAAyC;AACvC,UAAM,QAAQ,GAAG,KAAK,QAAtB;AACA,UAAM,OAAO,GAAG,KAAK,OAArB,CAFuC,CAGvC;AACA;AACA;;AACA,UAAM,YAAY,GAAG,QAAQ,CAAC,yBAAT,CAAmC,OAAnC,EAA4C,UAA5C,EAAwD,IAAxD,CAArB;AACA,UAAM,YAAY,GAAG,IAAI,CAAC,IAAL,CAAU,KAAK,MAAf,EAAuB,YAAvB,CAArB;AACA,UAAM,QAAQ,CAAC,IAAT,CAAc,wBAAd,CAAuC;AAC3C,MAAA,qBAAqB,EAAE,UADoB;AAE3C,MAAA,IAAI,EAAE,YAFqC;AAG3C,MAAA;AAH2C,KAAvC,CAAN;AAMA,UAAM,CAAC,GAAG,MAAM,OAAO,CAAC,GAAR,CAAY,CAC1B,KAAK,YAAL,CAAkB,KADQ,EAE1B,KAAK,MAAL,CAAY,KAFc,EAG1B,wDAAiC,QAAjC,EAA2C,IAA3C,EAAiD;AAAM;AAAvD,KAH0B,EAI1B,2CAA2B,OAAO,CAAC,OAAnC,EAA4C,KAAK,QAAjD,EAA2D,CAAC,KAAD,EAAQ,MAAR,CAA3D,CAJ0B,EAK1B,kCAAe,IAAf,EAAqB,QAArB,EAA+B,IAA/B,CAL0B,CAAZ,CAAhB;AAOA,UAAM,OAAO,GAAG,CAAC,CAAC,CAAD,CAAjB;AACA,UAAM,QAAQ,GAAG,CAAC,CAAC,CAAD,CAAlB;AAEA,UAAM,aAAa,GAAG,CAAC,CAAC,CAAD,CAAvB;;AACA,QAAI,aAAa,IAAI,IAArB,EAA2B;AACzB,YAAM,2BAAW,IAAI,CAAC,IAAL,CAAU,QAAQ,CAAC,eAAT,CAAyB,QAAQ,CAAC,GAAlC,CAAV,EAAkD,gBAAlD,CAAX,EAAgF,oCAAgB,aAAhB,CAAhF,CAAN;AACD;;AAED,QAAI,KAAK,QAAL,CAAc,eAAd,CAA8B,uBAA9B,IAAyD,IAAzD,KAAiE,MAAM,KAAK,QAAL,CAAc,eAAd,CAA8B,uBAA9B,CAAsD;AAAC,MAAA,OAAO,EAAE,MAAM,KAAK,YAAL,CAAkB;AAAlC,KAAtD,CAAvE,CAAJ,EAA4K;AAC1K;AACD;;AAED,UAAM,IAAI,GAAG,CACX,UADW,EAEX,SAFW,EAEA,QAAQ,CAAC,GAFT,EAGX,QAHW,EAGD,oBAAK,IAAL,CAHC,EAIX,UAJW,EAIC,YAJD,EAKX,OALW,EAKF,SALE,EAMX,iBANW,EAMS,IAAI,CAAC,SAAL,CAAe;AACjC,MAAA,WAAW,EAAE,KAAK,QAAL,CAAc,OAAd,CAAsB,WADF;AAEjC,MAAA,eAAe,EAAE,KAAK,QAAL,CAAc,OAAd,CAAsB,eAFN;AAGjC,MAAA,YAAY,EAAE,CAAC,CAAC,CAAD,CAHkB;AAIjC,MAAA,cAAc,EAAE,KAAK,QAAL,CAAc,cAJG;AAKjC,MAAA,KAAK,EAAE,CAAC,CAAC,CAAD,CALyB;AAMjC,MAAA,gBAAgB,EAAE,KAAK,QAAL,CAAc,gBANC;AAOjC,SAAG;AAP8B,KAAf,CANT,CAAb;AAgBA,oCAAa,IAAb,EAAmB;AACjB,MAAA;AADiB,KAAnB;;AAGA,QAAI,QAAQ,CAAC,WAAT,KAAyB,SAA7B,EAAwC;AACtC,MAAA,IAAI,CAAC,IAAL,CAAU,eAAV,EAA2B,IAA3B;AACD;;AAED,UAAM,QAAQ,CAAC,IAAT,CAAc,0BAAd,CAAyC;AAC7C,MAAA,IAAI,EAAE,YADuC;AAE7C,MAAA,gBAAgB,EAAE,QAAQ,CAAC,uBAAT,CAAiC,YAAjC,EAA+C,UAA/C,EAA2D,IAA3D,EAAiE,KAAjE,CAF2B;AAG7C,MAAA,MAAM,EAAE,IAHqC;AAI7C,MAAA,IAJ6C;AAK7C,MAAA,QAL6C;AAM7C,MAAA,iBAAiB,EAAE,IAN0B;AAO7C,MAAA,UAAU,EAAE,MAAM,2CAAwB,IAAxB;AAP2B,KAAzC,CAAN;AASD;;AA7E+C,C", "sourcesContent": ["import { Arch, serializeToYaml } from \"builder-util\"\nimport { outputFile } from \"fs-extra\"\nimport { Lazy } from \"lazy-val\"\nimport * as path from \"path\"\nimport { AppImageOptions } from \"..\"\nimport { Target } from \"../core\"\nimport { LinuxPackager } from \"../linuxPackager\"\nimport { getAppUpdatePublishConfiguration } from \"../publish/PublishManager\"\nimport { executeAppBuilderAsJson, objectToArgs } from \"../util/appBuilder\"\nimport { getNotLocalizedLicenseFile } from \"../util/license\"\nimport { LinuxTargetHelper } from \"./LinuxTargetHelper\"\nimport { createStageDir } from \"./targetUtil\"\n\n// https://unix.stackexchange.com/questions/375191/append-to-sub-directory-inside-squashfs-file\nexport default class AppImageTarget extends Target {\n  readonly options: AppImageOptions = {...this.packager.platformSpecificBuildOptions, ...(this.packager.config as any)[this.name]}\n  private readonly desktopEntry: Lazy<string>\n\n  constructor(ignored: string, private readonly packager: LinuxPackager, private readonly helper: LinuxTargetHelper, readonly outDir: string) {\n    super(\"appImage\")\n\n    this.desktopEntry = new Lazy<string>(() => helper.computeDesktopEntry(this.options, \"AppRun --no-sandbox %U\", {\n      \"X-AppImage-Version\": `${packager.appInfo.buildVersion}`,\n    }))\n  }\n\n  async build(appOutDir: string, arch: Arch): Promise<any> {\n    const packager = this.packager\n    const options = this.options\n    // https://github.com/electron-userland/electron-builder/issues/775\n    // https://github.com/electron-userland/electron-builder/issues/1726\n    // tslint:disable-next-line:no-invalid-template-strings\n    const artifactName = packager.expandArtifactNamePattern(options, \"AppImage\", arch)\n    const artifactPath = path.join(this.outDir, artifactName)\n    await packager.info.callArtifactBuildStarted({\n      targetPresentableName: \"AppImage\",\n      file: artifactPath,\n      arch,\n    })\n\n    const c = await Promise.all([\n      this.desktopEntry.value,\n      this.helper.icons,\n      getAppUpdatePublishConfiguration(packager, arch, false /* in any case validation will be done on publish */),\n      getNotLocalizedLicenseFile(options.license, this.packager, [\"txt\", \"html\"]),\n      createStageDir(this, packager, arch),\n    ])\n    const license = c[3]\n    const stageDir = c[4]!!\n\n    const publishConfig = c[2]\n    if (publishConfig != null) {\n      await outputFile(path.join(packager.getResourcesDir(stageDir.dir), \"app-update.yml\"), serializeToYaml(publishConfig))\n    }\n\n    if (this.packager.packagerOptions.effectiveOptionComputed != null && await this.packager.packagerOptions.effectiveOptionComputed({desktop: await this.desktopEntry.value})) {\n      return\n    }\n\n    const args = [\n      \"appimage\",\n      \"--stage\", stageDir.dir,\n      \"--arch\", Arch[arch],\n      \"--output\", artifactPath,\n      \"--app\", appOutDir,\n      \"--configuration\", (JSON.stringify({\n        productName: this.packager.appInfo.productName,\n        productFilename: this.packager.appInfo.productFilename,\n        desktopEntry: c[0],\n        executableName: this.packager.executableName,\n        icons: c[1],\n        fileAssociations: this.packager.fileAssociations,\n        ...options,\n      })),\n    ]\n    objectToArgs(args, {\n      license,\n    })\n    if (packager.compression === \"maximum\") {\n      args.push(\"--compression\", \"xz\")\n    }\n\n    await packager.info.callArtifactBuildCompleted({\n      file: artifactPath,\n      safeArtifactName: packager.computeSafeArtifactName(artifactName, \"AppImage\", arch, false),\n      target: this,\n      arch,\n      packager,\n      isWriteUpdateInfo: true,\n      updateInfo: await executeAppBuilderAsJson(args),\n    })\n  }\n}\n"], "sourceRoot": ""}