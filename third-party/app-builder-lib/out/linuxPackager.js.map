{"version": 3, "sources": ["../src/linuxPackager.ts"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAGA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAGA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AAEM,MAAO,aAAP,SAA6B,oCAA7B,CAAiE;AAGrE,EAAA,WAAA,CAAY,IAAZ,EAA0B;AACxB,UAAM,IAAN,EAAY,iBAAS,KAArB;AAEA,UAAM,cAAc,GAAG,KAAK,4BAAL,CAAkC,cAAzD;AACA,SAAK,cAAL,GAAsB,cAAc,IAAI,IAAlB,GAAyB,KAAK,OAAL,CAAa,aAAb,CAA2B,WAA3B,EAAzB,GAAoE,iCAAiB,cAAjB,CAA1F;AACD;;AAED,MAAI,aAAJ,GAAiB;AACf,WAAO,CAAC,MAAD,EAAS,UAAT,CAAP;AACD;;AAED,EAAA,aAAa,CAAC,OAAD,EAAyB,MAAzB,EAA4F;AACvG,QAAI,MAAJ;;AACA,UAAM,SAAS,GAAG,MAAK;AACrB,UAAI,MAAM,IAAI,IAAd,EAAoB;AAClB,QAAA,MAAM,GAAG,KAAI,sCAAJ,EAAsB,IAAtB,CAAT;AACD;;AACD,aAAO,MAAP;AACD,KALD;;AAOA,QAAI,aAAa,GAAyB,IAA1C;;AAEA,SAAK,MAAM,IAAX,IAAmB,OAAnB,EAA4B;AAC1B,UAAI,IAAI,KAAK,kBAAb,EAAyB;AACvB;AACD;;AAED,YAAM,WAAW,GAAwE,CAAC,MAAK;AAC7F,gBAAQ,IAAR;AACE,eAAK,UAAL;AACE,mBAAO,OAAO,CAAC,0BAAD,CAAP,CAAoC,OAA3C;;AACF,eAAK,MAAL;AACE,mBAAO,OAAO,CAAC,gBAAD,CAAP,CAA0B,OAAjC;;AACF,eAAK,KAAL;AACA,eAAK,KAAL;AACA,eAAK,IAAL;AACA,eAAK,SAAL;AACA,eAAK,QAAL;AACA,eAAK,KAAL;AACA,eAAK,KAAL;AACE,mBAAO,OAAO,CAAC,eAAD,CAAP,CAAyB,OAAhC;;AACF;AACE,mBAAO,IAAP;AAdJ;AAgBD,OAjBwF,GAAzF;;AAmBA,MAAA,MAAM,CAAC,IAAD,EAAO,MAAM,IAAG;AACpB,YAAI,WAAW,KAAK,IAApB,EAA0B;AACxB,iBAAO,yCAAmB,IAAnB,EAAyB,MAAzB,EAAiC,IAAjC,CAAP;AACD;;AAED,cAAM,MAAM,GAAG,IAAI,WAAJ,CAAgB,IAAhB,EAAsB,IAAtB,EAA4B,SAAS,EAArC,EAAyC,MAAzC,CAAf;;AACA,YAAI,OAAO,CAAC,QAAR,KAAqB,OAArB,IAAgC,OAAO,CAAC,GAAR,CAAY,aAAhD,EAA+D;AAC7D,cAAI,aAAa,IAAI,IAArB,EAA2B;AACzB,YAAA,aAAa,GAAG,KAAI,8BAAJ,EAAkB,IAAlB,CAAhB;AACD,WAH4D,CAI7D;;;AACA,iBAAO,IAAI,YAAJ,CAAiB,MAAjB,EAAyB,aAAzB,CAAP;AACD;;AACD,eAAO,MAAP;AACD,OAdK,CAAN;AAeD;AACF;;AAjEoE;;;;AAoEvE,MAAM,YAAN,SAA2B,cAA3B,CAAiC;AAW/B,EAAA,WAAA,CAA6B,MAA7B,EAA8D,aAA9D,EAA0F;AACxF,UAAM,MAAM,CAAC,IAAb,EAAmB;AAAK;AAAxB;AAD2B,SAAA,MAAA,GAAA,MAAA;AAAiC,SAAA,aAAA,GAAA,aAAA;AAVtD,SAAA,gBAAA,GAAmB,KAAI,+BAAJ,EAAqB,KAAK,aAAL,CAAmB,QAAnB,CAA4B,IAA5B,CAAiC,iBAAtD,CAAnB;AAYP;;AAVD,MAAI,OAAJ,GAAW;AACT,WAAO,KAAK,MAAL,CAAY,OAAnB;AACD;;AAED,MAAI,MAAJ,GAAU;AACR,WAAO,KAAK,MAAL,CAAY,MAAnB;AACD;;AAMD,QAAM,WAAN,GAAiB;AACf,UAAM,KAAK,gBAAL,CAAsB,UAAtB,EAAN;AACA,UAAM,KAAK,aAAL,CAAmB,KAAnB,EAAN;AACD;;AAED,EAAA,KAAK,CAAC,SAAD,EAAoB,IAApB,EAA8B;AACjC,UAAM,OAAO,GAAG,KAAK,OAAL,CAAa,SAAb,EAAwB,IAAxB,CAAhB;AACA,SAAK,gBAAL,CAAsB,OAAtB,CAA8B,OAA9B;AACA,WAAO,OAAP;AACD;;AAEO,QAAM,OAAN,CAAc,SAAd,EAAiC,IAAjC,EAA2C;AACjD,uBAAI,IAAJ,CAAS;AAAC,MAAA,MAAM,EAAE,KAAK,MAAL,CAAY,IAArB;AAA2B,MAAA,IAAI,EAAE,oBAAK,IAAL;AAAjC,KAAT,EAAuD,yBAAvD;;AACA,UAAM,KAAK,MAAL,CAAY,YAAZ,EAAN;AACA,SAAK,aAAL,CAAmB,aAAnB,CAAiC,KAAK,MAAtC,EAA8C,IAA9C,EAAoD,SAApD;AACD;;AA9B8B;;AAiC3B,SAAU,oBAAV,CAA+B,IAA/B,EAAyC;AAC7C,UAAQ,IAAR;AACE,SAAK,oBAAK,GAAV;AACE,aAAO,QAAP;;AACF,SAAK,oBAAK,IAAV;AACE,aAAO,MAAP;;AACF,SAAK,oBAAK,MAAV;AACE,aAAO,KAAP;;AACF,SAAK,oBAAK,KAAV;AACE,aAAO,aAAP;;AAEF;AACE,YAAM,IAAI,KAAJ,CAAU,oBAAoB,IAAI,EAAlC,CAAN;AAXJ;AAaD,C", "sourcesContent": ["import { Arch, AsyncTaskManager, log } from \"builder-util\"\nimport sanitizeFileName from \"sanitize-filename\"\nimport { DIR_TARGET, Platform, Target, TargetSpecificOptions } from \"./core\"\nimport { LinuxConfiguration } from \"./options/linuxOptions\"\nimport { Packager } from \"./packager\"\nimport { PlatformPackager } from \"./platformPackager\"\nimport { RemoteBuilder } from \"./remoteBuilder/RemoteBuilder\"\nimport AppImageTarget from \"./targets/AppImageTarget\"\nimport FpmTarget from \"./targets/fpm\"\nimport { LinuxTargetHelper } from \"./targets/LinuxTargetHelper\"\nimport SnapTarget from \"./targets/snap\"\nimport { createCommonTarget } from \"./targets/targetFactory\"\n\nexport class LinuxPackager extends PlatformPackager<LinuxConfiguration> {\n  readonly executableName: string\n\n  constructor(info: Packager) {\n    super(info, Platform.LINUX)\n\n    const executableName = this.platformSpecificBuildOptions.executableName\n    this.executableName = executableName == null ? this.appInfo.sanitizedName.toLowerCase() : sanitizeFileName(executableName)\n  }\n\n  get defaultTarget(): Array<string> {\n    return [\"snap\", \"appimage\"]\n  }\n\n  createTargets(targets: Array<string>, mapper: (name: string, factory: (outDir: string) => Target) => void): void {\n    let helper: LinuxTargetHelper | null\n    const getHelper = () => {\n      if (helper == null) {\n        helper = new LinuxTargetHelper(this)\n      }\n      return helper\n    }\n\n    let remoteBuilder: RemoteBuilder | null = null\n\n    for (const name of targets) {\n      if (name === DIR_TARGET) {\n        continue\n      }\n\n      const targetClass: typeof AppImageTarget | typeof SnapTarget | typeof FpmTarget | null = (() => {\n        switch (name) {\n          case \"appimage\":\n            return require(\"./targets/AppImageTarget\").default\n          case \"snap\":\n            return require(\"./targets/snap\").default\n          case \"deb\":\n          case \"rpm\":\n          case \"sh\":\n          case \"freebsd\":\n          case \"pacman\":\n          case \"apk\":\n          case \"p5p\":\n            return require(\"./targets/fpm\").default\n          default:\n            return null\n        }\n      })()\n\n      mapper(name, outDir => {\n        if (targetClass === null) {\n          return createCommonTarget(name, outDir, this)\n        }\n\n        const target = new targetClass(name, this, getHelper(), outDir)\n        if (process.platform === \"win32\" || process.env._REMOTE_BUILD) {\n          if (remoteBuilder == null) {\n            remoteBuilder = new RemoteBuilder(this)\n          }\n          // return remoteBuilder.buildTarget(this, arch, appOutDir, this.packager)\n          return new RemoteTarget(target, remoteBuilder)\n        }\n        return target\n      })\n    }\n  }\n}\n\nclass RemoteTarget extends Target {\n  private buildTaskManager = new AsyncTaskManager(this.remoteBuilder.packager.info.cancellationToken)\n\n  get options(): TargetSpecificOptions | null | undefined {\n    return this.target.options\n  }\n\n  get outDir(): string {\n    return this.target.outDir\n  }\n\n  constructor(private readonly target: Target, private readonly remoteBuilder: RemoteBuilder) {\n    super(target.name, true /* all must be scheduled in time (so, on finishBuild RemoteBuilder will have all targets added - so, we must set isAsyncSupported to true (resolved promise is returned)) */)\n  }\n\n  async finishBuild() {\n    await this.buildTaskManager.awaitTasks()\n    await this.remoteBuilder.build()\n  }\n\n  build(appOutDir: string, arch: Arch) {\n    const promise = this.doBuild(appOutDir, arch)\n    this.buildTaskManager.addTask(promise)\n    return promise\n  }\n\n  private async doBuild(appOutDir: string, arch: Arch) {\n    log.info({target: this.target.name, arch: Arch[arch]}, \"scheduling remote build\")\n    await this.target.checkOptions()\n    this.remoteBuilder.scheduleBuild(this.target, arch, appOutDir)\n  }\n}\n\nexport function toAppImageOrSnapArch(arch: Arch): string {\n  switch (arch) {\n    case Arch.x64:\n      return \"x86_64\"\n    case Arch.ia32:\n      return \"i386\"\n    case Arch.armv7l:\n      return \"arm\"\n    case Arch.arm64:\n      return \"arm_aarch64\"\n\n    default:\n      throw new Error(`Unsupported arch ${arch}`)\n  }\n}"], "sourceRoot": ""}