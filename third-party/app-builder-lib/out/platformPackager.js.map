{"version": 3, "sources": ["../src/platformPackager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;;;AAEM,MAAgB,gBAAhB,CAAgC;AA2BpC,EAAA,WAAA,CAA+B,IAA/B,EAAwD,QAAxD,EAA0E;AAA3C,SAAA,IAAA,GAAA,IAAA;AAAyB,SAAA,QAAA,GAAA,QAAA;AAJvC,SAAA,aAAA,GAAgB,KAAI,eAAJ,EAAwB,MAAM,iCAAiB,wBAAQ,KAAK,IAAL,CAAU,iBAAlB,CAAjB,EAAuD,EAAvD,CAA9B,CAAhB;AAKf,SAAK,4BAAL,GAAoC,gBAAgB,CAAC,qCAAjB,CAAwD,KAAK,MAAL,CAAoB,QAAQ,CAAC,qBAA7B,CAAxD,CAApC;AACA,SAAK,OAAL,GAAe,KAAK,cAAL,CAAoB,IAAI,CAAC,OAAzB,CAAf;AACD;;AA7BD,MAAI,eAAJ,GAAmB;AACjB,WAAO,KAAK,IAAL,CAAU,OAAjB;AACD;;AAED,MAAI,iBAAJ,GAAqB;AACnB,WAAO,KAAK,IAAL,CAAU,iBAAjB;AACD;;AAED,MAAI,UAAJ,GAAc;AACZ,WAAO,KAAK,IAAL,CAAU,UAAjB;AACD;;AAED,MAAI,MAAJ,GAAU;AACR,WAAO,KAAK,IAAL,CAAU,MAAjB;AACD;;AAID,MAAI,YAAJ,GAAgB;AACd,WAAO,KAAK,aAAL,CAAmB,KAA1B;AACD;;AAWD,MAAI,WAAJ,GAAe;AACb,UAAM,WAAW,GAAG,KAAK,4BAAL,CAAkC,WAAtD,CADa,CAEb;;AACA,QAAI,WAAW,KAAK,IAApB,EAA0B;AACxB,aAAO,QAAP;AACD;;AACD,WAAO,WAAW,IAAI,KAAK,MAAL,CAAY,WAA3B,IAA0C,QAAjD;AACD;;AAED,MAAI,WAAJ,GAAe;AACb,WAAO,KAAK,IAAL,CAAU,WAAjB;AACD,GA3CmC,CA+CpC;;;AACU,EAAA,cAAc,CAAC,OAAD,EAAiB;AACvC,WAAO,KAAI,kBAAJ,EAAY,KAAK,IAAjB,EAAuB,IAAvB,EAA6B,KAAK,4BAAlC,CAAP;AACD;;AAEO,SAAO,qCAAP,CAA6C,OAA7C,EAA4E;AAClF,WAAO,OAAO,IAAI,IAAX,GAAkB,MAAM,CAAC,MAAP,CAAc,IAAd,CAAlB,GAAwC,OAA/C;AACD;;AAIS,EAAA,cAAc,GAAA;AACtB,UAAM,QAAQ,GAAG,KAAK,gBAAL,EAAjB;;AACA,QAAI,oCAAgB,QAAhB,CAAJ,EAA+B;AAC7B,yBAAI,IAAJ,CAAS;AAAC,QAAA,MAAM,EAAE;AAAT,OAAT,EAAsD,8CAAtD;;AACA,aAAO,EAAP;AACD,KAHD,MAIK;AACH,aAAO,QAAS,CAAC,IAAV,EAAP;AACD;AACF;;AAES,EAAA,UAAU,CAAC,YAAD,EAA6B;AAC/C;AACA,UAAM,QAAQ,GAAG,aAAa,CAAC,YAAY,IAAI,IAAhB,GAAuB,IAAvB,GAA8B,OAAO,CAAC,GAAR,CAAY,YAAZ,CAA/B,EAA0D,OAAO,CAAC,GAAR,CAAY,QAAtE,CAA9B;AACA,WAAO,aAAa,CAAC,aAAa,CAAC,KAAK,IAAL,CAAU,MAAV,CAAiB,OAAlB,EAA2B,KAAK,4BAAL,CAAkC,OAA7D,CAAd,EAAqF,QAArF,CAApB;AACD;;AAES,EAAA,gBAAgB,GAAA;AACxB;AACA,WAAO,aAAa,CAAC,aAAa,CAAC,KAAK,IAAL,CAAU,MAAV,CAAiB,cAAlB,EAAkC,KAAK,4BAAL,CAAkC,cAApE,CAAd,EAAmG,OAAO,CAAC,GAAR,CAAY,gBAA/G,CAApB;AACD;;AAES,EAAA,gBAAgB,CAAC,MAAD,EAAiB,IAAjB,EAA2B;AACnD,WAAO,KAAK,eAAL,CAAqB,WAArB,IAAoC,IAAI,CAAC,IAAL,CAAU,MAAV,EAAkB,GAAG,KAAK,QAAL,CAAc,qBAAqB,GAAG,kCAAc,IAAd,CAAmB,GAAG,KAAK,QAAL,KAAkB,kBAAS,GAA3B,GAAiC,EAAjC,GAAsC,WAAW,EAAlI,CAA3C;AACD;;AAED,EAAA,uBAAuB,CAAC,IAAD,EAAe,MAAf,EAAsC,IAAtC,EAAyD,gBAAzD,EAAyF;AAC9G,WAAO,KAAK,IAAL,CAAU,0BAAV,CAAqC;AAC1C,MAAA,IAD0C;AACpC,MAAA,gBADoC;AAClB,MAAA,MADkB;AACV,MAAA,IADU;AAE1C,MAAA,QAAQ,EAAE;AAFgC,KAArC,CAAP;AAID;;AAED,QAAM,IAAN,CAAW,MAAX,EAA2B,IAA3B,EAAuC,OAAvC,EAA+D,WAA/D,EAA4F;AAC1F,UAAM,SAAS,GAAG,KAAK,gBAAL,CAAsB,MAAtB,EAA8B,IAA9B,CAAlB;AACA,UAAM,KAAK,MAAL,CAAY,MAAZ,EAAoB,SAApB,EAA+B,KAAK,QAAL,CAAc,QAA7C,EAA+E,IAA/E,EAAqF,KAAK,4BAA1F,EAAwH,OAAxH,CAAN;AACA,SAAK,4BAAL,CAAkC,SAAlC,EAA6C,IAA7C,EAAmD,OAAnD,EAA4D,WAA5D;AACD;;AAES,EAAA,4BAA4B,CAAC,SAAD,EAAoB,IAApB,EAAgC,OAAhC,EAAwD,WAAxD,EAAqF;AACzH,QAAI,OAAO,CAAC,IAAR,CAAa,EAAE,IAAI,CAAC,EAAE,CAAC,gBAAvB,KAA4C,IAAhD,EAAsD;AACpD,MAAA,gBAAgB,CAAC,iBAAjB,CAAmC,OAAnC,EAA4C,WAA5C,EAAyD,SAAzD,EAAoE,IAApE;AACA;AACD;;AAED,IAAA,WAAW,CAAC,GAAZ,CAAgB,YAAW;AACzB;AACA,YAAM,cAAc,GAAG,KAAI,+BAAJ,EAAqB,KAAK,IAAL,CAAU,iBAA/B,CAAvB;AACA,MAAA,gBAAgB,CAAC,iBAAjB,CAAmC,OAAnC,EAA4C,cAA5C,EAA4D,SAA5D,EAAuE,IAAvE;AACA,YAAM,cAAc,CAAC,UAAf,EAAN;;AAEA,WAAK,MAAM,MAAX,IAAqB,OAArB,EAA8B;AAC5B,YAAI,CAAC,MAAM,CAAC,gBAAZ,EAA8B;AAC5B,gBAAM,MAAM,CAAC,KAAP,CAAa,SAAb,EAAwB,IAAxB,CAAN;AACD;AACF;AACF,KAXD;AAYD;;AAEO,SAAO,iBAAP,CAAyB,OAAzB,EAAiD,WAAjD,EAAgF,SAAhF,EAAmG,IAAnG,EAA6G;AACnH,SAAK,MAAM,MAAX,IAAqB,OAArB,EAA8B;AAC5B,UAAI,MAAM,CAAC,gBAAX,EAA6B;AAC3B,QAAA,WAAW,CAAC,OAAZ,CAAoB,MAAM,CAAC,KAAP,CAAa,SAAb,EAAwB,IAAxB,CAApB;AACD;AACF;AACF;;AAEO,EAAA,oBAAoB,CAAC,WAAD,EAAuB,SAAvB,EAA0C,OAA1C,EAAyE;AACnG,UAAM,IAAI,GAAG,WAAW,GAAG,KAAK,eAAL,CAAqB,SAArB,CAAH,GAAsC,KAAK,QAAL,KAAkB,kBAAS,GAA3B,GAAiC,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,GAAG,KAAK,OAAL,CAAa,eAAe,MAApD,EAA4D,UAA5D,CAAjC,GAA2G,SAAzK;AACA,WAAO,oCAAgB,KAAK,MAArB,EAA6B,WAAW,GAAG,gBAAH,GAAsB,YAA9D,EAA4E,IAA5E,EAAkF,OAAlF,CAAP;AACD;;AAED,EAAA,4BAA4B,CAAC,MAAD,EAAiB,IAAjB,EAA6B,kBAA7B,EAA6E;AACvG,WAAO;AACL,MAAA,aAAa,EAAE,EAAE,IAAI,KAAK,WAAL,CAAiB,EAAjB,EAAqB,IAAI,IAAI,IAAR,GAAe,IAAf,GAAsB,oBAAK,IAAL,CAA3C,EAAuD;AAAC,cAAM;AAAP,OAAvD,CADhB;AAEL,MAAA,kBAFK;AAGL,MAAA,YAAY,EAAE,MAHT;AAIL,MAAA,UAAU,EAAE,KAAK;AAJZ,KAAP;AAMD;;AAES,QAAM,MAAN,CAAa,MAAb,EAA6B,SAA7B,EAAgD,YAAhD,EAAoF,IAApF,EAAgG,4BAAhG,EAAkI,OAAlI,EAA0J,IAAA,GAAgB,IAA1K,EAA8K;AACtL,QAAI,KAAK,eAAL,CAAqB,WAArB,IAAoC,IAAxC,EAA8C;AAC5C;AACD;;AAED,QAAI,KAAK,IAAL,CAAU,iBAAV,CAA4B,SAAhC,EAA2C;AACzC;AACD;;AAED,UAAM,KAAK,IAAL,CAAU,sBAAV,CAAiC,KAAK,QAAtC,EAAgD,IAAhD,CAAN;;AAEA,QAAI,KAAK,IAAL,CAAU,iBAAV,CAA4B,SAAhC,EAA2C;AACzC;AACD;;AAED,UAAM,SAAS,GAAG,KAAK,IAAL,CAAU,SAA5B;;AACA,uBAAI,IAAJ,CAAS;AACP,MAAA,QAAQ,EAAE,YADH;AAEP,MAAA,IAAI,EAAE,oBAAK,IAAL,CAFC;AAGP,OAAC,GAAG,SAAS,CAAC,IAAI,EAAlB,GAAuB,SAAS,CAAC,OAH1B;AAIP,MAAA,SAAS,EAAE,mBAAI,QAAJ,CAAa,SAAb;AAJJ,KAAT,EAKG,WALH;;AAOA,UAAM,SAAS,CAAC,gCAAV,CAA2C;AAC/C,MAAA,QAAQ,EAAE,IADqC;AAE/C,MAAA,SAF+C;AAG/C,MAAA,YAH+C;AAI/C,MAAA,IAAI,EAAE,oBAAK,IAAL,CAJyC;AAK/C,MAAA,OAAO,EAAE,SAAS,CAAC;AAL4B,KAA3C,CAAN;AAQA,UAAM,eAAe,GAAqB,EAA1C;;AAEA,UAAM,qBAAqB,GAAI,QAAD,IAAwC;AACpE,UAAI,QAAQ,IAAI,IAAhB,EAAsB;AACpB,aAAK,MAAM,OAAX,IAAsB,QAAtB,EAAgC;AAC9B,UAAA,OAAO,CAAC,qBAAR,CAA8B,eAA9B,EAA+C,KAAK,IAAL,CAAU,UAAzD;AACD;AACF;AACF,KAND;;AAQA,UAAM,sBAAsB,GAAG,KAAK,4BAAL,CAAkC,MAAlC,EAA0C,IAA1C,EAAgD,4BAAhD,CAA/B;AACA,UAAM,aAAa,GAAG,sBAAsB,CAAC,aAA7C;AACA,UAAM,qBAAqB,GAAG,KAAK,oBAAL,CAA0B,IAA1B,EAAgC,SAAhC,EAA2C,sBAA3C,CAA9B;AACA,IAAA,qBAAqB,CAAC,qBAAD,CAArB;AACA,UAAM,iBAAiB,GAAG,KAAK,oBAAL,CAA0B,KAA1B,EAAiC,SAAjC,EAA4C,sBAA5C,CAA1B;AACA,IAAA,qBAAqB,CAAC,iBAAD,CAArB;AAEA,UAAM,WAAW,GAAqB;AACpC,MAAA,SADoC;AACzB,MAAA,MADyB;AACjB,MAAA,IADiB;AACX,MAAA,OADW;AAEpC,MAAA,QAAQ,EAAE,IAF0B;AAGpC,MAAA,oBAAoB,EAAE;AAHc,KAAtC;AAMA,UAAM,WAAW,GAAG,MAAM,KAAK,kBAAL,CAAwB,4BAAxB,CAA1B;AACA,UAAM,aAAa,GAAG,KAAK,QAAL,KAAkB,kBAAS,GAA3B,GAAiC,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,SAAS,CAAC,gBAA/B,EAAiD,UAAjD,EAA6D,WAA7D,CAAjC,GAA8G,kCAAgB,SAAhB,IAA6B,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,WAArB,CAA7B,GAAiE,SAArM;AACA,UAAM,WAAW,GAAG,KAAI,+BAAJ,EAAqB,KAAK,IAAL,CAAU,iBAA/B,CAApB;AACA,SAAK,YAAL,CAAkB,WAAlB,EAA+B,WAA/B,EAA4C,aAA5C,EAA2D,IAAI,CAAC,IAAL,CAAU,aAAV,EAAyB,KAAzB,CAA3D,EAA4F,WAA5F,EAAyG,4BAAzG,EAAuI,eAAvI,EAAwJ,aAAxJ;AACA,UAAM,WAAW,CAAC,UAAZ,EAAN;;AAEA,QAAI,KAAK,IAAL,CAAU,iBAAV,CAA4B,SAAhC,EAA2C;AACzC;AACD;;AAED,QAAI,SAAS,CAAC,oBAAV,IAAkC,IAAtC,EAA4C;AAC1C,YAAM,SAAS,CAAC,oBAAV,CAA+B;AACnC,QAAA,QAAQ,EAAE,IADyB;AAEnC,QAAA,SAFmC;AAGnC,QAAA,aAAa,EAAE,WAAW,IAAI,IAAf,GAAsB,IAAtB,GAA6B,MAAM,8BAAY,aAAZ,EAA2B,WAAW,CAAC,eAAZ,GAA8B;AAAC,UAAA,eAAe,EAAE;AAAlB,SAA9B,GAAwD,IAAnF,CAHf;AAInC,QAAA;AAJmC,OAA/B,CAAN;AAMD;;AAED,QAAI,KAAK,IAAL,CAAU,iBAAV,CAA4B,SAAhC,EAA2C;AACzC;AACD;;AAED,UAAM,wBAAwB,GAAG,KAAK,8BAAL,CAAoC,WAApC,CAAjC;AACA,UAAM,8BAAU,qBAAV,EAAiC,wBAAjC,CAAN;AACA,UAAM,8BAAU,iBAAV,EAA6B,wBAA7B,CAAN;;AAEA,QAAI,KAAK,IAAL,CAAU,iBAAV,CAA4B,SAAhC,EAA2C;AACzC;AACD;;AAED,UAAM,KAAK,IAAL,CAAU,SAAV,CAAoB,WAApB,CAAN;;AAEA,QAAI,SAAS,CAAC,SAAV,IAAuB,IAA3B,EAAiC;AAC/B,YAAM,SAAS,CAAC,SAAV,CAAoB,WAApB,CAAN;AACD;;AAED,UAAM,MAAM,GAAG,WAAW,IAAI,IAA9B;AACA,UAAM,KAAK,kBAAL,CAAwB,SAAxB,EAAmC,MAAnC,EAA2C,SAA3C,CAAN;;AACA,QAAI,IAAJ,EAAU;AACR,YAAM,KAAK,eAAL,CAAqB,MAArB,EAA6B,SAA7B,EAAwC,YAAxC,EAAsD,IAAtD,EAA4D,4BAA5D,EAA0F,OAA1F,CAAN;AACD;AACF;;AAES,QAAM,eAAN,CAAsB,MAAtB,EAAsC,SAAtC,EAAyD,YAAzD,EAA6F,IAA7F,EAAyG,4BAAzG,EAA2I,OAA3I,EAAiK;AACzK,UAAM,WAAW,GAAG,MAAM,KAAK,kBAAL,CAAwB,4BAAxB,CAA1B;AACA,UAAM,MAAM,GAAG,WAAW,IAAI,IAA9B;AACA,UAAM,WAAW,GAAG;AAClB,MAAA,SADkB;AAElB,MAAA,MAFkB;AAGlB,MAAA,IAHkB;AAIlB,MAAA,OAJkB;AAKlB,MAAA,QAAQ,EAAE,IALQ;AAMlB,MAAA,oBAAoB,EAAE;AANJ,KAApB;AAQA,UAAM,KAAK,OAAL,CAAa,WAAb,EAA0B,MAA1B,CAAN;AACA,UAAM,SAAS,GAAG,eAAe,CAAC,KAAK,MAAL,CAAY,SAAb,EAAwB,WAAxB,CAAjC;;AACA,QAAI,SAAS,IAAI,IAAjB,EAAuB;AACrB,YAAM,OAAO,CAAC,OAAR,CAAgB,SAAS,CAAC,WAAD,CAAzB,CAAN;AACD;AACF,GA7PmC,CA+PpC;;;AACU,EAAA,8BAA8B,CAAC,WAAD,EAA8B;AACpE,WAAO,IAAP;AACD;;AAEO,EAAA,YAAY,CAAC,WAAD,EAAgC,WAAhC,EAAiE,YAAjE,EAAuF,kBAAvF,EAAmH,WAAnH,EAAkJ,4BAAlJ,EAAoL,eAApL,EAAuN,aAAvN,EAA8P;AAChR,UAAM,MAAM,GAAG,KAAK,IAAL,CAAU,MAAzB;AACA,UAAM,MAAM,GAAG,KAAK,MAApB;AACA,UAAM,iBAAiB,GAAG,WAAW,IAAI,IAAf,IAAuB,8CAAsB,KAAK,IAA3B,CAAjD;AAEA,UAAM,YAAY,GAAG,wCAAoB,MAApB,EAA4B,kBAA5B,EAAgD,aAAhD,EAA+D,4BAA/D,EAA6F,IAA7F,EAAmG,WAAW,CAAC,MAA/G,EAAuH,iBAAvH,CAArB;;AACA,QAAI,eAAe,CAAC,MAAhB,GAAyB,CAA7B,EAAgC;AAC9B,WAAK,MAAM,OAAX,IAAsB,YAAtB,EAAoC;AAClC,QAAA,OAAO,CAAC,eAAR,GAA0B,eAA1B;AACD;AACF;;AAED,UAAM,SAAS,GAAG,KAAK,IAAL,CAAU,SAA5B;AACA,UAAM,WAAW,GAAG,0CAAkB,MAAlB,EAA0B,MAA1B,EAAkC,iBAAiB,GAAG;AACxE,MAAA,YAAY,EAAE,KAAK,IAAL,CAAU,QAAV,CAAmB,IADuC;AAExE,MAAA,IAAI,EAAE,+CAFkE;AAGxE,SAAG,MAAM,CAAC;AAH8D,KAAH,GAInE,MAAM,CAAC,aAJS,EAIM,SAAS,CAAC,iBAAV,IAA+B,IAA/B,GAAsC,IAAtC,GAA6C,SAAS,CAAC,iBAAV,EAJnD,CAApB;;AAMA,UAAM,gBAAgB,GAAI,QAAD,IAAiC;AACxD,aAAO,sCAAgB,QAAhB,EAA0B,KAAK,IAAL,CAAU,kBAAV,GAA+B,IAA/B,GAAsC,WAAhE,EAA6E,IAA7E,EAAmF,iBAAnF,EACJ,IADI,CACC,MAAM,MAAN,IAAe;AACnB,YAAI,CAAC,KAAK,IAAL,CAAU,kBAAX,IAAiC,CAAC,KAAK,IAAL,CAAU,+BAAhD,EAAiF;AAC/E,gBAAM,iBAAiB,GAAG,6CAAyB,MAAzB,EAAiC,kBAAjC,EAAqD,aAArD,EAAoE,4BAApE,EAAkG,KAAK,IAAvG,CAA1B;AACA,UAAA,MAAM,GAAG,MAAM,CAAC,MAAP,CAAc,MAAM,gDAA0B,IAA1B,EAAgC,iBAAhC,CAApB,CAAT;AACD;;AACD,eAAO,MAAM,CAAC,MAAP,CAAc,EAAE,IAAI,EAAE,CAAC,KAAH,CAAS,MAAT,GAAkB,CAAtC,CAAP;AACD,OAPI,CAAP;AAQD,KATD;;AAWA,QAAI,KAAK,IAAL,CAAU,kBAAd,EAAkC;AAChC,MAAA,WAAW,CAAC,OAAZ,CAAoB,uBAAgB,IAAhB,CAAqB,gBAAgB,CAAC,CAAC,KAAI,0BAAJ,EAAgB,MAAhB,EAAwB,YAAxB,EAAsC,aAAtC,CAAD,CAAD,CAArC,EAA+F,EAAE,IAAI,mCAAa,EAAb,EAAiB,KAAK,IAAtB,EAA4B,WAA5B,CAArG,CAApB;AACD,KAFD,MAGK,IAAI,WAAW,IAAI,IAAnB,EAAyB;AAC5B;AACA;AACA;AACA,YAAM,wBAAwB,GAAG,KAAK,8BAAL,CAAoC,WAApC,CAAjC;;AACA,YAAM,mBAAmB,GAAoB,IAAI,IAAG;AAClD,YAAI,wBAAwB,IAAI,IAAhC,EAAsC;AACpC,gBAAM,MAAM,GAAG,wBAAwB,CAAC,IAAD,CAAvC;;AACA,cAAI,MAAM,IAAI,IAAd,EAAoB;AAClB,mBAAO,MAAP;AACD;AACF;;AACD,eAAO,WAAW,CAAC,IAAD,CAAlB;AACD,OARD;;AAUA,MAAA,WAAW,CAAC,OAAZ,CAAoB,uBAAgB,IAAhB,CAAqB,gBAAgB,CAAC,YAAD,CAArC,EAAqD,EAAE,IAAI,mCAAa,EAAb,EAAiB,KAAK,IAAtB,EAA4B,mBAA5B,CAA3D,CAApB;AACD,KAhBI,MAiBA;AACH,YAAM,aAAa,GAAG,oCAAgB,MAAhB,EAAwB,YAAxB,EAAsC,kBAAtC,EAA0D;AAC9E,QAAA,aAD8E;AAE9E,QAAA,kBAAkB,EAAE,4BAF0D;AAG9E,QAAA,YAAY,EAAE,WAAW,CAAC,MAHoD;AAI9E,QAAA,UAAU,EAAE;AAJkE,OAA1D,CAAtB;AAMA,YAAM,WAAW,GAAG,aAAa,IAAI,IAAjB,GAAwB,IAAxB,GAA+B,aAAa,CAAC,CAAD,CAAhE;AACA,MAAA,WAAW,CAAC,OAAZ,CAAoB,gBAAgB,CAAC,YAAD,CAAhB,CACjB,IADiB,CACZ,MAAM,QAAN,IAAiB;AACrB,aAAK,MAAM,OAAX,IAAsB,QAAtB,EAAgC;AAC9B,gBAAM,qCAAe,WAAf,EAA4B,OAA5B,CAAN;AACD;;AAED,cAAM,KAAI,wBAAJ,EAAiB,MAAjB,EAAyB,YAAzB,EAAuC,WAAvC,EAAoD,WAAW,IAAI,IAAf,GAAsB,IAAtB,GAA6B,WAAW,CAAC,YAAZ,EAAjF,EACH,IADG,CACE,QADF,EACY,IADZ,CAAN;AAED,OARiB,CAApB;AASD;AACF,GAxUmC,CA0UpC;;;AACU,EAAA,OAAO,CAAC,WAAD,EAAgC,MAAhC,EAA+C;AAC9D,WAAO,OAAO,CAAC,OAAR,EAAP;AACD;;AAED,QAAM,WAAN,GAAiB;AACf,WAAO,IAAP;AACD;;AAEO,QAAM,kBAAN,CAAyB,kBAAzB,EAA+C;AACrD,QAAI,CAAC,kCAAgB,KAAK,IAAL,CAAU,SAA1B,CAAL,EAA2C;AACzC,aAAO,IAAP;AACD;;AAED,aAAS,YAAT,CAAsB,IAAtB,EAAkC;AAChC,aAAO,GAAG,IAAI,wEAAd;AACD;;AAED,UAAM,aAAa,GAAG,KAAK,MAA3B;;AACA,QAAI,aAAa,CAAC,aAAD,CAAb,IAAgC,IAApC,EAA0C;AACxC,YAAM,IAAI,KAAJ,CAAU,YAAY,CAAC,aAAD,CAAtB,CAAN;AACD;;AACD,QAAI,aAAa,CAAC,iBAAD,CAAb,IAAoC,IAAxC,EAA8C;AAC5C,YAAM,IAAI,KAAJ,CAAU,YAAY,CAAC,iBAAD,CAAtB,CAAN;AACD;;AAED,UAAM,gBAAgB,GAAG,kBAAkB,CAAC,IAA5C;AACA,UAAM,MAAM,GAAG,gBAAgB,IAAI,IAApB,GAA2B,KAAK,MAAL,CAAY,IAAvC,GAA8C,gBAA7D;;AACA,QAAI,MAAM,KAAK,KAAf,EAAsB;AACpB,YAAM,WAAW,GAAG,MAAM,sBAAW,IAAI,CAAC,IAAL,CAAU,KAAK,IAAL,CAAU,MAApB,EAA4B,UAA5B,CAAX,CAA1B,CADoB,CAEpB;;AACA,UAAI,WAAW,IAAI,IAAf,IAAuB,CAAC,WAAW,CAAC,MAAZ,EAA5B,EAAkD;AAChD,2BAAI,IAAJ,CAAS;AACP,UAAA,QAAQ,EAAE;AADH,SAAT,EAEG,2DAFH;AAGD;;AACD,aAAO,IAAP;AACD;;AAED,QAAI,MAAM,IAAI,IAAV,IAAkB,MAAM,KAAK,IAAjC,EAAuC;AACrC,aAAO,EAAP;AACD;;AAED,SAAK,MAAM,IAAX,IAAmB,CAAC,WAAD,EAAc,QAAd,CAAnB,EAA4C;AAC1C,UAAK,MAAc,CAAC,IAAD,CAAd,IAAwB,IAA7B,EAAmC;AACjC,cAAM,IAAI,KAAJ,CAAU,YAAY,CAAC,QAAQ,IAAI,EAAb,CAAtB,CAAN;AACD;AACF;;AACD,WAAO,+BAAW,EAAX,EAAe,MAAf,CAAP;AACD;;AAEM,EAAA,iBAAiB,CAAC,IAAD,EAAa;AACnC,WAAO,IAAI,CAAC,OAAL,CAAa,KAAK,UAAlB,EAA8B,IAA9B,CAAP;AACD;;AAEM,EAAA,yBAAyB,CAAC,SAAD,EAAkB;AAChD,WAAO,SAAP;AACD;;AAED,EAAA,eAAe,CAAC,SAAD,EAAkB;AAC/B,QAAI,KAAK,QAAL,KAAkB,kBAAS,GAA/B,EAAoC;AAClC,aAAO,KAAK,oBAAL,CAA0B,SAA1B,CAAP;AACD,KAFD,MAGK,IAAI,kCAAgB,KAAK,IAAL,CAAU,SAA1B,CAAJ,EAA0C;AAC7C,aAAO,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,WAArB,CAAP;AACD,KAFI,MAGA;AACH,aAAO,SAAP;AACD;AACF;;AAEM,EAAA,oBAAoB,CAAC,SAAD,EAAkB;AAC3C,WAAO,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,GAAG,KAAK,OAAL,CAAa,eAAe,MAApD,EAA4D,UAA5D,EAAwE,WAAxE,CAAP;AACD;;AAEO,QAAM,kBAAN,CAAyB,YAAzB,EAA+C,IAA/C,EAA6D,aAA7D,EAAoF,MAApF,EAAmG;AACzG,UAAM,YAAY,GAAG,IAAI,CAAC,QAAL,CAAc,KAAK,IAAL,CAAU,MAAxB,EAAgC,IAAI,CAAC,OAAL,CAAa,KAAK,IAAL,CAAU,MAAvB,EAA+B,IAA/B,CAAhC,CAArB;;AACA,QAAI,MAAJ,EAAY;AACV,YAAM,2CAAmB,IAAI,CAAC,IAAL,CAAU,YAAV,EAAwB,UAAxB,CAAnB,EAAwD,YAAxD,EAAsE,aAAtE,CAAN;AACA;AACD;;AAED,UAAM,UAAU,GAAG,IAAI,CAAC,KAAL,CAAW,IAAX,CAAnB,CAPyG,CAQzG;AACA;;AACA,QAAI,UAAU,CAAC,GAAX,CAAe,QAAf,CAAwB,OAAxB,CAAJ,EAAsC;AACpC;AACA;AACA;AACA,YAAM,SAAS,GAAkB,UAAU,CAAC,GAAX,CAAe,KAAf,CAAqB,IAAI,CAAC,GAA1B,CAAjC;AACA,UAAI,iBAAiB,GAAG,CAAxB;AACA,MAAA,SAAS,CAAC,IAAV,CAAe,CAAC,QAAD,EAAmB,KAAnB,KAAoC;AACjD,QAAA,iBAAiB,GAAG,KAApB;AACA,eAAO,QAAQ,CAAC,QAAT,CAAkB,OAAlB,CAAP;AACD,OAHD;AAIA,YAAM,QAAQ,GAAG,IAAI,CAAC,IAAL,CAAU,GAAG,SAAS,CAAC,KAAV,CAAgB,CAAhB,EAAmB,iBAAiB,GAAG,CAAvC,CAAb,CAAjB;AACA,UAAI,QAAQ,GAAG,SAAS,CAAC,MAAV,GAAoB,iBAAiB,GAAG,CAAxC,GAA6C,IAAI,CAAC,IAAL,CAAU,KAAV,CAAgB,SAAS,CAAC,KAAV,CAAgB,iBAAiB,GAAG,CAApC,CAAhB,CAA7C,GAAuG,EAAtH;AACA,MAAA,QAAQ,IAAI,IAAI,CAAC,IAAL,CAAU,QAAV,EAAoB,UAAU,CAAC,IAA/B,CAAZ;AACA,YAAM,2CAAmB,IAAI,CAAC,IAAL,CAAU,YAAV,EAAwB,KAAxB,EAA+B,QAA/B,CAAnB,EAA6D,QAA7D,EAAuE,aAAvE,CAAN;AACD,KAdD,MAeK;AACH,YAAM,QAAQ,GAAG,IAAI,CAAC,IAAL,CAAU,YAAV,EAAwB,KAAxB,EAA+B,YAA/B,CAAjB;AACA,YAAM,OAAO,GAAG,MAAM,sBAAW,QAAX,CAAtB;;AACA,UAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,cAAM,IAAI,KAAJ,CAAU,GAAG,aAAa,KAAK,QAAQ,qDAAvC,CAAN;AACD,OAFD,MAGK;AACH;AACA,YAAI,CAAC,OAAO,CAAC,MAAR,EAAL,EAAuB;AACrB,gBAAM,IAAI,KAAJ,CAAU,GAAG,aAAa,KAAK,QAAQ,oDAAvC,CAAN;AACD;AACF;AACF;AACF;;AAEO,QAAM,kBAAN,CAAyB,SAAzB,EAA4C,MAA5C,EAA6D,SAA7D,EAAiF;AACvF,UAAM,OAAO,GAAG,MAAM,sBAAW,SAAX,CAAtB;;AACA,QAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,YAAM,IAAI,KAAJ,CAAU,qBAAqB,SAAS,qDAAxC,CAAN;AACD,KAFD,MAGK;AACH;AACA,UAAI,CAAC,OAAO,CAAC,WAAR,EAAL,EAA4B;AAC1B,cAAM,IAAI,KAAJ,CAAU,qBAAqB,SAAS,yDAAxC,CAAN;AACD;AACF;;AAED,UAAM,YAAY,GAAG,KAAK,eAAL,CAAqB,SAArB,CAArB;AACA,UAAM,QAAQ,GAAG,CAAC,SAAS,CAAC,WAAV,IAAyB,IAAzB,GAAgC,IAAhC,GAAuC,SAAS,CAAC,WAAV,CAAsB,KAAK,QAA3B,CAAxC,KAAiF,KAAK,IAAL,CAAU,QAAV,CAAmB,IAApG,IAA4G,UAA7H;AACA,UAAM,KAAK,kBAAL,CAAwB,YAAxB,EAAsC,QAAtC,EAAgD,wBAAhD,EAA0E,MAA1E,CAAN;AACA,UAAM,KAAK,kBAAL,CAAwB,YAAxB,EAAsC,cAAtC,EAAsD,aAAtD,EAAqE,MAArE,CAAN;AACD,GA7cmC,CA+cpC;;;AACA,EAAA,uBAAuB,CAAC,aAAD,EAA+B,GAA/B,EAA4C,IAA5C,EAAgE,aAAa,GAAG,IAAhF,EAAsF,WAAA,GAAsB,mCAA5G,EAA+I;AACpK,WAAO,+BAA+B,CAAC,aAAD,EAAgB,MAAM,KAAK,mBAAL,CAAyB,WAAzB,EAAsC,GAAtC,EAA2C,aAAa,IAAI,IAAI,KAAK,oBAAK,GAA/B,GAAqC,IAArC,GAA4C,IAAvF,CAAtB,CAAtC;AACD;;AAED,EAAA,yBAAyB,CAAC,qBAAD,EAAkE,GAAlE,EAA+E,IAA/E,EAAmG,cAAnG,EAA4H,aAAa,GAAG,IAA5I,EAAgJ;AACvK,QAAI,OAAO,GAAG,qBAAqB,IAAI,IAAzB,GAAgC,IAAhC,GAAuC,qBAAqB,CAAC,YAA3E;;AACA,QAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,MAAA,OAAO,GAAG,KAAK,4BAAL,CAAkC,YAAlC,IAAkD,KAAK,MAAL,CAAY,YAAxE;AACD;;AAED,QAAI,OAAO,IAAI,IAAf,EAAqB;AACnB;AACA,MAAA,OAAO,GAAG,cAAc,IAAI,0CAA5B;AACD,KAHD,MAGO;AACL;AACA;AACA,MAAA,aAAa,GAAG,KAAK,QAAL,KAAkB,kBAAS,GAA3C;AACD;;AACD,WAAO,KAAK,mBAAL,CAAyB,OAAzB,EAAkC,GAAlC,EAAuC,aAAa,IAAI,IAAI,KAAK,oBAAK,GAA/B,GAAqC,IAArC,GAA4C,IAAnF,CAAP;AACD;;AAED,EAAA,+BAA+B,CAAC,qBAAD,EAAkE,GAAlE,EAA+E,IAA/E,EAAiG;AAC9H;AACA,WAAO,KAAK,yBAAL,CAA+B,qBAA/B,EAAsD,GAAtD,EAA2D,IAA3D,EAAiE,0CAAjE,EAA6G,IAA7G,CAAP;AACD;;AAEO,EAAA,mBAAmB,CAAC,OAAD,EAAe,GAAf,EAA4B,IAA5B,EAAyD;AAClF,UAAM,QAAQ,GAAG,IAAI,IAAI,IAAR,GAAe,IAAf,GAAsB,iCAAoB,IAApB,EAA0B,GAA1B,CAAvC;AACA,WAAO,KAAK,WAAL,CAAiB,OAAjB,EAA0B,QAA1B,EAAoC;AACzC,MAAA;AADyC,KAApC,CAAP;AAGD;;AAED,EAAA,WAAW,CAAC,OAAD,EAAkB,IAAlB,EAAwC,KAAA,GAAa,EAArD,EAAyD,sBAAsB,GAAG,IAAlF,EAAsF;AAC/F,WAAO,kCAAc,OAAd,EAAuB,IAAvB,EAA6B,KAAK,OAAlC,EAA2C;AAAC,MAAA,EAAE,EAAE,KAAK,QAAL,CAAc,qBAAnB;AAA0C,SAAG;AAA7C,KAA3C,EAAgG,sBAAhG,CAAP;AACD;;AAED,EAAA,aAAa,CAAC,GAAD,EAAqB,UAArB,EAA4D,UAA5D,EAA+E;AAC1F,UAAM,MAAM,GAAG,GAAG,IAAI,IAAP,GAAc,EAAd,GAAmB,IAAI,GAAG,EAAzC;AACA,UAAM,SAAS,GAAG,GAAG,KAAK,KAAR,GAAgB,GAAhB,GAAsB,GAAxC;AACA,WAAO,GAAG,UAAU,GAAG,KAAK,OAAL,CAAa,IAAhB,GAAuB,KAAK,OAAL,CAAa,eAAe,GAAG,SAAS,GAAG,KAAK,OAAL,CAAa,OAAO,GAAG,UAAU,IAAI,IAAd,GAAqB,EAArB,GAA0B,GAAG,SAAS,GAAG,UAAU,EAAE,GAAG,MAAM,EAA3K;AACD;;AAED,EAAA,WAAW,CAAC,MAAD,EAAe;AACxB,WAAO,KAAK,IAAL,CAAU,cAAV,CAAyB,WAAzB,CAAqC;AAAC,MAAA;AAAD,KAArC,CAAP;AACD;;AAED,MAAI,gBAAJ,GAAoB;AAClB,WAAO,4BAAQ,KAAK,MAAL,CAAY,gBAApB,EAAsC,MAAtC,CAA6C,4BAAQ,KAAK,4BAAL,CAAkC,gBAA1C,CAA7C,CAAP;AACD;;AAED,QAAM,WAAN,CAAkB,MAAlB,EAAqD,GAAG,KAAxD,EAA4E;AAC1E,UAAM,YAAY,GAAG,KAAK,IAAL,CAAU,iBAA/B;;AACA,QAAI,MAAM,KAAK,SAAf,EAA0B;AACxB,YAAM,YAAY,GAAG,MAAM,KAAK,YAAhC;;AACA,WAAK,MAAM,IAAX,IAAmB,KAAnB,EAA0B;AACxB,YAAI,YAAY,CAAC,QAAb,CAAsB,IAAtB,CAAJ,EAAiC;AAC/B,iBAAO,IAAI,CAAC,IAAL,CAAU,YAAV,EAAwB,IAAxB,CAAP;AACD;AACF;AACF,KAPD,MAQK,IAAI,MAAM,IAAI,IAAV,IAAkB,CAAC,oCAAgB,MAAhB,CAAvB,EAAgD;AACnD,YAAM,YAAY,GAAG,MAAM,KAAK,YAAhC;;AACA,UAAI,YAAY,CAAC,QAAb,CAAsB,MAAtB,CAAJ,EAAmC;AACjC,eAAO,IAAI,CAAC,IAAL,CAAU,YAAV,EAAwB,MAAxB,CAAP;AACD;;AAED,UAAI,CAAC,GAAG,IAAI,CAAC,OAAL,CAAa,YAAb,EAA2B,MAA3B,CAAR;;AACA,UAAI,OAAM,sBAAW,CAAX,CAAN,KAAuB,IAA3B,EAAiC;AAC/B,QAAA,CAAC,GAAG,IAAI,CAAC,OAAL,CAAa,KAAK,UAAlB,EAA8B,MAA9B,CAAJ;;AACA,YAAI,OAAM,sBAAW,CAAX,CAAN,KAAuB,IAA3B,EAAiC;AAC/B,gBAAM,KAAI,wCAAJ,EAA8B,mCAAmC,MAAM,uBAAuB,YAAY,wCAAwC,KAAK,UAAU,IAAjK,CAAN;AACD;AACF;;AACD,aAAO,CAAP;AACD;;AACD,WAAO,IAAP;AACD;;AAED,MAAI,gBAAJ,GAAoB;AAClB,UAAM,wBAAwB,GAAG,KAAK,4BAAL,CAAkC,gBAAnE;AACA,WAAO,CAAC,wBAAwB,IAAI,IAA5B,GAAmC,KAAK,MAAL,CAAY,gBAA/C,GAAkE,wBAAnE,KAAgG,KAAvG;AACD;;AAES,QAAM,gBAAN,CAAuB,MAAvB,EAAyC;AACjD,UAAM,MAAM,GAAG,MAAM,KAAK,WAAL,CAAiB,4BAAQ,KAAK,4BAAL,CAAkC,IAAlC,IAA0C,KAAK,MAAL,CAAY,IAA9D,CAAjB,EAAsF,EAAtF,EAA0F,MAA1F,CAArB;;AACA,QAAI,MAAM,CAAC,MAAP,KAAkB,CAAtB,EAAyB;AACvB,YAAM,SAAS,GAAG,KAAK,IAAL,CAAU,SAA5B;;AACA,UAAI,SAAS,CAAC,cAAV,IAA4B,IAAhC,EAAsC;AACpC,eAAO,SAAS,CAAC,cAAV,CAAyB,KAAK,QAA9B,CAAP;AACD;;AAED,yBAAI,IAAJ,CAAS;AAAC,QAAA,MAAM,EAAE;AAAT,OAAT,EAAkD,WAAW,qBAAqB,CAAC,SAAS,CAAC,IAAX,CAAgB,eAAlG;;AACA,aAAO,KAAK,uBAAL,EAAP;AACD,KARD,MASK;AACH,aAAO,MAAM,CAAC,CAAD,CAAN,CAAU,IAAjB;AACD;AACF;;AAED,EAAA,uBAAuB,GAAA;AACrB,UAAM,SAAS,GAAG,KAAK,IAAL,CAAU,SAA5B;AACA,WAAO,SAAS,CAAC,cAAV,IAA4B,IAA5B,GAAmC,IAAnC,GAA0C,SAAS,CAAC,cAAV,CAAyB,KAAK,QAA9B,CAAjD;AACD,GAvjBmC,CAyjBpC;;;AACA,QAAM,WAAN,CAAkB,OAAlB,EAA0C,eAA1C,EAA0E,YAA1E,EAAkG;AAChG,UAAM,IAAI,GAAG,CACX,MADW,EAEX,UAFW,EAEC,YAFD,EAGX,QAHW,EAGD,KAAK,iBAHJ,EAIX,QAJW,EAID,KAAK,UAJJ,EAKX,OALW,EAKF,IAAI,CAAC,OAAL,CAAa,KAAK,UAAlB,EAA8B,KAAK,MAAL,CAAY,WAAZ,CAA0B,MAAxD,EAAkE,SAAS,YAAY,EAAvF,CALE,CAAb;;AAOA,SAAK,MAAM,MAAX,IAAqB,OAArB,EAA8B;AAC5B,MAAA,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,MAArB;AACD;;AACD,SAAK,MAAM,MAAX,IAAqB,eAArB,EAAsC;AACpC,MAAA,IAAI,CAAC,IAAL,CAAU,kBAAV,EAA8B,MAA9B;AACD;;AAED,UAAM,MAAM,GAAsB,MAAM,2CAAwB,IAAxB,CAAxC;AACA,UAAM,YAAY,GAAG,MAAM,CAAC,KAA5B;;AACA,QAAI,YAAY,IAAI,IAApB,EAA0B;AACxB,YAAM,KAAI,wCAAJ,EAA8B,YAA9B,EAA4C,MAAM,CAAC,SAAnD,CAAN;AACD;;AAED,QAAI,MAAM,CAAC,UAAX,EAAuB;AACrB,yBAAI,IAAJ,CAAS;AAAC,QAAA,MAAM,EAAE;AAAT,OAAT,EAAkD,WAAW,qBAAqB,CAAC,KAAK,IAAL,CAAU,SAAV,CAAoB,IAArB,CAA0B,eAA5G;AACD;;AAED,WAAO,MAAM,CAAC,KAAP,IAAgB,EAAvB;AACD;;AAplBmC;;;;AAsmBhC,SAAU,gBAAV,CAA2B,IAA3B,EAAuC;AAC3C,SAAO,oBAAoB,IAApB,CAAyB,IAAzB,CAAP;AACD;;AAEK,SAAU,+BAAV,CAA0C,aAA1C,EAAwE,gBAAxE,EAAsG;AAC1G;AACA,MAAI,aAAa,IAAI,IAArB,EAA2B;AACzB,QAAI,gBAAgB,CAAC,aAAD,CAApB,EAAqC;AACnC,aAAO,IAAP;AACD,KAHwB,CAKzB;;;AACA,IAAA,aAAa,GAAG,aAAa,CAAC,OAAd,CAAsB,IAAtB,EAA4B,GAA5B,CAAhB;;AACA,QAAI,gBAAgB,CAAC,aAAD,CAApB,EAAqC;AACnC,aAAO,aAAP;AACD;AACF;;AAED,SAAO,gBAAgB,EAAvB;AACD,C,CAED;;;AACM,SAAU,YAAV,CAAuB,GAAvB,EAAkC;AACtC,SAAO,GAAG,CAAC,UAAJ,CAAe,GAAf,IAAsB,GAAG,CAAC,SAAJ,CAAc,CAAd,CAAtB,GAAyC,GAAhD;AACD;;AAEK,SAAU,eAAV,CAA6B,QAA7B,EAAmD,IAAnD,EAA+D;AACnE,MAAI,QAAQ,IAAI,IAAZ,IAAoB,OAAO,QAAP,KAAoB,QAA5C,EAAsD;AACpD,WAAO,QAAP;AACD;;AAED,MAAI,CAAC,GAAG,QAAR;;AACA,MAAI,CAAC,CAAC,UAAF,CAAa,GAAb,CAAJ,EAAuB;AACrB,IAAA,CAAC,GAAG,IAAI,CAAC,OAAL,CAAa,CAAb,CAAJ;AACD;;AAED,MAAI;AACF,IAAA,CAAC,GAAG,OAAO,CAAC,OAAR,CAAgB,CAAhB,CAAJ;AACD,GAFD,CAGA,OAAO,CAAP,EAAU;AACR,8BAAM,CAAN;AACA,IAAA,CAAC,GAAG,IAAI,CAAC,OAAL,CAAa,CAAb,CAAJ;AACD,GAhBkE,CAkBnE;;;AACA,QAAM,CAAC,GAAG,OAAO,CAAC,CAAD,CAAjB;;AACA,QAAM,WAAW,GAAG,CAAC,CAAC,IAAD,CAArB;;AACA,MAAI,WAAW,IAAI,IAAnB,EAAyB;AACvB,WAAO,CAAC,CAAC,OAAF,IAAa,CAApB;AACD,GAFD,MAGK;AACH,WAAO,WAAP;AACD;AACF;;AAEK,SAAU,aAAV,CAAwB,EAAxB,EAAuD,EAAvD,EAAoF;AACxF,SAAO,EAAE,IAAI,IAAN,GAAa,EAAb,GAAkB,EAAzB;AACD;;AAED,SAAS,qBAAT,CAA+B,IAA/B,EAA2C;AACzC,SAAO,IAAI,CAAC,MAAL,CAAY,CAAZ,EAAe,WAAf,KAA+B,IAAI,CAAC,KAAL,CAAW,CAAX,CAAtC;AACD;;AAEK,SAAU,yCAAV,CAAoD,QAApD,EAAmF;AACvF,MAAI,QAAQ,CAAC,QAAT,KAAsB,kBAAS,KAA/B,IAAwC,QAAQ,CAAC,MAAT,CAAgB,WAAhB,KAAgC,KAA5E,EAAmF;AACjF,WAAO,KAAP;AACD;;AAED,MAAI,OAAO,CAAC,QAAR,KAAqB,OAArB,IAAgC,8BAAU,OAAO,CAAC,GAAR,CAAY,aAAtB,CAApC,EAA0E;AACxE,WAAO,QAAQ,CAAC,MAAT,CAAgB,YAAhB,IAAgC,IAAhC,IAAwC,QAAQ,CAAC,MAAT,CAAgB,gBAAhB,IAAoC,IAAnF;AACD;;AACD,SAAO,KAAP;AACD,C", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { Arch, as<PERSON>rray, AsyncTaskManager, debug, DebugLogger, deepAssign, getArchSuffix, InvalidConfigurationError, isEmptyOrSpaces, log, isEnvTrue } from \"builder-util\"\nimport { getArtifactArchName } from \"builder-util/out/arch\"\nimport { FileTransformer, statOrNull } from \"builder-util/out/fs\"\nimport { orIfFileNotExist } from \"builder-util/out/promise\"\nimport { readdir } from \"fs-extra\"\nimport { Lazy } from \"lazy-val\"\nimport { Minimatch } from \"minimatch\"\nimport * as path from \"path\"\nimport { AppInfo } from \"./appInfo\"\nimport { checkFileInArchive } from \"./asar/asarFileChecker\"\nimport { AsarPackager } from \"./asar/asarUtil\"\nimport { computeData } from \"./asar/integrity\"\nimport { copyFiles, FileMatcher, getFileMatchers, GetFileMatchersOptions, getMainFileMatchers, getNodeModuleFileMatcher } from \"./fileMatcher\"\nimport { createTransformer, isElectronCompileUsed } from \"./fileTransformer\"\nimport { Framework, isElectronBased } from \"./Framework\"\nimport { AfterPackContext, AsarOptions, CompressionLevel, Configuration, ElectronPlatformName, FileAssociation, Packager, PackagerOptions, Platform, PlatformSpecificBuildOptions, Target, TargetSpecificOptions } from \"./index\"\nimport { executeAppBuilderAsJson } from \"./util/appBuilder\"\nimport { computeFileSets, computeNodeModuleFileSets, copyAppFiles, ELECTRON_COMPILE_SHIM_FILENAME, transformFiles } from \"./util/appFileCopier\"\nimport { expandMacro as doExpandMacro } from \"./util/macroExpander\"\n\nexport abstract class PlatformPackager<DC extends PlatformSpecificBuildOptions> {\n  get packagerOptions(): PackagerOptions {\n    return this.info.options\n  }\n\n  get buildResourcesDir(): string {\n    return this.info.buildResourcesDir\n  }\n\n  get projectDir(): string {\n    return this.info.projectDir\n  }\n\n  get config(): Configuration {\n    return this.info.config\n  }\n\n  readonly platformSpecificBuildOptions: DC\n\n  get resourceList(): Promise<Array<string>> {\n    return this._resourceList.value\n  }\n\n  private readonly _resourceList = new Lazy<Array<string>>(() => orIfFileNotExist(readdir(this.info.buildResourcesDir), []))\n\n  readonly appInfo: AppInfo\n\n  protected constructor(readonly info: Packager, readonly platform: Platform) {\n    this.platformSpecificBuildOptions = PlatformPackager.normalizePlatformSpecificBuildOptions((this.config as any)[platform.buildConfigurationKey])\n    this.appInfo = this.prepareAppInfo(info.appInfo)\n  }\n\n  get compression(): CompressionLevel {\n    const compression = this.platformSpecificBuildOptions.compression\n    // explicitly set to null - request to use default value instead of parent (in the config)\n    if (compression === null) {\n      return \"normal\"\n    }\n    return compression || this.config.compression || \"normal\"\n  }\n\n  get debugLogger(): DebugLogger {\n    return this.info.debugLogger\n  }\n\n  abstract get defaultTarget(): Array<string>\n\n  // eslint-disable-next-line\n  protected prepareAppInfo(appInfo: AppInfo) {\n    return new AppInfo(this.info, null, this.platformSpecificBuildOptions)\n  }\n\n  private static normalizePlatformSpecificBuildOptions(options: any | null | undefined): any {\n    return options == null ? Object.create(null) : options\n  }\n\n  abstract createTargets(targets: Array<string>, mapper: (name: string, factory: (outDir: string) => Target) => void): void\n\n  protected getCscPassword(): string {\n    const password = this.doGetCscPassword()\n    if (isEmptyOrSpaces(password)) {\n      log.info({reason: \"CSC_KEY_PASSWORD is not defined\"}, \"empty password will be used for code signing\")\n      return \"\"\n    }\n    else {\n      return password!.trim()\n    }\n  }\n\n  protected getCscLink(extraEnvName?: string | null): string | null | undefined {\n    // allow to specify as empty string\n    const envValue = chooseNotNull(extraEnvName == null ? null : process.env[extraEnvName], process.env.CSC_LINK)\n    return chooseNotNull(chooseNotNull(this.info.config.cscLink, this.platformSpecificBuildOptions.cscLink), envValue)\n  }\n\n  protected doGetCscPassword(): string | null | undefined {\n    // allow to specify as empty string\n    return chooseNotNull(chooseNotNull(this.info.config.cscKeyPassword, this.platformSpecificBuildOptions.cscKeyPassword), process.env.CSC_KEY_PASSWORD)\n  }\n\n  protected computeAppOutDir(outDir: string, arch: Arch): string {\n    return this.packagerOptions.prepackaged || path.join(outDir, `${this.platform.buildConfigurationKey}${getArchSuffix(arch)}${this.platform === Platform.MAC ? \"\" : \"-unpacked\"}`)\n  }\n\n  dispatchArtifactCreated(file: string, target: Target | null, arch: Arch | null, safeArtifactName?: string | null): Promise<void> {\n    return this.info.callArtifactBuildCompleted({\n      file, safeArtifactName, target, arch,\n      packager: this,\n    })\n  }\n\n  async pack(outDir: string, arch: Arch, targets: Array<Target>, taskManager: AsyncTaskManager): Promise<any> {\n    const appOutDir = this.computeAppOutDir(outDir, arch)\n    await this.doPack(outDir, appOutDir, this.platform.nodeName as ElectronPlatformName, arch, this.platformSpecificBuildOptions, targets)\n    this.packageInDistributableFormat(appOutDir, arch, targets, taskManager)\n  }\n\n  protected packageInDistributableFormat(appOutDir: string, arch: Arch, targets: Array<Target>, taskManager: AsyncTaskManager): void {\n    if (targets.find(it => !it.isAsyncSupported) == null) {\n      PlatformPackager.buildAsyncTargets(targets, taskManager, appOutDir, arch)\n      return\n    }\n\n    taskManager.add(async () => {\n      // BluebirdPromise.map doesn't invoke target.build immediately, but for RemoteTarget it is very critical to call build() before finishBuild()\n      const subTaskManager = new AsyncTaskManager(this.info.cancellationToken)\n      PlatformPackager.buildAsyncTargets(targets, subTaskManager, appOutDir, arch)\n      await subTaskManager.awaitTasks()\n\n      for (const target of targets) {\n        if (!target.isAsyncSupported) {\n          await target.build(appOutDir, arch)\n        }\n      }\n    })\n  }\n\n  private static buildAsyncTargets(targets: Array<Target>, taskManager: AsyncTaskManager, appOutDir: string, arch: Arch) {\n    for (const target of targets) {\n      if (target.isAsyncSupported) {\n        taskManager.addTask(target.build(appOutDir, arch))\n      }\n    }\n  }\n\n  private getExtraFileMatchers(isResources: boolean, appOutDir: string, options: GetFileMatchersOptions): Array<FileMatcher> | null {\n    const base = isResources ? this.getResourcesDir(appOutDir) : (this.platform === Platform.MAC ? path.join(appOutDir, `${this.appInfo.productFilename}.app`, \"Contents\") : appOutDir)\n    return getFileMatchers(this.config, isResources ? \"extraResources\" : \"extraFiles\", base, options)\n  }\n\n  createGetFileMatchersOptions(outDir: string, arch: Arch, customBuildOptions: PlatformSpecificBuildOptions): GetFileMatchersOptions {\n    return {\n      macroExpander: it => this.expandMacro(it, arch == null ? null : Arch[arch], {\"/*\": \"{,/**/*}\"}),\n      customBuildOptions,\n      globalOutDir: outDir,\n      defaultSrc: this.projectDir,\n    }\n  }\n\n  protected async doPack(outDir: string, appOutDir: string, platformName: ElectronPlatformName, arch: Arch, platformSpecificBuildOptions: DC, targets: Array<Target>, sign: boolean = true) {\n    if (this.packagerOptions.prepackaged != null) {\n      return\n    }\n\n    if (this.info.cancellationToken.cancelled) {\n      return\n    }\n\n    await this.info.installAppDependencies(this.platform, arch);\n\n    if (this.info.cancellationToken.cancelled) {\n      return\n    }\n\n    const framework = this.info.framework\n    log.info({\n      platform: platformName,\n      arch: Arch[arch],\n      [`${framework.name}`]: framework.version,\n      appOutDir: log.filePath(appOutDir),\n    }, `packaging`)\n\n    await framework.prepareApplicationStageDirectory({\n      packager: this,\n      appOutDir,\n      platformName,\n      arch: Arch[arch],\n      version: framework.version,\n    })\n\n    const excludePatterns: Array<Minimatch> = []\n\n    const computeParsedPatterns = (patterns: Array<FileMatcher> | null) => {\n      if (patterns != null) {\n        for (const pattern of patterns) {\n          pattern.computeParsedPatterns(excludePatterns, this.info.projectDir)\n        }\n      }\n    }\n\n    const getFileMatchersOptions = this.createGetFileMatchersOptions(outDir, arch, platformSpecificBuildOptions)\n    const macroExpander = getFileMatchersOptions.macroExpander\n    const extraResourceMatchers = this.getExtraFileMatchers(true, appOutDir, getFileMatchersOptions)\n    computeParsedPatterns(extraResourceMatchers)\n    const extraFileMatchers = this.getExtraFileMatchers(false, appOutDir, getFileMatchersOptions)\n    computeParsedPatterns(extraFileMatchers)\n\n    const packContext: AfterPackContext = {\n      appOutDir, outDir, arch, targets,\n      packager: this,\n      electronPlatformName: platformName,\n    }\n\n    const asarOptions = await this.computeAsarOptions(platformSpecificBuildOptions)\n    const resourcesPath = this.platform === Platform.MAC ? path.join(appOutDir, framework.distMacOsAppName, \"Contents\", \"Resources\") : (isElectronBased(framework) ? path.join(appOutDir, \"resources\") : appOutDir)\n    const taskManager = new AsyncTaskManager(this.info.cancellationToken)\n    this.copyAppFiles(taskManager, asarOptions, resourcesPath, path.join(resourcesPath, \"app\"), packContext, platformSpecificBuildOptions, excludePatterns, macroExpander)\n    await taskManager.awaitTasks()\n\n    if (this.info.cancellationToken.cancelled) {\n      return\n    }\n\n    if (framework.beforeCopyExtraFiles != null) {\n      await framework.beforeCopyExtraFiles({\n        packager: this,\n        appOutDir,\n        asarIntegrity: asarOptions == null ? null : await computeData(resourcesPath, asarOptions.externalAllowed ? {externalAllowed: true} : null),\n        platformName,\n      })\n    }\n\n    if (this.info.cancellationToken.cancelled) {\n      return\n    }\n\n    const transformerForExtraFiles = this.createTransformerForExtraFiles(packContext)\n    await copyFiles(extraResourceMatchers, transformerForExtraFiles)\n    await copyFiles(extraFileMatchers, transformerForExtraFiles)\n\n    if (this.info.cancellationToken.cancelled) {\n      return\n    }\n\n    await this.info.afterPack(packContext)\n\n    if (framework.afterPack != null) {\n      await framework.afterPack(packContext)\n    }\n\n    const isAsar = asarOptions != null\n    await this.sanityCheckPackage(appOutDir, isAsar, framework)\n    if (sign) {\n      await this.doSignAfterPack(outDir, appOutDir, platformName, arch, platformSpecificBuildOptions, targets);\n    }\n  }\n\n  protected async doSignAfterPack(outDir: string, appOutDir: string, platformName: ElectronPlatformName, arch: Arch, platformSpecificBuildOptions: DC, targets: Array<Target>) {\n    const asarOptions = await this.computeAsarOptions(platformSpecificBuildOptions);\n    const isAsar = asarOptions != null;\n    const packContext = {\n      appOutDir,\n      outDir,\n      arch,\n      targets,\n      packager: this,\n      electronPlatformName: platformName\n    };\n    await this.signApp(packContext, isAsar);\n    const afterSign = resolveFunction(this.config.afterSign, \"afterSign\");\n    if (afterSign != null) {\n      await Promise.resolve(afterSign(packContext));\n    }\n  }\n\n  // eslint-disable-next-line\n  protected createTransformerForExtraFiles(packContext: AfterPackContext): FileTransformer | null {\n    return null\n  }\n\n  private copyAppFiles(taskManager: AsyncTaskManager, asarOptions: AsarOptions | null, resourcePath: string, defaultDestination: string, packContext: AfterPackContext, platformSpecificBuildOptions: DC, excludePatterns: Array<Minimatch>, macroExpander: ((it: string) => string)) {\n    const appDir = this.info.appDir\n    const config = this.config\n    const isElectronCompile = asarOptions != null && isElectronCompileUsed(this.info)\n\n    const mainMatchers = getMainFileMatchers(appDir, defaultDestination, macroExpander, platformSpecificBuildOptions, this, packContext.outDir, isElectronCompile)\n    if (excludePatterns.length > 0) {\n      for (const matcher of mainMatchers) {\n        matcher.excludePatterns = excludePatterns\n      }\n    }\n\n    const framework = this.info.framework\n    const transformer = createTransformer(appDir, config, isElectronCompile ? {\n      originalMain: this.info.metadata.main,\n      main: ELECTRON_COMPILE_SHIM_FILENAME,\n      ...config.extraMetadata\n    } : config.extraMetadata, framework.createTransformer == null ? null : framework.createTransformer())\n\n    const _computeFileSets = (matchers: Array<FileMatcher>) => {\n      return computeFileSets(matchers, this.info.isPrepackedAppAsar ? null : transformer, this, isElectronCompile)\n        .then(async result => {\n          if (!this.info.isPrepackedAppAsar && !this.info.areNodeModulesHandledExternally) {\n            const moduleFileMatcher = getNodeModuleFileMatcher(appDir, defaultDestination, macroExpander, platformSpecificBuildOptions, this.info)\n            result = result.concat(await computeNodeModuleFileSets(this, moduleFileMatcher))\n          }\n          return result.filter(it => it.files.length > 0)\n        })\n    }\n\n    if (this.info.isPrepackedAppAsar) {\n      taskManager.addTask(BluebirdPromise.each(_computeFileSets([new FileMatcher(appDir, resourcePath, macroExpander)]), it => copyAppFiles(it, this.info, transformer)))\n    }\n    else if (asarOptions == null) {\n      // for ASAR all asar unpacked files will be extra transformed (e.g. sign of EXE and DLL) later,\n      // for prepackaged asar extra transformation not supported yet,\n      // so, extra transform if asar is disabled\n      const transformerForExtraFiles = this.createTransformerForExtraFiles(packContext)\n      const combinedTransformer: FileTransformer = file => {\n        if (transformerForExtraFiles != null) {\n          const result = transformerForExtraFiles(file)\n          if (result != null) {\n            return result\n          }\n        }\n        return transformer(file)\n      }\n\n      taskManager.addTask(BluebirdPromise.each(_computeFileSets(mainMatchers), it => copyAppFiles(it, this.info, combinedTransformer)))\n    }\n    else {\n      const unpackPattern = getFileMatchers(config, \"asarUnpack\", defaultDestination, {\n        macroExpander,\n        customBuildOptions: platformSpecificBuildOptions,\n        globalOutDir: packContext.outDir,\n        defaultSrc: appDir,\n      })\n      const fileMatcher = unpackPattern == null ? null : unpackPattern[0]\n      taskManager.addTask(_computeFileSets(mainMatchers)\n        .then(async fileSets => {\n          for (const fileSet of fileSets) {\n            await transformFiles(transformer, fileSet)\n          }\n\n          await new AsarPackager(appDir, resourcePath, asarOptions, fileMatcher == null ? null : fileMatcher.createFilter())\n            .pack(fileSets, this)\n        }))\n    }\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  protected signApp(packContext: AfterPackContext, isAsar: boolean): Promise<any> {\n    return Promise.resolve()\n  }\n\n  async getIconPath(): Promise<string | null> {\n    return null\n  }\n\n  private async computeAsarOptions(customBuildOptions: DC): Promise<AsarOptions | null> {\n    if (!isElectronBased(this.info.framework)) {\n      return null\n    }\n\n    function errorMessage(name: string) {\n      return `${name} is deprecated is deprecated and not supported — please use asarUnpack`\n    }\n\n    const buildMetadata = this.config as any\n    if (buildMetadata[\"asar-unpack\"] != null) {\n      throw new Error(errorMessage(\"asar-unpack\"))\n    }\n    if (buildMetadata[\"asar-unpack-dir\"] != null) {\n      throw new Error(errorMessage(\"asar-unpack-dir\"))\n    }\n\n    const platformSpecific = customBuildOptions.asar\n    const result = platformSpecific == null ? this.config.asar : platformSpecific\n    if (result === false) {\n      const appAsarStat = await statOrNull(path.join(this.info.appDir, \"app.asar\"))\n      //noinspection ES6MissingAwait\n      if (appAsarStat == null || !appAsarStat.isFile()) {\n        log.warn({\n          solution: \"enable asar and use asarUnpack to unpack files that must be externally available\",\n        }, \"asar usage is disabled — this is strongly not recommended\")\n      }\n      return null\n    }\n\n    if (result == null || result === true) {\n      return {}\n    }\n\n    for (const name of [\"unpackDir\", \"unpack\"]) {\n      if ((result as any)[name] != null) {\n        throw new Error(errorMessage(`asar.${name}`))\n      }\n    }\n    return deepAssign({}, result)\n  }\n\n  public getElectronSrcDir(dist: string): string {\n    return path.resolve(this.projectDir, dist)\n  }\n\n  public getElectronDestinationDir(appOutDir: string): string {\n    return appOutDir\n  }\n\n  getResourcesDir(appOutDir: string): string {\n    if (this.platform === Platform.MAC) {\n      return this.getMacOsResourcesDir(appOutDir)\n    }\n    else if (isElectronBased(this.info.framework)) {\n      return path.join(appOutDir, \"resources\")\n    }\n    else {\n      return appOutDir\n    }\n  }\n\n  public getMacOsResourcesDir(appOutDir: string): string {\n    return path.join(appOutDir, `${this.appInfo.productFilename}.app`, \"Contents\", \"Resources\")\n  }\n\n  private async checkFileInPackage(resourcesDir: string, file: string, messagePrefix: string, isAsar: boolean) {\n    const relativeFile = path.relative(this.info.appDir, path.resolve(this.info.appDir, file))\n    if (isAsar) {\n      await checkFileInArchive(path.join(resourcesDir, \"app.asar\"), relativeFile, messagePrefix)\n      return\n    }\n\n    const pathParsed = path.parse(file)\n    // Even when packaging to asar is disabled, it does not imply that the main file can not be inside an .asar archive.\n    // This may occur when the packaging is done manually before processing with electron-builder.\n    if (pathParsed.dir.includes(\".asar\")) {\n      // The path needs to be split to the part with an asar archive which acts like a directory and the part with\n      // the path to main file itself. (e.g. path/arch.asar/dir/index.js -> path/arch.asar, dir/index.js)\n      // noinspection TypeScriptValidateJSTypes\n      const pathSplit: Array<string> = pathParsed.dir.split(path.sep)\n      let partWithAsarIndex = 0\n      pathSplit.some((pathPart: string, index: number) => {\n        partWithAsarIndex = index\n        return pathPart.endsWith(\".asar\")\n      })\n      const asarPath = path.join(...pathSplit.slice(0, partWithAsarIndex + 1))\n      let mainPath = pathSplit.length > (partWithAsarIndex + 1) ? path.join.apply(pathSplit.slice(partWithAsarIndex + 1)) : \"\"\n      mainPath += path.join(mainPath, pathParsed.base)\n      await checkFileInArchive(path.join(resourcesDir, \"app\", asarPath), mainPath, messagePrefix)\n    }\n    else {\n      const fullPath = path.join(resourcesDir, \"app\", relativeFile)\n      const outStat = await statOrNull(fullPath)\n      if (outStat == null) {\n        throw new Error(`${messagePrefix} \"${fullPath}\" does not exist. Seems like a wrong configuration.`)\n      }\n      else {\n        //noinspection ES6MissingAwait\n        if (!outStat.isFile()) {\n          throw new Error(`${messagePrefix} \"${fullPath}\" is not a file. Seems like a wrong configuration.`)\n        }\n      }\n    }\n  }\n\n  private async sanityCheckPackage(appOutDir: string, isAsar: boolean, framework: Framework): Promise<any> {\n    const outStat = await statOrNull(appOutDir)\n    if (outStat == null) {\n      throw new Error(`Output directory \"${appOutDir}\" does not exist. Seems like a wrong configuration.`)\n    }\n    else {\n      //noinspection ES6MissingAwait\n      if (!outStat.isDirectory()) {\n        throw new Error(`Output directory \"${appOutDir}\" is not a directory. Seems like a wrong configuration.`)\n      }\n    }\n\n    const resourcesDir = this.getResourcesDir(appOutDir)\n    const mainFile = (framework.getMainFile == null ? null : framework.getMainFile(this.platform)) || this.info.metadata.main || \"index.js\"\n    await this.checkFileInPackage(resourcesDir, mainFile, \"Application entry file\", isAsar)\n    await this.checkFileInPackage(resourcesDir, \"package.json\", \"Application\", isAsar)\n  }\n\n  // tslint:disable-next-line:no-invalid-template-strings\n  computeSafeArtifactName(suggestedName: string | null, ext: string, arch?: Arch | null, skipArchIfX64 = true, safePattern: string = \"${name}-${version}-${arch}.${ext}\"): string | null {\n    return computeSafeArtifactNameIfNeeded(suggestedName, () => this.computeArtifactName(safePattern, ext, skipArchIfX64 && arch === Arch.x64 ? null : arch))\n  }\n\n  expandArtifactNamePattern(targetSpecificOptions: TargetSpecificOptions | null | undefined, ext: string, arch?: Arch | null, defaultPattern?: string, skipArchIfX64 = true): string {\n    let pattern = targetSpecificOptions == null ? null : targetSpecificOptions.artifactName\n    if (pattern == null) {\n      pattern = this.platformSpecificBuildOptions.artifactName || this.config.artifactName\n    }\n\n    if (pattern == null) {\n      // tslint:disable-next-line:no-invalid-template-strings\n      pattern = defaultPattern || \"${productName}-${version}-${arch}.${ext}\"\n    } else {\n      // https://github.com/electron-userland/electron-builder/issues/3510\n      // always respect arch in user custom artifact pattern\n      skipArchIfX64 = this.platform === Platform.MAC\n    }\n    return this.computeArtifactName(pattern, ext, skipArchIfX64 && arch === Arch.x64 ? null : arch)\n  }\n\n  expandArtifactBeautyNamePattern(targetSpecificOptions: TargetSpecificOptions | null | undefined, ext: string, arch?: Arch | null): string {\n    // tslint:disable-next-line:no-invalid-template-strings\n    return this.expandArtifactNamePattern(targetSpecificOptions, ext, arch, \"${productName} ${version} ${arch}.${ext}\", true)\n  }\n\n  private computeArtifactName(pattern: any, ext: string, arch: Arch | null | undefined): string {\n    const archName = arch == null ? null : getArtifactArchName(arch, ext)\n    return this.expandMacro(pattern, archName, {\n      ext\n    })\n  }\n\n  expandMacro(pattern: string, arch?: string | null, extra: any = {}, isProductNameSanitized = true): string {\n    return doExpandMacro(pattern, arch, this.appInfo, {os: this.platform.buildConfigurationKey, ...extra}, isProductNameSanitized)\n  }\n\n  generateName2(ext: string | null, classifier: string | null | undefined, deployment: boolean): string {\n    const dotExt = ext == null ? \"\" : `.${ext}`\n    const separator = ext === \"deb\" ? \"_\" : \"-\"\n    return `${deployment ? this.appInfo.name : this.appInfo.productFilename}${separator}${this.appInfo.version}${classifier == null ? \"\" : `${separator}${classifier}`}${dotExt}`\n  }\n\n  getTempFile(suffix: string): Promise<string> {\n    return this.info.tempDirManager.getTempFile({suffix})\n  }\n\n  get fileAssociations(): Array<FileAssociation> {\n    return asArray(this.config.fileAssociations).concat(asArray(this.platformSpecificBuildOptions.fileAssociations))\n  }\n\n  async getResource(custom: string | null | undefined, ...names: Array<string>): Promise<string | null> {\n    const resourcesDir = this.info.buildResourcesDir\n    if (custom === undefined) {\n      const resourceList = await this.resourceList\n      for (const name of names) {\n        if (resourceList.includes(name)) {\n          return path.join(resourcesDir, name)\n        }\n      }\n    }\n    else if (custom != null && !isEmptyOrSpaces(custom)) {\n      const resourceList = await this.resourceList\n      if (resourceList.includes(custom)) {\n        return path.join(resourcesDir, custom)\n      }\n\n      let p = path.resolve(resourcesDir, custom)\n      if (await statOrNull(p) == null) {\n        p = path.resolve(this.projectDir, custom)\n        if (await statOrNull(p) == null) {\n          throw new InvalidConfigurationError(`cannot find specified resource \"${custom}\", nor relative to \"${resourcesDir}\", neither relative to project dir (\"${this.projectDir}\")`)\n        }\n      }\n      return p\n    }\n    return null\n  }\n\n  get forceCodeSigning(): boolean {\n    const forceCodeSigningPlatform = this.platformSpecificBuildOptions.forceCodeSigning\n    return (forceCodeSigningPlatform == null ? this.config.forceCodeSigning : forceCodeSigningPlatform) || false\n  }\n\n  protected async getOrConvertIcon(format: IconFormat): Promise<string | null> {\n    const result = await this.resolveIcon(asArray(this.platformSpecificBuildOptions.icon || this.config.icon), [], format)\n    if (result.length === 0) {\n      const framework = this.info.framework\n      if (framework.getDefaultIcon != null) {\n        return framework.getDefaultIcon(this.platform)\n      }\n\n      log.warn({reason: \"application icon is not set\"}, `default ${capitalizeFirstLetter(framework.name)} icon is used`)\n      return this.getDefaultFrameworkIcon()\n    }\n    else {\n      return result[0].file\n    }\n  }\n\n  getDefaultFrameworkIcon(): string | null {\n    const framework = this.info.framework\n    return framework.getDefaultIcon == null ? null : framework.getDefaultIcon(this.platform)\n  }\n\n  // convert if need, validate size (it is a reason why tool is called even if file has target extension (already specified as foo.icns for example))\n  async resolveIcon(sources: Array<string>, fallbackSources: Array<string>, outputFormat: IconFormat): Promise<Array<IconInfo>> {\n    const args = [\n      \"icon\",\n      \"--format\", outputFormat,\n      \"--root\", this.buildResourcesDir,\n      \"--root\", this.projectDir,\n      \"--out\", path.resolve(this.projectDir, this.config.directories!!.output!!, `.icon-${outputFormat}`),\n    ]\n    for (const source of sources) {\n      args.push(\"--input\", source)\n    }\n    for (const source of fallbackSources) {\n      args.push(\"--fallback-input\", source)\n    }\n\n    const result: IconConvertResult = await executeAppBuilderAsJson(args)\n    const errorMessage = result.error\n    if (errorMessage != null) {\n      throw new InvalidConfigurationError(errorMessage, result.errorCode)\n    }\n\n    if (result.isFallback) {\n      log.warn({reason: \"application icon is not set\"}, `default ${capitalizeFirstLetter(this.info.framework.name)} icon is used`)\n    }\n\n    return result.icons || []\n  }\n}\n\nexport interface IconInfo {\n  file: string\n  size: number\n}\n\ninterface IconConvertResult {\n  icons?: Array<IconInfo>\n\n  error?: string\n  errorCode?: string\n  isFallback?: boolean\n}\n\nexport type IconFormat = \"icns\" | \"ico\" | \"set\"\n\nexport function isSafeGithubName(name: string) {\n  return /^[0-9A-Za-z._-]+$/.test(name)\n}\n\nexport function computeSafeArtifactNameIfNeeded(suggestedName: string | null, safeNameProducer: () => string): string | null {\n  // GitHub only allows the listed characters in file names.\n  if (suggestedName != null) {\n    if (isSafeGithubName(suggestedName)) {\n      return null\n    }\n\n    // prefer to use suggested name - so, if space is the only problem, just replace only space to dash\n    suggestedName = suggestedName.replace(/ /g, \"-\")\n    if (isSafeGithubName(suggestedName)) {\n      return suggestedName\n    }\n  }\n\n  return safeNameProducer()\n}\n\n// remove leading dot\nexport function normalizeExt(ext: string) {\n  return ext.startsWith(\".\") ? ext.substring(1) : ext\n}\n\nexport function resolveFunction<T>(executor: T | string, name: string): T {\n  if (executor == null || typeof executor !== \"string\") {\n    return executor\n  }\n\n  let p = executor as string\n  if (p.startsWith(\".\")) {\n    p = path.resolve(p)\n  }\n\n  try {\n    p = require.resolve(p)\n  }\n  catch (e) {\n    debug(e)\n    p = path.resolve(p)\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-var-requires\n  const m = require(p)\n  const namedExport = m[name]\n  if (namedExport == null) {\n    return m.default || m\n  }\n  else {\n    return namedExport\n  }\n}\n\nexport function chooseNotNull(v1: string | null | undefined, v2: string | null | undefined): string | null | undefined {\n  return v1 == null ? v2 : v1\n}\n\nfunction capitalizeFirstLetter(text: string) {\n  return text.charAt(0).toUpperCase() + text.slice(1)\n}\n\nexport function isSafeToUnpackElectronOnRemoteBuildServer(packager: PlatformPackager<any>) {\n  if (packager.platform !== Platform.LINUX || packager.config.remoteBuild === false) {\n    return false\n  }\n\n  if (process.platform === \"win32\" || isEnvTrue(process.env._REMOTE_BUILD)) {\n    return packager.config.electronDist == null && packager.config.electronDownload == null\n  }\n  return false\n}\n"], "sourceRoot": ""}