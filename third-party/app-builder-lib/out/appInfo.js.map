{"version": 3, "sources": ["../src/appInfo.ts"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAGA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AAEA;AACA;AACM,SAAU,OAAV,CAAkB,CAAlB,EAA2B;AAC/B;AACA,EAAA,CAAC,GAAG,CAAC,CAAC,OAAF,CAAU,sBAAV,EAAkC,UAAlC,CAAJ,CAF+B,CAG/B;;AACA,EAAA,CAAC,GAAG,CAAC,CAAC,OAAF,CAAU,IAAV,EAAgB,QAAhB,CAAJ,CAJ+B,CAK/B;;AACA,EAAA,CAAC,GAAG,CAAC,CAAC,OAAF,CAAU,4BAAV,EAAwC,UAAxC,CAAJ,CAN+B,CAO/B;;AACA,EAAA,CAAC,GAAG,CAAC,CAAC,OAAF,CAAU,IAAV,EAAgB,QAAhB,CAAJ;AACA,SAAO,CAAP;AACD;;AAEK,MAAO,OAAP,CAAc;AAYlB,EAAA,WAAA,CAA6B,IAA7B,EAA6C,YAA7C,EAAuG,uBAAA,GAA+D,IAAtK,EAA0K;AAA7I,SAAA,IAAA,GAAA,IAAA;AAA0E,SAAA,uBAAA,GAAA,uBAAA;AAX9F,SAAA,WAAA,GAAc,OAAO,CAAC,KAAK,IAAL,CAAU,QAAV,CAAmB,WAAnB,IAAkC,EAAnC,CAArB;AAYP,SAAK,OAAL,GAAe,IAAI,CAAC,QAAL,CAAc,OAA7B;;AAEA,QAAI,YAAY,IAAI,IAApB,EAA0B;AACxB,MAAA,YAAY,GAAG,IAAI,CAAC,MAAL,CAAY,YAA3B;AACD;;AAED,SAAK,WAAL,GAAmB,OAAO,CAAC,GAAR,CAAY,YAAZ,IAA4B,OAAO,CAAC,GAAR,CAAY,mBAAxC,IAA+D,OAAO,CAAC,GAAR,CAAY,qBAA3E,IAAoG,OAAO,CAAC,GAAR,CAAY,gBAAhH,IAAoI,OAAO,CAAC,GAAR,CAAY,iBAAhJ,IAAqK,OAAO,CAAC,GAAR,CAAY,eAApM;;AACA,QAAI,YAAY,IAAI,IAApB,EAA0B;AACxB,MAAA,YAAY,GAAG,KAAK,OAApB;;AACA,UAAI,CAAC,oCAAgB,KAAK,WAArB,CAAL,EAAwC;AACtC,QAAA,YAAY,IAAI,IAAI,KAAK,WAAW,EAApC;AACD;AACF;;AACD,SAAK,YAAL,GAAoB,YAApB;;AAEA,QAAI,IAAI,CAAC,QAAL,CAAc,YAAlB,EAAgC;AAC9B,WAAK,YAAL,GAAoB,IAAI,CAAC,QAAL,CAAc,YAAlC;AACD;;AACD,QAAI,IAAI,CAAC,QAAL,CAAc,mBAAlB,EAAuC;AACrC,WAAK,mBAAL,GAA2B,IAAI,CAAC,QAAL,CAAc,mBAAzC;AACD;;AAED,SAAK,WAAL,GAAmB,IAAI,CAAC,MAAL,CAAY,WAAZ,IAA2B,IAAI,CAAC,QAAL,CAAc,WAAzC,IAAwD,IAAI,CAAC,QAAL,CAAc,IAAzF;AACA,SAAK,eAAL,GAAuB,iCAAiB,KAAK,WAAtB,CAAvB;AACD;;AAED,MAAI,OAAJ,GAAW;AACT,UAAM,cAAc,GAAG,0BAAW,KAAK,OAAhB,CAAvB;;AACA,QAAI,cAAc,IAAI,IAAlB,IAA0B,cAAc,CAAC,MAAf,GAAwB,CAAtD,EAAyD;AACvD,aAAO,cAAc,CAAC,CAAD,CAArB;AACD;;AACD,WAAO,IAAP;AACD;;AAED,EAAA,4BAA4B,CAAC,gBAAgB,GAAG,IAApB,EAAwB;AAClD,UAAM,aAAa,GAAG,KAAI,gBAAJ,EAAW,KAAK,OAAhB,CAAtB,CADkD,CAElD;;AACA,QAAI,WAAW,GAAG,gBAAgB,GAAG,KAAK,WAAR,GAAsB,IAAxD;;AACA,QAAI,WAAW,IAAI,IAAf,IAAuB,CAAC,QAAQ,IAAR,CAAa,WAAb,CAA5B,EAAuD;AACrD,MAAA,WAAW,GAAG,GAAd;AACD;;AACD,WAAO,GAAG,aAAa,CAAC,KAAK,IAAI,aAAa,CAAC,KAAK,IAAI,aAAa,CAAC,KAAK,IAAI,WAAW,EAA1F;AACD;;AAED,MAAY,kBAAZ,GAA8B;AAC5B,WAAO,KAAK,IAAL,CAAU,WAAV,IAAyB,EAAhC;AACD;;AAED,MAAI,WAAJ,GAAe;AACb,UAAM,MAAM,GAAG,KAAK,IAAL,CAAU,QAAV,CAAmB,MAAnB,IAA6B,KAAK,kBAAL,CAAwB,MAApE;AACA,WAAO,MAAM,IAAI,IAAV,GAAiB,IAAjB,GAAwB,MAAM,CAAC,IAAtC;AACD;;AAED,MAAI,EAAJ,GAAM;AACJ,QAAI,KAAK,GAA8B,IAAvC;;AACA,SAAK,MAAM,OAAX,IAAsB,CAAC,KAAK,uBAAN,EAA+B,KAAK,IAAL,CAAU,MAAzC,CAAtB,EAAwE;AACtE,UAAI,OAAO,IAAI,IAAX,IAAmB,KAAK,IAAI,IAAhC,EAAsC;AACpC,QAAA,KAAK,GAAG,OAAO,CAAC,KAAhB;AACD;AACF;;AAED,UAAM,oBAAoB,GAAG,MAAK;AAChC,YAAM,IAAI,GAAG,KAAK,IAAlB;AACA,aAAO,GAAG,IAAI,CAAC,SAAL,CAAe,kBAAkB,GAAG,IAAI,CAAC,QAAL,CAAc,IAAd,CAAoB,WAApB,EAAiC,EAA/E;AACD,KAHD;;AAKA,QAAI,KAAK,IAAI,IAAT,KAAkB,KAAK,KAAK,SAAV,IAAuB,oCAAgB,KAAhB,CAAzC,CAAJ,EAAsE;AACpE,YAAM,cAAc,GAAG,KAAvB;AACA,MAAA,KAAK,GAAG,oBAAoB,EAA5B;;AACA,yBAAI,IAAJ,CAAS,eAAe,cAAc,gBAAgB,KAAK,wBAA3D;AACD;;AAED,WAAO,KAAK,IAAI,IAAT,GAAgB,oBAAoB,EAApC,GAAyC,KAAhD;AACD;;AAED,MAAI,mBAAJ,GAAuB;AACrB,WAAO,wBAAwB,CAAC,KAAK,EAAN,CAA/B;AACD;;AAED,MAAI,IAAJ,GAAQ;AACN,WAAO,KAAK,IAAL,CAAU,QAAV,CAAmB,IAA1B;AACD;;AAED,MAAI,gBAAJ,GAAoB;AAClB,UAAM,IAAI,GAAG,KAAK,IAAlB,CADkB,CAElB;;AACA,WAAO,IAAI,CAAC,UAAL,CAAgB,GAAhB,IAAuB,KAAK,eAA5B,GAA8C,IAArD;AACD;;AAED,MAAI,aAAJ,GAAiB;AACf,WAAO,iCAAiB,KAAK,IAAtB,CAAP;AACD;;AAED,MAAI,mBAAJ,GAAuB;AACrB,WAAO,KAAK,aAAL,CAAmB,WAAnB,KAAmC,UAA1C;AACD;;AAED,MAAI,SAAJ,GAAa;AACX,UAAM,SAAS,GAAG,KAAK,IAAL,CAAU,MAAV,CAAiB,SAAnC;;AACA,QAAI,SAAS,IAAI,IAAjB,EAAuB;AACrB,aAAO,kCAAY,SAAZ,EAAuB,IAAvB,EAA6B,IAA7B,CAAP;AACD;;AACD,WAAO,eAAe,IAAI,IAAJ,GAAW,WAAX,EAAwB,IAAI,KAAK,WAAL,IAAoB,KAAK,WAAW,EAAtF;AACD;;AAED,QAAM,iBAAN,GAAuB;AACrB,UAAM,GAAG,GAAG,KAAK,IAAL,CAAU,QAAV,CAAmB,QAAnB,IAA+B,KAAK,kBAAL,CAAwB,QAAnE;;AACA,QAAI,GAAG,IAAI,IAAX,EAAiB;AACf,aAAO,GAAP;AACD;;AAED,UAAM,IAAI,GAAG,MAAM,KAAK,IAAL,CAAU,cAA7B;AACA,WAAO,IAAI,IAAI,IAAR,IAAgB,IAAI,CAAC,IAAL,KAAc,QAA9B,GAAyC,IAAzC,GAAgD,WAAW,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,EAA1G;AACD;;AA9HiB;AAiIpB;;;;;AACM,SAAU,wBAAV,CAAmC,UAAnC,EAAqD;AACzD;AACA;AACA,SAAO,UAAU,CAAC,OAAX,CAAmB,IAAnB,EAAyB,GAAzB,EAA8B,OAA9B,CAAsC,iBAAtC,EAAyD,EAAzD,CAAP;AACD,C", "sourcesContent": ["import { isEmptyOrSpaces, log } from \"builder-util\"\nimport sanitizeFileName from \"sanitize-filename\"\nimport { prerelease, SemVer } from \"semver\"\nimport { PlatformSpecificBuildOptions } from \"./options/PlatformSpecificBuildOptions\"\nimport { Packager } from \"./packager\"\nimport { expandMacro } from \"./util/macroExpander\"\n\n// fpm bug - rpm build --description is not escaped, well... decided to replace quite to smart quote\n// http://leancrew.com/all-this/2010/11/smart-quotes-in-javascript/\nexport function smarten(s: string): string {\n  // opening singles\n  s = s.replace(/(^|[-\\u2014\\s([\"])'/g, \"$1\\u2018\")\n  // closing singles & apostrophes\n  s = s.replace(/'/g, \"\\u2019\")\n  // opening doubles\n  s = s.replace(/(^|[-\\u2014/[(\\u2018\\s])\"/g, \"$1\\u201c\")\n  // closing doubles\n  s = s.replace(/\"/g, \"\\u201d\")\n  return s\n}\n\nexport class AppInfo {\n  readonly description = smarten(this.info.metadata.description || \"\")\n  readonly version: string\n  readonly shortVersion: string | undefined\n  readonly shortVersionWindows: string | undefined\n\n  readonly buildNumber: string | undefined\n  readonly buildVersion: string\n\n  readonly productName: string\n  readonly productFilename: string\n\n  constructor(private readonly info: Packager, buildVersion: string | null | undefined, private readonly platformSpecificOptions: PlatformSpecificBuildOptions | null = null) {\n    this.version = info.metadata.version!!\n\n    if (buildVersion == null) {\n      buildVersion = info.config.buildVersion\n    }\n\n    this.buildNumber = process.env.BUILD_NUMBER || process.env.TRAVIS_BUILD_NUMBER || process.env.APPVEYOR_BUILD_NUMBER || process.env.CIRCLE_BUILD_NUM || process.env.BUILD_BUILDNUMBER || process.env.CI_PIPELINE_IID\n    if (buildVersion == null) {\n      buildVersion = this.version\n      if (!isEmptyOrSpaces(this.buildNumber)) {\n        buildVersion += `.${this.buildNumber}`\n      }\n    }\n    this.buildVersion = buildVersion\n\n    if (info.metadata.shortVersion) {\n      this.shortVersion = info.metadata.shortVersion\n    }\n    if (info.metadata.shortVersionWindows) {\n      this.shortVersionWindows = info.metadata.shortVersionWindows\n    }\n\n    this.productName = info.config.productName || info.metadata.productName || info.metadata.name!!\n    this.productFilename = sanitizeFileName(this.productName)\n  }\n\n  get channel(): string | null {\n    const prereleaseInfo = prerelease(this.version)\n    if (prereleaseInfo != null && prereleaseInfo.length > 0) {\n      return prereleaseInfo[0]\n    }\n    return null\n  }\n\n  getVersionInWeirdWindowsForm(isSetBuildNumber = true): string {\n    const parsedVersion = new SemVer(this.version)\n    // https://github.com/electron-userland/electron-builder/issues/2635#issuecomment-*********\n    let buildNumber = isSetBuildNumber ? this.buildNumber : null\n    if (buildNumber == null || !/^\\d+$/.test(buildNumber)) {\n      buildNumber = \"0\"\n    }\n    return `${parsedVersion.major}.${parsedVersion.minor}.${parsedVersion.patch}.${buildNumber}`\n  }\n\n  private get notNullDevMetadata() {\n    return this.info.devMetadata || {}\n  }\n\n  get companyName(): string | null {\n    const author = this.info.metadata.author || this.notNullDevMetadata.author\n    return author == null ? null : author.name\n  }\n\n  get id(): string {\n    let appId: string | null | undefined = null\n    for (const options of [this.platformSpecificOptions, this.info.config]) {\n      if (options != null && appId == null) {\n        appId = options.appId\n      }\n    }\n\n    const generateDefaultAppId = () => {\n      const info = this.info\n      return `${info.framework.defaultAppIdPrefix}${info.metadata.name!.toLowerCase()}`\n    }\n\n    if (appId != null && (appId === \"your.id\" || isEmptyOrSpaces(appId))) {\n      const incorrectAppId = appId\n      appId = generateDefaultAppId()\n      log.warn(`do not use \"${incorrectAppId}\" as appId, \"${appId}\" will be used instead`)\n    }\n\n    return appId == null ? generateDefaultAppId() : appId\n  }\n\n  get macBundleIdentifier(): string {\n    return filterCFBundleIdentifier(this.id)\n  }\n\n  get name(): string {\n    return this.info.metadata.name!!\n  }\n\n  get linuxPackageName(): string {\n    const name = this.name\n    // https://github.com/electron-userland/electron-builder/issues/2963\n    return name.startsWith(\"@\") ? this.productFilename : name\n  }\n\n  get sanitizedName(): string {\n    return sanitizeFileName(this.name)\n  }\n\n  get updaterCacheDirName(): string {\n    return this.sanitizedName.toLowerCase() + \"-updater\"\n  }\n\n  get copyright(): string {\n    const copyright = this.info.config.copyright\n    if (copyright != null) {\n      return expandMacro(copyright, null, this)\n    }\n    return `Copyright © ${new Date().getFullYear()} ${this.companyName || this.productName}`\n  }\n\n  async computePackageUrl(): Promise<string | null> {\n    const url = this.info.metadata.homepage || this.notNullDevMetadata.homepage\n    if (url != null) {\n      return url\n    }\n\n    const info = await this.info.repositoryInfo\n    return info == null || info.type !== \"github\" ? null : `https://${info.domain}/${info.user}/${info.project}`\n  }\n}\n\n/** @internal */\nexport function filterCFBundleIdentifier(identifier: string) {\n  // Remove special characters and allow only alphanumeric (A-Z,a-z,0-9), hyphen (-), and period (.)\n  // Apple documentation: https://developer.apple.com/library/mac/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html#//apple_ref/doc/uid/20001431-102070\n  return identifier.replace(/ /g, \"-\").replace(/[^a-zA-Z0-9.-]/g, \"\")\n}\n"], "sourceRoot": ""}