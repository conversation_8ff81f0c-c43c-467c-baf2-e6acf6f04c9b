{"version": 3, "sources": ["../src/packager.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAIA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;;;AAGA,SAAS,UAAT,CAAoB,OAApB,EAA2C,KAA3C,EAA0D,OAA1D,EAAgG;AAC9F,EAAA,OAAO,CAAC,EAAR,CAAW,KAAX,EAAkB,OAAlB;AACD;;AAID,eAAe,mBAAf,CAAmC,aAAnC,EAAiE,QAAjE,EAAmF;AACjF,MAAI,SAAS,GAAG,aAAa,CAAC,SAA9B;;AACA,MAAI,SAAS,IAAI,IAAjB,EAAuB;AACrB,IAAA,SAAS,GAAG,SAAS,CAAC,WAAV,EAAZ;AACD;;AAED,MAAI,WAAW,GAAG,aAAa,CAAC,WAAhC;;AACA,MAAI,SAAS,KAAK,UAAd,IAA4B,SAAS,IAAI,IAA7C,EAAmD;AACjD,WAAO,MAAM,yDAA+B,aAA/B,EAA8C,QAA9C,CAAb;AACD;;AAED,MAAI,WAAW,IAAI,IAAf,IAAuB,WAAW,KAAK,SAA3C,EAAsD;AACpD,IAAA,WAAW,GAAG,OAAO,CAAC,QAAR,CAAiB,IAA/B;AACD;;AAED,QAAM,aAAa,GAAG,GAAG,QAAQ,CAAC,OAAT,CAAiB,eAAe,MAAzD;AACA,QAAM,aAAa,GAAG,aAAa,CAAC,eAAd,KAAkC,KAAxD;;AACA,MAAI,SAAS,KAAK,QAAd,IAA0B,SAAS,KAAK,eAA5C,EAA6D;AAC3D,WAAO,KAAI,kCAAJ,EAAoB,WAApB,EAAiC,aAAjC,EAAgD,aAAhD,CAAP;AACD,GAFD,MAGK,IAAI,SAAS,KAAK,OAAlB,EAA2B;AAC9B,WAAO,KAAI,gCAAJ,EAAmB,WAAnB,EAAgC,aAAhC,EAA+C,aAA/C,CAAP;AACD,GAFI,MAGA;AACH,UAAM,KAAI,wCAAJ,EAA8B,sBAAsB,SAAS,EAA7D,CAAN;AACD;AACF;;AAEK,MAAO,QAAP,CAAe;AA6GnB;AACA,EAAA,WAAA,CAAY,OAAZ,EAA+C,iBAAA,GAAoB,KAAI,uCAAJ,GAAnE,EAA0F;AAA3C,SAAA,iBAAA,GAAA,iBAAA;AAtGvC,SAAA,SAAA,GAA6B,IAA7B;AAKA,SAAA,6BAAA,GAAyC,KAAzC;AAMA,SAAA,mBAAA,GAA+B,KAA/B;AAMA,SAAA,YAAA,GAAgC,IAAhC;AAKA,SAAA,cAAA,GAAuC,IAAvC;AAMR,SAAA,iCAAA,GAAoC,KAApC;AAES,SAAA,YAAA,GAAe,KAAI,sBAAJ,GAAf;AAET,SAAA,QAAA,GAA2B,IAA3B;AAKS,SAAA,cAAA,GAAiB,KAAI,qBAAJ,EAAW,UAAX,CAAjB;AAED,SAAA,eAAA,GAAkB,KAAI,eAAJ,EAAsC,MAAM,yCAAkB,KAAK,UAAvB,EAAmC,KAAK,QAAxC,EAAkD,KAAK,WAAvD,CAA5C,CAAlB;AAES,SAAA,iBAAA,GAA+E,EAA/E;AAIR,SAAA,WAAA,GAAc,KAAI,0BAAJ,EAAgB,mBAAI,cAApB,CAAd;AAMD,SAAA,kBAAA,GAAqB,IAAI,GAAJ,EAArB;;AAoBR,SAAA,sBAAA,GAAkG,CAAC,MAAD,EAAS,QAAT,EAAmB,IAAnB,KAA2B;AAC3H,aAAO,IAAI,CAAC,IAAL,CAAU,MAAM,CAAC,MAAjB,EAAyB,KAAK,MAAM,CAAC,IAAI,IAAI,iCAAoB,IAApB,EAA0B,MAAM,CAAC,IAAjC,CAAsC,EAAnF,CAAP;AACD,KAFD;;AAIQ,SAAA,kBAAA,GAAoC,IAApC;AAeA,SAAA,UAAA,GAA+B,IAA/B;AAKS,SAAA,SAAA,GAAwC,EAAxC;;AAQf,QAAI,iBAAiB,OAArB,EAA8B;AAC5B,YAAM,KAAI,wCAAJ,EAA8B,qEAA9B,CAAN;AACD;;AACD,QAAI,mBAAmB,OAAvB,EAAgC;AAC9B,YAAM,KAAI,wCAAJ,EAA8B,qFAA9B,CAAN;AACD;;AAED,UAAM,OAAO,GAAG,OAAO,CAAC,OAAR,IAAmB,IAAI,GAAJ,EAAnC;;AACA,QAAI,OAAO,CAAC,OAAR,IAAmB,IAAvB,EAA6B;AAC3B,MAAA,OAAO,CAAC,OAAR,GAAkB,OAAlB;AACD;;AAED,aAAS,cAAT,CAAwB,QAAxB,EAA4C,KAA5C,EAAgE;AAC9D,eAAS,UAAT,CAAoB,qBAApB,EAAkD;AAChD,cAAM,MAAM,GAAG,KAAK,EAApB;AACA,eAAO,MAAM,CAAC,MAAP,KAAkB,CAAlB,IAAuB,qBAAvB,GAA+C,CAAC,mCAAe,OAAO,CAAC,IAAvB,CAAD,CAA/C,GAAgF,MAAvF;AACD;;AAED,UAAI,UAAU,GAAG,OAAO,CAAC,GAAR,CAAY,QAAZ,CAAjB;;AACA,UAAI,UAAU,IAAI,IAAlB,EAAwB;AACtB,QAAA,UAAU,GAAG,IAAI,GAAJ,EAAb;AACA,QAAA,OAAO,CAAC,GAAR,CAAY,QAAZ,EAAsB,UAAtB;AACD;;AAED,UAAI,KAAK,CAAC,MAAN,KAAiB,CAArB,EAAwB;AACtB,aAAK,MAAM,IAAX,IAAmB,UAAU,CAAC,KAAD,CAA7B,EAAsC;AACpC,UAAA,UAAU,CAAC,GAAX,CAAe,IAAf,EAAqB,EAArB;AACD;;AACD;AACD;;AAED,WAAK,MAAM,IAAX,IAAmB,KAAnB,EAA0B;AACxB,cAAM,SAAS,GAAG,IAAI,CAAC,WAAL,CAAiB,GAAjB,CAAlB;;AACA,YAAI,SAAS,GAAG,CAAhB,EAAmB;AACjB,uCAAS,UAAT,EAAqB,mCAAe,IAAI,CAAC,SAAL,CAAe,SAAS,GAAG,CAA3B,CAAf,CAArB,EAAoE,IAAI,CAAC,SAAL,CAAe,CAAf,EAAkB,SAAlB,CAApE;AACD,SAFD,MAGK;AACH,eAAK,MAAM,IAAX,IAAmB,UAAU,CAAC,IAAD,CAA7B,EAAqC;AACnC,yCAAS,UAAT,EAAqB,IAArB,EAA2B,IAA3B;AACD;AACF;AACF;AACF;;AAED,QAAI,OAAO,CAAC,GAAR,IAAe,IAAnB,EAAyB;AACvB,MAAA,cAAc,CAAC,kBAAS,GAAV,EAAe,OAAO,CAAC,GAAvB,CAAd;AACD;;AACD,QAAI,OAAO,CAAC,KAAR,IAAiB,IAArB,EAA2B;AACzB,MAAA,cAAc,CAAC,kBAAS,KAAV,EAAiB,OAAO,CAAC,KAAzB,CAAd;AACD;;AACD,QAAI,OAAO,CAAC,GAAR,IAAe,IAAnB,EAAyB;AACvB,MAAA,cAAc,CAAC,kBAAS,OAAV,EAAmB,OAAO,CAAC,GAA3B,CAAd;AACD;;AAED,SAAK,UAAL,GAAkB,OAAO,CAAC,UAAR,IAAsB,IAAtB,GAA6B,OAAO,CAAC,GAAR,EAA7B,GAA6C,IAAI,CAAC,OAAL,CAAa,OAAO,CAAC,UAArB,CAA/D;AACA,SAAK,OAAL,GAAe,KAAK,UAApB;AACA,SAAK,OAAL,GAAe,EACb,GAAG,OADU;AAEb,MAAA,WAAW,EAAE,OAAO,CAAC,WAAR,IAAuB,IAAvB,GAA8B,IAA9B,GAAqC,IAAI,CAAC,OAAL,CAAa,KAAK,UAAlB,EAA8B,OAAO,CAAC,WAAtC;AAFrC,KAAf;;AAKA,QAAI;AACF,yBAAI,IAAJ,CAAS;AAAC,QAAA,OAAO,WAAR;AAA2B,QAAA,EAAE,EAAE,OAAO,CAAC,IAAD,CAAP,CAAc,OAAd;AAA/B,OAAT,EAAkE,kBAAlE;AACD,KAFD,CAGA,OAAO,CAAP,EAAU;AACR;AACA,UAAI,EAAE,CAAC,YAAY,cAAf,CAAJ,EAAoC;AAClC,cAAM,CAAN;AACD;AACF;AACF;;AAjLD,MAAI,MAAJ,GAAU;AACR,WAAO,KAAK,OAAZ;AACD;;AAGD,MAAI,QAAJ,GAAY;AACV,WAAO,KAAK,SAAZ;AACD;;AAID,MAAI,+BAAJ,GAAmC;AACjC,WAAO,KAAK,6BAAZ;AACD;;AAID,MAAI,kBAAJ,GAAsB;AACpB,WAAO,KAAK,mBAAZ;AACD;;AAGD,MAAI,WAAJ,GAAe;AACb,WAAO,KAAK,YAAZ;AACD;;AAID,MAAI,MAAJ,GAAU;AACR,WAAO,KAAK,cAAZ;AACD;;AAOD,MAAI,OAAJ,GAAW;AACT,WAAO,KAAK,QAAZ;AACD;;AAYD,MAAI,cAAJ,GAAkB;AAChB,WAAO,KAAK,eAAL,CAAqB,KAA5B;AACD;;AAID,EAAA,qBAAqB,CAAC,QAAD,EAA0B;AAC7C,QAAI,GAAG,GAAG,EAAV;AACA,QAAI,oBAAoB,GAAyB,IAAjD;;AACA,QAAI,QAAQ,IAAI,IAAZ,IAAoB,KAAK,SAAL,CAAe,uBAAf,IAA0C,IAAlE,EAAwE;AACtE,MAAA,oBAAoB,GAAG,KAAK,SAAL,CAAe,uBAAf,CAAuC,QAAvC,CAAvB;;AACA,UAAI,oBAAoB,IAAI,IAA5B,EAAkC;AAChC,QAAA,GAAG,IAAI,IAAI,QAAQ,CAAC,IAAI,EAAxB;AACD;AACF;;AAED,QAAI,MAAM,GAAG,KAAK,kBAAL,CAAwB,GAAxB,CAA4B,GAA5B,CAAb;;AACA,QAAI,MAAM,IAAI,IAAd,EAAoB;AAClB,MAAA,MAAM,GAAG,qDAAyB,KAAK,MAA9B,EAAsC,oBAAtC,CAAT;AACA,WAAK,kBAAL,CAAwB,GAAxB,CAA4B,GAA5B,EAAiC,MAAjC;AACD;;AACD,WAAO,MAAP;AACD;;AAQD,MAAI,iBAAJ,GAAqB;AACnB,QAAI,MAAM,GAAG,KAAK,kBAAlB;;AACA,QAAI,MAAM,IAAI,IAAd,EAAoB;AAClB,MAAA,MAAM,GAAG,IAAI,CAAC,OAAL,CAAa,KAAK,UAAlB,EAA8B,KAAK,6BAAnC,CAAT;AACA,WAAK,kBAAL,GAA0B,MAA1B;AACD;;AACD,WAAO,MAAP;AACD;;AAED,MAAI,6BAAJ,GAAiC;AAC/B,WAAO,KAAK,MAAL,CAAY,WAAZ,CAA0B,cAAjC;AACD;;AAGD,MAAI,SAAJ,GAAa;AACX,WAAO,KAAK,UAAZ;AACD;;AAID,EAAA,oBAAoB,CAAC,QAAD,EAA8B;AAChD,SAAK,SAAL,CAAe,IAAf,CAAoB,QAApB;AACD;;AA4ED,EAAA,mBAAmB,CAAC,OAAD,EAA4D;AAC7E,SAAK,iBAAL,CAAuB,IAAvB,CAA4B,OAA5B;AACD;;AAED,EAAA,eAAe,CAAC,OAAD,EAA0C;AACvD,IAAA,UAAU,CAAC,KAAK,YAAN,EAAoB,iBAApB,EAAuC,OAAvC,CAAV;AACA,WAAO,IAAP;AACD;;AAED,QAAM,wBAAN,CAA+B,KAA/B,EAA4D,SAA5D,EAA2E;AACzE,uBAAI,IAAJ,CAAS,SAAS,IAAI;AACpB,MAAA,MAAM,EAAE,KAAK,CAAC,qBADM;AAEpB,MAAA,IAAI,EAAE,KAAK,CAAC,IAAN,IAAc,IAAd,GAAqB,IAArB,GAA4B,oBAAK,KAAK,CAAC,IAAX,CAFd;AAGpB,MAAA,IAAI,EAAE,mBAAI,QAAJ,CAAa,KAAK,CAAC,IAAnB;AAHc,KAAtB,EAIG,UAJH;;AAKA,UAAM,OAAO,GAAG,yCAAgB,KAAK,MAAL,CAAY,oBAA5B,EAAkD,sBAAlD,CAAhB;;AACA,QAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,YAAM,OAAO,CAAC,OAAR,CAAgB,OAAO,CAAC,KAAD,CAAvB,CAAN;AACD;AACF;AAED;;;;;AAGA,EAAA,uBAAuB,CAAC,KAAD,EAAuB;AAC5C,SAAK,YAAL,CAAkB,IAAlB,CAAuB,iBAAvB,EAA0C,KAA1C;AACD;;AAED,QAAM,0BAAN,CAAiC,KAAjC,EAAuD;AACrD,SAAK,uBAAL,CAA6B,KAA7B;AAEA,UAAM,OAAO,GAAG,yCAAgB,KAAK,MAAL,CAAY,sBAA5B,EAAoD,wBAApD,CAAhB;;AACA,QAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,YAAM,OAAO,CAAC,OAAR,CAAgB,OAAO,CAAC,KAAD,CAAvB,CAAN;AACD;AACF;;AAED,QAAM,uBAAN,CAA8B,IAA9B,EAA0C;AACxC,UAAM,OAAO,GAAG,yCAAgB,KAAK,MAAL,CAAY,mBAA5B,EAAiD,qBAAjD,CAAhB;;AACA,QAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,YAAM,OAAO,CAAC,OAAR,CAAgB,OAAO,CAAC,IAAD,CAAvB,CAAN;AACD;AACF;;AAED,QAAM,KAAN,GAAW;AACT,QAAI,UAAU,GAAkB,IAAhC;AACA,QAAI,iBAAiB,GAAG,KAAK,OAAL,CAAa,MAArC;;AACA,QAAI,OAAO,iBAAP,KAA6B,QAAjC,EAA2C;AACzC;AACA,MAAA,UAAU,GAAG,iBAAb;AACA,MAAA,iBAAiB,GAAG,IAApB;AACD,KAJD,MAKK,IAAI,iBAAiB,IAAI,IAArB,IAA6B,iBAAiB,CAAC,OAAlB,IAA6B,IAA1D,IAAkE,iBAAiB,CAAC,OAAlB,CAA0B,QAA1B,CAAmC,GAAnC,CAAtE,EAA+G;AAClH,MAAA,UAAU,GAAG,iBAAiB,CAAC,OAA/B;AACA,aAAO,iBAAiB,CAAC,OAAzB;AACD;;AAED,UAAM,UAAU,GAAG,KAAK,UAAxB;AAEA,UAAM,cAAc,GAAG,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,cAAtB,CAAvB;AACA,SAAK,YAAL,GAAoB,MAAM,qCAAqB,wCAAgB,cAAhB,CAArB,CAA1B;AAEA,UAAM,WAAW,GAAG,KAAK,WAAzB;AACA,UAAM,aAAa,GAAG,MAAM,yBAAU,UAAV,EAAsB,UAAtB,EAAkC,iBAAlC,EAAqD,KAAI,eAAJ,EAAS,MAAM,OAAO,CAAC,OAAR,CAAgB,WAAhB,CAAf,CAArD,CAA5B;;AACA,QAAI,mBAAI,cAAR,EAAwB;AACtB,yBAAI,KAAJ,CAAU;AAAC,QAAA,MAAM,EAAE,sBAAsB,CAAC,aAAD;AAA/B,OAAV,EAA2D,kBAA3D;AACD;;AAED,SAAK,OAAL,GAAe,MAAM,0CAA2B,UAA3B,EAAuC,aAAa,CAAC,WAAd,CAA4B,GAAnE,CAArB;AACA,SAAK,iCAAL,GAAyC,KAAK,OAAL,KAAiB,UAA1D;AAEA,UAAM,cAAc,GAAG,KAAK,iCAAL,GAAyC,IAAI,CAAC,IAAL,CAAU,KAAK,MAAf,EAAuB,cAAvB,CAAzC,GAAkF,cAAzG,CA3BS,CA6BT;;AACA,QAAI,KAAK,WAAL,IAAoB,IAApB,IAA4B,CAAC,KAAK,iCAAtC,EAAyE;AACvE,WAAK,SAAL,GAAiB,KAAK,WAAtB;AACD,KAFD,MAGK;AACH,WAAK,SAAL,GAAiB,MAAM,KAAK,mDAAL,CAAyD,cAAzD,CAAvB;AACD;;AACD,mCAAW,KAAK,QAAhB,EAA0B,aAAa,CAAC,aAAxC;;AAEA,QAAI,KAAK,iCAAT,EAA4C;AAC1C,yBAAI,KAAJ,CAAU;AAAC,QAAA,cAAD;AAAiB,QAAA;AAAjB,OAAV,EAA4C,oCAA5C;AACD;;AACD,0CAAc,KAAK,QAAnB,EAA6B,KAAK,WAAlC,EAA+C,cAA/C,EAA+D,cAA/D;AAEA,WAAO,MAAM,KAAK,MAAL,CAAY,aAAZ,EAA2B,KAAK,SAAhC,EAA2C,KAAK,YAAhD,CAAb;AACD,GA/QkB,CAiRnB;;;AACA,QAAM,MAAN,CAAa,aAAb,EAA2C,QAA3C,EAA+D,WAA/D,EAA6F,cAA7F,EAAkI;AAChI,UAAM,8BAAe,aAAf,EAA8B,KAAK,WAAnC,CAAN;AACA,SAAK,cAAL,GAAsB,aAAtB;AACA,SAAK,SAAL,GAAiB,QAAjB;AACA,SAAK,YAAL,GAAoB,WAApB;;AAEA,QAAI,cAAc,IAAI,IAAtB,EAA4B;AAC1B,WAAK,eAAL,CAAqB,KAArB,GAA6B,OAAO,CAAC,OAAR,CAAgB,cAAhB,CAA7B;AACD;;AAED,SAAK,QAAL,GAAgB,KAAI,kBAAJ,EAAY,IAAZ,EAAkB,IAAlB,CAAhB;AACA,SAAK,UAAL,GAAkB,MAAM,mBAAmB,CAAC,KAAK,MAAN,EAAc,IAAd,CAA3C;AAEA,UAAM,kCAAkC,GAAG,IAAI,CAAC,OAAL,CAAa,KAAK,UAAlB,EAA8B,kCAAY,aAAa,CAAC,WAAd,CAA4B,MAAxC,EAAkD,IAAlD,EAAwD,KAAK,QAA7D,EAAuE;AAC9I,MAAA,EAAE,EAAE;AAD0I,KAAvE,CAA9B,CAA3C;;AAIA,QAAI,CAAC,eAAD,IAAU,OAAO,CAAC,MAAR,CAAuB,KAArC,EAA4C;AAC1C,YAAM,mBAAmB,GAAG,IAAI,CAAC,IAAL,CAAU,kCAAV,EAA8C,+BAA9C,CAA5B;;AACA,yBAAI,IAAJ,CAAS;AAAC,QAAA,IAAI,EAAE,mBAAI,QAAJ,CAAa,mBAAb;AAAP,OAAT,EAAoD,0BAApD;;AACA,YAAM,2BAAW,mBAAX,EAAgC,sBAAsB,CAAC,aAAD,CAAtD,CAAN;AACD,KArB+H,CAuBhI;;;AACA,UAAM,aAAa,GAAG,IAAI,GAAJ,EAAtB;AACA,SAAK,eAAL,CAAqB,KAAK,IAAG;AAC3B,UAAI,KAAK,CAAC,IAAN,IAAc,IAAlB,EAAwB;AACtB,QAAA,aAAa,CAAC,GAAd,CAAkB,KAAK,CAAC,IAAxB;AACD;AACF,KAJD;AAMA,SAAK,oBAAL,CAA0B,MAAM,KAAK,cAAL,CAAoB,OAApB,EAAhC;AACA,UAAM,iBAAiB,GAAG,MAAM,+BAAe,KAAK,OAAL,EAAf,EAA+B,YAAW;AACxE,UAAI,KAAK,WAAL,CAAiB,SAArB,EAAgC;AAC9B,cAAM,KAAK,WAAL,CAAiB,IAAjB,CAAsB,IAAI,CAAC,IAAL,CAAU,kCAAV,EAA8C,mBAA9C,CAAtB,CAAN;AACD;;AAED,YAAM,SAAS,GAAG,KAAK,SAAL,CAAe,KAAf,EAAlB;AACA,WAAK,SAAL,CAAe,MAAf,GAAwB,CAAxB;;AACA,WAAK,MAAM,QAAX,IAAuB,SAAvB,EAAkC;AAChC,cAAM,QAAQ,GAAG,KAAX,CAAiB,CAAC,IAAG;AACzB,6BAAI,IAAJ,CAAS;AAAC,YAAA,KAAK,EAAE;AAAR,WAAT,EAAqB,gBAArB;AACD,SAFK,CAAN;AAGD;AACF,KAZ+B,CAAhC;AAcA,WAAO;AACL,MAAA,MAAM,EAAE,kCADH;AAEL,MAAA,aAAa,EAAE,KAAK,CAAC,IAAN,CAAW,aAAX,CAFV;AAGL,MAAA,iBAHK;AAIL,MAAA;AAJK,KAAP;AAMD;;AAEO,QAAM,mDAAN,CAA0D,cAA1D,EAAgF;AACtF,QAAI,IAAI,GAAG,MAAM,qCAAqB,wCAAgB,cAAhB,CAArB,CAAjB;;AACA,QAAI,IAAI,IAAI,IAAZ,EAAkB;AAChB,aAAO,IAAP;AACD;;AAED,IAAA,IAAI,GAAG,MAAM,qCAAqB,0BAAa,IAAI,CAAC,IAAL,CAAU,KAAK,UAAf,EAA2B,UAA3B,CAAb,EAAqD,cAArD,CAArB,CAAb;;AACA,QAAI,IAAI,IAAI,IAAZ,EAAkB;AAChB,WAAK,mBAAL,GAA2B,IAA3B;AACA,aAAO,IAAP;AACD;;AAED,UAAM,IAAI,KAAJ,CAAU,mCAAmC,IAAI,CAAC,OAAL,CAAa,cAAb,CAA4B,EAAzE,CAAN;AACD;;AAEO,QAAM,OAAN,GAAa;AACnB,UAAM,WAAW,GAAG,KAAI,+BAAJ,EAAqB,KAAK,iBAA1B,CAApB;AAEA,UAAM,gBAAgB,GAAG,IAAI,GAAJ,EAAzB;AACA,UAAM,cAAc,GAAG,IAAI,GAAJ,EAAvB;;AAEA,SAAK,MAAM,CAAC,QAAD,EAAW,UAAX,CAAX,IAAqC,KAAK,OAAL,CAAa,OAAlD,EAA6D;AAC3D,UAAI,KAAK,iBAAL,CAAuB,SAA3B,EAAsC;AACpC;AACD;;AAED,UAAI,QAAQ,KAAK,kBAAS,GAAtB,IAA6B,OAAO,CAAC,QAAR,KAAqB,kBAAS,OAAT,CAAiB,QAAvE,EAAiF;AAC/E,cAAM,KAAI,wCAAJ,EAA8B,oGAA9B,CAAN;AACD;;AAED,YAAM,QAAQ,GAAG,KAAK,YAAL,CAAkB,QAAlB,CAAjB;AACA,YAAM,YAAY,GAAwB,IAAI,GAAJ,EAA1C;AACA,MAAA,gBAAgB,CAAC,GAAjB,CAAqB,QAArB,EAA+B,YAA/B;;AAEA,WAAK,MAAM,CAAC,IAAD,EAAO,WAAP,CAAX,IAAkC,kDAA4B,UAA5B,EAAwC,QAAxC,EAAkD,QAAlD,CAAlC,EAA+F;AAC7F,YAAI,KAAK,iBAAL,CAAuB,SAA3B,EAAsC;AACpC;AACD,SAH4F,CAK7F;;;AACA,cAAM,MAAM,GAAG,IAAI,CAAC,OAAL,CAAa,KAAK,UAAlB,EAA8B,QAAQ,CAAC,WAAT,CAAqB,KAAK,cAAL,CAAsB,WAAtB,CAAoC,MAAzD,EAAmE,oBAAK,IAAL,CAAnE,CAA9B,CAAf;AACA,cAAM,UAAU,GAAG,oCAAc,YAAd,EAA4B,WAAW,CAAC,MAAZ,KAAuB,CAAvB,GAA2B,QAAQ,CAAC,aAApC,GAAoD,WAAhF,EAA6F,MAA7F,EAAqG,QAArG,CAAnB;AACA,cAAM,kBAAkB,CAAC,UAAD,EAAa,cAAb,CAAxB;AACA,cAAM,QAAQ,CAAC,IAAT,CAAc,MAAd,EAAsB,IAAtB,EAA4B,UAA5B,EAAwC,WAAxC,CAAN;AACD;;AAED,UAAI,KAAK,iBAAL,CAAuB,SAA3B,EAAsC;AACpC;AACD;;AAED,WAAK,MAAM,MAAX,IAAqB,YAAY,CAAC,MAAb,EAArB,EAA4C;AAC1C,QAAA,WAAW,CAAC,OAAZ,CAAoB,MAAM,CAAC,WAAP,EAApB;AACD;AACF;;AAED,UAAM,WAAW,CAAC,UAAZ,EAAN;AACA,WAAO,gBAAP;AACD;;AAEO,EAAA,YAAY,CAAC,QAAD,EAAmB;AACrC,QAAI,KAAK,OAAL,CAAa,uBAAb,IAAwC,IAA5C,EAAkD;AAChD,aAAO,KAAK,OAAL,CAAa,uBAAb,CAAsC,IAAtC,EAA4C,QAA5C,CAAP;AACD;;AAED,YAAQ,QAAR;AACE,WAAK,kBAAS,GAAd;AAAmB;AACjB,gBAAM,WAAW,GAAuB,OAAO,CAAC,eAAD,CAAP,CAAyB,OAAjE;;AACA,iBAAO,IAAI,WAAJ,CAAgB,IAAhB,CAAP;AACD;;AAED,WAAK,kBAAS,OAAd;AAAuB;AACrB,gBAAM,WAAW,GAAuB,OAAO,CAAC,eAAD,CAAP,CAAyB,WAAjE;;AACA,iBAAO,IAAI,WAAJ,CAAgB,IAAhB,CAAP;AACD;;AAED,WAAK,kBAAS,KAAd;AACE,eAAO,KAAK,OAAO,CAAC,iBAAD,CAAP,CAA2B,aAAhC,EAA+C,IAA/C,CAAP;;AAEF;AACE,cAAM,IAAI,KAAJ,CAAU,qBAAqB,QAAQ,EAAvC,CAAN;AAfJ;AAiBD;;AAEM,QAAM,sBAAN,CAA6B,QAA7B,EAAiD,IAAjD,EAA2D;AAChE,QAAI,KAAK,OAAL,CAAa,WAAb,IAA4B,IAA5B,IAAoC,KAAK,SAAL,CAAe,oBAAf,KAAwC,IAAhF,EAAsF;AACpF;AACD;;AAED,UAAM,aAAa,GAAG;AAAC,MAAA,OAAO,EAAE,KAAK,SAAL,CAAe,OAAzB;AAAkC,MAAA,aAAa,EAAE;AAAjD,KAAtB;AACA,UAAM,MAAM,GAAG,KAAK,MAApB;;AACA,QAAI,MAAM,CAAC,cAAP,KAA0B,IAA9B,EAAoC;AAClC,YAAM,4BAAe,QAAQ,CAAC,QAAxB,EAAkC,oBAAK,IAAL,CAAlC,EAA8C,aAA9C,CAAN;AACD;;AAED,QAAI,MAAM,CAAC,UAAP,KAAsB,KAA1B,EAAiC;AAC/B,yBAAI,IAAJ,CAAS;AAAC,QAAA,MAAM,EAAE;AAAT,OAAT,EAAiD,8BAAjD;;AACA;AACD;;AAED,UAAM,WAAW,GAAG,yCAAgB,MAAM,CAAC,WAAvB,EAAoC,aAApC,CAApB;;AACA,QAAI,WAAW,IAAI,IAAnB,EAAyB;AACvB,YAAM,mCAAmC,GAAG,MAAM,WAAW,CAAC;AAC5D,QAAA,MAAM,EAAE,KAAK,MAD+C;AAE5D,QAAA,eAAe,EAAE,KAAK,MAAL,CAAY,eAF+B;AAG5D,QAAA,QAH4D;AAI5D,QAAA,IAAI,EAAE,oBAAK,IAAL;AAJsD,OAAD,CAA7D,CADuB,CAQvB;;AACA,WAAK,6BAAL,GAAqC,CAAC,mCAAtC;;AACA,UAAI,CAAC,mCAAL,EAA0C;AACxC;AACD;AACF;;AAED,QAAI,MAAM,CAAC,2BAAP,KAAuC,IAAvC,IAA+C,QAAQ,CAAC,QAAT,KAAsB,OAAO,CAAC,QAAjF,EAA2F;AACzF,yBAAI,IAAJ,CAAS;AAAC,QAAA,MAAM,EAAE;AAAT,OAAT,EAA2F,8BAA3F;AACD,KAFD,MAGK;AACH,YAAM,8BAAiB,MAAjB,EAAyB,KAAK,MAA9B,EAAsC;AAC1C,QAAA,aAD0C;AAE1C,QAAA,QAAQ,EAAE,QAAQ,CAAC,QAFuB;AAG1C,QAAA,IAAI,EAAE,oBAAK,IAAL,CAHoC;AAI1C,QAAA,cAAc,EAAE,KAAK,qBAAL,CAA2B,IAA3B;AAJ0B,OAAtC,CAAN;AAMD;AACF;;AAED,QAAM,SAAN,CAAgB,OAAhB,EAAyC;AACvC,UAAM,SAAS,GAAG,yCAAgB,KAAK,MAAL,CAAY,SAA5B,EAAuC,WAAvC,CAAlB;AACA,UAAM,QAAQ,GAAG,KAAK,iBAAL,CAAuB,KAAvB,EAAjB;;AACA,QAAI,SAAS,IAAI,IAAjB,EAAuB;AACrB;AACA,MAAA,QAAQ,CAAC,IAAT,CAAc,SAAd;AACD;;AAED,SAAK,MAAM,OAAX,IAAsB,QAAtB,EAAgC;AAC9B,YAAM,OAAO,CAAC,OAAR,CAAgB,OAAO,CAAC,OAAD,CAAvB,CAAN;AACD;AACF;;AAndkB;;;;AAsdrB,SAAS,kBAAT,CAA4B,UAA5B,EAAuD,cAAvD,EAAkF;AAChF,QAAM,OAAO,GAAG,IAAI,GAAJ,EAAhB;;AACA,OAAK,MAAM,MAAX,IAAqB,UAArB,EAAiC;AAC/B;AACA,QAAI,MAAM,YAAY,2BAAtB,EAAkC;AAChC;AACD;;AAED,UAAM,MAAM,GAAI,MAAiB,CAAC,MAAlC;;AACA,QAAI,CAAC,cAAc,CAAC,GAAf,CAAmB,MAAnB,CAAL,EAAiC;AAC/B,MAAA,OAAO,CAAC,GAAR,CAAY,MAAZ;AACD;AACF;;AAED,MAAI,OAAO,CAAC,IAAR,KAAiB,CAArB,EAAwB;AACtB,WAAO,OAAO,CAAC,OAAR,EAAP;AACD;;AAED,SAAO,OAAO,CAAC,GAAR,CAAY,KAAK,CAAC,IAAN,CAAW,OAAX,EAAoB,IAApB,GAA2B,GAA3B,CAA+B,GAAG,IAAG;AACtD,WAAO,uBAAO,GAAP,EACJ,IADI,CACC,MAAM,sBAAM,GAAN,EAAW,KAAX;AAAkB;AADzB,MAEJ,IAFI,CAEC,MAAM,cAAc,CAAC,GAAf,CAAmB,GAAnB,CAFP,CAAP;AAGD,GAJkB,CAAZ,CAAP;AAKD;;AASD,SAAS,sBAAT,CAAgC,aAAhC,EAA4D;AAC1D,QAAM,CAAC,GAAG,IAAI,CAAC,KAAL,CAAW,sCAAkB,aAAlB,CAAX,CAAV;;AACA,MAAI,CAAC,CAAC,OAAF,IAAa,IAAjB,EAAuB;AACrB,IAAA,CAAC,CAAC,OAAF,GAAY,qBAAZ;AACD;;AACD,SAAO,oCAAgB,CAAhB,EAAmB,IAAnB,CAAP;AACD,C", "sourcesContent": ["import { addValue, Arch, archFromString, AsyncTaskManager, DebugLogger, deepAssign, InvalidConfigurationError, log, safeStringify<PERSON>son, serializeToYaml, TmpDir } from \"builder-util\"\nimport { CancellationToken } from \"builder-util-runtime\"\nimport { executeFinally, orNullIfFileNotExist } from \"builder-util/out/promise\"\nimport { EventEmitter } from \"events\"\nimport { mkdirs, chmod, outputFile } from \"fs-extra\"\nimport isCI from \"is-ci\"\nimport { Lazy } from \"lazy-val\"\nimport * as path from \"path\"\nimport { getArtifactArchName } from \"builder-util/out/arch\"\nimport { AppInfo } from \"./appInfo\"\nimport { readAsarJson } from \"./asar/asar\"\nimport { createElectronFrameworkSupport } from \"./electron/ElectronFramework\"\nimport { LibUiFramework } from \"./frameworks/LibUiFramework\"\nimport { AfterPackContext, Configuration, Framework, Platform, SourceRepositoryInfo, Target } from \"./index\"\nimport MacPackager from \"./macPackager\"\nimport { Metadata } from \"./options/metadata\"\nimport { ArtifactBuildStarted, ArtifactCreated, PackagerOptions } from \"./packagerApi\"\nimport { PlatformPackager, resolveFunction } from \"./platformPackager\"\nimport { ProtonFramework } from \"./ProtonFramework\"\nimport { computeArchToTargetNamesMap, createTargets, NoOpTarget } from \"./targets/targetFactory\"\nimport { computeDefaultAppDirectory, getConfig, validateConfig } from \"./util/config\"\nimport { expandMacro } from \"./util/macroExpander\"\nimport { createLazyProductionDeps, NodeModuleDirInfo } from \"./util/packageDependencies\"\nimport { checkMetadata, readPackageJson } from \"./util/packageMetadata\"\nimport { getRepositoryInfo } from \"./util/repositoryInfo\"\nimport { installOrRebuild, nodeGypRebuild } from \"./util/yarn\"\nimport { WinPackager } from \"./winPackager\"\n\nfunction addHandler(emitter: EventEmitter, event: string, handler: (...args: Array<any>) => void) {\n  emitter.on(event, handler)\n}\n\ndeclare const PACKAGE_VERSION: string\n\nasync function createFrameworkInfo(configuration: Configuration, packager: Packager): Promise<Framework> {\n  let framework = configuration.framework\n  if (framework != null) {\n    framework = framework.toLowerCase()\n  }\n\n  let nodeVersion = configuration.nodeVersion\n  if (framework === \"electron\" || framework == null) {\n    return await createElectronFrameworkSupport(configuration, packager)\n  }\n\n  if (nodeVersion == null || nodeVersion === \"current\") {\n    nodeVersion = process.versions.node\n  }\n\n  const distMacOsName = `${packager.appInfo.productFilename}.app`\n  const isUseLaunchUi = configuration.launchUiVersion !== false\n  if (framework === \"proton\" || framework === \"proton-native\") {\n    return new ProtonFramework(nodeVersion, distMacOsName, isUseLaunchUi)\n  }\n  else if (framework === \"libui\") {\n    return new LibUiFramework(nodeVersion, distMacOsName, isUseLaunchUi)\n  }\n  else {\n    throw new InvalidConfigurationError(`Unknown framework: ${framework}`)\n  }\n}\n\nexport class Packager {\n  readonly projectDir: string\n\n  private _appDir: string\n  get appDir(): string {\n    return this._appDir\n  }\n\n  private _metadata: Metadata | null = null\n  get metadata(): Metadata {\n    return this._metadata!!\n  }\n\n  private _nodeModulesHandledExternally: boolean = false\n\n  get areNodeModulesHandledExternally(): boolean {\n    return this._nodeModulesHandledExternally\n  }\n\n  private _isPrepackedAppAsar: boolean = false\n\n  get isPrepackedAppAsar(): boolean {\n    return this._isPrepackedAppAsar\n  }\n\n  private _devMetadata: Metadata | null = null\n  get devMetadata(): Metadata | null {\n    return this._devMetadata\n  }\n\n  private _configuration: Configuration | null = null\n\n  get config(): Configuration {\n    return this._configuration!!\n  }\n\n  isTwoPackageJsonProjectLayoutUsed = false\n\n  readonly eventEmitter = new EventEmitter()\n\n  _appInfo: AppInfo | null = null\n  get appInfo(): AppInfo {\n    return this._appInfo!!\n  }\n\n  readonly tempDirManager = new TmpDir(\"packager\")\n\n  private _repositoryInfo = new Lazy<SourceRepositoryInfo | null>(() => getRepositoryInfo(this.projectDir, this.metadata, this.devMetadata))\n\n  private readonly afterPackHandlers: Array<(context: AfterPackContext) => Promise<any> | null> = []\n\n  readonly options: PackagerOptions\n\n  readonly debugLogger = new DebugLogger(log.isDebugEnabled)\n\n  get repositoryInfo(): Promise<SourceRepositoryInfo | null> {\n    return this._repositoryInfo.value\n  }\n\n  private nodeDependencyInfo = new Map<string, Lazy<Array<any>>>()\n\n  getNodeDependencyInfo(platform: Platform | null): Lazy<Array<NodeModuleDirInfo>> {\n    let key = \"\"\n    let excludedDependencies: Array<string> | null = null\n    if (platform != null && this.framework.getExcludedDependencies != null) {\n      excludedDependencies = this.framework.getExcludedDependencies(platform)\n      if (excludedDependencies != null) {\n        key += `-${platform.name}`\n      }\n    }\n\n    let result = this.nodeDependencyInfo.get(key)\n    if (result == null) {\n      result = createLazyProductionDeps(this.appDir, excludedDependencies)\n      this.nodeDependencyInfo.set(key, result)\n    }\n    return result\n  }\n\n  stageDirPathCustomizer: (target: Target, packager: PlatformPackager<any>, arch: Arch) => string = (target, packager, arch) => {\n    return path.join(target.outDir, `__${target.name}-${getArtifactArchName(arch, target.name)}`)\n  }\n\n  private _buildResourcesDir: string | null = null\n\n  get buildResourcesDir(): string {\n    let result = this._buildResourcesDir\n    if (result == null) {\n      result = path.resolve(this.projectDir, this.relativeBuildResourcesDirname)\n      this._buildResourcesDir = result\n    }\n    return result\n  }\n\n  get relativeBuildResourcesDirname(): string {\n    return this.config.directories!!.buildResources!!\n  }\n\n  private _framework: Framework | null = null\n  get framework(): Framework {\n    return this._framework!!\n  }\n\n  private readonly toDispose: Array<() => Promise<void>> = []\n\n  disposeOnBuildFinish(disposer: () => Promise<void>) {\n    this.toDispose.push(disposer)\n  }\n\n  //noinspection JSUnusedGlobalSymbols\n  constructor(options: PackagerOptions, readonly cancellationToken = new CancellationToken()) {\n    if (\"devMetadata\" in options) {\n      throw new InvalidConfigurationError(\"devMetadata in the options is deprecated, please use config instead\")\n    }\n    if (\"extraMetadata\" in options) {\n      throw new InvalidConfigurationError(\"extraMetadata in the options is deprecated, please use config.extraMetadata instead\")\n    }\n\n    const targets = options.targets || new Map<Platform, Map<Arch, Array<string>>>()\n    if (options.targets == null) {\n      options.targets = targets\n    }\n\n    function processTargets(platform: Platform, types: Array<string>) {\n      function commonArch(currentIfNotSpecified: boolean): Array<Arch> {\n        const result = Array<Arch>()\n        return result.length === 0 && currentIfNotSpecified ? [archFromString(process.arch)] : result\n      }\n\n      let archToType = targets.get(platform)\n      if (archToType == null) {\n        archToType = new Map<Arch, Array<string>>()\n        targets.set(platform, archToType)\n      }\n\n      if (types.length === 0) {\n        for (const arch of commonArch(false)) {\n          archToType.set(arch, [])\n        }\n        return\n      }\n\n      for (const type of types) {\n        const suffixPos = type.lastIndexOf(\":\")\n        if (suffixPos > 0) {\n          addValue(archToType, archFromString(type.substring(suffixPos + 1)), type.substring(0, suffixPos))\n        }\n        else {\n          for (const arch of commonArch(true)) {\n            addValue(archToType, arch, type)\n          }\n        }\n      }\n    }\n\n    if (options.mac != null) {\n      processTargets(Platform.MAC, options.mac)\n    }\n    if (options.linux != null) {\n      processTargets(Platform.LINUX, options.linux)\n    }\n    if (options.win != null) {\n      processTargets(Platform.WINDOWS, options.win)\n    }\n\n    this.projectDir = options.projectDir == null ? process.cwd() : path.resolve(options.projectDir)\n    this._appDir = this.projectDir\n    this.options = {\n      ...options,\n      prepackaged: options.prepackaged == null ? null : path.resolve(this.projectDir, options.prepackaged)\n    }\n\n    try {\n      log.info({version: PACKAGE_VERSION, os: require(\"os\").release()}, \"electron-builder\")\n    }\n    catch (e) {\n      // error in dev mode without babel\n      if (!(e instanceof ReferenceError)) {\n        throw e\n      }\n    }\n  }\n\n  addAfterPackHandler(handler: (context: AfterPackContext) => Promise<any> | null) {\n    this.afterPackHandlers.push(handler)\n  }\n\n  artifactCreated(handler: (event: ArtifactCreated) => void): Packager {\n    addHandler(this.eventEmitter, \"artifactCreated\", handler)\n    return this\n  }\n\n  async callArtifactBuildStarted(event: ArtifactBuildStarted, logFields?: any): Promise<void> {\n    log.info(logFields || {\n      target: event.targetPresentableName,\n      arch: event.arch == null ? null : Arch[event.arch],\n      file: log.filePath(event.file),\n    }, \"building\")\n    const handler = resolveFunction(this.config.artifactBuildStarted, \"artifactBuildStarted\")\n    if (handler != null) {\n      await Promise.resolve(handler(event))\n    }\n  }\n\n  /**\n   * Only for sub artifacts (update info), for main artifacts use `callArtifactBuildCompleted`.\n   */\n  dispatchArtifactCreated(event: ArtifactCreated): void {\n    this.eventEmitter.emit(\"artifactCreated\", event)\n  }\n\n  async callArtifactBuildCompleted(event: ArtifactCreated): Promise<void> {\n    this.dispatchArtifactCreated(event)\n\n    const handler = resolveFunction(this.config.artifactBuildCompleted, \"artifactBuildCompleted\")\n    if (handler != null) {\n      await Promise.resolve(handler(event))\n    }\n  }\n\n  async callAppxManifestCreated(path: string): Promise<void> {\n    const handler = resolveFunction(this.config.appxManifestCreated, \"appxManifestCreated\")\n    if (handler != null) {\n      await Promise.resolve(handler(path))\n    }\n  }\n\n  async build(): Promise<BuildResult> {\n    let configPath: string | null = null\n    let configFromOptions = this.options.config\n    if (typeof configFromOptions === \"string\") {\n      // it is a path to config file\n      configPath = configFromOptions\n      configFromOptions = null\n    }\n    else if (configFromOptions != null && configFromOptions.extends != null && configFromOptions.extends.includes(\".\")) {\n      configPath = configFromOptions.extends\n      delete configFromOptions.extends\n    }\n\n    const projectDir = this.projectDir\n\n    const devPackageFile = path.join(projectDir, \"package.json\")\n    this._devMetadata = await orNullIfFileNotExist(readPackageJson(devPackageFile))\n\n    const devMetadata = this.devMetadata\n    const configuration = await getConfig(projectDir, configPath, configFromOptions, new Lazy(() => Promise.resolve(devMetadata)))\n    if (log.isDebugEnabled) {\n      log.debug({config: getSafeEffectiveConfig(configuration)}, \"effective config\")\n    }\n\n    this._appDir = await computeDefaultAppDirectory(projectDir, configuration.directories!!.app)\n    this.isTwoPackageJsonProjectLayoutUsed = this._appDir !== projectDir\n\n    const appPackageFile = this.isTwoPackageJsonProjectLayoutUsed ? path.join(this.appDir, \"package.json\") : devPackageFile\n\n    // tslint:disable:prefer-conditional-expression\n    if (this.devMetadata != null && !this.isTwoPackageJsonProjectLayoutUsed) {\n      this._metadata = this.devMetadata\n    }\n    else {\n      this._metadata = await this.readProjectMetadataIfTwoPackageStructureOrPrepacked(appPackageFile)\n    }\n    deepAssign(this.metadata, configuration.extraMetadata)\n\n    if (this.isTwoPackageJsonProjectLayoutUsed) {\n      log.debug({devPackageFile, appPackageFile}, \"two package.json structure is used\")\n    }\n    checkMetadata(this.metadata, this.devMetadata, appPackageFile, devPackageFile)\n\n    return await this._build(configuration, this._metadata, this._devMetadata)\n  }\n\n  // external caller of this method always uses isTwoPackageJsonProjectLayoutUsed=false and appDir=projectDir, no way (and need) to use another values\n  async _build(configuration: Configuration, metadata: Metadata, devMetadata: Metadata | null, repositoryInfo?: SourceRepositoryInfo): Promise<BuildResult> {\n    await validateConfig(configuration, this.debugLogger)\n    this._configuration = configuration\n    this._metadata = metadata\n    this._devMetadata = devMetadata\n\n    if (repositoryInfo != null) {\n      this._repositoryInfo.value = Promise.resolve(repositoryInfo)\n    }\n\n    this._appInfo = new AppInfo(this, null)\n    this._framework = await createFrameworkInfo(this.config, this)\n\n    const commonOutDirWithoutPossibleOsMacro = path.resolve(this.projectDir, expandMacro(configuration.directories!!.output!!, null, this._appInfo, {\n      os: \"\",\n    }))\n\n    if (!isCI && (process.stdout as any).isTTY) {\n      const effectiveConfigFile = path.join(commonOutDirWithoutPossibleOsMacro, \"builder-effective-config.yaml\")\n      log.info({file: log.filePath(effectiveConfigFile)}, \"writing effective config\")\n      await outputFile(effectiveConfigFile, getSafeEffectiveConfig(configuration))\n    }\n\n    // because artifact event maybe dispatched several times for different publish providers\n    const artifactPaths = new Set<string>()\n    this.artifactCreated(event => {\n      if (event.file != null) {\n        artifactPaths.add(event.file)\n      }\n    })\n\n    this.disposeOnBuildFinish(() => this.tempDirManager.cleanup())\n    const platformToTargets = await executeFinally(this.doBuild(), async () => {\n      if (this.debugLogger.isEnabled) {\n        await this.debugLogger.save(path.join(commonOutDirWithoutPossibleOsMacro, \"builder-debug.yml\"))\n      }\n\n      const toDispose = this.toDispose.slice()\n      this.toDispose.length = 0\n      for (const disposer of toDispose) {\n        await disposer().catch(e => {\n          log.warn({error: e}, \"cannot dispose\")\n        })\n      }\n    })\n\n    return {\n      outDir: commonOutDirWithoutPossibleOsMacro,\n      artifactPaths: Array.from(artifactPaths),\n      platformToTargets,\n      configuration,\n    }\n  }\n\n  private async readProjectMetadataIfTwoPackageStructureOrPrepacked(appPackageFile: string): Promise<Metadata> {\n    let data = await orNullIfFileNotExist(readPackageJson(appPackageFile))\n    if (data != null) {\n      return data\n    }\n\n    data = await orNullIfFileNotExist(readAsarJson(path.join(this.projectDir, \"app.asar\"), \"package.json\"))\n    if (data != null) {\n      this._isPrepackedAppAsar = true\n      return data\n    }\n\n    throw new Error(`Cannot find package.json in the ${path.dirname(appPackageFile)}`)\n  }\n\n  private async doBuild(): Promise<Map<Platform, Map<string, Target>>> {\n    const taskManager = new AsyncTaskManager(this.cancellationToken)\n\n    const platformToTarget = new Map<Platform, Map<string, Target>>()\n    const createdOutDirs = new Set<string>()\n\n    for (const [platform, archToType] of this.options.targets!!) {\n      if (this.cancellationToken.cancelled) {\n        break\n      }\n\n      if (platform === Platform.MAC && process.platform === Platform.WINDOWS.nodeName) {\n        throw new InvalidConfigurationError(\"Build for macOS is supported only on macOS, please see https://electron.build/multi-platform-build\")\n      }\n\n      const packager = this.createHelper(platform)\n      const nameToTarget: Map<string, Target> = new Map()\n      platformToTarget.set(platform, nameToTarget)\n\n      for (const [arch, targetNames] of computeArchToTargetNamesMap(archToType, packager, platform)) {\n        if (this.cancellationToken.cancelled) {\n          break\n        }\n\n        // support os and arch macro in output value\n        const outDir = path.resolve(this.projectDir, packager.expandMacro(this._configuration!!.directories!!.output!!, Arch[arch]))\n        const targetList = createTargets(nameToTarget, targetNames.length === 0 ? packager.defaultTarget : targetNames, outDir, packager)\n        await createOutDirIfNeed(targetList, createdOutDirs)\n        await packager.pack(outDir, arch, targetList, taskManager)\n      }\n\n      if (this.cancellationToken.cancelled) {\n        break\n      }\n\n      for (const target of nameToTarget.values()) {\n        taskManager.addTask(target.finishBuild())\n      }\n    }\n\n    await taskManager.awaitTasks()\n    return platformToTarget\n  }\n\n  private createHelper(platform: Platform): PlatformPackager<any> {\n    if (this.options.platformPackagerFactory != null) {\n      return this.options.platformPackagerFactory!(this, platform)\n    }\n\n    switch (platform) {\n      case Platform.MAC: {\n        const helperClass: typeof MacPackager = require(\"./macPackager\").default\n        return new helperClass(this)\n      }\n\n      case Platform.WINDOWS: {\n        const helperClass: typeof WinPackager = require(\"./winPackager\").WinPackager\n        return new helperClass(this)\n      }\n\n      case Platform.LINUX:\n        return new (require(\"./linuxPackager\").LinuxPackager)(this)\n\n      default:\n        throw new Error(`Unknown platform: ${platform}`)\n    }\n  }\n\n  public async installAppDependencies(platform: Platform, arch: Arch): Promise<any> {\n    if (this.options.prepackaged != null || this.framework.isNpmRebuildRequired !== true) {\n      return\n    }\n\n    const frameworkInfo = {version: this.framework.version, useCustomDist: true}\n    const config = this.config\n    if (config.nodeGypRebuild === true) {\n      await nodeGypRebuild(platform.nodeName, Arch[arch], frameworkInfo)\n    }\n\n    if (config.npmRebuild === false) {\n      log.info({reason: \"npmRebuild is set to false\"}, \"skipped dependencies rebuild\")\n      return\n    }\n\n    const beforeBuild = resolveFunction(config.beforeBuild, \"beforeBuild\")\n    if (beforeBuild != null) {\n      const performDependenciesInstallOrRebuild = await beforeBuild({\n        appDir: this.appDir,\n        electronVersion: this.config.electronVersion!,\n        platform,\n        arch: Arch[arch]\n      })\n\n      // If beforeBuild resolves to false, it means that handling node_modules is done outside of electron-builder.\n      this._nodeModulesHandledExternally = !performDependenciesInstallOrRebuild\n      if (!performDependenciesInstallOrRebuild) {\n        return\n      }\n    }\n\n    if (config.buildDependenciesFromSource === true && platform.nodeName !== process.platform) {\n      log.info({reason: \"platform is different and buildDependenciesFromSource is set to true\"}, \"skipped dependencies rebuild\")\n    }\n    else {\n      await installOrRebuild(config, this.appDir, {\n        frameworkInfo,\n        platform: platform.nodeName,\n        arch: Arch[arch],\n        productionDeps: this.getNodeDependencyInfo(null),\n      })\n    }\n  }\n\n  async afterPack(context: AfterPackContext): Promise<any> {\n    const afterPack = resolveFunction(this.config.afterPack, \"afterPack\")\n    const handlers = this.afterPackHandlers.slice()\n    if (afterPack != null) {\n      // user handler should be last\n      handlers.push(afterPack)\n    }\n\n    for (const handler of handlers) {\n      await Promise.resolve(handler(context))\n    }\n  }\n}\n\nfunction createOutDirIfNeed(targetList: Array<Target>, createdOutDirs: Set<string>): Promise<any> {\n  const ourDirs = new Set<string>()\n  for (const target of targetList) {\n    // noinspection SuspiciousInstanceOfGuard\n    if (target instanceof NoOpTarget) {\n      continue\n    }\n\n    const outDir = (target as Target).outDir\n    if (!createdOutDirs.has(outDir)) {\n      ourDirs.add(outDir)\n    }\n  }\n\n  if (ourDirs.size === 0) {\n    return Promise.resolve()\n  }\n\n  return Promise.all(Array.from(ourDirs).sort().map(dir => {\n    return mkdirs(dir)\n      .then(() => chmod(dir, 0o755) /* set explicitly */)\n      .then(() => createdOutDirs.add(dir))\n  }))\n}\n\nexport interface BuildResult {\n  readonly outDir: string\n  readonly artifactPaths: Array<string>\n  readonly platformToTargets: Map<Platform, Map<string, Target>>\n  readonly configuration: Configuration\n}\n\nfunction getSafeEffectiveConfig(configuration: Configuration): string {\n  const o = JSON.parse(safeStringifyJson(configuration))\n  if (o.cscLink != null) {\n    o.cscLink = \"<hidden by builder>\"\n  }\n  return serializeToYaml(o, true)\n}"], "sourceRoot": ""}