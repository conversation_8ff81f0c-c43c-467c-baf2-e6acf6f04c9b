{"version": 3, "sources": ["../src/fileTransformer.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;;;;;AAIA;AACO,MAAM,oBAAoB,GAAG,GAAG,IAAI,CAAC,GAAG,eAAe,IAAI,CAAC,GAAG,EAA/D;AAEP;;;;AACM,SAAU,qBAAV,CAAgC,IAAhC,EAA8C;AAClD,MAAI,IAAI,CAAC,MAAL,CAAY,eAAZ,IAA+B,IAAnC,EAAyC;AACvC,WAAO,IAAI,CAAC,MAAL,CAAY,eAAnB;AACD,GAHiD,CAKlD;;;AACA,SAAO,MAAM,CAAC,kBAAD,EAAqB,IAArB,CAAb;AACD;AAED;;;AACM,SAAU,MAAV,CAAiB,IAAjB,EAA+B,IAA/B,EAA6C;AACjD,QAAM,IAAI,GAAG,IAAI,CAAC,QAAL,CAAc,YAA3B;AACA,SAAO,IAAI,IAAI,IAAR,IAAgB,IAAI,IAAI,IAA/B;AACD;AAED;;;AACM,SAAU,iBAAV,CAA4B,MAA5B,EAA4C,aAA5C,EAA0E,aAA1E,EAA8F,gBAA9F,EAAsI;AAC1I,QAAM,eAAe,GAAG,IAAI,CAAC,IAAL,CAAU,MAAV,EAAkB,cAAlB,CAAxB;AACA,QAAM,sBAAsB,GAAG,aAAa,CAAC,oBAAd,KAAuC,KAAtE;AACA,QAAM,WAAW,GAAG,IAAI,CAAC,GAAL,GAAW,cAA/B;AACA,SAAO,IAAI,IAAG;AACZ,QAAI,IAAI,KAAK,eAAb,EAA8B;AAC5B,aAAO,qBAAqB,CAAC,IAAD,EAAO,aAAP,EAAsB,sBAAtB,CAA5B;AACD;;AAED,QAAI,IAAI,CAAC,QAAL,CAAc,WAAd,KAA8B,IAAI,CAAC,QAAL,CAAc,oBAAd,CAAlC,EAAuE;AACrE,aAAO,yBAAS,IAAT,EAAe,OAAf,EACJ,IADI,CACC,EAAE,IAAI,kBAAkB,CAAC,IAAI,CAAC,KAAL,CAAW,EAAX,CAAD,EAAiB;AAC7C,QAAA,MAAM,EAAE,KADqC;AAE7C,QAAA;AAF6C,OAAjB,CADzB,EAKJ,KALI,CAKE,CAAC,IAAI,mBAAI,IAAJ,CAAS,CAAT,CALP,CAAP;AAMD,KAPD,MAQK,IAAI,gBAAgB,IAAI,IAAxB,EAA8B;AACjC,aAAO,gBAAgB,CAAC,IAAD,CAAvB;AACD,KAFI,MAGA;AACH,aAAO,IAAP;AACD;AACF,GAnBD;AAoBD;AASD;;;AACM,SAAU,0BAAV,CAAqC,UAArC,EAAyD,QAAzD,EAAyE;AAC7E,QAAM,mBAAmB,GAAG,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,cAAtB,EAAsC,kBAAtC,EAA0D,KAA1D,CAA5B;AACA,SAAO,OAAO,CAAC,IAAI,CAAC,IAAL,CAAU,mBAAV,EAA+B,eAA/B,CAAD,CAAP,CAAyD,iCAAzD,CAA2F,UAA3F,EAAuG,QAAvG,CAAP;AACD;;AAED,MAAM,gCAAgC,GAAG,IAAI,GAAJ,CAAQ,CAAC,MAAD,EAAS,SAAT,EAAoB,UAApB,EAAgC,OAAhC,EAAyC,MAAzC,EAAiD,KAAjD,EAAwD,IAAxD,EAA8D,KAA9D,EAAqE,cAArE,EAAqF,cAArF,EAAqG,oBAArG,EAA2H,MAA3H,CAAR,CAAzC;;AAOA,SAAS,kBAAT,CAA4B,IAA5B,EAAuC,OAAvC,EAAyE;AACvE,QAAM,IAAI,GAAG,IAAI,CAAC,YAAlB,CADuE,CAEvE;;AACA,QAAM,aAAa,GAAG,IAAI,IAAI,IAAR,IAAgB,OAAO,IAAP,KAAgB,QAAhC,IAA4C,CAAC,MAAM,CAAC,mBAAP,CAA2B,IAA3B,EAAiC,IAAjC,CAAsC,EAAE,IAAI,EAAE,CAAC,UAAH,CAAc,OAAd,CAA5C,CAAnE;;AACA,MAAI;AACF,QAAI,OAAO,GAAG,KAAd;;AACA,SAAK,MAAM,IAAX,IAAmB,MAAM,CAAC,mBAAP,CAA2B,IAA3B,CAAnB,EAAqD;AACnD;AACA,UAAI,IAAI,CAAC,CAAD,CAAJ,KAAY,GAAZ,IACF,gCAAgC,CAAC,GAAjC,CAAqC,IAArC,CADE,IAED,OAAO,CAAC,sBAAR,IAAkC,IAAI,KAAK,SAF1C,IAGD,OAAO,CAAC,MAAR,IAAkB,IAAI,KAAK,iBAH1B,IAID,CAAC,OAAO,CAAC,MAAT,IAAmB,IAAI,KAAK,MAJ3B,IAKD,aAAa,IAAI,IAAI,KAAK,OAL7B,EAKuC;AACrC,eAAO,IAAI,CAAC,IAAD,CAAX;AACA,QAAA,OAAO,GAAG,IAAV;AACD;AACF;;AAED,QAAI,OAAJ,EAAa;AACX,aAAO,IAAI,CAAC,SAAL,CAAe,IAAf,EAAqB,IAArB,EAA2B,CAA3B,CAAP;AACD;AACF,GAlBD,CAmBA,OAAO,CAAP,EAAU;AACR,8BAAM,CAAN;AACD;;AAED,SAAO,IAAP;AACD;;AAED,eAAe,qBAAf,CAAqC,IAArC,EAAmD,aAAnD,EAAuE,sBAAvE,EAAsG;AACpG,QAAM,eAAe,GAAG,IAAI,CAAC,KAAL,CAAW,MAAM,yBAAS,IAAT,EAAe,OAAf,CAAjB,CAAxB;;AACA,MAAI,aAAa,IAAI,IAArB,EAA2B;AACzB,mCAAW,eAAX,EAA4B,aAA5B;AACD,GAJmG,CAMpG;;;AACA,QAAM,uBAAuB,GAAG,kBAAkB,CAAC,eAAD,EAAkB;AAClE,IAAA,MAAM,EAAE,IAD0D;AAElE,IAAA;AAFkE,GAAlB,CAAlD;;AAIA,MAAI,uBAAuB,IAAI,IAA/B,EAAqC;AACnC,WAAO,uBAAP;AACD,GAFD,MAGK,IAAI,aAAa,IAAI,IAArB,EAA2B;AAC9B,WAAO,IAAI,CAAC,SAAL,CAAe,eAAf,EAAgC,IAAhC,EAAsC,CAAtC,CAAP;AACD;;AACD,SAAO,IAAP;AACD,C", "sourcesContent": ["import { debug, log, deepAssign } from \"builder-util\"\nimport { FileTransformer } from \"builder-util/out/fs\"\nimport { readFile } from \"fs-extra\"\nimport * as path from \"path\"\nimport { Configuration } from \"./configuration\"\nimport { Packager } from \"./packager\"\n\n/** @internal */\nexport const NODE_MODULES_PATTERN = `${path.sep}node_modules${path.sep}`\n\n/** @internal */\nexport function isElectronCompileUsed(info: Packager): boolean {\n  if (info.config.electronCompile != null) {\n    return info.config.electronCompile\n  }\n\n  // if in devDependencies - it means that babel is used for precompilation or for some reason user decided to not use electron-compile for production\n  return hasDep(\"electron-compile\", info)\n}\n\n/** @internal */\nexport function hasDep(name: string, info: Packager) {\n  const deps = info.metadata.dependencies\n  return deps != null && name in deps\n}\n\n/** @internal */\nexport function createTransformer(srcDir: string, configuration: Configuration, extraMetadata: any, extraTransformer: FileTransformer | null): FileTransformer {\n  const mainPackageJson = path.join(srcDir, \"package.json\")\n  const isRemovePackageScripts = configuration.removePackageScripts !== false\n  const packageJson = path.sep + \"package.json\"\n  return file => {\n    if (file === mainPackageJson) {\n      return modifyMainPackageJson(file, extraMetadata, isRemovePackageScripts)\n    }\n\n    if (file.endsWith(packageJson) && file.includes(NODE_MODULES_PATTERN)) {\n      return readFile(file, \"utf-8\")\n        .then(it => cleanupPackageJson(JSON.parse(it), {\n          isMain: false,\n          isRemovePackageScripts,\n        }))\n        .catch(e => log.warn(e))\n    }\n    else if (extraTransformer != null) {\n      return extraTransformer(file)\n    }\n    else {\n      return null\n    }\n  }\n}\n\n/** @internal */\nexport interface CompilerHost {\n  compile(file: string): any\n\n  saveConfiguration(): Promise<any>\n}\n\n/** @internal */\nexport function createElectronCompilerHost(projectDir: string, cacheDir: string): Promise<CompilerHost> {\n  const electronCompilePath = path.join(projectDir, \"node_modules\", \"electron-compile\", \"lib\")\n  return require(path.join(electronCompilePath, \"config-parser\")).createCompilerHostFromProjectRoot(projectDir, cacheDir)\n}\n\nconst ignoredPackageMetadataProperties = new Set([\"dist\", \"gitHead\", \"keywords\", \"build\", \"jspm\", \"ava\", \"xo\", \"nyc\", \"eslintConfig\", \"contributors\", \"bundleDependencies\", \"tags\"])\n\ninterface CleanupPackageFileOptions {\n  readonly isRemovePackageScripts: boolean\n  readonly isMain: boolean\n}\n\nfunction cleanupPackageJson(data: any, options: CleanupPackageFileOptions): any {\n  const deps = data.dependencies\n  // https://github.com/electron-userland/electron-builder/issues/507#issuecomment-312772099\n  const isRemoveBabel = deps != null && typeof deps === \"object\" && !Object.getOwnPropertyNames(deps).some(it => it.startsWith(\"babel\"))\n  try {\n    let changed = false\n    for (const prop of Object.getOwnPropertyNames(data)) {\n      // removing devDependencies from package.json breaks levelup in electron, so, remove it only from main package.json\n      if (prop[0] === \"_\" ||\n        ignoredPackageMetadataProperties.has(prop) ||\n        (options.isRemovePackageScripts && prop === \"scripts\") ||\n        (options.isMain && prop === \"devDependencies\") ||\n        (!options.isMain && prop === \"bugs\") ||\n        (isRemoveBabel && prop === \"babel\")) {\n        delete data[prop]\n        changed = true\n      }\n    }\n\n    if (changed) {\n      return JSON.stringify(data, null, 2)\n    }\n  }\n  catch (e) {\n    debug(e)\n  }\n\n  return null\n}\n\nasync function modifyMainPackageJson(file: string, extraMetadata: any, isRemovePackageScripts: boolean) {\n  const mainPackageData = JSON.parse(await readFile(file, \"utf-8\"))\n  if (extraMetadata != null) {\n    deepAssign(mainPackageData, extraMetadata)\n  }\n\n  // https://github.com/electron-userland/electron-builder/issues/1212\n  const serializedDataIfChanged = cleanupPackageJson(mainPackageData, {\n    isMain: true,\n    isRemovePackageScripts,\n  })\n  if (serializedDataIfChanged != null) {\n    return serializedDataIfChanged\n  }\n  else if (extraMetadata != null) {\n    return JSON.stringify(mainPackageData, null, 2)\n  }\n  return null\n}"], "sourceRoot": ""}