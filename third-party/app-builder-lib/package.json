{"//": "electron-builder-squirrel-windows and dmg-builder added as dev dep for tests (as otherwise `require` doesn't work using Yarn 2)", "///": "babel in devDependencies for proton tests", "_args": [["app-builder-lib@22.10.4", "C:\\Users\\<USER>\\workspace\\abc\\abcyun_clinic_desktop"]], "_development": true, "_from": "app-builder-lib@22.10.4", "_id": "app-builder-lib@22.10.4", "_inBundle": false, "_integrity": "sha1-P8cIIbdr65yCedneIpYO8hdNoVM=", "_location": "/app-builder-lib", "_phantomChildren": {"ms": "2.1.2", "sax": "1.2.4"}, "_requested": {"type": "version", "registry": true, "raw": "app-builder-lib@22.10.4", "name": "app-builder-lib", "escapedName": "app-builder-lib", "rawSpec": "22.10.4", "saveSpec": null, "fetchSpec": "22.10.4"}, "_requiredBy": ["/dmg-builder", "/electron-builder"], "_resolved": "https://registry.npm.taobao.org/app-builder-lib/download/app-builder-lib-22.10.4.tgz", "_spec": "22.10.4", "_where": "C:\\Users\\<USER>\\workspace\\abc\\abcyun_clinic_desktop", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/electron-userland/electron-builder/issues"}, "dependencies": {"7zip-bin": "~5.0.3", "@develar/schema-utils": "~2.6.5", "@electron/universal": "1.0.4", "async-exit-hook": "^2.0.1", "bluebird-lst": "^1.0.9", "builder-util": "22.10.4", "builder-util-runtime": "8.7.3", "chromium-pickle-js": "^0.2.0", "debug": "^4.3.1", "ejs": "^3.1.5", "electron-publish": "22.10.4", "fs-extra": "^9.0.1", "hosted-git-info": "^3.0.7", "is-ci": "^2.0.0", "isbinaryfile": "^4.0.6", "js-yaml": "^3.14.1", "lazy-val": "^1.0.4", "normalize-package-data": "^3.0.0", "read-config-file": "6.0.0", "sanitize-filename": "^1.6.3", "semver": "^7.3.4", "temp-file": "^3.3.7"}, "description": "electron-builder lib", "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-proposal-do-expressions": "^7.12.1", "@babel/plugin-proposal-export-default-from": "^7.12.1", "@babel/plugin-proposal-export-namespace-from": "^7.12.1", "@babel/plugin-proposal-function-bind": "^7.12.1", "@babel/plugin-proposal-function-sent": "^7.12.1", "@babel/plugin-proposal-json-strings": "^7.12.1", "@babel/plugin-proposal-logical-assignment-operators": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-proposal-numeric-separator": "^7.12.7", "@babel/plugin-proposal-optional-chaining": "^7.12.7", "@babel/plugin-proposal-pipeline-operator": "^7.12.1", "@babel/plugin-proposal-throw-expressions": "^7.12.1", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/preset-env": "^7.12.11", "@babel/preset-react": "^7.12.10", "@types/debug": "^4.1.5", "@types/ejs": "^3.0.5", "@types/fs-extra": "^9.0.5", "@types/is-ci": "^2.0.0", "@types/js-yaml": "^3.12.5", "@types/normalize-package-data": "^2.4.0", "@types/semver": "^7.3.4", "dmg-builder": "22.10.4", "electron-builder-squirrel-windows": "22.10.4"}, "engines": {"node": ">=8.12.0"}, "files": ["out", "templates", "scheme.json", "electron-osx-sign", "certs/root_certs.keychain"], "homepage": "https://github.com/electron-userland/electron-builder", "keywords": ["electron", "builder", "build", "installer", "install", "packager", "pack", "nsis", "app", "dmg", "pkg", "msi", "exe", "setup", "Windows", "OS X", "MacOS", "<PERSON>", "appx", "snap", "portable"], "license": "MIT", "main": "out/index.js", "name": "app-builder-lib", "repository": {"type": "git", "url": "git+https://github.com/electron-userland/electron-builder.git"}, "typings": "./out/index.d.ts", "version": "22.10.4"}