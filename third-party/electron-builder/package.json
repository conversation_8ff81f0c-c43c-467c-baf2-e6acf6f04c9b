{"_args": [["electron-builder@22.10.4", "C:\\Users\\<USER>\\workspace\\abc\\abcyun_clinic_desktop"]], "_development": true, "_from": "electron-builder@22.10.4", "_id": "electron-builder@22.10.4", "_inBundle": false, "_integrity": "sha1-4fQAz0HrtjL795qobF4Kseoe1+U=", "_location": "/electron-builder", "_phantomChildren": {"ms": "2.1.2", "sax": "1.2.4"}, "_requested": {"type": "version", "registry": true, "raw": "electron-builder@22.10.4", "name": "electron-builder", "escapedName": "electron-builder", "rawSpec": "22.10.4", "saveSpec": null, "fetchSpec": "22.10.4"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npm.taobao.org/electron-builder/download/electron-builder-22.10.4.tgz", "_spec": "22.10.4", "_where": "C:\\Users\\<USER>\\workspace\\abc\\abcyun_clinic_desktop", "author": {"name": "<PERSON>"}, "bin": {"electron-builder": "out/cli/cli.js", "install-app-deps": "out/cli/install-app-deps.js"}, "bugs": {"url": "https://github.com/electron-userland/electron-builder/issues"}, "contributors": [{"name": "<PERSON>"}], "dependencies": {"@types/yargs": "^15.0.12", "app-builder-lib": "file:../app-builder-lib", "bluebird-lst": "^1.0.9", "builder-util": "22.10.4", "builder-util-runtime": "8.7.3", "chalk": "^4.1.0", "dmg-builder": "22.10.4", "fs-extra": "^9.0.1", "is-ci": "^2.0.0", "lazy-val": "^1.0.4", "read-config-file": "6.0.0", "sanitize-filename": "^1.6.3", "update-notifier": "^5.0.1", "yargs": "^16.2.0"}, "description": "A complete solution to package and build a ready for distribution Electron app for MacOS, Windows and Linux with “auto update” support out of the box", "devDependencies": {"@types/fs-extra": "^9.0.5", "@types/is-ci": "^2.0.0"}, "engines": {"node": ">=8.12.0"}, "files": ["out"], "homepage": "https://github.com/electron-userland/electron-builder", "keywords": ["electron", "builder", "build", "installer", "install", "packager", "pack", "nsis", "app", "dmg", "pkg", "msi", "exe", "setup", "Windows", "OS X", "MacOS", "<PERSON>", "appx", "snap", "portable"], "license": "MIT", "main": "out/index.js", "name": "electron-builder", "publishConfig": {"tag": "next"}, "repository": {"type": "git", "url": "git+https://github.com/electron-userland/electron-builder.git"}, "typings": "./out/index.d.ts", "version": "22.10.4"}