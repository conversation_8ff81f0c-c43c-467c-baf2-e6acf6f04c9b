{"version": 3, "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAcA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA", "sourcesContent": ["export { getArchSuffix, Arch, archFromString } from \"builder-util\"\nexport { build, CliOptions, createTargets } from \"./builder\"\nexport {\n  TargetConfiguration, Platform, Target, DIR_TARGET, BeforeBuildContext, SourceRepositoryInfo, TargetSpecificOptions, TargetConfigType, DEFAULT_TARGET, CompressionLevel,\n  MacConfiguration, DmgOptions, MasConfiguration, MacOsTargetName, PkgOptions, DmgContent, DmgWindow,\n  PlatformSpecificBuildOptions, AsarOptions, FileSet,\n  LinuxConfiguration, DebOptions, CommonLinuxOptions, LinuxTargetSpecificOptions, AppImageOptions,\n  Configuration, AfterPackContext, MetadataDirectories, Protocol, ReleaseInfo, ElectronDownloadOptions,\n  SnapOptions, CommonWindowsInstallerConfiguration, FileAssociation, MsiOptions, AppXOptions, WindowsConfiguration,\n  Packager, BuildResult, PackagerOptions, ArtifactCreated, Artifact<PERSON><PERSON>Started,\n  NsisOptions, NsisWebOptions, PortableOptions, CommonNsisOptions, SquirrelWindowsOptions,\n  WindowsSignOptions, CustomWindowsSignTaskConfiguration, WindowsSignTaskConfiguration, CustomWindowsSign, FileCodeSigningInfo, CertificateFromStoreInfo,\n  Metadata, AuthorMetadata, RepositoryInfo, AppInfo,\n  UploadTask, PublishManager, PublishOptions, ProgressInfo\n} from \"app-builder-lib\"\nexport { buildForge, ForgeOptions } from \"app-builder-lib\"\nexport { CancellationToken } from \"builder-util-runtime\"\n"], "sourceRoot": ""}