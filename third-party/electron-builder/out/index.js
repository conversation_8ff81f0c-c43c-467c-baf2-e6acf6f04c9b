"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "getArchSuffix", {
  enumerable: true,
  get: function () {
    return _builderUtil().getArchSuffix;
  }
});
Object.defineProperty(exports, "Arch", {
  enumerable: true,
  get: function () {
    return _builderUtil().Arch;
  }
});
Object.defineProperty(exports, "archFromString", {
  enumerable: true,
  get: function () {
    return _builderUtil().archFromString;
  }
});
Object.defineProperty(exports, "build", {
  enumerable: true,
  get: function () {
    return _builder().build;
  }
});
Object.defineProperty(exports, "createTargets", {
  enumerable: true,
  get: function () {
    return _builder().createTargets;
  }
});
Object.defineProperty(exports, "Platform", {
  enumerable: true,
  get: function () {
    return _appBuilderLib().Platform;
  }
});
Object.defineProperty(exports, "Target", {
  enumerable: true,
  get: function () {
    return _appBuilderLib().Target;
  }
});
Object.defineProperty(exports, "DIR_TARGET", {
  enumerable: true,
  get: function () {
    return _appBuilderLib().DIR_TARGET;
  }
});
Object.defineProperty(exports, "DEFAULT_TARGET", {
  enumerable: true,
  get: function () {
    return _appBuilderLib().DEFAULT_TARGET;
  }
});
Object.defineProperty(exports, "Packager", {
  enumerable: true,
  get: function () {
    return _appBuilderLib().Packager;
  }
});
Object.defineProperty(exports, "AppInfo", {
  enumerable: true,
  get: function () {
    return _appBuilderLib().AppInfo;
  }
});
Object.defineProperty(exports, "PublishManager", {
  enumerable: true,
  get: function () {
    return _appBuilderLib().PublishManager;
  }
});
Object.defineProperty(exports, "buildForge", {
  enumerable: true,
  get: function () {
    return _appBuilderLib().buildForge;
  }
});
Object.defineProperty(exports, "CancellationToken", {
  enumerable: true,
  get: function () {
    return _builderUtilRuntime().CancellationToken;
  }
});

function _builderUtil() {
  const data = require("builder-util");

  _builderUtil = function () {
    return data;
  };

  return data;
}

function _builder() {
  const data = require("./builder");

  _builder = function () {
    return data;
  };

  return data;
}

function _appBuilderLib() {
  const data = require("app-builder-lib");

  _appBuilderLib = function () {
    return data;
  };

  return data;
}

function _builderUtilRuntime() {
  const data = require("builder-util-runtime");

  _builderUtilRuntime = function () {
    return data;
  };

  return data;
}
// __ts-babel@6.0.4
//# sourceMappingURL=index.js.map