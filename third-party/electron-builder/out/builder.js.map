{"version": 3, "sources": ["../src/builder.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AAEM,SAAU,WAAV,GAAqB;AACzB,SAAO,iBACJ,mBADI,CACgB;AACnB,4BAAwB;AADL,GADhB,CAAP;AAID;AAeD;;;AACM,SAAU,gBAAV,CAA2B,IAA3B,EAA2C;AAC/C,MAAI,IAAI,CAAC,OAAL,IAAgB,IAApB,EAA0B;AACxB,WAAO,IAAP;AACD;;AAED,QAAM,OAAO,GAAG,IAAI,GAAJ,EAAhB;;AAEA,WAAS,cAAT,CAAwB,QAAxB,EAA4C,KAA5C,EAAgE;AAC9D,aAAS,UAAT,CAAoB,qBAApB,EAAkD;AAChD,YAAM,MAAM,GAAG,KAAK,EAApB;;AACA,UAAI,IAAI,CAAC,GAAT,EAAc;AACZ,QAAA,MAAM,CAAC,IAAP,CAAY,oBAAK,GAAjB;AACD;;AACD,UAAI,IAAI,CAAC,MAAT,EAAiB;AACf,QAAA,MAAM,CAAC,IAAP,CAAY,oBAAK,MAAjB;AACD;;AACD,UAAI,IAAI,CAAC,KAAT,EAAgB;AACd,QAAA,MAAM,CAAC,IAAP,CAAY,oBAAK,KAAjB;AACD;;AACD,UAAI,IAAI,CAAC,IAAT,EAAe;AACb,QAAA,MAAM,CAAC,IAAP,CAAY,oBAAK,IAAjB;AACD;;AACD,UAAI,IAAI,CAAC,SAAT,EAAoB;AAClB,QAAA,MAAM,CAAC,IAAP,CAAY,oBAAK,SAAjB;AACD;;AAED,aAAO,MAAM,CAAC,MAAP,KAAkB,CAAlB,IAAuB,qBAAvB,GAA+C,CAAC,mCAAe,OAAO,CAAC,IAAvB,CAAD,CAA/C,GAAgF,MAAvF;AACD;;AAED,QAAI,UAAU,GAAG,OAAO,CAAC,GAAR,CAAY,QAAZ,CAAjB;;AACA,QAAI,UAAU,IAAI,IAAlB,EAAwB;AACtB,MAAA,UAAU,GAAG,IAAI,GAAJ,EAAb;AACA,MAAA,OAAO,CAAC,GAAR,CAAY,QAAZ,EAAsB,UAAtB;AACD;;AAED,QAAI,KAAK,CAAC,MAAN,KAAiB,CAArB,EAAwB;AACtB,YAAM,kBAAkB,GAAG,IAAI,CAAC,GAAL,GAAW,CAAC,2BAAD,CAAX,GAA0B,EAArD;;AACA,WAAK,MAAM,IAAX,IAAmB,UAAU,CAAC,IAAI,CAAC,GAAL,KAAa,IAAd,CAA7B,EAAkD;AAChD,QAAA,UAAU,CAAC,GAAX,CAAe,IAAf,EAAqB,kBAArB;AACD;;AACD;AACD;;AAED,SAAK,MAAM,IAAX,IAAmB,KAAnB,EAA0B;AACxB,YAAM,SAAS,GAAG,IAAI,CAAC,WAAL,CAAiB,GAAjB,CAAlB;;AACA,UAAI,SAAS,GAAG,CAAhB,EAAmB;AACjB,qCAAS,UAAT,EAAqB,mCAAe,IAAI,CAAC,SAAL,CAAe,SAAS,GAAG,CAA3B,CAAf,CAArB,EAAoE,IAAI,CAAC,SAAL,CAAe,CAAf,EAAkB,SAAlB,CAApE;AACD,OAFD,MAGK;AACH,aAAK,MAAM,IAAX,IAAmB,UAAU,CAAC,IAAD,CAA7B,EAAqC;AACnC,uCAAS,UAAT,EAAqB,IAArB,EAA2B,IAA3B;AACD;AACF;AACF;AACF;;AAED,MAAI,IAAI,CAAC,GAAL,IAAY,IAAhB,EAAsB;AACpB,IAAA,cAAc,CAAC,0BAAS,GAAV,EAAe,IAAI,CAAC,GAApB,CAAd;AACD;;AAED,MAAI,IAAI,CAAC,KAAL,IAAc,IAAlB,EAAwB;AACtB,IAAA,cAAc,CAAC,0BAAS,KAAV,EAAiB,IAAI,CAAC,KAAtB,CAAd;AACD;;AAED,MAAI,IAAI,CAAC,GAAL,IAAY,IAAhB,EAAsB;AACpB,IAAA,cAAc,CAAC,0BAAS,OAAV,EAAmB,IAAI,CAAC,GAAxB,CAAd;AACD;;AAED,MAAI,OAAO,CAAC,IAAR,KAAiB,CAArB,EAAwB;AACtB,IAAA,cAAc,CAAC,0BAAS,OAAT,EAAD,EAAqB,EAArB,CAAd;AACD;;AAED,QAAM,MAAM,GAAQ,EAAC,GAAG;AAAJ,GAApB;AACA,EAAA,MAAM,CAAC,OAAP,GAAiB,OAAjB;AAEA,SAAO,MAAM,CAAC,GAAd;AACA,SAAO,MAAM,CAAC,GAAd;AACA,SAAO,MAAM,CAAC,KAAd;AACA,SAAO,MAAM,CAAC,GAAd;AAEA,QAAM,CAAC,GAAG,MAAV;AACA,SAAO,CAAC,CAAC,CAAT;AACA,SAAO,CAAC,CAAC,CAAT;AACA,SAAO,CAAC,CAAC,CAAT;AACA,SAAO,CAAC,CAAC,CAAT;AACA,SAAO,CAAC,CAAC,OAAT;AACA,SAAO,CAAC,CAAC,KAAT;AACA,SAAO,CAAC,CAAC,EAAT;AACA,SAAO,CAAC,CAAC,CAAT;AACA,SAAO,CAAC,CAAC,OAAT;AACA,SAAO,CAAC,CAAC,IAAT;AACA,SAAO,CAAC,CAAC,CAAT;AACA,SAAO,CAAC,CAAC,CAAT;AACA,SAAO,CAAC,CAAC,EAAT;AAEA,SAAO,MAAM,CAAC,IAAd;AACA,SAAO,MAAM,CAAC,GAAd;AACA,SAAO,MAAM,CAAC,MAAd;AACA,SAAO,MAAM,CAAC,KAAd;AACA,SAAO,MAAM,CAAC,SAAd;AAEA,MAAI,MAAM,GAAG,MAAM,CAAC,MAApB,CArG+C,CAuG/C;AACA;;AACA,MAAI,KAAK,CAAC,OAAN,CAAc,MAAd,CAAJ,EAA2B;AACzB,UAAM,SAAS,GAAkB,EAAjC;;AACA,SAAK,MAAM,UAAX,IAAyB,MAAzB,EAAiC;AAC/B,UAAI,OAAO,UAAP,KAAsB,QAA1B,EAAoC;AAClC,uCAAW,SAAX,EAAsB,UAAtB;AACD,OAFD,MAGK,IAAI,OAAO,UAAP,KAAsB,QAA1B,EAAoC;AACvC,QAAA,SAAS,CAAC,OAAV,GAAoB,UAApB;AACD;AACF;;AAED,IAAA,MAAM,GAAG,SAAT;AACA,IAAA,MAAM,CAAC,MAAP,GAAgB,SAAhB;AACD,GAtH8C,CAwH/C;;;AACA,MAAI,MAAM,IAAI,IAAV,IAAkB,OAAO,MAAP,KAAkB,QAAxC,EAAkD;AAChD,QAAI,MAAM,CAAC,aAAP,IAAwB,IAA5B,EAAkC;AAChC,MAAA,WAAW,CAAC,MAAM,CAAC,aAAR,CAAX;AACD,KAH+C,CAKhD;;;AACA,QAAI,MAAM,CAAC,GAAP,IAAc,IAAlB,EAAwB;AACtB,MAAA,WAAW,CAAC,MAAM,CAAC,GAAR,EAAa,UAAb,CAAX;AACD,KAR+C,CAUhD;;;AACA,QAAI,MAAM,CAAC,IAAP,IAAe,IAAnB,EAAyB;AACvB,MAAA,WAAW,CAAC,MAAM,CAAC,IAAR,CAAX;AACD;;AACD,QAAI,MAAM,CAAC,OAAP,IAAkB,IAAtB,EAA4B;AAC1B,MAAA,WAAW,CAAC,MAAM,CAAC,OAAR,CAAX;AACD;AACF;;AAED,MAAI,aAAa,CAAb,IAAkB,EAAE,gBAAgB,MAAlB,CAAtB,EAAiD;AAC/C,IAAA,MAAM,CAAC,UAAP,GAAoB,CAAC,CAAC,OAAtB;AACD;;AACD,SAAO,CAAC,CAAC,OAAT;AAEA,SAAO,MAAP;AACD;;AAED,SAAS,WAAT,CAAqB,IAArB,EAAgC,GAAhC,EAA2C;AACzC,QAAM,KAAK,GAAG,IAAI,CAAC,GAAD,CAAlB;;AACA,MAAI,KAAK,KAAK,MAAd,EAAsB;AACpB,IAAA,IAAI,CAAC,GAAD,CAAJ,GAAY,IAAZ;AACD,GAFD,MAGK,IAAI,KAAK,KAAK,OAAd,EAAuB;AAC1B,IAAA,IAAI,CAAC,GAAD,CAAJ,GAAY,KAAZ;AACD,GAFI,MAGA,IAAI,KAAK,KAAK,MAAd,EAAsB;AACzB,IAAA,IAAI,CAAC,GAAD,CAAJ,GAAY,IAAZ;AACD,GAFI,MAGA,IAAI,GAAG,KAAK,SAAR,IAAqB,OAAO,KAAP,KAAiB,QAA1C,EAAoD;AACvD,IAAA,IAAI,CAAC,GAAD,CAAJ,GAAY,KAAK,CAAC,QAAN,EAAZ;AACD,GAFI,MAGA,IAAI,KAAK,IAAI,IAAT,IAAiB,OAAO,KAAP,KAAiB,QAAtC,EAAgD;AACnD,IAAA,WAAW,CAAC,KAAD,CAAX;AACD;AACF;AAED;;;AACM,SAAU,WAAV,CAAsB,IAAtB,EAA+B;AACnC,OAAK,MAAM,GAAX,IAAkB,MAAM,CAAC,mBAAP,CAA2B,IAA3B,CAAlB,EAAoD;AAClD,IAAA,WAAW,CAAC,IAAD,EAAO,GAAP,CAAX;AACD;;AACD,SAAO,IAAP;AACD;;AAEK,SAAU,aAAV,CAAwB,SAAxB,EAAoD,IAApD,EAA0E,IAA1E,EAA8F;AAClG,QAAM,OAAO,GAAG,IAAI,GAAJ,EAAhB;;AACA,OAAK,MAAM,QAAX,IAAuB,SAAvB,EAAkC;AAChC,UAAM,KAAK,GAAI,IAAI,KAAK,KAAT,GAAkB,QAAQ,KAAK,0BAAS,GAAtB,GAA4B,CAAC,oBAAK,GAAN,EAAW,oBAAK,KAAhB,EAAuB,oBAAK,SAA5B,CAA5B,GAAqE,CAAC,oBAAK,GAAN,EAAW,oBAAK,IAAhB,CAAvF,GAAgH,CAAC,mCAAe,IAAI,IAAI,IAAR,GAAe,OAAO,CAAC,IAAvB,GAA8B,IAA7C,CAAD,CAA/H;AACA,UAAM,UAAU,GAAG,IAAI,GAAJ,EAAnB;AACA,IAAA,OAAO,CAAC,GAAR,CAAY,QAAZ,EAAsB,UAAtB;;AAEA,SAAK,MAAM,IAAX,IAAmB,KAAnB,EAA0B;AACxB,MAAA,UAAU,CAAC,GAAX,CAAe,IAAf,EAAqB,IAAI,IAAI,IAAR,GAAe,EAAf,GAAoB,CAAC,IAAD,CAAzC;AACD;AACF;;AACD,SAAO,OAAP;AACD;;AAEK,SAAU,KAAV,CAAgB,UAAhB,EAAuC;AAC3C,QAAM,YAAY,GAAG,gBAAgB,CAAC,UAAU,IAAI,EAAf,CAArC;AACA,SAAO,4BAAO,YAAP,EAAqB,KAAI,yBAAJ,EAAa,YAAb,CAArB,CAAP;AACD;AAED;;;;;AAGM,SAAU,qBAAV,CAAgC,KAAhC,EAAiD;AACrD,QAAM,YAAY,GAAG,aAArB;AACA,QAAM,UAAU,GAAG,WAAnB;AACA,SAAO,KAAK,CACT,MADI,CACG,KADH,EACU;AACb,IAAA,KAAK,EAAE,UADM;AAEb,IAAA,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,OAAX,CAFM;AAGb,IAAA,WAAW,EAAE,6CAA6C,iBAAM,SAAN,CAAgB,uBAAhB,CAAwC,IAHrF;AAIb,IAAA,IAAI,EAAE;AAJO,GADV,EAOJ,MAPI,CAOG,OAPH,EAOY;AACf,IAAA,KAAK,EAAE,UADQ;AAEf,IAAA,KAAK,EAAE,GAFQ;AAGf,IAAA,WAAW,EAAE,6CAA6C,iBAAM,SAAN,CAAgB,uBAAhB,CAAwC,GAHnF;AAIf,IAAA,IAAI,EAAE;AAJS,GAPZ,EAaJ,MAbI,CAaG,KAbH,EAaU;AACb,IAAA,KAAK,EAAE,UADM;AAEb,IAAA,KAAK,EAAE,CAAC,GAAD,EAAM,SAAN,CAFM;AAGb,IAAA,WAAW,EAAE,+CAA+C,iBAAM,SAAN,CAAgB,uBAAhB,CAAwC,GAHvF;AAIb,IAAA,IAAI,EAAE;AAJO,GAbV,EAmBJ,MAnBI,CAmBG,KAnBH,EAmBU;AACb,IAAA,KAAK,EAAE,UADM;AAEb,IAAA,WAAW,EAAE,eAFA;AAGb,IAAA,IAAI,EAAE;AAHO,GAnBV,EAwBJ,MAxBI,CAwBG,MAxBH,EAwBW;AACd,IAAA,KAAK,EAAE,UADO;AAEd,IAAA,WAAW,EAAE,gBAFC;AAGd,IAAA,IAAI,EAAE;AAHQ,GAxBX,EA6BJ,MA7BI,CA6BG,QA7BH,EA6Ba;AAChB,IAAA,KAAK,EAAE,UADS;AAEhB,IAAA,WAAW,EAAE,kBAFG;AAGhB,IAAA,IAAI,EAAE;AAHU,GA7Bb,EAkCJ,MAlCI,CAkCG,OAlCH,EAkCY;AACf,IAAA,KAAK,EAAE,UADQ;AAEf,IAAA,WAAW,EAAE,iBAFE;AAGf,IAAA,IAAI,EAAE;AAHS,GAlCZ,EAuCJ,MAvCI,CAuCG,WAvCH,EAuCgB;AACnB,IAAA,KAAK,EAAE,UADY;AAEnB,IAAA,WAAW,EAAE,qBAFM;AAGnB,IAAA,IAAI,EAAE;AAHa,GAvChB,EA4CJ,MA5CI,CA4CG,KA5CH,EA4CU;AACb,IAAA,KAAK,EAAE,UADM;AAEb,IAAA,WAAW,EAAE,qCAFA;AAGb,IAAA,IAAI,EAAE;AAHO,GA5CV,EAiDJ,MAjDI,CAiDG,SAjDH,EAiDc;AACjB,IAAA,KAAK,EAAE,YADU;AAEjB,IAAA,KAAK,EAAE,GAFU;AAGjB,IAAA,WAAW,EAAE,0BAA0B,iBAAM,SAAN,CAAgB,uBAAhB,CAAwC,EAH9D;AAIjB,IAAA,OAAO,EAAE,CAAC,OAAD,EAAU,cAAV,EAA0B,QAA1B,EAAoC,OAApC,EAA6C,SAA7C;AAJQ,GAjDd,EAuDJ,MAvDI,CAuDG,aAvDH,EAuDkB;AACrB,IAAA,KAAK,EAAE,CAAC,IAAD,CADc;AAErB,IAAA,KAAK,EAAE,UAFc;AAGrB,IAAA,WAAW,EAAE;AAHQ,GAvDlB,EA4DJ,MA5DI,CA4DG,YA5DH,EA4DiB;AACpB,IAAA,KAAK,EAAE,CAAC,SAAD,CADa;AAEpB,IAAA,KAAK,EAAE,UAFa;AAGpB,IAAA,WAAW,EAAE;AAHO,GA5DjB,EAiEJ,MAjEI,CAiEG,QAjEH,EAiEa;AAChB,IAAA,KAAK,EAAE,CAAC,GAAD,CADS;AAEhB,IAAA,KAAK,EAAE,UAFS;AAGhB,IAAA,WAAW,EAAE,6GAA6G,iBAAM,SAAN,CAAgB,uBAAhB;AAH1G,GAjEb,EAsEJ,KAtEI,CAsEE,CAAC,MAAD,EAAS,SAAT,CAtEF,EAsEuB,QAtEvB,EAuEJ,OAvEI,CAuEI,uBAvEJ,EAuE6B,oCAvE7B,EAwEJ,OAxEI,CAwEI,qCAxEJ,EAwE2C,gCAxE3C,EAyEJ,OAzEI,CAyEI,+BAzEJ,EAyEqC,wBAzErC,EA0EJ,OA1EI,CA0EI,2CA1EJ,EA0EiD,0CA1EjD,EA2EJ,OA3EI,CA2EI,8CA3EJ,EA2EoD,oCA3EpD,CAAP;AA4ED,C", "sourcesContent": ["import { addValue, Arch, archFromString, deepAssign } from \"builder-util\"\nimport chalk from \"chalk\"\nimport { build as _build, Configuration, DIR_TARGET, Packager, PackagerOptions, Platform } from \"app-builder-lib\"\nimport { PublishOptions } from \"electron-publish\"\nimport yargs from \"yargs\"\n\nexport function createYargs() {\n  return yargs\n    .parserConfiguration({\n      \"camel-case-expansion\": false,\n    })\n}\n\nexport interface BuildOptions extends PackagerOptions, PublishOptions {\n}\n\nexport interface CliOptions extends PackagerOptions, PublishOptions {\n  x64?: boolean\n  ia32?: boolean\n  armv7l?: boolean\n  arm64?: boolean\n  universal?: boolean\n\n  dir?: boolean\n}\n\n/** @private */\nexport function normalizeOptions(args: CliOptions): BuildOptions {\n  if (args.targets != null) {\n    return args\n  }\n\n  const targets = new Map<Platform, Map<Arch, Array<string>>>()\n\n  function processTargets(platform: Platform, types: Array<string>) {\n    function commonArch(currentIfNotSpecified: boolean): Array<Arch> {\n      const result = Array<Arch>()\n      if (args.x64) {\n        result.push(Arch.x64)\n      }\n      if (args.armv7l) {\n        result.push(Arch.armv7l)\n      }\n      if (args.arm64) {\n        result.push(Arch.arm64)\n      }\n      if (args.ia32) {\n        result.push(Arch.ia32)\n      }\n      if (args.universal) {\n        result.push(Arch.universal)\n      }\n\n      return result.length === 0 && currentIfNotSpecified ? [archFromString(process.arch)] : result\n    }\n\n    let archToType = targets.get(platform)\n    if (archToType == null) {\n      archToType = new Map<Arch, Array<string>>()\n      targets.set(platform, archToType)\n    }\n\n    if (types.length === 0) {\n      const defaultTargetValue = args.dir ? [DIR_TARGET] : []\n      for (const arch of commonArch(args.dir === true)) {\n        archToType.set(arch, defaultTargetValue)\n      }\n      return\n    }\n\n    for (const type of types) {\n      const suffixPos = type.lastIndexOf(\":\")\n      if (suffixPos > 0) {\n        addValue(archToType, archFromString(type.substring(suffixPos + 1)), type.substring(0, suffixPos))\n      }\n      else {\n        for (const arch of commonArch(true)) {\n          addValue(archToType, arch, type)\n        }\n      }\n    }\n  }\n\n  if (args.mac != null) {\n    processTargets(Platform.MAC, args.mac)\n  }\n\n  if (args.linux != null) {\n    processTargets(Platform.LINUX, args.linux)\n  }\n\n  if (args.win != null) {\n    processTargets(Platform.WINDOWS, args.win)\n  }\n\n  if (targets.size === 0) {\n    processTargets(Platform.current(), [])\n  }\n\n  const result: any = {...args}\n  result.targets = targets\n\n  delete result.dir\n  delete result.mac\n  delete result.linux\n  delete result.win\n\n  const r = result as any\n  delete r.m\n  delete r.o\n  delete r.l\n  delete r.w\n  delete r.windows\n  delete r.macos\n  delete r.$0\n  delete r._\n  delete r.version\n  delete r.help\n  delete r.c\n  delete r.p\n  delete r.pd\n\n  delete result.ia32\n  delete result.x64\n  delete result.armv7l\n  delete result.arm64\n  delete result.universal\n\n  let config = result.config\n\n  // config is array when combining dot-notation values with a config file value\n  // https://github.com/electron-userland/electron-builder/issues/2016\n  if (Array.isArray(config)) {\n    const newConfig: Configuration = {}\n    for (const configItem of config) {\n      if (typeof configItem === \"object\") {\n        deepAssign(newConfig, configItem)\n      }\n      else if (typeof configItem === \"string\") {\n        newConfig.extends = configItem\n      }\n    }\n\n    config = newConfig\n    result.config = newConfig\n  }\n\n  // AJV cannot coerce \"null\" string to null if string is also allowed (because null string is a valid value)\n  if (config != null && typeof config !== \"string\") {\n    if (config.extraMetadata != null) {\n      coerceTypes(config.extraMetadata)\n    }\n\n    // ability to disable code sign using -c.mac.identity=null\n    if (config.mac != null) {\n      coerceValue(config.mac, \"identity\")\n    }\n\n    // fix Boolean type by coerceTypes\n    if (config.nsis != null) {\n      coerceTypes(config.nsis)\n    }\n    if (config.nsisWeb != null) {\n      coerceTypes(config.nsisWeb)\n    }\n  }\n\n  if (\"project\" in r && !(\"projectDir\" in result)) {\n    result.projectDir = r.project\n  }\n  delete r.project\n\n  return result as BuildOptions\n}\n\nfunction coerceValue(host: any, key: string): void {\n  const value = host[key]\n  if (value === \"true\") {\n    host[key] = true\n  }\n  else if (value === \"false\") {\n    host[key] = false\n  }\n  else if (value === \"null\") {\n    host[key] = null\n  }\n  else if (key === \"version\" && typeof value === \"number\") {\n    host[key] = value.toString()\n  }\n  else if (value != null && typeof value === \"object\") {\n    coerceTypes(value)\n  }\n}\n\n/** @private */\nexport function coerceTypes(host: any): any {\n  for (const key of Object.getOwnPropertyNames(host)) {\n    coerceValue(host, key)\n  }\n  return host\n}\n\nexport function createTargets(platforms: Array<Platform>, type?: string | null, arch?: string | null): Map<Platform, Map<Arch, Array<string>>> {\n  const targets = new Map<Platform, Map<Arch, Array<string>>>()\n  for (const platform of platforms) {\n    const archs = (arch === \"all\" ? (platform === Platform.MAC ? [Arch.x64, Arch.arm64, Arch.universal] : [Arch.x64, Arch.ia32]) : [archFromString(arch == null ? process.arch : arch)])\n    const archToType = new Map<Arch, Array<string>>()\n    targets.set(platform, archToType)\n\n    for (const arch of archs) {\n      archToType.set(arch, type == null ? [] : [type])\n    }\n  }\n  return targets\n}\n\nexport function build(rawOptions?: CliOptions): Promise<Array<string>> {\n  const buildOptions = normalizeOptions(rawOptions || {})\n  return _build(buildOptions, new Packager(buildOptions))\n}\n\n/**\n * @private\n */\nexport function configureBuildCommand(yargs: yargs.Argv): yargs.Argv {\n  const publishGroup = \"Publishing:\"\n  const buildGroup = \"Building:\"\n  return yargs\n    .option(\"mac\", {\n      group: buildGroup,\n      alias: [\"m\", \"o\", \"macos\"],\n      description: `Build for macOS, accepts target list (see ${chalk.underline(\"https://goo.gl/5uHuzj\")}).`,\n      type: \"array\",\n    })\n    .option(\"linux\", {\n      group: buildGroup,\n      alias: \"l\",\n      description: `Build for Linux, accepts target list (see ${chalk.underline(\"https://goo.gl/4vwQad\")})`,\n      type: \"array\",\n    })\n    .option(\"win\", {\n      group: buildGroup,\n      alias: [\"w\", \"windows\"],\n      description: `Build for Windows, accepts target list (see ${chalk.underline(\"https://goo.gl/jYsTEJ\")})`,\n      type: \"array\",\n    })\n    .option(\"x64\", {\n      group: buildGroup,\n      description: \"Build for x64\",\n      type: \"boolean\",\n    })\n    .option(\"ia32\", {\n      group: buildGroup,\n      description: \"Build for ia32\",\n      type: \"boolean\",\n    })\n    .option(\"armv7l\", {\n      group: buildGroup,\n      description: \"Build for armv7l\",\n      type: \"boolean\",\n    })\n    .option(\"arm64\", {\n      group: buildGroup,\n      description: \"Build for arm64\",\n      type: \"boolean\",\n    })\n    .option(\"universal\", {\n      group: buildGroup,\n      description: \"Build for universal\",\n      type: \"boolean\",\n    })\n    .option(\"dir\", {\n      group: buildGroup,\n      description: \"Build unpacked dir. Useful to test.\",\n      type: \"boolean\",\n    })\n    .option(\"publish\", {\n      group: publishGroup,\n      alias: \"p\",\n      description: `Publish artifacts, see ${chalk.underline(\"https://goo.gl/tSFycD\")}`,\n      choices: [\"onTag\", \"onTagOrDraft\", \"always\", \"never\", undefined as any],\n    })\n    .option(\"prepackaged\", {\n      alias: [\"pd\"],\n      group: buildGroup,\n      description: \"The path to prepackaged app (to pack in a distributable format)\",\n    })\n    .option(\"projectDir\", {\n      alias: [\"project\"],\n      group: buildGroup,\n      description: \"The path to project directory. Defaults to current working directory.\",\n    })\n    .option(\"config\", {\n      alias: [\"c\"],\n      group: buildGroup,\n      description: \"The path to an electron-builder config. Defaults to `electron-builder.yml` (or `json`, or `json5`), see \" + chalk.underline(\"https://goo.gl/YFRJOM\"),\n    })\n    .group([\"help\", \"version\"], \"Other:\")\n    .example(\"electron-builder -mwl\", \"build for macOS, Windows and Linux\")\n    .example(\"electron-builder --linux deb tar.xz\", \"build deb and tar.xz for Linux\")\n    .example(\"electron-builder --win --ia32\", \"build for Windows ia32\")\n    .example(\"electron-builder -c.extraMetadata.foo=bar\", \"set package.json property `foo` to `bar`\")\n    .example(\"electron-builder --config.nsis.unicode=false\", \"configure unicode options for NSIS\")\n}"], "sourceRoot": ""}