{"version": 3, "sources": ["../../src/cli/cli.ts"], "names": [], "mappings": ";;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;;;AAEA;AACA,8BACG,OADH,CACW,CAAC,OAAD,EAAU,GAAV,CADX,EAC2B,OAD3B,EACoC,gCADpC,EAC2D,IAAI,CAAC,gBAAD,CAD/D,EAEG,OAFH,CAEW,kBAFX,EAE+B,kBAF/B,EAEmD,gDAFnD,EAEmF,IAAI,CAAC,gCAAD,CAFvF,EAGG,OAHH,CAGW,kBAHX,EAG+B,yBAH/B,EAG0D;AAA+B;AAHzF,EAG2I,IAAI,CAAC,oBAAD,CAH/I,EAIG,OAJH,CAIW,yBAJX,EAIsC,uDAJtC,EAKI,KAAK,IAAI,KAAK,CACX,MADM,CACC,WADD,EACc;AACnB,EAAA,KAAK,EAAE,CAAC,GAAD,CADY;AAEnB,EAAA,IAAI,EAAE,QAFa;AAGnB,EAAA,WAAW,EAAE,IAHM;AAInB,EAAA,WAAW,EAAE;AAJM,CADd,EAON,YAPM,CAOO,WAPP,CALb,EAaI,IAAI,CAAC,IAAI,IAAI,kDAAqB,IAAI,CAAC,SAA1B,CAAT,CAbR,EAcG,OAdH,CAcW,OAdX,EAcoB,8DAdpB,EAeI,KAAK,IAAI,KAfb,EAgBI,IAAI,CAAC,MAAM,qBAAP,CAhBR,EAiBG,IAjBH,GAkBG,MAlBH,CAkBU,OAAO,iBAAM,SAAN,CAAgB,wBAAhB,CAAyC,0BAlB1D,EAmBG,MAnBH,GAoBG,iBApBH,GAqBG,IArBH;;AAuBA,SAAS,IAAT,CAAc,IAAd,EAA+C;AAC7C,SAAQ,IAAD,IAAc;AACnB,IAAA,eAAe;AACf,mCAAQ,IAAI,CAAC,IAAL,CAAU,OAAO,CAAC,GAAR,EAAV,EAAyB,sBAAzB,CAAR,EACG,IADH,CACQ,MAAM,IAAI,CAAC,IAAD,CADlB,EAEG,KAFH,CAES,KAAK,IAAG;AACb,MAAA,OAAO,CAAC,QAAR,GAAmB,CAAnB,CADa,CAEb;;AACA,MAAA,OAAO,CAAC,EAAR,CAAW,MAAX,EAAmB,MAAM,OAAO,CAAC,QAAR,GAAmB,CAA5C;;AACA,UAAI,KAAK,YAAY,wCAArB,EAAgD;AAC9C,2BAAI,KAAJ,CAAU,IAAV,EAAgB,KAAK,CAAC,OAAtB;AACD,OAFD,MAGK,IAAI,EAAE,KAAK,YAAY,iBAAnB,KAAiC,CAAC,KAAK,CAAC,aAA5C,EAA2D;AAC9D,2BAAI,KAAJ,CAAU;AAAC,UAAA,UAAU,EAAE,KAAK,CAAC;AAAnB,SAAV,EAAqC,KAAK,CAAC,OAA3C;AACD;AACF,KAZH;AAaD,GAfD;AAgBD;;AAED,SAAS,eAAT,GAAwB;AACtB,MAAI,mBAAQ,OAAO,CAAC,GAAR,CAAY,kBAAZ,IAAkC,IAA9C,EAAoD;AAClD;AACD;;AAED,2BAAS,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,IAArB,EAA2B,IAA3B,EAAiC,cAAjC,CAAT,EACG,IADH,CACQ,MAAM,EAAN,IAAW;AACf,QAAI,EAAE,CAAC,OAAH,KAAe,wBAAnB,EAA6C;AAC3C;AACD;;AAED,UAAM,cAAc,GAAG,OAAM,2BAAW,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,IAArB,EAA2B,IAA3B,EAAiC,mBAAjC,CAAX,CAAN,IAA0E,KAA1E,GAAkF,MAAzG;AAEA,UAAM,QAAQ,GAAG,+BAAe;AAAC,MAAA,GAAG,EAAE;AAAN,KAAf,CAAjB;;AACA,QAAI,QAAQ,CAAC,MAAT,IAAmB,IAAvB,EAA6B;AAC3B,MAAA,QAAQ,CAAC,MAAT,CAAgB;AACd,QAAA,OAAO,EAAE,oBAAoB,iBAAM,GAAN,CAAU,QAAQ,CAAC,MAAT,CAAgB,OAA1B,CAAkC,GAAG,iBAAM,KAAN,CAAY,KAAZ,CAAkB,GAAG,iBAAM,KAAN,CAAY,QAAQ,CAAC,MAAT,CAAgB,MAA5B,CAAmC,UAAU,iBAAM,IAAN,CAAW,GAAG,cAAc,2BAA5B,CAAwD;AAD9K,OAAhB;AAGD;AACF,GAdH,EAeG,KAfH,CAeS,CAAC,IAAI,mBAAI,IAAJ,CAAS;AAAC,IAAA,KAAK,EAAE;AAAR,GAAT,EAAqB,sBAArB,CAfd;AAgBD;;AAED,eAAe,oBAAf,CAAoC,IAApC,EAA6C;AAC3C,QAAM,UAAU,GAAG,OAAO,CAAC,GAAR,EAAnB,CAD2C,CAE3C;;AACA,SAAO,4BAAe,IAAI,CAAC,QAApB,EAA8B,IAAI,CAAC,IAAnC,EAAyC;AAAC,IAAA,OAAO,EAAE,MAAM,2CAAmB,UAAnB,CAAhB;AAAgD,IAAA,aAAa,EAAE;AAA/D,GAAzC,CAAP;AACD,C", "sourcesContent": ["#! /usr/bin/env node\n\nimport { InvalidConfigurationError, log } from \"builder-util\"\nimport chalk from \"chalk\"\nimport { getElectronVersion } from \"app-builder-lib/out/electron/electronVersion\"\nimport { pathExists, readJson } from \"fs-extra\"\nimport isCi from \"is-ci\"\nimport * as path from \"path\"\nimport { loadEnv } from \"read-config-file\"\nimport updateNotifier from \"update-notifier\"\nimport { ExecError } from \"builder-util/out/util\"\nimport { build, configureBuildCommand, createYargs } from \"../builder\"\nimport { createSelfSignedCert } from \"./create-self-signed-cert\"\nimport { configureInstallAppDepsCommand, installAppDeps } from \"./install-app-deps\"\nimport { start } from \"./start\"\nimport { nodeGypRebuild } from \"app-builder-lib/out/util/yarn\"\n\n// tslint:disable:no-unused-expression\ncreateYargs()\n  .command([\"build\", \"*\"], \"Build\", configureBuildCommand, wrap(build))\n  .command(\"install-app-deps\", \"Install app deps\", configureInstallAppDepsCommand, wrap(installAppDeps))\n  .command(\"node-gyp-rebuild\", \"Rebuild own native code\", configureInstallAppDepsCommand /* yes, args the same as for install app deps */, wrap(rebuildAppNativeCode))\n  .command(\"create-self-signed-cert\", \"Create self-signed code signing cert for Windows apps\",\n    yargs => yargs\n      .option(\"publisher\", {\n        alias: [\"p\"],\n        type: \"string\",\n        requiresArg: true,\n        description: \"The publisher name\",\n      })\n      .demandOption(\"publisher\"),\n    wrap(argv => createSelfSignedCert(argv.publisher)))\n  .command(\"start\", \"Run application in a development mode using electron-webpack\",\n    yargs => yargs,\n    wrap(() => start()))\n  .help()\n  .epilog(`See ${chalk.underline(\"https://electron.build\")} for more documentation.`)\n  .strict()\n  .recommendCommands()\n  .argv\n\nfunction wrap(task: (args: any) => Promise<any>) {\n  return (args: any) => {\n    checkIsOutdated()\n    loadEnv(path.join(process.cwd(), \"electron-builder.env\"))\n      .then(() => task(args))\n      .catch(error => {\n        process.exitCode = 1\n        // https://github.com/electron-userland/electron-builder/issues/2940\n        process.on(\"exit\", () => process.exitCode = 1)\n        if (error instanceof InvalidConfigurationError) {\n          log.error(null, error.message)\n        }\n        else if (!(error instanceof ExecError) || !error.alreadyLogged) {\n          log.error({stackTrace: error.stack}, error.message)\n        }\n      })\n  }\n}\n\nfunction checkIsOutdated() {\n  if (isCi || process.env.NO_UPDATE_NOTIFIER != null) {\n    return\n  }\n\n  readJson(path.join(__dirname, \"..\", \"..\", \"package.json\"))\n    .then(async it => {\n      if (it.version === \"0.0.0-semantic-release\") {\n        return\n      }\n\n      const packageManager = await pathExists(path.join(__dirname, \"..\", \"..\", \"package-lock.json\")) ? \"npm\" : \"yarn\"\n\n      const notifier = updateNotifier({pkg: it})\n      if (notifier.update != null) {\n        notifier.notify({\n          message: `Update available ${chalk.dim(notifier.update.current)}${chalk.reset(\" → \")}${chalk.green(notifier.update.latest)} \\nRun ${chalk.cyan(`${packageManager} upgrade electron-builder`)} to update`\n        })\n      }\n    })\n    .catch(e => log.warn({error: e}, \"cannot check updates\"))\n}\n\nasync function rebuildAppNativeCode(args: any) {\n  const projectDir = process.cwd()\n  // this script must be used only for electron\n  return nodeGypRebuild(args.platform, args.arch, {version: await getElectronVersion(projectDir), useCustomDist: true})\n}\n"], "sourceRoot": ""}