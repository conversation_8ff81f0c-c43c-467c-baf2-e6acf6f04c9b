{"version": 3, "sources": ["../../src/cli/install-app-deps.ts"], "names": [], "mappings": ";;;;;;;;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;;;AAIA;AACM,SAAU,8BAAV,CAAyC,KAAzC,EAA0D;AAC9D;AACA;AACA,SAAO,KAAK,CACT,mBADI,CACgB;AACnB,4BAAwB;AADL,GADhB,EAIJ,MAJI,CAIG,UAJH,EAIe;AAClB,IAAA,OAAO,EAAE,CAAC,OAAD,EAAU,QAAV,EAAoB,OAApB,CADS;AAElB,IAAA,OAAO,EAAE,OAAO,CAAC,QAFC;AAGlB,IAAA,WAAW,EAAE;AAHK,GAJf,EASJ,MATI,CASG,MATH,EASW;AACd,IAAA,OAAO,EAAE,sCAAkB,MAAlB,CAAyB,KAAzB,CADK;AAEd,IAAA,OAAO,EAAE,OAAO,CAAC,IAAR,KAAiB,KAAjB,GAAyB,QAAzB,GAAoC,OAAO,CAAC,IAFvC;AAGd,IAAA,WAAW,EAAE;AAHC,GATX,CAAP;AAcD;AAED;;;AACO,eAAe,cAAf,CAA8B,IAA9B,EAAuC;AAC5C,MAAI;AACF,uBAAI,IAAJ,CAAS;AAAC,MAAA,OAAO;AAAR,KAAT,EAAqC,kBAArC;AACD,GAFD,CAGA,OAAO,CAAP,EAAU;AACR;AACA,QAAI,EAAE,CAAC,YAAY,cAAf,CAAJ,EAAoC;AAClC,YAAM,CAAN;AACD;AACF;;AAED,QAAM,UAAU,GAAG,OAAO,CAAC,GAAR,EAAnB;AACA,QAAM,eAAe,GAAG,KAAI,eAAJ,EAAS,MAAM,4CAAqB,yBAAS,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,cAAtB,CAAT,CAArB,CAAf,CAAxB;AACA,QAAM,MAAM,GAAG,MAAM,yBAAU,UAAV,EAAsB,IAAtB,EAA4B,IAA5B,EAAkC,eAAlC,CAArB;AACA,QAAM,CAAC,MAAD,EAAS,OAAT,IAAoB,MAAM,OAAO,CAAC,GAAR,CAAoB,CAClD,0CAA2B,UAA3B,EAAuC,wBAAI,MAAM,CAAC,WAAX,EAAwB,EAAE,IAAI,EAAG,CAAC,GAAlC,CAAvC,CADkD,EAElD,2CAAmB,UAAnB,EAA+B,MAA/B,EAAuC,eAAvC,CAFkD,CAApB,CAAhC,CAd4C,CAmB5C;;AACA,QAAM,8BAAiB,MAAjB,EAAyB,MAAzB,EAAiC;AACrC,IAAA,aAAa,EAAE;AAAC,MAAA,OAAD;AAAU,MAAA,aAAa,EAAE;AAAzB,KADsB;AAErC,IAAA,QAAQ,EAAE,IAAI,CAAC,QAFsB;AAGrC,IAAA,IAAI,EAAE,IAAI,CAAC,IAH0B;AAIrC,IAAA,cAAc,EAAE,qDAAyB,MAAzB,EAAiC,IAAjC;AAJqB,GAAjC,EAKH,MAAM,KAAK,UALR,CAAN;AAMD;;AAED,SAAS,IAAT,GAAa;AACX,SAAO,cAAc,CAAC,8BAA8B,CAAC,gBAAD,CAA9B,CAAsC,IAAvC,CAArB;AACD;;AAED,IAAI,OAAO,CAAC,IAAR,KAAiB,MAArB,EAA6B;AAC3B,qBAAI,IAAJ,CAAS,6DAAT;;AACA,EAAA,IAAI,GACD,KADH,CACS,4BADT;AAED,C", "sourcesContent": ["#! /usr/bin/env node\n\nimport { log, use, getArchCliNames } from \"builder-util\"\nimport { printErrorAndExit } from \"builder-util/out/promise\"\nimport { computeDefaultAppDirectory, getConfig } from \"app-builder-lib/out/util/config\"\nimport { getElectronVersion } from \"app-builder-lib/out/electron/electronVersion\"\nimport { createLazyProductionDeps } from \"app-builder-lib/out/util/packageDependencies\"\nimport { installOrRebuild } from \"app-builder-lib/out/util/yarn\"\nimport { readJson } from \"fs-extra\"\nimport { Lazy } from \"lazy-val\"\nimport * as path from \"path\"\nimport { orNullIfFileNotExist } from \"read-config-file\"\nimport yargs from \"yargs\"\n\ndeclare const PACKAGE_VERSION: string\n\n/** @internal */\nexport function configureInstallAppDepsCommand(yargs: yargs.Argv): yargs.Argv {\n  // https://github.com/yargs/yargs/issues/760\n  // demandOption is required to be set\n  return yargs\n    .parserConfiguration({\n      \"camel-case-expansion\": false,\n    })\n    .option(\"platform\", {\n      choices: [\"linux\", \"darwin\", \"win32\"],\n      default: process.platform,\n      description: \"The target platform\",\n    })\n    .option(\"arch\", {\n      choices: getArchCliNames().concat(\"all\"),\n      default: process.arch === \"arm\" ? \"armv7l\" : process.arch,\n      description: \"The target arch\",\n    })\n}\n\n/** @internal */\nexport async function installAppDeps(args: any) {\n  try {\n    log.info({version: PACKAGE_VERSION}, \"electron-builder\")\n  }\n  catch (e) {\n    // error in dev mode without babel\n    if (!(e instanceof ReferenceError)) {\n      throw e\n    }\n  }\n\n  const projectDir = process.cwd()\n  const packageMetadata = new Lazy(() => orNullIfFileNotExist(readJson(path.join(projectDir, \"package.json\"))))\n  const config = await getConfig(projectDir, null, null, packageMetadata)\n  const [appDir, version] = await Promise.all<string>([\n    computeDefaultAppDirectory(projectDir, use(config.directories, it => it!.app)),\n    getElectronVersion(projectDir, config, packageMetadata),\n  ])\n\n  // if two package.json — force full install (user wants to install/update app deps in addition to dev)\n  await installOrRebuild(config, appDir, {\n    frameworkInfo: {version, useCustomDist: true},\n    platform: args.platform,\n    arch: args.arch,\n    productionDeps: createLazyProductionDeps(appDir, null),\n  }, appDir !== projectDir)\n}\n\nfunction main() {\n  return installAppDeps(configureInstallAppDepsCommand(yargs).argv)\n}\n\nif (require.main === module) {\n  log.warn(\"please use as subcommand: electron-builder install-app-deps\")\n  main()\n    .catch(printErrorAndExit)\n}"], "sourceRoot": ""}