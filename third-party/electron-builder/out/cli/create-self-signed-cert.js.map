{"version": 3, "sources": ["../../src/cli/create-self-signed-cert.ts"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;;;;;AAEA;AACO,eAAe,oBAAf,CAAoC,SAApC,EAAqD;AAC1D,QAAM,MAAM,GAAG,KAAI,qBAAJ,EAAW,yBAAX,CAAf;AACA,QAAM,SAAS,GAAG,OAAO,CAAC,GAAR,EAAlB;AACA,QAAM,UAAU,GAAG,IAAI,CAAC,IAAL,CAAU,MAAM,MAAM,CAAC,UAAP,CAAkB;AAAC,IAAA,MAAM,EAAE;AAAT,GAAlB,CAAhB,EAAyE,iCAAiB,SAAjB,CAAzE,CAAnB;AACA,QAAM,GAAG,GAAG,GAAG,UAAU,MAAzB;AACA,QAAM,GAAG,GAAG,GAAG,UAAU,MAAzB;;AAEA,qBAAI,IAAJ,CAAS,iBAAM,IAAN,CAAW,uFAAX,CAAT;;AAEA,MAAI;AACF,UAAM,0BAAU,IAAI,CAAC,OAAL,CAAa,UAAb,CAAV,CAAN;AACA,UAAM,UAAU,GAAG,IAAI,CAAC,IAAL,CAAU,MAAM,2CAAhB,EAAqC,YAArC,EAAmD,OAAO,CAAC,IAA3D,CAAnB;AACA,UAAM,yBAAK,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,cAAtB,CAAL,EACJ,CAAC,IAAD,EAAO,IAAP,EAAa,GAAb,EAAkB,IAAlB,EAAwB,MAAM,WAAW,CAAC,SAAD,CAAW,EAApD,EAAwD,MAAxD,EAAgE,mBAAhE,EAAqF,KAArF,EAA4F,KAA5F,EAAmG,GAAnG,EAAwG,GAAxG,CADI,CAAN;AAGA,UAAM,GAAG,GAAG,IAAI,CAAC,IAAL,CAAU,SAAV,EAAqB,GAAG,iCAAiB,SAAjB,CAA2B,MAAnD,CAAZ;AACA,UAAM,0BAAe,GAAf,CAAN;AACA,UAAM,yBAAK,IAAI,CAAC,IAAL,CAAU,UAAV,EAAsB,aAAtB,CAAL,EAA2C,CAAC,MAAD,EAAS,GAAT,EAAc,MAAd,EAAsB,GAAtB,EAA2B,MAA3B,EAAmC,GAAnC,CAA3C,CAAN;;AACA,uBAAI,IAAJ,CAAS;AAAC,MAAA,IAAI,EAAE;AAAP,KAAT,EAAsB,gFAAtB;;AAEA,UAAM,YAAY,GAAG,oCAArB;;AACA,uBAAI,IAAJ,CAAS;AAAC,MAAA,IAAI,EAAE,GAAP;AAAY,MAAA;AAAZ,KAAT,EAAoC,gGAApC;;AACA,UAAM,0BAAM,gBAAN,EAAwB,CAAC,YAAD,EAAe,iBAAf,EAAkC,UAAlC,EAA8C,uBAA9C,EAAuE,WAAvE,EAAoF,IAAI,GAAG,GAA3F,EAAgG,oBAAhG,EAAsH,EAAtH,CAAxB,CAAN;AACD,GAdD,SAeQ;AACN,UAAM,MAAM,CAAC,OAAP,EAAN;AACD;AACF;;AAED,SAAS,WAAT,CAAqB,CAArB,EAA8B;AAC5B,MAAI,CAAC,CAAC,CAAC,QAAF,CAAW,GAAX,CAAD,IAAoB,CAAC,CAAC,CAAC,QAAF,CAAW,GAAX,CAAzB,EAA0C;AACxC,WAAO,CAAP;AACD;;AAED,SAAO,IAAI,CAAC,CAAC,OAAF,CAAU,IAAV,EAAgB,KAAhB,CAAsB,GAAjC;AACD,C", "sourcesContent": ["import { exec, log, spawn, TmpDir } from \"builder-util\"\nimport { unlinkIfExists } from \"builder-util/out/fs\"\nimport chalk from \"chalk\"\nimport { getSignVendorPath } from \"app-builder-lib/out/codeSign/windowsCodeSign\"\nimport { ensureDir } from \"fs-extra\"\nimport * as path from \"path\"\nimport sanitizeFileName from \"sanitize-filename\"\n\n/** @internal */\nexport async function createSelfSignedCert(publisher: string) {\n  const tmpDir = new TmpDir(\"create-self-signed-cert\")\n  const targetDir = process.cwd()\n  const tempPrefix = path.join(await tmpDir.getTempDir({prefix: \"self-signed-cert-creator\"}), sanitizeFileName(publisher))\n  const cer = `${tempPrefix}.cer`\n  const pvk = `${tempPrefix}.pvk`\n\n  log.info(chalk.bold('When asked to enter a password (\"Create Private Key Password\"), please select \"None\".'))\n\n  try {\n    await ensureDir(path.dirname(tempPrefix))\n    const vendorPath = path.join(await getSignVendorPath(), \"windows-10\", process.arch)\n    await exec(path.join(vendorPath, \"makecert.exe\"),\n      [\"-r\", \"-h\", \"0\", \"-n\", `CN=${quoteString(publisher)}`, \"-eku\", \"1.3.6.1.5.5.7.3.3\", \"-pe\", \"-sv\", pvk, cer])\n\n    const pfx = path.join(targetDir, `${sanitizeFileName(publisher)}.pfx`)\n    await unlinkIfExists(pfx)\n    await exec(path.join(vendorPath, \"pvk2pfx.exe\"), [\"-pvk\", pvk, \"-spc\", cer, \"-pfx\", pfx])\n    log.info({file: pfx}, `created. Please see https://electron.build/code-signing how to use it to sign.`)\n\n    const certLocation = \"Cert:\\\\LocalMachine\\\\TrustedPeople\"\n    log.info({file: pfx, certLocation}, `importing. Operation will be succeed only if runned from root. Otherwise import file manually.`)\n    await spawn(\"powershell.exe\", [\"-NoProfile\", \"-NonInteractive\", \"-Command\", \"Import-PfxCertificate\", \"-FilePath\", `\"${pfx}\"`, \"-CertStoreLocation\", \"\"])\n  }\n  finally {\n    await tmpDir.cleanup()\n  }\n}\n\nfunction quoteString(s: string): string {\n  if (!s.includes(\",\") && !s.includes('\"')) {\n    return s\n  }\n\n  return `\"${s.replace(/\"/g, '\\\\\"')}\"`\n}"], "sourceRoot": ""}