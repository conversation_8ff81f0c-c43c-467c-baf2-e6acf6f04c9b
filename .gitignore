src/native-lib/node_modules
src/native-lib/build
/dist
.idea/
.idea/workspace.xml
/build
yarn.lock
ULOG.*

third-party/social_security_sdk/Debug/
third-party/social_security_sdk/Release/
third-party/social_security_sdk/x64/
third-party/social_security_sdk/.vs/

third-party/libffi/Release
third-party/libffi/Debug



data/


test-lis/node_modules/
test/lisdata

subject/common/node_modules/
node_modules/
*.dll

SiInterface.ini
.DS_Store
._.DS_Store
si.ini
hnhisbridgedemo_n.pbd
#*.exe

IniFileName.ini
lisdata/git


third-party/electron/dist
#package-lock.json
third-party/native-utils/.vs/
third-party/native-utils/native-utils/Debug/
third-party/native-utils/native-utils/Release/
third-party/native-utils/Release/
third-party/native-utils/Debug/
third-party/native-utils/native-utils/x64/
third-party/native-utils/x64
third-party/native-utils/native-utils-test/x64/
third-party/native-utils/native-utils-test/Release
third-party/native-utils/native-utils-test/Debug

.vs/

test/TestNativeLib/Release
test/TestNativeLib/Debug
test/TestNativeLib/x64
test/TestNativeLib/TestNativeLib/Debug
test/TestNativeLib/TestNativeLib/Release
test/TestNativeLib/TestNativeLib/x64
test/TestNativeLib/TestNativeLib/*.dll
test/TestNativeLib/TestNativeLib/*.ico
test/TestNativeLib/TestNativeLib/*.pbl
test/TestNativeLib/TestNativeLib/*.srw

test/TestNativeLib/TestConsoleApp/Debug
test/TestNativeLib/TestConsoleApp/Release
test/TestNativeLib/TestConsoleApp/x64
abc-server/Debug/
abc-server/Release/
abc-server/x64/


ABCBluetooth/Debug/
ABCBluetooth/Release/
ABCBluetooth/x64/
ABCBluetooth/bin/*
!ABCBluetooth/bin/res
!ABCBluetooth/bin/res.zip
!ABCBluetooth/bin/zip-res.bat
ABCBluetooth/abc-bluetooth.log


third-party/DuiLib/Build
third-party/DuiLib/Release

abc-screenshot.log

abcscreenshot/Debug
abcscreenshot/Release
abcscreenshot/bin/*
!abcscreenshot/bin/abcscreenshot-res
!abcscreenshot/bin/zip-res.bat


abc-uidisgner/Debug
abc-uidisgner/Release
abc-uidisgner/bin/*
!abc-uidisgner/bin/abc-uidisgner.exe

third-party/abc-lib/Debug
third-party/abc-lib/Release

third-party/NIM_Duilib_Framework/tmp
third-party/NIM_Duilib_Framework/out
third-party/NIM_Duilib_Framework/bin

abc-remote-helper/Screenshot/Debug/
abc-remote-helper/Screenshot/Release/
abc-remote-helper/Screenshot/x64/
abc-remote-helper/log/
abc-remote-helper/bin/log
abc-remote-helper/bin/ABC远程协助.exe
abc-remote-helper/bin/*.pdb
abc-remote-helper/bin/*.ilk
abc-remote-helper/bin/*.ipdb
abc-remote-helper/bin/*.iobj

!abc-remote-helper/bin/zip-res.bat

abc-remote-helper/Debug/
abc-remote-helper/Release/
latest.json
upgrade.txt


asar-updater/Debug/
asar-updater/Release/
asar-updater/bin/

tools/abc-cypress-launcher/Debug/
tools/abc-cypress-launcher/Release/
tools/abc-cypress-launcher/bin/

abc-service/Debug/
abc-service/Release/
abc-service/X64/
abc-service/bin/*.iobj
abc-service/bin/ABC打印服务.*


third-party/sqlite3/node_modules
third-party/sqlite3/build
third-party/sqlite3/build-*
third-party/sqlite3/lib/binding

.run/
abc-bluetooth.log

third-party/bsdiff-node/build/*

third-party/abc-vox-desktop-addon/dist
tools/stack_decoder/dist
!bin/ABC远程协助/*
!bin/ABC截图.exe
!bin/ABC数字医疗云服务.exe
!bin/ABC专网安全.exe
!bin/libusb_tunnel_so.dll

electron-tmp-cache

launch.json
tasks.json
.history
.windsurf