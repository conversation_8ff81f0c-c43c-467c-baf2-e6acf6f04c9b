const path = require('path');

module.exports = function (plop) {
    // 页面生成器
    plop.setGenerator('page', {
        description: '创建新的页面',
        prompts: [
            {
                type: 'input',
                name: 'name',
                message: '页面名称 (kebab-case, 例如: user-profile):',
                validate: function (value) {
                    if (!value) {
                        return '页面名称不能为空';
                    }

                    // 检查是否为kebab-case格式
                    if (!/^[a-z][a-z0-9]*(-[a-z0-9]+)*$/.test(value)) {
                        return '页面名称必须是kebab-case格式 (例如: user-profile, login, voice-record)';
                    }

                    // 检查页面是否已存在
                    const fs = require('fs');
                    const pagePath = path.resolve(__dirname, 'src/pages', value);
                    if (fs.existsSync(pagePath)) {
                        return `页面 "${value}" 已存在`;
                    }

                    return true;
                }
            }
        ],
        actions: [
            {
                type: 'add',
                path: 'src/pages/{{name}}/index.js',
                templateFile: 'plop-templates/index.js.hbs'
            },
            {
                type: 'add',
                path: 'src/pages/{{name}}/index.vue',
                templateFile: 'plop-templates/index.vue.hbs'
            }
        ]
    });
};
