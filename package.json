{"name": "abc-mira-desktop", "version": "0.0.1", "asarUpdateMinVersion": "0.0.1", "description": "ABC医生助手", "main": "dist/src/main.js", "productName": "ABCVoxDesktop", "author": {"name": "成都字节流科技有限公司"}, "scripts": {"build": "npm run buildAddon && node buildscripts/prebuild.js && tsc --build tsconfig.json", "buildAddon": "cd third-party\\abc-vox-desktop-addon&&npm run buildAndPack && cd ..\\..", "copyAddon": "copy /y third-party\\abc-vox-desktop-addon\\dist_for_zip\\abc-vox-desktop-addon.zip resources\\abc-plugins\\abc-vox-desktop-addon.zip&&node buildscripts/modify-addon-plugin-conf.js", "buildAddonAndCopy": "npm run buildAddon&&npm run copyAddon", "dev": "node buildscripts/prebuild.js && concurrently \"tsc -w\" \"cd third-party\\abc-vox-desktop-addon&&npm run dev\"", "dev:mac": "node buildscripts/prebuild.js && concurrently \"tsc -w\" \"cd third-party/abc-vox-desktop-addon&&npm run dev\"", "start": "electron .", "pack": "electron-builder --dir", "dist": "electron-builder -lwm", "dist-win-64": "npm run build && electron-builder -w --x64 && npm run packResources64 && npm run pack-dev-ext-libs && npm run pack-ext-dlls", "dist-win-32": "npm run buildAddonAndCopy && npm run build && electron-builder -w --ia32 && npm run packResources && npm run pack-dev-ext-libs && npm run pack-ext-dlls", "dist-mac": "electron-builder -m", "gyp-win-32": "set HOME=~/.electron-gyp && node-gyp rebuild --target=5.0.7 --arch=ia32 --target_arch=ia32 --dist-url=https://electronjs.org/headers", "gyp-win-64": "set HOME=~/.electron-gyp && node-gyp rebuild --target=5.0.7 --arch=x64 --dist-url=https://electronjs.org/headers", "postinstall": "electron-builder install-app-deps", "packResources64": "node buildscripts/packResoures.js --arch=64", "packResources": "node buildscripts/packResoures.js", "pack-dev-ext-libs": "node buildscripts/packDevExtLibs.js", "uploadPackDevExtLibs": "webpack --config ./buildscripts/oss-upload-dev-modules.js", "buildForExtDlls": "node buildscripts/prebuild.js && tsc --build tsconfig.json", "pack-ext-dlls": "node buildscripts/packExtDlls.js", "uploadPackExtDlls": "webpack --config ./buildscripts/oss-upload-ext-dlls.js", "uploadTest": "webpack --config ./buildscripts/oss-upload.js --uploadTarget=test  --jsbundle=true --appExe=true", "uploadTestJSBundle": "webpack --config ./buildscripts/oss-upload.js --uploadTarget=test --jsbundle=true --appExe=false", "uploadTestAppExe": "webpack --config ./buildscripts/oss-upload.js --uploadTarget=test --jsbundle=false --appExe=true", "uploadRelease": "此命令危险，不要运行， 用于发布正式版本，确定要发布时移除这段话再运行 run webpack --config ./buildscripts/oss-upload.js --uploadTarget=release  --jsbundle=true --appExe=true", "uploadReleaseToLatest": "webpack --config ./buildscripts/oss-update-latest.js --arch=ia32", "uploadRelease64ToLatest": "webpack --config ./buildscripts/oss-update-latest.js --arch=x64 --updatePluginInfo=false", "uploadCI": "webpack --config ./buildscripts/oss-upload.js --jsbundle=true --appExe=true", "uploadX64CI": "webpack --config ./buildscripts/oss-upload.js --jsbundle=true --appExe=true --arch=64"}, "build": {"appId": "cn.abcyun.app.desktop", "productName": "ABC医生助手", "artifactName": "abcyun-desktop-${os}-${arch}-${version}.${ext}", "directories": {"buildResources": "resources"}, "files": ["node_modules/**", "dist/src/**", "dist/static/**", "abc-conf", "!src"], "win": {"target": [{"target": "nsis"}], "requestedExecutionLevel": "highestAvailable", "signingHashAlgorithms": ["sha256"], "rfc3161TimeStampServer": "http://timestamp.sectigo.com", "certificateSubjectName": "CN=成都字节流科技有限公司, O=成都字节流科技有限公司, S=四川省, C=CN", "publisherName": "成都字节流科技有限公司", "signDlls": true}, "nsis": {"oneClick": false, "perMachine": true, "allowElevation": false, "allowToChangeInstallationDirectory": false, "createDesktopShortcut": true, "createStartMenuShortcut": true, "installerSidebar": "resources/icon_side_bar.bmp", "uninstallerSidebar": "resources/icon_side_bar.bmp", "include": "buildscripts\\installer.nsh", "script": "buildscripts\\installer.nsi", "unicode": true}, "releaseInfo": {"releaseNotes": "0.0.1 正式版"}, "extraResources": [{"from": "./resources/flat_resources/", "to": "flat_resources", "filter": ["**/*"]}, {"from": "./resources/bin/", "to": "bin", "filter": ["**/*"]}, {"from": "./resources/abc-plugins/", "to": "abc-plugins", "filter": ["**/*"]}, {"from": "./resources/conf/", "to": "conf", "filter": ["**/*"]}, {"from": "./bin/", "to": "../", "filter": ["**/*"]}]}, "dependencies": {"@electron/remote": "^2.1.2", "adm-zip": "^0.5.2", "bsdiff-node": "^2.5.0", "cors": "^2.8.5", "electron-log": "^4.3.1", "electron-reload": "^2.0.0-alpha.1", "formidable": "^2.0.1", "fs-extra": "^9.1.0", "iconv-lite": "^0.6.2", "ini": "^1.3.5", "lodash": "^4.17.20", "socks-proxy-agent": "^7.0.0", "unzipper": "^0.10.11", "x2js": "^3.3.0", "yaml": "^2.2.1"}, "devDependencies": {"@babel/core": "^7.13.10", "@babel/preset-env": "^7.7.6", "@babel/preset-react": "^7.7.4", "@babel/preset-typescript": "^7.8.3", "@types/electron": "^1.6.10", "@types/node": "^22.13.5", "@typescript-eslint/eslint-plugin": "^3.4.0", "@typescript-eslint/parser": "^3.4.0", "@vue/compiler-sfc": "^3.0.4", "abc-vox-desktop-addon": "file:third-party/abc-vox-desktop-addon", "ali-oss": "^6.17.1", "axios": "^0.27.2", "babel-loader": "^8.2.2", "case-sensitive-paths-webpack-plugin": "^2.2.0", "chai": "^4.2.0", "concurrently": "^6.5.1", "copy-webpack-plugin": "^5.1.2", "css-loader": "^5.1.3", "electron": "^37.2.4", "electron-builder": "^26.0.12", "eslint": "^7.7.0", "eslint-plugin-prettier": "^3.1.4", "ftp-srv": "4.6.3", "husky": "^4.3.0", "iconv": "^3.0.1", "json-loader": "^0.5.7", "mocha": "^8.2.1", "node-adodb": "^5.0.3", "node-gyp": "5.0.3", "node-poppler": "^6.2.7", "oracledb": "^6.1.0", "pg": "^8.14.1", "style-loader": "^2.0.0", "ts-loader": "^8.4.0", "typescript": "^4.9.4"}}