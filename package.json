{"name": "abc-vox-web", "version": "1.0.0", "license": "MIT", "scripts": {"build": "export NODE_OPTIONS=\"--max-old-space-size=6144\" && export RSPACK_CONFIG_VALIDATE=loose && rspack build --config build/rspack.prod.config.js", "dev": "rspack serve --config build/rspack.dev.config.js", "page": "plop page", "postinstall": "npx husky install"}, "dependencies": {"@abc/ui-pc": "1.429.2", "@abc/utils": "1.2.1025", "@abc/utils-date": "1.2.1026", "@abc/utils-dom": "1.7.379", "axios": "0.15.3", "crypto-js": "^4.2.0", "google-libphonenumber": "^3.2.42", "jquery": "^3.2.1", "pinia": "2.0.15", "socket.io-client": "^4.8.1", "vue": "2.7.14"}, "devDependencies": {"@babel/core": "^7.22.17", "@babel/eslint-parser": "^7.19.1", "@babel/parser": "^7.13.13", "@babel/plugin-proposal-class-properties": "^7.17.12", "@babel/plugin-proposal-decorators": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0", "@babel/plugin-proposal-function-sent": "^7.0.0", "@babel/plugin-proposal-json-strings": "^7.0.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.0", "@babel/plugin-proposal-numeric-separator": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.16.0", "@babel/plugin-proposal-optional-chaining": "^7.16.0", "@babel/plugin-proposal-throw-expressions": "^7.0.0", "@babel/plugin-syntax-dynamic-import": "^7.0.0", "@babel/plugin-syntax-import-meta": "^7.0.0", "@babel/plugin-syntax-jsx": "^7.0.0", "@babel/plugin-transform-runtime": "^7.22.15", "@babel/preset-env": "^7.22.15", "@babel/runtime": "^7.14.0", "@rspack/cli": "1.4.0", "@rspack/core": "1.4.0", "@vue/babel-helper-vue-jsx-merge-props": "1.2.1", "@vue/babel-preset-jsx": "1.2.4", "abc-fed-build-tool": "^0.8.5", "babel-eslint": "^10.1.0", "babel-loader": "^9.1.3", "css-loader": "^6.6.0", "css-minimizer-webpack-plugin": "^3.4.1", "eslint": "^8.29.0", "eslint-plugin-abc": "0.2.0", "eslint-plugin-vue": "^9.8.0", "html-webpack-plugin": "^5.6.0", "husky": "^6.0.0", "plop": "^3.1.1", "postcss-import": "^11.0.0", "postcss-loader": "^6.2.1", "postcss-url": "^8.x", "sass": "1.69.7", "sass-loader": "7.x", "stylelint": "^13.12.0", "stylelint-config-recess-order": "^2.3.0", "stylelint-config-standard": "^21.0.0", "stylelint-order": "^4.1.0", "stylelint-scss": "^3.19.0", "vue-eslint-parser": "^9.1.0", "vue-loader": "^15", "vue-style-loader": "^4.1.3", "webpack": "^5.90.3", "webpack-merge": "^5.8.0"}, "lint-staged": {"*.{js,jsx,vue}": ["eslint --fix"], "*.{scss,css,vue}": ["stylelint --config ./.stylelintrc --fix"]}}