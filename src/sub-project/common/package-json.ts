/**
 *
 * @type {{name: string, asarUpdateMinVersion: string, version: string, description: string, main: string, productName: string, author: {name: string}, scripts: {start: string, pack: string, dist: string, "dist-win-64": string, "dist-win-32": string, "dist-mac": string, "gyp-win-32": string, "gyp-win-64": string, postinstall: string, test: string, packResources: string}, build: {appId: string, productName: string, artifactName: string, directories: {buildResources: string}, files: *, win: {target: *, requestedExecutionLevel: string}, nsis: {oneClick: boolean, perMachine: boolean, allowElevation: boolean, allowToChangeInstallationDirectory: boolean, createDesktopShortcut: boolean, createStartMenuShortcut: boolean, installerSidebar: string, uninstallerSidebar: string, include: string, script: string}, publish: *, releaseInfo: {releaseNotes: string}, extraResources: *}, abcAsarUpdateUrl: string, dependencies: {"adm-zip": string, common: string, "electron-context-menu": string, "electron-log": string, "electron-promise-ipc": string, "electron-reload": string, "electron-updater": string, "fs-extra": string, hl7: string, iconv: string, ini: string, lodash: string, "native-lib": string, nedb: string, ping: string, x2js: string}, devDependencies: {chai: string, electron: string, "electron-builder": string, eslint: string, "eslint-plugin-prettier": string, husky: string, mocha: string, "node-gyp": string}}}
 */
const packageJson = require('../../../../package.json');

export {packageJson}

