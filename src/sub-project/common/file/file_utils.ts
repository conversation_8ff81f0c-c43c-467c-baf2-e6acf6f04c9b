import {Stats} from "fs";

let fs = require('fs');
const path = require('path');

export class FileUtils {

    /**
     * 读取路径信息
     * @param {string} path 路径
     */
    static getStat(path: string): Promise<Stats | false> {
        return new Promise((resolve) => {
            fs.stat(path, (err: any, stats: any) => {
                if (err) {
                    resolve(false);
                } else {
                    resolve(stats);
                }
            })
        })
    }


    /**
     * 创建路径
     * @param {string} dir 路径
     */
    static mkdir(dir: string) {
        return new Promise((resolve) => {
            fs.mkdir(dir, (err: any) => {
                if (err) {
                    resolve(false);
                } else {
                    resolve(true);
                }
            })
        })
    }

    static mkdirSync(dir: string) {
        return fs.mkdirSync(dir);
    }

    /**
     * 路径是否存在，不存在则创建
     * @param {string} dir 路径
     */
    static async dirExists(dir: string) {
        let isExists = await FileUtils.getStat(dir);
        //如果该路径且不是文件，返回true
        if (isExists && isExists.isDirectory()) {
            return true;
        } else if (isExists) {     //如果该路径存在但是文件，返回false
            return false;
        }
        //如果该路径不存在
        let tempDir = path.parse(dir).dir;      //拿到上级路径
        //递归判断，如果上级目录也不存在，则会代码会在此处继续循环执行，直到目录存在
        let status = await FileUtils.dirExists(tempDir);
        let mkdirStatus;
        if (status) {
            mkdirStatus = await FileUtils.mkdir(dir);
        }
        return mkdirStatus;
    }

    static dirExistsSync(dir: string, createIfNotExist: boolean) {
        let stat = null;
        try {
            stat = fs.statSync(dir);
        } catch (e) {
        }

        //如果该路径且不是文件，返回true
        if (stat && stat.isDirectory()) {
            return true;
        } else if (stat) {     //如果该路径存在但是文件，返回false
            return false;
        }

        if (createIfNotExist) {
            fs.mkdirSync(dir, {recursive: true});
        }

        return createIfNotExist ?? false;
    }


    /**
     * 判断文件是否存存
     * @param file
     * @returns {boolean} true文件存在,false文件不存在
     */
    static fileExist(file: string) {
        try {
            let ignore = fs.statSync(file);
            return true;
        } catch (e) {
        }

        return false;
    }


    /**
     * 获取文件大小
     * @param file
     */
    static fileSizeSync(file: string): number {
        const stats = fs.statSync(file)
        return stats.size;
    }

    /**
     *
     * @param filename{string}
     * @param encode {string |undefined}
     * @returns {string}
     */
    static readFileAsString(filename: string, encode?: string) {
        if (!encode || encode === 'utf8')
            return fs.readFileSync(filename, 'utf8');

        const Iconv = require('iconv').Iconv;
        const gbk_to_utf8 = new Iconv(encode, 'UTF8');
        const buffer = gbk_to_utf8.convert(fs.readFileSync(filename));

        return buffer.toString();
    }

    /**
     *
     * 读取指定目录下的所有文件
     * @param dir {string}
     * @returns {Promise<string[]>}
     */
    static lsDir(dir: string): Promise<string[]> {
        return new Promise((resolve, reject) => {
            fs.readdir(dir, (err: any, files: string[]) => {
                if (err) reject(err);
                else resolve(files);
            });
        });
    }

    /**
     *
     * @param filename {string}
     * @param content{string}
     * @param [encode] {string}
     *
     * @returns Promise<boolean>
     */
    static writeFileAsString(filename: string, content: string, encode?: string) {
        return new Promise((resolve, reject) => {
            encode = encode ? encode : 'utf8';
            fs.writeFile(filename, content, (err: any) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(true);
                }
            });
        });


    }

    /**
     * 异步读取文件
     * @param filename{string}
     * @param encode
     * @returns {Promise<string>}
     */
    static readFileAsStringAsync(filename: string, encode?: string): Promise<string> {
        return new Promise((resolve, reject) => {
            if (!encode || encode === 'utf8') {
                fs.readFile(filename, 'utf8', (err: any, data: string) => {
                    if (err) reject(err);
                    else resolve(data);
                });
            } else {
                resolve("支持的编码类型");
            }
        });

    }


    /**
     *
     * @param filename {string}
     * @param content {string}
     * @param encode {string | undefined} , value can be "utf8"
     * @return {Promise<boolean>}, success true, otherwise reject
     */
    static appendStrToFile(filename: string, content: string, encode?: string): Promise<boolean> {
        return new Promise((resolve, reject) => {
            fs.appendFile(filename, content, (err: any) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(true);
                }
            });
        });
    }


    /**
     * 删除文件夹功能
     * @param  {string} url  文件路径，绝对路径
     * @return {void}
     */
    static async deleteDir(url: string) {
        let files: string[] = [];
        if (await FileUtils.fileExist(url)) {  //判断给定的路径是否存在
            files = await FileUtils.lsDir(url);   //返回文件和子目录的数组
            for (let file of files) {
                let curPath = path.join(url, file);
                if (fs.statSync(curPath).isDirectory()) { //同步读取文件夹文件，如果是文件夹，则函数回调
                    await FileUtils.deleteDir(curPath);
                } else {
                    await fs.unlinkSync(curPath);    //是指定文件，则删除
                }
            }

            await fs.rmdirSync(url); //清除文件夹
        } else {
            console.log("给定的路径不存在！");
        }
    }

    /**
     * 删除文件
     * @param file
     */
    static deleteFileSync(file: string): void {
        try {
            fs.unlinkSync(file);    //是指定文件，则删除
        } catch (ignored) {

        }
    }

    static deleteDirSync(url: string) {
        let files = [];
        if (FileUtils.fileExist(url)) {  //判断给定的路径是否存在
            files = fs.readdirSync(url);   //返回文件和子目录的数组
            for (let file of files) {
                let curPath = path.join(url, file);
                if (fs.statSync(curPath).isDirectory()) { //同步读取文件夹文件，如果是文件夹，则函数回调
                    FileUtils.deleteDirSync(curPath);
                } else {
                    fs.unlinkSync(curPath);    //是指定文件，则删除
                }
            }

            fs.rmdirSync(url); //清除文件夹
        } else {
            console.log("给定的路径不存在！");
        }
    }
}
