import {FileUtils, logger, NetworkUtils, packageJson, PathUtils} from "../common";
import {abcConfig} from "../conf/abc-conf"
import {AddonLatestPluginInfo, AddonPluginConf, AddonPluginInfoWithLocalEntryUrl} from "./addon-plugin-conf";
import * as fs from "fs";
import {GrayEnv, kUrlConfig} from "../../constants";

const kTAG = 'AddonPluginManager';


/**
 * 插件管理，用于下载更新插件资源
 */
class AddonPluginManager {
    private _onlineLatestPluginList: AddonLatestPluginInfo[] = [];
    private _embedPluginsConfig?: {
        plugins: {
            name: string,
            buildTime: number
        }[]
    };

    private _env: GrayEnv = GrayEnv.PROD;

    public constructor(env: GrayEnv) {
        try {
            this._env = env;
            const confStr = fs.readFileSync(`${this._embedPluginRootDir()}/conf.json`).toString();
            this._embedPluginsConfig = JSON.parse(confStr);
        } catch (e) {
        }
    }

    public getPluginEntryUrl(name: string): string | null {
        const conf = this.getPluginInfo(name);
        return conf?.localEntry;
    }

    public getPluginInfo(name: string): AddonPluginInfoWithLocalEntryUrl | null {
        const rootDir = this._getPluginRootDir(name);
        const confFile = rootDir + "/conf.json";
        if (!FileUtils.fileExist(confFile))
            return null;

        const jsonContent = FileUtils.readFileAsString(confFile);
        const conf: AddonPluginConf = JSON.parse(jsonContent);

        return {
            ...conf,
            localEntry: `${rootDir}/${conf.entry}`
        }
    }


    public syncPreparePlugin(name: string): void {
        logger.log(kTAG, `同步准备插件 ${name}`);
        let config = this.getPluginInfo(name);
        let embedConf = this._embedPluginsConfig?.plugins?.find(it => it.name === name);
        logger.log(kTAG, `同步准备插件 ${name}, 内置配置 = ${JSON.stringify(embedConf)}， 当前配置=${JSON.stringify(config)}`);
        //插件不存在或是内置版本更新从本地复制一份过去
        if (!config || this._buildTimeCompare(config.buildTime, embedConf.buildTime) < 0 || !FileUtils.fileExist(config.localEntry)) {
            this._copyFromEmbed(name);
        }
    }

    /**
     * 准备插件
     * @param name 插件名称
     * @param checkUpgrade 检查并升级
     * @return boolean true表示有更新发生
     */
    public async preparePlugin(name: string, checkUpgrade = true): Promise<boolean> {
        logger.log(kTAG, `准备插件 ${name}， 检查更新:${checkUpgrade}`);

        let config = this.getPluginConf(name);
        //插件不存在，从本地复制一份过去
        if (!config) {
            this._copyFromEmbed(name);
        }

        //判断是否要更新
        const pluginUpdateEnable = abcConfig.pluginAutoUpdate ?? true;
        if (!pluginUpdateEnable || !checkUpgrade) return false;

        //重新获取配置
        config = this.getPluginConf(name);
        await this._refreshLatestPluginList().catch(error => null);
        logger.log(kTAG, `插件${name}当前配置： ${JSON.stringify(config)}`);
        const latestPluginInfo = this._onlineLatestPluginList.find(item => item.name === name);

        logger.log(kTAG, `插件${name}最新配置 = ${JSON.stringify(latestPluginInfo)}`)
        //本地版本与线上版本不一致
        if (latestPluginInfo && this._buildTimeCompare(config.buildTime, latestPluginInfo.buildTime) < 0) {
            return await this._updatePlugin(latestPluginInfo);
        }

        //没有更新
        return false;
    }

    private _buildTimeCompare(buildTime1?: string | number, buildTime2?: string | number) {
        let buildTime1Num = buildTime1 ? Number(buildTime1) : 0;
        let buildTime2Num = buildTime2 ? Number(buildTime2) : 0;

        return buildTime1Num - buildTime2Num;
    }

    /**
     * 获取插件对应的地址
     * @param name 插件名称
     * @param waitUpdate 是否等更新完成
     */
    public async getPluginUrl(name: string, waitUpdate = true): Promise<string> {
        await this.preparePlugin(name, false);
        let entryUrl = await this.getPluginEntryUrl(name);

        const prepareResult = this.preparePlugin(name, true);
        if (!entryUrl && !waitUpdate) {
            return entryUrl;
        }

        return this.getPluginEntryUrl(name);
    }

    public getPluginConf(name: string): AddonPluginConf | null {
        const rootDir = this._getPluginRootDir(name);
        const confFile = rootDir + "/conf.json";
        if (!FileUtils.fileExist(confFile)) {
            logger.warn(kTAG, `未找到插件${name}对应的配置文件:${confFile}`)
            return null;
        }

        let jsonContent = null;
        try {
            jsonContent = FileUtils.readFileAsString(confFile);
            return JSON.parse(jsonContent);
        } catch (e) {
            logger.warn(kTAG, `插件${name}, 配置文件解析失败：${confFile}, 内容:${jsonContent}`);
        }
    }

    private _getPluginRootDir(name: string): string {
        let subEnv = "prod";
        switch (this._env) {
            case GrayEnv.RC:
                subEnv = "rc";
                break;
            case GrayEnv.GRAY:
                subEnv = "gray";
                break;
            default:
                break;
        }

        return `${PathUtils.getAppPluginRootDir()}/${abcConfig.env}/${name}/${subEnv}`;
    }


    /**
     * 获取最新插件列表
     * @private
     */
    private async _refreshLatestPluginList(): Promise<void> {
        const urlPrefix = kUrlConfig[abcConfig.env];
        //0:正式， 1: 预发布, 2:灰度
        const latestApiUrl = `${urlPrefix}/api/v2/app/plugin/latest?appId=abcyun-mira-pc&version=${packageJson.version}&platform=3&env=${this._env}`
        const latestInfo = await NetworkUtils.getAsString(latestApiUrl);
        logger.info(kTAG, `下载插件列表:${latestApiUrl}, 最新插件配置 = ${latestInfo}`);
        this._onlineLatestPluginList = JSON.parse(latestInfo).data?.list ?? [];
    }


    private _embedPluginRootDir(): string {
        const dir = `${PathUtils.getResourcesDir()}/abc-plugins`;
        logger.log(kTAG, `内置插件目录：${dir}`);
        return dir;
    }

    /**
     * 从内置包里复制插件到指定位置
     * @param name
     * @private
     */
    private _copyFromEmbed(name: string) {
        const srcZip = `${this._embedPluginRootDir()}/${name}.zip`;
        if (!FileUtils.fileExist(srcZip)) {
            logger.error(kTAG, `查找内置版本插件${name} 失败`);
            return;
        }

        logger.log(kTAG, `首次加载 ${name}，从内置包复制, srcZip = ${srcZip}`);
        const targetDir = this._getPluginRootDir(name);
        FileUtils.dirExistsSync(targetDir, true);
        logger.log(kTAG, `复制 ${name}完成，开始解压, srcZip = ${srcZip}, targetDir = ${targetDir}`);

        const AdmZip = require('adm-zip');
        const zip = new AdmZip(srcZip);
        zip.extractAllTo(targetDir, true);

        logger.log(kTAG, `解压完成 ${name}`);
    }

    /**
     * 下载更新插件
     * @param plugin 最新的插件配置
     * @private 更新成功返回true,否则返回false
     */
    private async _updatePlugin(plugin: AddonLatestPluginInfo): Promise<boolean> {
        logger.log(kTAG, `更新插件 name ${plugin.name}, info = ${JSON.stringify(plugin)}`);

        //下载最新插件
        const tmpFile = `${PathUtils.tmpDir()}/${plugin.name}-${this._env}.zip`;
        await NetworkUtils.downloadFile({
            url: plugin.url,
            md5: plugin.md5,
            filePath: tmpFile
        });

        //下载后解压
        const rootDir = await this._getPluginRootDir(plugin.name);

        try {
            await FileUtils.deleteDir(rootDir);
        } catch (e) {
            logger.error(kTAG, `插件删除失败, error = ${e.message}`);
        }

        logger.log(kTAG, `插件下载完成开始解压 name ${plugin.name}, info = ${JSON.stringify(plugin)}`);
        const AdmZip = require('adm-zip');
        const zip = new AdmZip(tmpFile);
        zip.extractAllTo(rootDir, true);

        //更新完成后，做一次校验
        const latestConfig = await this.getPluginConf(plugin.name);
        if (latestConfig.buildTime?.toString() !== plugin.buildTime?.toString()) {
            logger.error(kTAG, `插件更新后检查失败, 构建时间（buildTime)不一致，配置(${plugin.buildTime}, 配置文件中为:${latestConfig.buildTime}`);
        }

        return true;
    }

    deletePlugin(kPluginAddonName: string) {
        //下载后解压
        const rootDir = this._getPluginRootDir(kPluginAddonName);
        FileUtils.deleteDirSync(rootDir);
    }
}

export {AddonPluginManager}
