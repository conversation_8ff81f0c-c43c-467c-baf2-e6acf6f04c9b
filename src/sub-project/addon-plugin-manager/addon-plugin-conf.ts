export interface AddonPluginConf {
    buildTime: string;
    name: string;
    entry: string;
    repoTag: string;
    width?: number;
    height?: number;
}

export interface AddonLatestPluginInfo {
    name: string;
    buildTime: string;
    env: number;
    version: string;
    url: string;
    md5: string;
}

export interface AddonPluginInfoWithLocalEntryUrl extends AddonPluginConf {
    localEntry: string;
}