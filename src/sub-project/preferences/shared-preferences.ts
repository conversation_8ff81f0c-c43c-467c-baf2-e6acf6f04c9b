//lis设备相关的数据存储在这个目录下
import {FileUtils, PathUtils} from "../common";

const kPreferencesDataFile = PathUtils.getDataDir() + "/preferences.dat";

class SharedPreferences {
    _preferences: {[key:string]:any} = {}
    constructor() {

        try {
            let content = FileUtils.readFileAsString(kPreferencesDataFile, undefined);
            this._preferences = JSON.parse(content);
        } catch (e) {
        }

        if (!this._preferences)
            this._preferences = {};
    }

    /**
     *
     * @param key {string}
     * @param data {any}
     */
    setObject(key:string, data:any) {
        this._preferences[key] = data;

        this._save();
    }

    /**
     *获取对应的值
     * @param key {string}
     * @return any
     */
    getObject(key:string) {
        return this._preferences[key];
    }


    /**
     *
     * @param key {string}
     * @param value{boolean}
     */
    setBool(key:string, value:string) {
        this.setObject(key, value);
    }

    /**
     *
     * @param key {string}
     * @returns [{bool}]
     */
    getBool(key:string) {
        return this.getObject(key);
    }

    /**
     * 保存文件
     * @private
     */
    _save() {
        FileUtils.writeFileAsString(kPreferencesDataFile, JSON.stringify(this._preferences)).catch();
    }
}

const sharedPreferences = new SharedPreferences();
export {sharedPreferences}
