/**
 * 认证相关类型定义
 */

/**
 * 用户信息接口
 */
export interface UserInfo {
  /** 用户ID */
  id: string;
  /** 用户昵称 */
  nickname?: string;
  /** 手机号 */
  phone?: string;
  /** 头像URL */
  avatar?: string;
  /** 登录方式 */
  loginType: 'wechat' | 'sms';
  /** 创建时间 */
  createdAt?: string;
  /** 更新时间 */
  updatedAt?: string;
}

/**
 * 登录数据接口
 */
export interface LoginData {
  /** 访问令牌 */
  token: string;
  /** 刷新令牌 */
  refreshToken: string;
  /** 用户信息 */
  user: UserInfo;
  /** 令牌过期时间（秒） */
  expiresIn?: number;
}

/**
 * 认证状态接口
 */
export interface AuthState {
  /** 是否已登录 */
  isLoggedIn: boolean;
  /** 用户信息 */
  user: UserInfo | null;
  /** 访问令牌 */
  token: string | null;
  /** 登录时间戳 */
  loginTime: number | null;
  /** 令牌过期时间戳 */
  expiresAt: number | null;
}

/**
 * IPC 响应接口
 */
export interface IPCResponse<T = any> {
  /** 是否成功 */
  success: boolean;
  /** 响应数据 */
  data?: T;
  /** 错误信息 */
  error?: string;
}

/**
 * 微信扫码登录状态
 */
export type WechatScanStatus = 'pending' | 'scanned' | 'confirmed' | 'expired' | 'cancelled';

/**
 * 微信扫码登录数据
 */
export interface WechatLoginData {
  /** 二维码ID */
  qrCodeId: string;
  /** 二维码URL */
  qrCodeUrl: string;
  /** 扫码状态 */
  status: WechatScanStatus;
  /** 用户信息（确认后） */
  userInfo?: {
    nickname: string;
    avatar: string;
  };
}

/**
 * 短信登录数据
 */
export interface SmsLoginData {
  /** 手机号 */
  phone: string;
  /** 验证码 */
  code: string;
}

/**
 * 认证事件类型
 */
export interface AuthEvents {
  /** 认证状态变更 */
  'auth:state-changed': AuthState;
  /** 登录成功 */
  'auth:login-success': { user: UserInfo };
  /** 登出 */
  'auth:logout': {};
  /** Token 刷新 */
  'auth:token-refreshed': { token: string; expiresAt: number };
  /** 需要刷新 Token */
  'auth:token-refresh-needed': { refreshToken: string };
}

/**
 * 窗口管理器接口
 */
export interface WindowManager {
  /** 获取所有窗口 */
  getAllWindows(): Electron.BrowserWindow[];
  /** 根据 WebContents 获取窗口 */
  getWindowByWebContents(webContents: Electron.WebContents): Electron.BrowserWindow | null;
  /** 创建窗口 */
  createWindow(options: Electron.BrowserWindowConstructorOptions): Electron.BrowserWindow;
}

/**
 * 共享偏好设置接口
 */
export interface SharedPreferences {
  /** 设置字符串值 */
  setString(key: string, value: string): void;
  /** 获取字符串值 */
  getString(key: string, defaultValue?: string | null): string | null;
  /** 设置长整型值 */
  setLong(key: string, value: number): void;
  /** 获取长整型值 */
  getLong(key: string, defaultValue?: number | null): number | null;
  /** 设置布尔值 */
  setBoolean(key: string, value: boolean): void;
  /** 获取布尔值 */
  getBoolean(key: string, defaultValue?: boolean): boolean;
  /** 移除键值 */
  remove(key: string): void;
  /** 清除所有数据 */
  clear(): void;
}

/**
 * Electron API 接口
 */
export interface ElectronAPI {
  /** 调用主进程方法 */
  invoke(channel: string, data?: any): Promise<IPCResponse>;
  /** 发送消息到主进程 */
  send(channel: string, data?: any): void;
  /** 监听主进程事件 */
  on(channel: string, callback: (data: any) => void): void;
  /** 移除事件监听 */
  off(channel: string, callback: (data: any) => void): void;
}

/**
 * 全局 Window 接口扩展
 */
declare global {
  interface Window {
    /** Electron API */
    electronAPI?: ElectronAPI;
  }
}

/**
 * 认证相关常量
 */
export const AUTH_CONSTANTS = {
  /** 存储键名 */
  STORAGE_KEYS: {
    TOKEN: 'mira_auth_token',
    REFRESH_TOKEN: 'mira_auth_refresh_token',
    USER_INFO: 'mira_auth_user_info',
    EXPIRES_AT: 'mira_auth_expires_at',
    LOGIN_TIME: 'mira_auth_login_time'
  },
  
  /** 登录方式 */
  LOGIN_TYPES: {
    WECHAT: 'wechat' as const,
    SMS: 'sms' as const
  },
  
  /** Token 刷新阈值（毫秒） */
  TOKEN_REFRESH_THRESHOLD: 10 * 60 * 1000, // 10分钟
  
  /** Token 自动刷新提前时间（毫秒） */
  TOKEN_AUTO_REFRESH_ADVANCE: 5 * 60 * 1000, // 5分钟
  
  /** 默认 Token 过期时间（秒） */
  DEFAULT_TOKEN_EXPIRES_IN: 7200 // 2小时
} as const;
