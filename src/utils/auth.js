/**
 * 用户认证工具类
 */
export class AuthManager {
    constructor() {
        this.TOKEN_KEY = 'mira_token';
        this.USER_KEY = 'mira_user';
        this.REFRESH_TOKEN_KEY = 'mira_refresh_token';
    }

    /**
   * 保存用户登录信息
   * @param {Object} loginData 登录返回的数据
   */
    saveLoginData(loginData) {
        const {
            token, refreshToken, user, 
        } = loginData;
    
        if (token) {
            localStorage.setItem(this.TOKEN_KEY, token);
        }
    
        if (refreshToken) {
            localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
        }
    
        if (user) {
            localStorage.setItem(this.USER_KEY, JSON.stringify(user));
        }
    }

    /**
   * 获取访问令牌
   * @returns {string|null}
   */
    getToken() {
        return localStorage.getItem(this.TOKEN_KEY);
    }

    /**
   * 获取刷新令牌
   * @returns {string|null}
   */
    getRefreshToken() {
        return localStorage.getItem(this.REFRESH_TOKEN_KEY);
    }

    /**
   * 获取用户信息
   * @returns {Object|null}
   */
    getUser() {
        const userStr = localStorage.getItem(this.USER_KEY);
        if (userStr) {
            try {
                return JSON.parse(userStr);
            } catch (error) {
                console.error('解析用户信息失败:', error);
                return null;
            }
        }
        return null;
    }

    /**
   * 检查是否已登录
   * @returns {boolean}
   */
    isLoggedIn() {
        return !!this.getToken();
    }

    /**
   * 清除登录信息
   */
    clearLoginData() {
        localStorage.removeItem(this.TOKEN_KEY);
        localStorage.removeItem(this.REFRESH_TOKEN_KEY);
        localStorage.removeItem(this.USER_KEY);
    }

    /**
   * 登出
   */
    logout() {
        this.clearLoginData();
    // 可以在这里添加其他登出逻辑，比如通知服务器
    }
}

// 创建单例实例
export const authManager = new AuthManager();

/**
 * 手机号验证工具
 */
export const phoneValidator = {
    /**
   * 验证手机号格式
   * @param {string} phone 手机号
   * @returns {boolean}
   */
    isValid(phone) {
        const phoneRegex = /^1[3-9]\d{9}$/;
        return phoneRegex.test(phone);
    },

    /**
   * 格式化手机号显示（中间4位用*代替）
   * @param {string} phone 手机号
   * @returns {string}
   */
    format(phone) {
        if (!phone || phone.length !== 11) {
            return phone;
        }
        return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    },
};

/**
 * 验证码相关工具
 */
export const codeValidator = {
    /**
   * 验证验证码格式（6位数字）
   * @param {string} code 验证码
   * @returns {boolean}
   */
    isValid(code) {
        const codeRegex = /^\d{6}$/;
        return codeRegex.test(code);
    },
};

/**
 * 倒计时工具
 */
export class CountdownTimer {
    constructor(duration = 60) {
        this.duration = duration;
        this.remaining = 0;
        this.timer = null;
        this.callbacks = [];
    }

    /**
   * 开始倒计时
   * @param {Function} callback 每秒回调函数
   */
    start(callback) {
        if (this.timer) {
            this.stop();
        }

        this.remaining = this.duration;
    
        if (callback) {
            this.callbacks.push(callback);
        }

        this.timer = setInterval(() => {
            this.remaining--;
      
            // 执行所有回调
            this.callbacks.forEach((cb) => cb(this.remaining));
      
            if (this.remaining <= 0) {
                this.stop();
            }
        }, 1000);

        // 立即执行一次回调
        this.callbacks.forEach((cb) => cb(this.remaining));
    }

    /**
   * 停止倒计时
   */
    stop() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
        this.remaining = 0;
        this.callbacks = [];
    }

    /**
   * 是否正在倒计时
   * @returns {boolean}
   */
    isRunning() {
        return !!this.timer;
    }
}

