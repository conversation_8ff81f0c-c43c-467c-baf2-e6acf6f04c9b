/**
 * IPC 客户端通信工具
 * 负责渲染进程与主进程之间的通信
 */

import { useAuthStore } from '@/stores/auth';

/**
 * IPC 客户端类
 */
export class IPCClient {
    constructor() {
        this.authStore = null;
        this.eventListeners = new Map();
        this._setupEventListeners();
    }

    /**
   * 初始化 IPC 客户端
   * @param {Object} authStore - 认证状态管理 store
   */
    initialize(authStore) {
        this.authStore = authStore;
        console.log('IPC 客户端已初始化');
    }

    /**
   * 设置事件监听器
   * @private
   */
    _setupEventListeners() {
        if (!window.electronAPI) {
            console.warn('Electron API 不可用，运行在开发模式');
            return;
        }

        // 监听认证状态变更
        window.electronAPI.on('auth:state-changed', (authState) => {
            console.log('收到认证状态变更事件:', authState);
            if (this.authStore) {
                this.authStore.updateAuthState(authState);
            }
        });

        // 监听登录成功事件
        window.electronAPI.on('auth:login-success', (data) => {
            console.log('收到登录成功事件:', data);
            this._triggerEvent('auth:login-success', data);
        });

        // 监听登出事件
        window.electronAPI.on('auth:logout', () => {
            console.log('收到登出事件');
            this._triggerEvent('auth:logout', {});
        });

        // 监听 Token 刷新事件
        window.electronAPI.on('auth:token-refreshed', (data) => {
            console.log('收到 Token 刷新事件:', data);
            if (this.authStore) {
                this.authStore.token = data.token;
                this.authStore.expiresAt = data.expiresAt;
            }
            this._triggerEvent('auth:token-refreshed', data);
        });
    }

    /**
   * 调用主进程方法
   * @param {string} channel - 通道名称
   * @param {*} data - 数据
   * @returns {Promise<*>}
   */
    async invoke(channel, data) {
        if (!window.electronAPI) {
            console.warn(`IPC 调用失败: ${channel}，Electron API 不可用`);
            return {
                success: false, error: 'Electron API 不可用', 
            };
        }

        try {
            console.log(`IPC 调用: ${channel}`, data);
            const result = await window.electronAPI.invoke(channel, data);
            console.log(`IPC 响应: ${channel}`, result);
            return result;
        } catch (error) {
            console.error(`IPC 调用失败: ${channel}`, error);
            return {
                success: false, error: error.message, 
            };
        }
    }

    /**
   * 发送消息到主进程
   * @param {string} channel - 通道名称
   * @param {*} data - 数据
   */
    send(channel, data) {
        if (!window.electronAPI) {
            console.warn(`IPC 发送失败: ${channel}，Electron API 不可用`);
            return;
        }

        try {
            console.log(`IPC 发送: ${channel}`, data);
            window.electronAPI.send(channel, data);
        } catch (error) {
            console.error(`IPC 发送失败: ${channel}`, error);
        }
    }

    /**
   * 监听事件
   * @param {string} eventName - 事件名称
   * @param {Function} callback - 回调函数
   */
    on(eventName, callback) {
        if (!this.eventListeners.has(eventName)) {
            this.eventListeners.set(eventName, []);
        }
        this.eventListeners.get(eventName).push(callback);
    }

    /**
   * 移除事件监听
   * @param {string} eventName - 事件名称
   * @param {Function} callback - 回调函数
   */
    off(eventName, callback) {
        if (!this.eventListeners.has(eventName)) return;
    
        const listeners = this.eventListeners.get(eventName);
        const index = listeners.indexOf(callback);
        if (index > -1) {
            listeners.splice(index, 1);
        }
    }

    /**
   * 触发事件
   * @private
   */
    _triggerEvent(eventName, data) {
        if (!this.eventListeners.has(eventName)) return;
    
        const listeners = this.eventListeners.get(eventName);
        listeners.forEach((callback) => {
            try {
                callback(data);
            } catch (error) {
                console.error(`事件回调执行失败: ${eventName}`, error);
            }
        });
    }

    /**
   * 认证相关 API
   */
    auth = {
    /**
     * 登录
     * @param {Object} loginData - 登录数据
     */
        login: (loginData) => this.invoke('auth:login', loginData),

        /**
     * 登出
     */
        logout: () => this.invoke('auth:logout'),

        /**
     * 获取用户信息
     */
        getUserInfo: () => this.invoke('auth:getUserInfo'),

        /**
     * 获取认证状态
     */
        getStatus: () => this.invoke('auth:getStatus'),

        /**
     * 刷新 Token
     */
        refreshToken: () => this.invoke('auth:refreshToken'),
    };

    /**
   * 窗口相关 API
   */
    window = {
    /**
     * 关闭窗口
     */
        close: () => this.invoke('window:close'),

        /**
     * 最小化窗口
     */
        minimize: () => this.invoke('window:minimize'),

        /**
     * 最大化窗口
     */
        maximize: () => this.invoke('window:maximize'),

        /**
     * 恢复窗口
     */
        restore: () => this.invoke('window:restore'),
    };
}

// 创建全局 IPC 客户端实例
export const ipcClient = new IPCClient();

/**
 * Vue 插件：安装 IPC 客户端
 */
export const IPCPlugin = {
    install(app) {
    // 将 IPC 客户端添加到全局属性
        app.config.globalProperties.$ipc = ipcClient;
    
        // 提供 IPC 客户端
        app.provide('ipc', ipcClient);
    },
};

/**
 * Composition API：使用 IPC 客户端
 */
export function useIPC() {
    return ipcClient;
}

/**
 * 初始化 IPC 通信
 * 在应用启动时调用
 */
export async function initializeIPC() {
    try {
    // 获取认证 store
        const authStore = useAuthStore();
    
        // 初始化 IPC 客户端
        ipcClient.initialize(authStore);
    
        // 初始化认证状态
        await authStore.initializeAuth();
    
        console.log('IPC 通信初始化完成');
    } catch (error) {
        console.error('IPC 通信初始化失败:', error);
    }
}

/**
 * 设置 Electron API 到 window 对象
 * 用于开发环境模拟
 */
export function setupElectronAPI() {
    if (window.electronAPI) return;
  
    // 开发环境下的模拟 API
    window.electronAPI = {
        invoke: async (channel, data) => {
            console.log(`模拟 IPC 调用: ${channel}`, data);
      
            // 模拟不同的响应
            switch (channel) {
                case 'auth:getStatus':
                    const token = localStorage.getItem('mira_token');
                    const user = localStorage.getItem('mira_user');
          
                    if (token && user) {
                        return {
                            success: true,
                            data: {
                                isLoggedIn: true,
                                token,
                                user: JSON.parse(user),
                                loginTime: Date.now(),
                                expiresAt: Date.now() + 2 * 60 * 60 * 1000,
                            },
                        };
                    } 
                    return {
                        success: true,
                        data: {
                            isLoggedIn: false,
                            user: null,
                            token: null,
                        },
                    };
          
          
                case 'auth:login':
                    localStorage.setItem('mira_token', data.token);
                    localStorage.setItem('mira_user', JSON.stringify(data.user));
                    return {
                        success: true,
                        data: {
                            isLoggedIn: true, user: data.user, 
                        }, 
                    };
          
                case 'auth:logout':
                    localStorage.removeItem('mira_token');
                    localStorage.removeItem('mira_user');
                    return { success: true };
          
                case 'window:close':
                    window.close();
                    return { success: true };
          
                default:
                    return {
                        success: true, data: null, 
                    };
            }
        },
    
        send: (channel, data) => {
            console.log(`模拟 IPC 发送: ${channel}`, data);
        },
    
        on: (channel, callback) => {
            console.log(`模拟 IPC 监听: ${channel}`);
            // 在开发环境下不需要实际监听
        },
    };
  
    console.log('开发环境 Electron API 已设置');
}

