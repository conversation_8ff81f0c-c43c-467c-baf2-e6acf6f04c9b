/* eslint-disable */
/**
 * Author <PERSON> <<EMAIL>>
 * 频率控制 返回函数连续调用时，fn 执行频率限定为每多少时间执行一次
 * @param fn {function}  需要调用的函数
 * @param delay  {number}    延迟时间，单位毫秒
 * @param immediate  {boolean} 给 immediate参数传递false 绑定的函数先执行，而不是delay后后执行。
 * @return {function}实际调用函数
 */
export function throttle(fn, delay, immediate = false, debounce) {
    let curr = +new Date(), //当前事件
        lastCall = 0,
        lastExec = 0,
        timer = null,
        diff, //时间差
        context, //上下文
        args;
    const exec = function () {
        lastExec = curr;
        fn.apply(context, args);
    };
    return function () {
        curr = +new Date();
        (context = this), (args = arguments), (diff = curr - (debounce ? lastCall : lastExec) - delay);
        clearTimeout(timer);
        if (debounce) {
            if (immediate) {
                timer = setTimeout(exec, delay);
            } else if (diff >= 0) {
                exec();
            }
        } else {
            if (diff >= 0) {
                exec();
            } else if (immediate) {
                timer = setTimeout(exec, -diff);
            }
        }
        lastCall = curr;
    };
}

/**
 * 空闲控制 返回函数连续调用时，空闲时间必须大于或等于 delay，fn 才会执行
 * @param fn {function}  要调用的函数
 * @param delay   {number}    空闲时间
 * @param immediate  {boolean} 给 immediate参数传递false 绑定的函数先执行，而不是delay后后执行。
 * @return {function}实际调用函数
 */
export function debounce(fn, delay, immediate) {
    return throttle(fn, delay, immediate, true);
}

/**
 * Author Jason Yang <<EMAIL>>
 *   可以深度比较两个对象
 * @return {Boolean}
 */
export function isEqual(x, y) {
    //标识是否相似
    let i, l, leftChain, rightChain;

    function compare2Objects(x, y) {
        let p;

        // remember that NaN === NaN returns false
        // and isNaN(undefined) returns true
        if (isNaN(x) && isNaN(y) && typeof x === 'number' && typeof y === 'number') {
            return true;
        }

        // Compare primitives and functions.
        // Check if both arguments link to the same object.
        // Especially useful on the step where we compare prototypes
        if (x === y) {
            return true;
        }

        // Works in case when functions are created in constructor.
        // Comparing dates is a common scenario. Another built-ins?
        // We can even handle functions passed across iframes
        if (
            (typeof x === 'function' && typeof y === 'function') ||
            (x instanceof Date && y instanceof Date) ||
            (x instanceof RegExp && y instanceof RegExp) ||
            (x instanceof String && y instanceof String) ||
            (x instanceof Number && y instanceof Number)
        ) {
            return x.toString() === y.toString();
        }

        // At last checking prototypes as good as we can
        if (!(x instanceof Object && y instanceof Object)) {
            return false;
        }

        if (x.isPrototypeOf(y) || y.isPrototypeOf(x)) {
            return false;
        }

        if (x.constructor !== y.constructor) {
            return false;
        }

        if (x.prototype !== y.prototype) {
            return false;
        }

        // Check for infinitive linking loops
        if (leftChain.indexOf(x) > -1 || rightChain.indexOf(y) > -1) {
            return false;
        }

        // Quick checking of one object being a subset of another.
        // todo: cache the structure of arguments[0] for performance
        for (p in y) {
            if (y.hasOwnProperty(p) !== x.hasOwnProperty(p)) {
                return false;
            } if (typeof y[p] !== typeof x[p]) {
                return false;
            }
        }

        for (p in x) {
            if (y.hasOwnProperty(p) !== x.hasOwnProperty(p)) {
                return false;
            } if (typeof y[p] !== typeof x[p]) {
                return false;
            }

            switch (typeof x[p]) {
                case 'object':
                case 'function':
                    leftChain.push(x);
                    rightChain.push(y);

                    if (!compare2Objects(x[p], y[p])) {
                        return false;
                    }

                    leftChain.pop();
                    rightChain.pop();
                    break;

                default:
                    if (x[p] !== y[p]) {
                        return false;
                    }
                    break;
            }
        }

        return true;
    }

    if (arguments.length < 1) {
        return true; //Die silently? Don't know how to handle such case, please help...
        // throw "Need two or more arguments to compare";
    }

    for (i = 1, l = arguments.length; i < l; i++) {
        leftChain = []; //Todo: this can be cached
        rightChain = [];

        if (!compare2Objects(arguments[0], arguments[i])) {
            return false;
        }
    }

    return true;
}

export function isEmptyObject(obj) {
    if (obj.length && obj.length > 0) {
        return false;
    }
    for (const key in obj) {
        if (hasOwnProperty.call(obj, key)) {
            return false;
        }
    }
    return true;
}

/*
* 比较两个数组项是否发生变化
* @param {Array} oldArr
* @param {Array} newArr
* @param {Function} compareFn
* @return {Boolean} 是否发生变化
*/
export function compareArray(oldArr = [], newArr = [], key = 'id') {
    if (oldArr.length !== newArr.length) return true;
    // 使用字符串对比
    const oldArrString = oldArr.map((item) => item[key]).sort().toString();
    const newArrString = newArr.map((item) => item[key]).sort().toString();

    return oldArrString !== newArrString;
}

/**
 * 数组去重
 * @param arr
 */
export function unique(arr) {
    const res = [];
    const json = Object.create(null);
    for (let i = 0; i < arr.length; i++) {
        if (!json[arr[i]]) {
            res.push(arr[i]);
            json[arr[i]] = 1;
        }
    }
    return res;
}

export function uniqueWithKey(arr, prop) {
    const obj = {};
    return arr.reduce((item, next) => {
        obj[next[prop]] ? '' : (obj[next[prop]] = true && item.push(next));
        return item;
    }, []);
}

export function isObject(x) {
    return typeof x === 'object' && x !== null;
}

export function obj2arr(obj) {
    const result = [];
    for (const p in obj) {
        if (obj.hasOwnProperty(p)) result.push(obj[p]);
    }
    return result;
}

/**
 * @desc 子元素有id的 高性能数组去重
 * <AUTHOR>
 * @date 2019/07/05 11:48:47
 * @params distinctBy 根据 什么属性 去filter
 */
export function distinct(a, b, distinctBy = 'id') {
    const arr = a.concat(b);
    const result = [];
    const obj = new Map();

    for (const item of arr) {
        if (!obj.get(item[distinctBy])) {
            result.push(item);
            obj.set(item[distinctBy], true);
        }
    }
    return result;
}

/**
 * @desc 非空判断
 * <AUTHOR>
 * @date 2021/04/27 14:32:11
 * @params
 * @return
 */
export const isNull = (value) => {
    return value === undefined || value === null || value === '';
};

/**
 * @desc 从 params 里挑选出 target 所拥有的 key
 * <AUTHOR>
 * @date 2021/04/27 14:33:35
 * @params
 * @return
 */
export function pick(target, params = {}) {
    for (const key in target) {
        if (Object.prototype.hasOwnProperty.call(target, key)) {
            const value = target[ key ];
            if (isObject(value)) {
                target[ key ] = pick(value, params[ key ]);
            } else {
                target[ key ] = isNull(params[ key ]) ? target[ key ] : params[ key ];
            }
        }
        return target;
    }
}
