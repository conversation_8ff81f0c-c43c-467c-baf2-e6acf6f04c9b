/* 验证手机号 */
export async function validateMobile(value, callback, countryCode) {
    // 没有填 就不验证
    if (!value) {
        callback({ validate: true });
        return;
    }
    try {
        const {
            metadata, PhoneNumberUtil, 
        } = await import('google-libphonenumber');
        // 有区号根据区号的校验规则处理
        if (countryCode) {
            if (!PhoneNumberUtil.isViablePhoneNumber(`${value}`)) {
                return callback({
                    validate: false,
                    message: '输入正确格式的手机号',
                });
            }
            const phoneUtil = PhoneNumberUtil.getInstance();
            const number = phoneUtil.parseAndKeepRawInput(`${value}`, metadata.countryCodeToRegionCodeMap[+countryCode][0]);
            const isValidate = phoneUtil.isValidNumber(number);
            if (!isValidate) {
                return callback({
                    validate: false,
                    message: '手机号格式不正确',
                });
            }
            return callback({ validate: true });
        }

        // 手机号|座机号 正则规则集合
        const phoneRuleRegs = [
            /^1[3|4|5|6|7|8|9]\d{9}$/,
            /^(?:0[1-9][0-9]{1,2}-?)?[2-8][0-9]{6,7}$/,
            /^[6|9]\d{7}$/,
            /^[6][8|6]\d{5}$/,
            /^[0][9]\d{8}$/,
            /(^400[016789]\d{6}$)|(^400-[016789]\d{2}-\d{4}$)/, // 400开头手机号
        ];
        // 增加对座机的校验，长度为8或者长度为9（包含-），若包含区号则长度为11或12（包含-）
        // 增加对香港：9或6开头后面跟7位数字 澳门：66或68开头后面跟5位数字 台湾：09开头后面跟8位数字的校验
        // 增加对400开头的手机号的校验
        if (
            value.length !== 11 &&
            value.length !== 8 &&
            value.length !== 7 &&
            value.length !== 10 &&
            !((value.length === 9 || value.length === 12 || value.length === 13) && value.includes('-'))
        ) {
            callback({
                validate: false,
                message: '输入正确格式的手机号或座机号',
            });
        } else if (
            !phoneRuleRegs.some((reg) => reg.test(value))
        ) {
            callback({
                validate: false,
                message: '手机/座机号格式不正确',
            });
        } else {
            callback({ validate: true });
        }

    } catch {
        callback({ validate: false });
    }
}

/* 验证11位手机号码 */
export const validateMobilePhone = (value, callback) => {
    if (!value) {
        return callback({ validate: true });
    } if (value.length !== 11) {
        return callback({
            validate: false, message: '输入11位的手机号', 
        });
    } if (!/^1[3|4|5|6|7|8|9]\d{9}$/.test(value)) {
        return callback({
            validate: false, message: '手机号格式不正确', 
        });
    }
    return callback({ validate: true });

};


//短信验证码
export function validateCode(value, callback) {
    if (value.length !== 4) {
        callback({
            validate: false,
            message: '输入4位短信验证码',
        });
    } else if (!/\d{4}$/.test(value)) {
        callback({
            validate: false,
            message: '短信验证格式不正确',
        });
    } else {
        callback({ validate: true });
    }
}

//密码验证
export function validatePwd(value, callback) {
    if (value.indexOf(' ') !== -1) {
        callback({
            validate: false,
            message: '密码不能含有空格',
        });
    } else {
        // eslint-disable-next-line
        const reg = /^(?![a-zA-Z]+$)(?![.@$!%*#_~?&^\-=<>:;|\\/\[\]]+$)(?![\d]+$)[a-zA-Z\d.@$!%*#_~?&^\-=<>:;|\\/\[\]]{6,12}$/;
        if (reg.test(value)) {
            callback({
                validate: true,
            });
        } else {
            callback({
                validate: false,
                message: '密码须是6~12位英文字母/数字/字符组合',
            });
        }
    }
}
