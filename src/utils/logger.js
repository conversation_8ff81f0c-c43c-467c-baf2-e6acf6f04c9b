export const resolveErrorMessage = (err) => {
    if (!err) return '未知错误';

    if (typeof err === 'string') return err;

    try {
        return JSON.stringify(err, Object.getOwnPropertyNames(err));
    } catch (e) {
        console.error('handle error message', e);
        return err;
    }
};

function _send(data) {
    try {
        if (window.feLogger) {
            window.feLogger.send(data);
        } else {
            console.log('Logger: ', data);
        }
    } catch (e) {
        console.error('Logger.exception', e);
    }
}

/**
 * @desc 日志上报
 * <AUTHOR>
 * @date 2023-07-11 17:26:41
 */
export default class Logger {
    /**
     *  用于数据上报
     * @param scene {string} 场景值，用于区分上报场景
     * @param data {Any} 自定义数据
     */
    static report({
        scene, data, 
    }) {
        return _send({
            scene,
            data,
        });
    }

    /**
     * 用于错误上报，与 report 的区别在于多了一个 err 对象，该对象为 Error 实例，例如 try catch 中捕获的 err
     * @param scene {string} 场景值，用于区分上报场景
     * @param data {Any} 自定义数据
     * @param err {Error} Error 对象
     */
    static error({
        scene, data, err, 
    }) {
        return _send({
            scene,
            data,
            err: resolveErrorMessage(err),
        });
    }
}
