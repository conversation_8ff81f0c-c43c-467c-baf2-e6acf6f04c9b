/*
 * <AUTHOR>
 * @DateTime 2020-07-21 11:49:08
 */
import Logger from '@/utils/logger';

export function openWindow(url, options, callbacks = {}) {
    if (!window.electron) {
        return;
    }
    const { remote } = window.electron;
    const { BrowserWindow } = remote;
    options = options || {};

    if (typeof options.show === 'undefined') {
        options.show = true;
    }


    const top = remote.getCurrentWindow();
    const windowOpts = {
        parent: top,
        modal: true,
        resizable: !!(options && options.resizable),
        minimizable: !!(options && options.minimizable),
        width: options && options.width >= 0 ? options.width : 1024,
        height: options && options.height >= 0 ? options.height : 768,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            webviewTag: true,
            enableRemoteModule: true,
            preload: remote.app.jsApiFile,
        },
        show: options.show,
    };
    const win = new BrowserWindow(windowOpts);
    win.on('close', () => callbacks.close && callbacks.close());
    if (callbacks) {
        const events = Object.keys(callbacks);
        for (let i = 0; i < events.length; i++) {
            win.webContents.on(events[i], callbacks[events[i]]);
        }
    }
    win.loadURL(url);
    remote.app.registerJSAPForBrowserWindow(win);

    if (options && options.openDevTools) {
        win.webContents.openDevTools();
    }

    win.setVisibleOnAllWorkspaces(false);
    return win;
}

export function openURL(url, callback, options) {
    if (!window.electron) {
        return;
    }

    const { remote } = window.electron;
    const { BrowserWindow } = remote;
    const top = remote.getCurrentWindow();
    if (!options) {
        options = {};
    }

    const {
        nodeIntegration = true,
        contextIsolation = false,
        modal = true,
        resizable = true,
        minimizable = true,
        width = 1024,
        height = 1024,
        webPreferences = {},
    } = options;
    const windowOpts = {
        ...options,
        parent: top,
        modal,
        resizable,
        minimizable,
        width,
        height,
        webPreferences: {
            nodeIntegration,
            contextIsolation,
            webviewTag: true,
            enableRemoteModule: true,
            preload: remote.app.jsApiFile,
            ...webPreferences,
        },
        show: true,
    };

    const child = new BrowserWindow(windowOpts);
    let isTryAgain = false;
    child.on('close', () => callback && !isTryAgain && callback('close'));
    try {
        child.loadURL(url);

        if (options && options.openDevTools) {
            child.webContents.openDevTools();
        }
        return child;
    } catch (error) {
        console.log('loadURL error', error);
        isTryAgain = true;
        child.close();
        return openURL(url, callback, options); //try again
    }
}


/**
 * 获取当前客户端版本
 * <AUTHOR>
 * @date 2020-07-21
 * @returns {String}
 */
export const getAppVersion = () => {
    const userAgent = navigator.userAgent.toLowerCase();
    const arr = userAgent.match(/abcclinicdesktop\/([\d.]+)/);
    return arr ? arr[1] : '';
};

/**
 * 版本比较 ver1 > ver2
 * <AUTHOR>
 * @date 2020-07-21
 * @param {String} ver1 版本1
 * @param {String} ver2 版本2
 * @returns {Boolean} true 大于成立；false 大于不成立
 */
export const diffVersion = (ver1, ver2) => {
    const verArr1 = ver1.split('.');
    const verArr2 = ver2.split('.');
    const minL = Math.min(verArr1.length, verArr2.length);
    let cPos = 0, flag = false;
    while (cPos < minL) {
        if (verArr1[cPos] === verArr2[cPos]) {
            cPos++;
            continue;
        } else if (verArr1[cPos] > verArr2[cPos]) {
            flag = true;
            return flag;
        } else {
            flag = false;
            return flag;
        }
    }
    return cPos === minL && verArr1.length > minL;
};

/**
 * 检查版本是否支持
 * <AUTHOR>
 * @date 2020-07-21
 * @param {String} minVersion 传入最小版本号
 * @returns {Boolean} true 版本支持；false 版本不支持
 */
export const checkVersionSupport = (minVersion) => {
    const version = getAppVersion();
    return version === minVersion || diffVersion(version, minVersion);
};

/**
 * 胖客户端中切换门店时需要重新拉取对应环境的资源
 * public onSwitchClinic(options: ISwitchClinicOptions):void
 *
 * interface ISwitchClinicOptions {
 *     region?: string,
 *     env?: string,
 *     chainId?: string,
 *     clinicId?: string,
 *     employeeId?: string,
 *     onBundleUpdateProgress?:IBundleUpdateProgress;
 * }
 *
 * interface IBundleUpdateProgress {
 *     (options: {
 *         bundleCount: number; //需要更新的bundle数量
 *         bundleIndex: number; //当前更新的bundle索引
 *         bundleName: string; //当前bundle名称
 *         currentBytes: number; //当前bundle已更新的字节数
 *         currentTotalBytes: number; //当前bundle总字节数
 *         totalCurrentBytes: number; //所有已更新的bundle的总字节数
 *         totalBytes: number; //所有bundle的总字节
 *         finish: boolean; //是否全部完成
 *         error?: string; //是否出错
 *     }): void;
 * }
 * @return {Promise<boolean>}
 */
export async function getOfflineBundleByClinicInfo(options) {
    const onSwitchClinic = window.remote?.app?.onSwitchClinic;
    if (typeof onSwitchClinic !== 'function') {
        Logger.report({
            scene: 'FAT_CLIENT_LOGIN',
            data: {
                message: 'onSwitchClinic is not a function',
            },
        });
        return true;
    }
    if (options.env === 'dev') {
        options.env = 'prod';
    }
    return new Promise((resolve) => {
        onSwitchClinic({
            ...options,
            onBundleUpdateProgress: (result) => {
                const {
                    bundleCount,
                    bundleIndex,
                    bundleName,
                    currentBytes,
                    currentTotalBytes,
                    totalCurrentBytes,
                    totalBytes,
                    finish,
                } = result;

                console.log(`
                    bundleCount: ${bundleCount}
                    bundleIndex: ${bundleIndex}
                    bundleName: ${bundleName}
                    currentBytes: ${currentBytes}
                    currentTotalBytes: ${currentTotalBytes}
                    totalCurrentBytes: ${totalCurrentBytes}
                    totalBytes: ${totalBytes}
                `);

                if (finish) {
                    resolve();
                }
            },
        });
    });
}

/**
 * 根据hostname获取分区
 * @param hostname
 * @return {string}
 */
export function getRegionByHostname(hostname) {
    if (!hostname) {
        return 'region1';
    }
    const match = hostname.match(/^region(\d+)/);
    if (match && match[1]) {
        return `region${match[1]}`;
    }
    return 'region1';
}


/**
 * 获取websocket协议
 * 兼容 abcyun:// 以及 own 和 本地环境
 */
export function getWebSocketProtocol() {
    return location.protocol === 'http:' ? 'ws:' : 'wss:';
}

export function isMacOS() {
    return navigator.userAgent.includes('Macintosh');
}
