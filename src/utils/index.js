export const guid = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
        const r = Math.random() * 16 | 0,
            v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
};

/**
 * 格式化时间
 * @param ms {number} - 毫秒数
 * @returns {string} - 格式化后的时间
 */
export const formatTime = (ms) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60).toString().padStart(2, '0');
    const remainingSeconds = (seconds % 60).toString().padStart(2, '0');
    return `${minutes}:${remainingSeconds}`;
};

export const sleep = (t) => {
    return new Promise((resolve) => {
        // eslint-disable-next-line abc/no-timer-id
        setTimeout(() => resolve(), t);
    });
};