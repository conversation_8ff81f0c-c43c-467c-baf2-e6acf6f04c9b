// 中国手机号分区号码
export const CountryCodes = Object.freeze([
    {
        label: '中国大陆',
        code: '86',
        countryAreaCode: 'CN',
    },
    {
        label: '中国香港',
        code: '852',
        countryAreaCode: 'HK',
    },
    {
        label: '中国澳门',
        code: '853',
        countryAreaCode: 'MO',
    },
    {
        label: '中国台湾',
        code: '886',
        countryAreaCode: 'TW',
    },
    {
        label: '马来西亚',
        code: '60',
        countryAreaCode: 'MY',
    },
    {
        label: '菲律宾',
        code: '63',
        countryAreaCode: 'PH',
    },
    {
        label: '缅甸',
        code: '95',
        countryAreaCode: 'MM',
    },
    {
        label: '新加坡',
        code: '65',
        countryAreaCode: 'SG',
    },
    {
        label: '老挝',
        code: '856',
        countryAreaCode: 'LA',
    },
    {
        label: '泰国',
        code: '66',
        countryAreaCode: 'TH',
    },
    {
        label: '越南',
        code: '84',
        countryAreaCode: 'VN',
    },
    {
        label: '日本',
        code: '81',
        countryAreaCode: 'JP',
    },
    {
        label: '韩国',
        code: '82',
        countryAreaCode: 'KR',
    },
    {
        label: '印度尼西亚',
        code: '62',
        countryAreaCode: 'ID',
    },
    {
        label: '阿联酋',
        code: '971',
        countryAreaCode: 'AE',
    },
]);

// 支持短信验证的区号列表
export const supportSMSCountryCodeList = ['86', '852', '853', '886'];

// 默认选择中国大陆
export const defaultCountryCode = CountryCodes[0].code;
