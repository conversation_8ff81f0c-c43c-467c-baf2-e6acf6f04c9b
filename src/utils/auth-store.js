/**
 * 主进程登录状态管理模块
 * 负责用户认证状态的持久化存储和管理
 */

import { EventEmitter } from 'events';

/**
 * 用户信息接口
 * @typedef {Object} UserInfo
 * @property {string} id - 用户ID
 * @property {string} nickname - 用户昵称
 * @property {string} phone - 手机号
 * @property {string} avatar - 头像URL
 * @property {string} loginType - 登录方式 ('wechat' | 'sms')
 */

/**
 * 登录数据接口
 * @typedef {Object} LoginData
 * @property {string} token - 访问令牌
 * @property {string} refreshToken - 刷新令牌
 * @property {UserInfo} user - 用户信息
 * @property {number} expiresAt - 令牌过期时间戳
 */

/**
 * 登录状态管理类
 */
export class AuthStore extends EventEmitter {
  constructor(sharedPreferences) {
    super();
    this.sharedPreferences = sharedPreferences;
    this.refreshTimer = null;
    
    // 存储键名
    this.STORAGE_KEYS = {
      TOKEN: 'mira_auth_token',
      REFRESH_TOKEN: 'mira_auth_refresh_token',
      USER_INFO: 'mira_auth_user_info',
      EXPIRES_AT: 'mira_auth_expires_at',
      LOGIN_TIME: 'mira_auth_login_time'
    };
    
    // 初始化时检查登录状态
    this._initializeAuthState();
  }

  /**
   * 初始化认证状态
   * @private
   */
  _initializeAuthState() {
    const token = this.getToken();
    const expiresAt = this.getExpiresAt();
    
    if (token && expiresAt) {
      const now = Date.now();
      
      if (now < expiresAt) {
        // Token 未过期，设置自动刷新
        this._scheduleTokenRefresh();
        this.emit('auth:state-changed', { isLoggedIn: true, user: this.getUserInfo() });
      } else {
        // Token 已过期，清除状态
        this.clearAuthData();
        this.emit('auth:state-changed', { isLoggedIn: false, user: null });
      }
    }
  }

  /**
   * 保存登录数据
   * @param {LoginData} loginData - 登录数据
   */
  saveLoginData(loginData) {
    const { token, refreshToken, user, expiresIn = 7200 } = loginData;
    const expiresAt = Date.now() + (expiresIn * 1000);
    
    // 保存到持久化存储
    this.sharedPreferences.setString(this.STORAGE_KEYS.TOKEN, token);
    this.sharedPreferences.setString(this.STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
    this.sharedPreferences.setString(this.STORAGE_KEYS.USER_INFO, JSON.stringify(user));
    this.sharedPreferences.setLong(this.STORAGE_KEYS.EXPIRES_AT, expiresAt);
    this.sharedPreferences.setLong(this.STORAGE_KEYS.LOGIN_TIME, Date.now());
    
    // 设置自动刷新
    this._scheduleTokenRefresh();
    
    // 触发状态变更事件
    this.emit('auth:state-changed', { isLoggedIn: true, user });
    this.emit('auth:login-success', { user });
    
    console.log('登录数据已保存:', { userId: user.id, loginType: user.loginType });
  }

  /**
   * 获取访问令牌
   * @returns {string|null}
   */
  getToken() {
    return this.sharedPreferences.getString(this.STORAGE_KEYS.TOKEN, null);
  }

  /**
   * 获取刷新令牌
   * @returns {string|null}
   */
  getRefreshToken() {
    return this.sharedPreferences.getString(this.STORAGE_KEYS.REFRESH_TOKEN, null);
  }

  /**
   * 获取用户信息
   * @returns {UserInfo|null}
   */
  getUserInfo() {
    const userInfoStr = this.sharedPreferences.getString(this.STORAGE_KEYS.USER_INFO, null);
    if (userInfoStr) {
      try {
        return JSON.parse(userInfoStr);
      } catch (error) {
        console.error('解析用户信息失败:', error);
        return null;
      }
    }
    return null;
  }

  /**
   * 获取令牌过期时间
   * @returns {number|null}
   */
  getExpiresAt() {
    return this.sharedPreferences.getLong(this.STORAGE_KEYS.EXPIRES_AT, null);
  }

  /**
   * 获取登录时间
   * @returns {number|null}
   */
  getLoginTime() {
    return this.sharedPreferences.getLong(this.STORAGE_KEYS.LOGIN_TIME, null);
  }

  /**
   * 检查是否已登录
   * @returns {boolean}
   */
  isLoggedIn() {
    const token = this.getToken();
    const expiresAt = this.getExpiresAt();
    
    if (!token || !expiresAt) {
      return false;
    }
    
    return Date.now() < expiresAt;
  }

  /**
   * 获取当前认证状态
   * @returns {Object}
   */
  getAuthState() {
    const isLoggedIn = this.isLoggedIn();
    const user = isLoggedIn ? this.getUserInfo() : null;
    
    return {
      isLoggedIn,
      user,
      token: isLoggedIn ? this.getToken() : null,
      loginTime: this.getLoginTime(),
      expiresAt: this.getExpiresAt()
    };
  }

  /**
   * 刷新令牌
   * @param {string} newToken - 新的访问令牌
   * @param {string} newRefreshToken - 新的刷新令牌
   * @param {number} expiresIn - 过期时间（秒）
   */
  refreshToken(newToken, newRefreshToken, expiresIn = 7200) {
    const expiresAt = Date.now() + (expiresIn * 1000);
    
    this.sharedPreferences.setString(this.STORAGE_KEYS.TOKEN, newToken);
    this.sharedPreferences.setString(this.STORAGE_KEYS.REFRESH_TOKEN, newRefreshToken);
    this.sharedPreferences.setLong(this.STORAGE_KEYS.EXPIRES_AT, expiresAt);
    
    // 重新设置自动刷新
    this._scheduleTokenRefresh();
    
    this.emit('auth:token-refreshed', { token: newToken, expiresAt });
    console.log('Token 已刷新');
  }

  /**
   * 清除认证数据
   */
  clearAuthData() {
    // 清除定时器
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
    
    // 清除存储数据
    Object.values(this.STORAGE_KEYS).forEach(key => {
      this.sharedPreferences.remove(key);
    });
    
    // 触发状态变更事件
    this.emit('auth:state-changed', { isLoggedIn: false, user: null });
    this.emit('auth:logout', {});
    
    console.log('认证数据已清除');
  }

  /**
   * 登出
   */
  logout() {
    this.clearAuthData();
  }

  /**
   * 安排令牌自动刷新
   * @private
   */
  _scheduleTokenRefresh() {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }
    
    const expiresAt = this.getExpiresAt();
    if (!expiresAt) return;
    
    // 在过期前 5 分钟刷新
    const refreshTime = expiresAt - Date.now() - (5 * 60 * 1000);
    
    if (refreshTime > 0) {
      this.refreshTimer = setTimeout(() => {
        this.emit('auth:token-refresh-needed', {
          refreshToken: this.getRefreshToken()
        });
      }, refreshTime);
      
      console.log(`Token 将在 ${Math.round(refreshTime / 1000 / 60)} 分钟后自动刷新`);
    }
  }

  /**
   * 销毁实例
   */
  destroy() {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
    this.removeAllListeners();
  }
}

/**
 * 创建认证存储实例
 * @param {Object} sharedPreferences - 共享偏好设置实例
 * @returns {AuthStore}
 */
export function createAuthStore(sharedPreferences) {
  return new AuthStore(sharedPreferences);
}
