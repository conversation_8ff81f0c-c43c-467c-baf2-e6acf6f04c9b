/**
 * 倒计时工具
 */
export class CountdownTimer {
    constructor(duration = 60) {
        this.duration = duration;
        this.remaining = 0;
        this.timer = null;
        this.callbacks = [];
    }
    
    /**
     * 开始倒计时
     * @param {Function} callback 每秒回调函数
     */
    start(callback) {
        if (this.timer) {
            this.stop();
        }
        
        this.remaining = this.duration;
        
        if (callback) {
            this.callbacks.push(callback);
        }
        
        this.timer = setInterval(() => {
            this.remaining--;
            
            // 执行所有回调
            this.callbacks.forEach((cb) => cb(this.remaining));
            
            if (this.remaining <= 0) {
                this.stop();
            }
        }, 1000);
        
        // 立即执行一次回调
        this.callbacks.forEach((cb) => cb(this.remaining));
    }
    
    /**
     * 停止倒计时
     */
    stop() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
        this.remaining = 0;
        this.callbacks = [];
    }
    
    /**
     * 是否正在倒计时
     * @returns {boolean}
     */
    isRunning() {
        return !!this.timer;
    }
}
