import axios from 'axios';

// 创建 axios 实例
const api = axios.create({
    baseURL: process.env.NODE_ENV === 'development' ?
        'http://localhost:3000/api' :
        'https://api.abcyun.cn/api',
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json',
    },
});

// 请求拦截器
api.interceptors.request.use(
    (config) => {
    // 添加 token
        const token = localStorage.getItem('mira_token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    },
);

// 响应拦截器
api.interceptors.response.use(
    (response) => {
        const { data } = response;
        if (data.code === 0) {
            return data.data;
        }
        return Promise.reject(new Error(data.message || '请求失败'));
        
    },
    (error) => {
        if (error.response?.status === 401) {
            // 清除本地存储的用户信息
            localStorage.removeItem('mira_token');
            localStorage.removeItem('mira_user');
            // 跳转到登录页
            window.location.href = '/login.html';
        }
        return Promise.reject(error);
    },
);

/**
 * 认证相关 API
 */
export const authAPI = {
    /**
   * 获取微信扫码登录二维码
   */
    getWechatQRCode() {
        return api.post('/auth/wechat/qrcode');
    },

    /**
   * 检查微信扫码登录状态
   * @param {string} qrCodeId 二维码ID
   */
    checkWechatScanStatus(qrCodeId) {
        return api.get(`/auth/wechat/check/${qrCodeId}`);
    },

    /**
   * 发送短信验证码
   * @param {string} phone 手机号
   */
    sendSmsCode(phone) {
        return api.post('/auth/sms/send', { phone });
    },

    /**
   * 短信验证码登录
   * @param {string} phone 手机号
   * @param {string} code 验证码
   */
    loginWithSmsCode(phone, code) {
        return api.post('/auth/sms/login', {
            phone, code,
        });
    },

    /**
   * 刷新 token
   * @param {string} refreshToken 刷新令牌
   */
    refreshToken(refreshToken) {
        return api.post('/auth/refresh', { refreshToken });
    },

    /**
   * 登出
   */
    logout() {
        return api.post('/auth/logout');
    },

    /**
   * 获取用户信息
   */
    getUserInfo() {
        return api.get('/auth/user');
    },
};


export default api;
