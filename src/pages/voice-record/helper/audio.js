export function requestMicPermission() {
    return new Promise((resolve, reject) => {
        navigator.mediaDevices.getUserMedia({ audio: true })
            .then((stream) => {
                stream.getTracks().forEach((track) => track.stop());
                resolve(true);
            })
            .catch((e) => {
                if (e?.name === 'NotFoundError') {
                    reject(new Error('请连接录音设备'));
                } else if (e?.name === 'NotAllowedError') {
                    reject(new Error('请允许录音设备的使用权限，或者下载 ABC 客户端使用'));
                } else {
                    reject(e);
                }
            });
    });
}



export function checkMicPermission() {
    return new Promise((resolve, reject) => {
        if (navigator.permissions && navigator.permissions.query) {
            navigator.permissions.query({ name: 'microphone' })
                .then((permissionStatus) => {
                    if (permissionStatus.state === 'granted') {
                        resolve(true);
                    } else if (permissionStatus.state === 'prompt') {
                        requestMicPermission()
                            .then(resolve)
                            .catch(reject);
                    } else {
                        reject(new Error('请允许录音设备的使用权限，或者下载 ABC 客户端使用'));
                    }
                })
                .catch(() => {
                    requestMicPermission()
                        .then(resolve)
                        .catch(reject);
                });
        } else {
            requestMicPermission()
                .then(resolve)
                .catch(reject);
        }
    });
}

export function to16BitPCM(input) {
    const dataLength = input.length * (16 / 8);
    const dataBuffer = new ArrayBuffer(dataLength);
    const dataView = new DataView(dataBuffer);
    let offset = 0;
    for (let i = 0; i < input.length; i++, offset += 2) {
        const s = Math.max(-1, Math.min(1, input[i]));
        dataView.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7fff, true);
    }
    return dataView;
}

export function to16kHz(audioData, sampleRate = 44100) {
    const data = new Float32Array(audioData);
    const fitCount = Math.round(data.length * (16000 / sampleRate));
    const newData = new Float32Array(fitCount);
    const springFactor = (data.length - 1) / (fitCount - 1);
    newData[0] = data[0];
    for (let i = 1; i < fitCount - 1; i++) {
        const tmp = i * springFactor;
        const before = Math.floor(tmp).toFixed();
        const after = Math.ceil(tmp).toFixed();
        const atPoint = tmp - before;
        newData[i] = data[before] + (data[after] - data[before]) * atPoint;
    }
    newData[fitCount - 1] = data[data.length - 1];
    return newData;
}

