import { defineStore } from 'pinia';
import AsrRecorderManager from '../asr/common/asr-recorder-manager';
import { BUSINESS_EVENTS } from '../asr/common/constants';
import {
    ToastFunc as Toast,
} from '@abc/ui-pc';
import { createTraceLogger } from '../helper/logger';
import { formatTime } from '@/utils/index';

const TAG = 'VoiceRecordStore';

/**
 * Pinia store for managing voice record state and logic
 */
export const useVoiceRecordStore = defineStore('voiceRecord', {
    state: () => ({
        outpatientSheetId: null,
        taskId: null, // 本次录制任务 ID
        state: 'initial', // 'initial' | 'waiting-device' | 'recording' | 'paused'
        recordingError: null,
        showRecordingError: false,
        /**
         * @type {AsrRecorderWrapper}
         */
        asrRecorder: null, // 录音器实例
        isRecording: false,
        waveformData: null,
        recordTimer: null,
        recordTime: 0,
        completedSentences: [],
        currentSentence: '',
        currentStartTime: 0,
        currentEndTime: 0,
        timeOffset: 0,
        isContinueRecording: false,
        finalResult: [],
        finishCallbacks: [],
        isStarting: false, // 正在启动
        isPausing: false, // 正在停止
        isStopping: false, // 正在停止
        logger: null,
        appLogger: null,
        // 录音设备相关状态
        recordingDeviceType: 'pc', // 'pc' | 'mobile'
        availableDevices: [], // 可用的录音设备列表
        selectedDeviceId: '', // 选中的设备ID
        isLoadingDevices: false, // 是否正在加载设备列表
        // 手机录音管理器
        manager: null, // Manager实例
        eventListenerIds: [], // 事件监听器ID列表（用于清理）
    }),
    getters: {
        formattedTime(state) {
            const minutes = Math.floor(state.recordTime / 60).toString().padStart(2, '0');
            const seconds = (state.recordTime % 60).toString().padStart(2, '0');
            return `${minutes}:${seconds}`;
        },
        // 向后兼容性：提供 isWaitingForDevice 的计算属性
        isWaitingForDevice(state) {
            return state.state === 'waiting-device';
        },
        /**
         * 剩余时间，最多录制 10 分钟
         * @returns {number}
         */
        remainTime(state) {
            return Math.max(0, 60 * 15 - state.recordTime);
        },
        formattedRemainTime(state) {
            const minutes = Math.floor(state.remainTime / 60).toString().padStart(2, '0');
            const seconds = (state.remainTime % 60).toString().padStart(2, '0');
            return `${minutes}:${seconds}`;
        },
        fullSentence(state) {
            return state.completedSentences.map((s) => s.text).join('') + this.currentSentence;
        },
    },
    actions: {
        setState(state) {
            this.state = state;
            if (state === 'initial') {
                // 恢复到初始态，需要清空数据
                this.completedSentences = [];
                this.currentSentence = '';
                this.currentStartTime = 0;
                this.currentEndTime = 0;
                this.timeOffset = 0;
                this.isContinueRecording = false;
                this.finalResult = [];
                // 清理手机录音器
                if (this.asrRecorder) {
                    this.asrRecorder.cleanup();
                    this.asrRecorder = null;
                }
            }
        },
        /**
         * 绑定门诊单ID
         * @param outpatientSheetId
         */
        init(outpatientSheetId) {
            this.outpatientSheetId = outpatientSheetId;
            this.logger = createTraceLogger('voice-record-asr', {
                outpatientSheetId,
            }, true);

            // 创建Manager实例
            this.createManagerInstance();
        },

        /**
         * 创建Manager实例
         */
        createManagerInstance() {
            // 如果已经有实例，先清理
            if (this.manager) {
                this.manager.cleanup();
                this.manager = null;
            }

            // 创建日志记录器
            this.appLogger = createTraceLogger('app-asr', {}, true);

            // 创建新的Manager实例
            this.manager = new AsrRecorderManager({
                logger: this.appLogger,
            });

            // 初始化Manager
            this.manager.initialize(this.outpatientSheetId);

            // 设置事件监听器
            this.setupEventListeners(this.manager);

            this.appLogger.report('手机录音管理器实例创建成功', {
                outpatientSheetId: this.outpatientSheetId,
                managerStatus: this.manager.getStatus(),
                eventListenerCount: this.eventListenerIds.length,
            });
        },

        /**
         * 设置事件监听器
         * @param {AsrRecorderManager} manager 管理器实例
         */
        setupEventListeners(manager) {
            // 状态变化事件
            const statusChangedId = manager.addEventListener(
                BUSINESS_EVENTS.DEVICE_STATUS_CHANGED,
                (status) => {
                    this.logger?.report('录音设备状态变化', status);
                    this.refreshAvailableDevices();
                },
                `store_${Date.now()}_status_changed`,
            );
            this.eventListenerIds.push({
                eventName: BUSINESS_EVENTS.DEVICE_STATUS_CHANGED, id: statusChangedId,
            });
        },
        /**
         * 清理事件监听器
         */
        cleanupEventListeners(manager) {
            if (this.eventListenerIds.length > 0) {
                this.eventListenerIds.forEach(({
                    eventName, id,
                }) => {
                    manager.removeEventListener(eventName, id);
                });

                this.logger?.report('清理事件监听器', {
                    removedCount: this.eventListenerIds.length,
                });

                this.eventListenerIds = [];
            }
        },

        /**
         * 刷新可用设备列表
         */
        async refreshAvailableDevices() {
            // 重新获取设备列表，这会触发UI更新
            await this.getAvailableDevices(false);
        },

        /**
         * 处理等待设备连接（UI 状态更新）
         * 注意：超时控制由 Recorder 层管理
         */
        handleDeviceWaiting() {
            this.setState('waiting-device');
            this.logger?.report('开始等待设备连接');
        },

        /**
         * 处理取消等待设备连接
         */
        handleDeviceWaitingCancel() {
            this.setState('initial');
            this.isStarting = false;
            this.logger?.report('取消设备等待');
        },

        /**
         * 取消等待设备连接（用户主动取消）
         */
        cancelWaitingForDevice() {
            // 通知 Recorder 层取消等待
            if (this.asrRecorder) {
                this.asrRecorder.cancelWaitingForDevice();
            }

            this.setState('initial');
            this.isStarting = false;
            this.logger?.report('用户取消等待设备连接');
        },
        /**
         * 处理录音错误
         * @param error
         */
        handleRecordingError(error) {
            this.logger?.report('录音错误', { error });
            Toast.error('录音出现错误');
            this.showRecordingError = true;
            this.recordingError = error;
        },
        close(cb) {
            if (typeof cb === 'function') cb('close');
            this.setState('initial');
        },
        // startRecording() {
        // return this.checkMicPermission()
        //     .then(async () => {
        //         await this.startWebRecording();
        //     })
        //     .catch((e) => {
        //         console.error('录音权限检查失败', e);
        //         // Toast.error(e?.message || '获取录音权限失败');
        //         AbcModal.alert({
        //             type: 'info',
        //             title: '提示',
        //             content: e?.message || '请允许录音设备的使用权限，或者下载 ABC 客户端使用',
        //             closeAfterConfirm: true,
        //             showClose: false,
        //         });
        //         this.showPermissionTip = true;
        //         this.logger.report('录音权限检查失败', {
        //             err: e?.message,
        //         });
        //     }).finally(() => {
        //         this.isStarting = false;
        //     });

        // },
        /**
         * 开始手机录音
         */
        async startRecording() {
            if (this.isStarting || this.isPausing || this.isRecording) return;
            this.isStarting = true;
            this.logger.report('开始录音', {
                deviceType: this.recordingDeviceType,
                deviceId: this.selectedDeviceId,
            });
            try {
                // 确保管理器已初始化
                const { manager } = this;
                if (!manager) {
                    throw new Error('录音管理器实例创建失败');
                }

                // 通过管理器创建手机录音器实例
                if (!this.asrRecorder) {
                    this.asrRecorder = await manager.createRecorder(this.recordingDeviceType, {
                        recorderId: `voice_record_${this.outpatientSheetId}`,
                        onStart: () => {
                            this.setState('recording');
                            this.isRecording = true;
                            this.recordTimer = setInterval(() => {
                                this.recordTime++;
                            }, 1000);
                        },
                        onPause: () => {
                            this.setState('paused');
                            this.isPausing = false;
                            this.isRecording = false;
                            this.waveformData = null;
                            clearInterval(this.recordTimer);
                            this.recordTimer = null;
                        },
                        onResume: () => {
                            this.setState('recording');
                            this.isContinueRecording = false;
                            this.isRecording = true;
                            this.recordTimer = setInterval(() => {
                                this.recordTime++;
                            }, 1000);
                        },
                        onStop: () => {
                            // 只处理录音停止的状态清理，不处理识别结果
                            this.isStopping = false;
                            this.isRecording = false;

                            this.logger?.report(`${TAG}录音停止`, {
                                recordTime: this.recordTime,
                            });

                            // 处理识别结果
                            this.handleRecognitionComplete();

                            this.setState('initial');
                            this.resetRecording();
                        },
                        onError: (error) => {
                            this.handleRecordingError(error);
                        },
                        onWaitingDevice: () => {
                            this.handleDeviceWaiting();
                        },
                        onWaitingDeviceCancel: () => {
                            this.handleDeviceWaitingCancel();
                        },
                        onRecognitionStart: () => {
                            // 语音识别开始时上报
                            this.logger?.report('手机录音识别开始');
                        },
                        onSentenceBegin: (res) => {
                            this.currentSentence = '';
                            if (res.result && res.result.voiceText) {
                                const timeOffsetToUse = this.isContinueRecording ? this.timeOffset : 0;
                                this.currentStartTime = (res.result.startTime || 0) + timeOffsetToUse;
                            }
                            this.logger?.report('手机录音句子开始', res);
                        },
                        onRecognitionResultChange: (res) => {
                            if (res.result && res.result.voiceText) {
                                this.currentSentence = res.result.voiceText;
                                const originalStartTime = res.result.startTime || 0;
                                const originalEndTime = res.result.endTime || 0;
                                this.currentStartTime = originalStartTime + this.timeOffset;
                                this.currentEndTime = originalEndTime + this.timeOffset;
                            }
                            this.logger?.report('手机录音识别结果变化', res);
                        },
                        onSentenceEnd: (res) => {
                            if (res.result && res.result.voiceText) {
                                const originalStartMs = res.result.startTime || 0;
                                const originalEndMs = res.result.endTime || 0;
                                const startMs = originalStartMs + this.timeOffset;
                                const endMs = originalEndMs + this.timeOffset;
                                const timeStr = formatTime(startMs);
                                const completedSentence = {
                                    text: res.result.voiceText,
                                    startTime: startMs,
                                    endTime: endMs,
                                    time: timeStr,
                                    index: res.result.index,
                                };
                                this.completedSentences.push(completedSentence);
                                this.currentSentence = '';
                            }
                            this.logger?.report('手机录音句子结束', res);
                        },
                        // onRecognitionComplete: () => {
                        //     this.logger?.report('手机录音识别完成');
                        //     // 专门处理识别完成时的最终结果处理
                        //     this.handleRecognitionComplete();
                        // },
                        onWaveformUpdate: (data) => {
                            this.waveformData = data;
                        },
                    });
                }

                // 使用新的智能录音开始逻辑
                const result = await this.asrRecorder.startRecording({
                    outpatientSheetId: this.outpatientSheetId,
                    deviceId: this.selectedDeviceId,
                });

                if (result) {
                    this.taskId = this.manager.getTaskId();
                    this.logger?.report(`[${TAG}]手机录音开始成功`, {
                        taskId: this.taskId,
                    });
                } else {
                    this.logger?.report(`[${TAG}]手机录音开始失败`);
                    Toast.warning('手机录音开始失败，请重试');
                }
            } catch (error) {
                this.logger?.report(`[${TAG}]开始手机录音失败`, { error });
                Toast.error('开始手机录音失败');
                this.setState('initial');
            } finally {
                this.isStarting = false;
            }
        },
        async pauseRecording() {
            if (this.isStarting || this.isPausing) {
                return;
            }
            this.isPausing = true;
            // 上报录音暂停
            this.logger.report('暂停录音', {
                recordTime: this.recordTime,
                deviceType: this.recordingDeviceType,
            });

            if (this.asrRecorder) {
                await this.asrRecorder.pause();
            }
            this.isPausing = false;
        },
        async continueRecording() {
            this.isContinueRecording = true;
            this.logger.report('继续录音', {
                recordTime: this.recordTime,
                deviceType: this.recordingDeviceType,
            });

            // 手机录音继续
            if (this.asrRecorder) {
                await this.asrRecorder.resume();
            }
        },
        resetRecording() {
            clearInterval(this.recordTimer);
            this.recordTimer = null;
            this.recordTime = 0;
            this.waveformData = null;
            this.isRecording = false;
            this.timeOffset = 0;
        },
        /**
         * 处理识别完成事件
         * 统一的识别完成处理逻辑，确保只在识别真正完成时处理结果
         */
        handleRecognitionComplete() {
            this.logger?.report('处理识别完成事件', {
                completedSentencesCount: this.completedSentences.length,
                recordTime: this.recordTime,
            });

            this.processAsrResult();
        },

        /**
         * 处理ASR识别结果
         * 专门负责数据处理和状态重置
         */
        processAsrResult() {
            // 上报语音识别结果处理
            this.logger.report('processAsrResult', {
                recordTime: this.recordTime,
            });

            // 通过回调通知父组件生成
            for (const callback of this.finishCallbacks) {
                if (typeof callback === 'function') {
                    callback({
                        logger: this.logger,
                        // 添加录音来源信息
                        recordingSource: this.recordingDeviceType, // 'pc' 或 'mobile'
                        taskId: this.taskId, // 本次录制的 taskId
                        outpatientSheetId: this.outpatientSheetId,
                    });
                }
            }

            this.completedSentences = [];
            this.currentSentence = '';
            this.currentStartTime = 0;
            this.currentEndTime = 0;
            this.outpatientSheetId = null;

            this.setState('initial');
        },
        async stopRecording() {
            if (this.isStopping) {
                return;
            }
            this.isStopping = true;
            // 上报录音结束
            this.logger.report('录音结束', {
                recordTime: this.recordTime,
                deviceType: this.recordingDeviceType,
                currentState: this.state,
            });

            if (this.asrRecorder) {
                this.asrRecorder.stop();
            }
        },
        /**
         * 录音完成回调
         * @param {Function} callback
         */
        setFinishRecordingCallback(callback) {
            this.finishCallbacks.push(callback);
            // 返回一个销毁方法，用于移除回调
            return () => {
                this.finishCallbacks = this.finishCallbacks.filter((cb) => cb !== callback);
            };
        },
        splitTextIntoLines(text) {
            if (!text) return [];
            const lines = [];
            let remainText = text;
            while (remainText.length > 0) {
                if (remainText.length <= 20) {
                    lines.push(remainText);
                    break;
                }
                lines.push(remainText.substring(0, 20));
                remainText = remainText.substring(20);
            }
            return lines;
        },
        cleanup() {
            this.setState('initial');
            this.resetRecording();
            if (this.asrRecorder) {
                this.asrRecorder.cleanup();
                this.asrRecorder = null;
            }
            // 销毁Manager实例
            if (this.manager) {
                // 清理事件监听器
                this.cleanupEventListeners(this.manager);
                this.manager.cleanup();
                this.manager = null;
            }
            this.finishCallbacks = [];
            this.outpatientSheetId = null;
            this.$reset();
        },
        /**
         * 获取可用的录音设备列表
         * 纯粹的设备获取方法，不包含初始化逻辑
         */
        async getAvailableDevices(isInit = true) {
            if (this.isLoadingDevices) return;

            this.isLoadingDevices = true;
            try {
                this.availableDevices = await this.manager.getAvailableDevices();

                // 如果没有选中设备
                if (!this.selectedDeviceId) {
                    const pcDevices = this.availableDevices.filter((device) => device.type === 'pc');
                    const mobileDevices = this.availableDevices.filter((device) => device.type === 'mobile');
                    // 优先选择PC设备
                    if (pcDevices.length > 0) {
                        this.selectedDeviceId = pcDevices[0].deviceId;
                        this.recordingDeviceType = 'pc';
                    } else if (mobileDevices.length > 0) {
                        this.selectedDeviceId = mobileDevices[0].deviceId;
                        this.recordingDeviceType = 'mobile';
                    }
                }

                this.logger?.report('获取录音设备列表', {
                    deviceCount: this.availableDevices.length,
                    selectedDeviceId: this.selectedDeviceId,
                    managerStatus: this.manager.getStatus(),
                });
            } catch (error) {
                console.error('获取录音设备失败:', error);
                this.logger?.report('获取录音设备失败', { error });
                isInit && Toast.warning(`获取录音设备失败:${error.message}`);
            } finally {
                this.isLoadingDevices = false;
            }
        },

        /**
         * 切换录音设备
         */
        async switchRecordingDevice(deviceId, deviceType) {
            if (this.isRecording) {
                Toast.warning('录音进行中，无法切换设备');
                return;
            }

            this.selectedDeviceId = deviceId;
            this.recordingDeviceType = deviceType;

            this.logger?.report('切换录音设备', {
                deviceId,
                deviceType,
            });

            // 清理当前录音器实例
            if (this.asrRecorder) {
                this.asrRecorder.cleanup();
                this.asrRecorder = null;
            }
        },
    },
});
