<template>
    <div class="voice-record-page">
        <play-pause-button></play-pause-button>
    </div>
</template>

<script>
    import PlayPauseButton from './components/play-pause-button.vue';
    export default {
        name: 'VoiceRecordPage',
        components: {
            PlayPauseButton,
        },
    };
</script>

<style lang="scss" scoped>
@import 'src/style/style.scss';

.voice-record-page {
    width: 340px;
    background: linear-gradient(180deg, #d9ecff 0%, #edf5ff 100%);
}
</style>