import {
    RECORDING_EVENTS,
    DATA_EVENTS,
} from './constants';
import AsrProcessor from './asr-processor';
import WebRecorder from '../web/web-recorder';
import AppRecorder from '../app/app-recorder';

/**
 * 录音器包装类，屏蔽 WebRecorder 和 AppRecorder 的细节
 * 专注于录音会话管理，设备管理由Manager负责
 *
 * @class AsrRecorderWrapper
 * <AUTHOR> Assistant
 * @date 2025-01-19
 */
export default class AsrRecorderWrapper {
    /**
     * 构造函数
     * @param {Object} options 配置选项
     * @param {string} options.deviceType 设备类型 'mobile' | 'pc'
     * @param {string} options.businessId 录音器所在的房间 id
     * @param {string} options.taskId 录音任务ID
     * @param {Function} options.onStart 开始录音回调
     * @param {Function} options.onPause 暂停录音回调
     * @param {Function} options.onResume 继续录音回调
     * @param {Function} options.onStop 停止录音回调
     * @param {Function} options.onError 错误回调
     * @param {Function} options.onWaitingDevice 等待设备连接回调
     * @param {Function} options.onWaitingDeviceCancel 取消设备等待
     * @param {Function} options.onRecognitionStart 识别开始回调
     * @param {Function} options.onSentenceBegin 句子开始回调
     * @param {Function} options.onRecognitionResultChange 识别结果变化回调
     * @param {Function} options.onSentenceEnd 句子结束回调
     * @param {Function} options.onRecognitionComplete 识别完成回调
     * @param {Function} options.onWaveformUpdate 波形数据回调
     * @param {Socket} options.socket 共享的Socket对象
     * @param {Object} options.logger 日志记录器
     */
    constructor(options = {}) {
        this.businessId = options.businessId;
        this.taskId = options.taskId; // 本次录音任务 ID
        this.deviceType = options.deviceType;

        // 事件回调函数
        this.onStart = options.onStart || (() => {});
        this.onPause = options.onPause || (() => {});
        this.onResume = options.onResume || (() => {});
        this.onStop = options.onStop || (() => {});
        this.onError = options.onError || (() => {});
        this.onWaitingDevice = options.onWaitingDevice || (() => {});
        this.onWaitingDeviceCancel = options.onWaitingDeviceCancel || (() => {});
        this.onRecognitionStart = options.onRecognitionStart || (() => {});
        this.onSentenceBegin = options.onSentenceBegin || (() => {});
        this.onRecognitionResultChange = options.onRecognitionResultChange || (() => {});
        this.onSentenceEnd = options.onSentenceEnd || (() => {});
        this.onRecognitionComplete = options.onRecognitionComplete || (() => {});
        this.onWaveformUpdate = options.onWaveformUpdate || (() => {});

        // 日志记录器
        this.logger = options.logger;

        // Socket连接
        this.socket = options.socket;

        // Manager引用（用于设备状态检测和事件发送）
        this.manager = options.manager;

        // 统一的录音状态管理
        this.state = {
            taskId: null, // 录音任务ID，一次完整的录音任务，暂停继续，异常退出后的恢复，都属于同一个录音任务，从 app-recording-started 事件中获取
            isRecording: false,
            isPaused: false,
            error: null, // 存储最后一次错误信息
        };
        
        this.recorderImpl = null;
    }
    
    async initialize() {
        if (this.deviceType === 'mobile') {
            this.recorderImpl = new AppRecorder({
                businessId: this.businessId,
                logger: this.logger,
                socket: this.socket,
                manager: this.manager,
            });
        } else {
            this.recorderImpl = new WebRecorder({
                businessId: this.businessId,
                logger: this.logger,
                socket: this.socket,
                manager: this.manager,
            });
        }
        
        this.recorderImpl.onStart = this.handleRecordingStarted.bind(this);
        this.recorderImpl.onPause = this.handlePaused.bind(this);
        this.recorderImpl.onResume = this.handleResumed.bind(this);
        this.recorderImpl.onStop = this.handleStoped.bind(this);
        this.recorderImpl.onError = this.handleRecordingError.bind(this);
        
        this.recorderImpl.onWaitingDevice = this.onWaitingDevice;
        this.recorderImpl.onWaitingDeviceCancel = this.onWaitingDeviceCancel;
        this.recorderImpl.onWaveformUpdate = this.onWaveformUpdate;
        
        // 创建统一的ASR处理器
        this.asrProcessor = new AsrProcessor({
            socket: this.socket,
            callbacks: {
                onRecognitionStart: this.onRecognitionStart,
                onSentenceBegin: this.onSentenceBegin,
                onRecognitionResultChange: this.onRecognitionResultChange,
                onSentenceEnd: this.onSentenceEnd,
                onRecognitionComplete: this.onRecognitionComplete,
                onError: this.onError,
            },
            logger: this.logger,
            processorId: `app_asr_${Date.now()}`,
        });
        
        // 初始化ASR处理器
        this.asrProcessor.initialize();
        
        this.logger?.report('初始化App录音器');
        return true;
    }
    
    /**
     * 开始录音（统一入口）
     * 支持新的智能录音逻辑和向后兼容
     * @param {Object} config 录音配置
     * @param {string} config.deviceId 设备ID
     * @returns {Promise<boolean>} 是否成功开始录音
     */
    async startRecording(config) {
        this.logger?.report('开始录音', {
            config,
            currentState: this.getStatus(),
        });

        try {
            // 清理之前的录音状态
            this.clearRecordingState();

            return await this.recorderImpl.start();

        } catch (error) {
            console.error('开始录音失败:', error);
            this.emitRecordingError(error);
            return false;
        }
    }

    /**
     * 向后兼容的start方法
     * @param {Object} config 录音配置
     * @returns {Promise<boolean>} 是否成功开始录音
     */
    async start(config) {
        return this.startRecording(config);
    }
    
    /**
     * 暂停录音
     * @returns {Promise<void>}
     */
    async pause() {
        if (!this.state.isRecording || this.state.isPaused) {
            return;
        }

        this.logger?.report('暂停录音', {
            taskId: this.state.taskId,
        });

        // 真实手机录音暂停 - taskId 会在 sendRecordingCommand 中自动添加
        if (!this.state.taskId) {
            this.logger?.report('警告：暂停录音时缺少 taskId');
            console.warn('暂停录音时缺少 taskId，操作可能失败');
        }
        this.recorderImpl.pause();
    }

    /**
     * 继续录音
     * 根据之前的错误状态智能选择恢复策略
     * @returns {Promise<void>}
     */
    async resume() {
        if (!this.state.isRecording || !this.state.isPaused) {
            return;
        }

        this.logger?.report('继续录音', {
            taskId: this.state.taskId,
        });
        
        this.recorderImpl.resume();
    }
    
    /**
     * 停止录音
     * @returns {Promise<void>}
     */
    async stop() {
        if (!this.state.isRecording) {
            return;
        }

        this.logger?.report('停止录音', {
            taskId: this.state.taskId,
        });
        
        // 真实手机录音停止 - taskId 会在 sendRecordingCommand 中自动添加
        if (!this.state.taskId) {
            this.logger?.report('警告：停止录音时缺少 taskId');
            console.warn('停止录音时缺少 taskId，操作可能失败');
        }
        this.recorderImpl.stop();
    }
    
    getTaskId() {
        return this.state.taskId;
    }
    
    
    /**
     * 处理录音开始事件
     * @param {Object} data 录音开始数据
     * @param {string} data.taskId 录音任务ID
     */
    handleRecordingStarted(data) {
        this.logger?.report('handleRecordingStarted', data);

        // 提取并保存 taskId
        this.state.taskId = data.taskId;
        this.logger?.report('保存录音任务ID', {
            taskId: this.state.taskId,
        });
  
        // 更新录音状态
        this.state.isRecording = true;

        // 清空错误信息（录音成功开始时）
        this.state.error = null;

        // 重置ASR识别状态（委托给AsrProcessor）
        this.asrProcessor.resetAsrState();

        // 调用原有的回调
        this.onStart(data);
    }

    /**
     * 取消等待设备连接
     * 主动取消当前的设备连接等待
     */
    cancelWaitingForDevice() {
        this.recorderImpl.cancelWaitingForDevice();
    }

    /**
     * 触发录音错误事件
     * @param {Error} error 错误对象
     * @param {Object} config 录音配置
     */
    emitRecordingError(error, config) {
        this.logger?.report('录音错误', {
            error, config,
        });

        // 保存错误信息到状态中
        this.state.error = error;

        // 清理录音状态
        this.clearRecordingState();

        // 直接通过回调处理错误，不通过 Manager 转发
        this.onError(error);
    }

    /**
     * 清理录音状态
     */
    clearRecordingState() {
        // 重置状态
        this.state.isRecording = false;
        this.state.error = null; // 清空错误信息
        
        this.recorderImpl.clearState();

        // 重置ASR识别状态（委托给AsrProcessor）
        if (this.asrProcessor) {
            this.asrProcessor.resetAsrState();
        }
    }

    /**
     * 清理资源
     */
    cleanup() {
        // 停止录音
        if (this.state.isRecording) {
            this.stop();
        }

        // 取消设备连接等待
        this.cancelWaitingForDevice();

        // 清理录音状态
        this.clearRecordingState();

        // 清理录音事件监听器（新协议）
        if (this.socket) {
            // 新协议事件
            this.socket.off(RECORDING_EVENTS.RECORDING_ACTION);

            // 波形数据事件
            this.socket.off(DATA_EVENTS.WAVEFORM_DATA);
        }
        
        if (this.recorderImpl) {
            this.recorderImpl.cleanup();
        }

        // 清理ASR处理器
        if (this.asrProcessor) {
            this.asrProcessor.cleanup();
        }

        // 重置状态（不清理Socket，因为它是共享的）
        this.state.isRecording = false;
        this.state.isPaused = false;
        this.state.taskId = null;

        this.logger?.report('AppAsrRecorder资源已清理');
    }

    /**
     * 获取当前状态
     * @returns {Object} 当前状态信息
     */
    getStatus() {
        return {
            taskId: this.state.taskId, // 任务 ID
            isRecording: this.state.isRecording,
            isPaused: this.state.isPaused,
        };
    }
    
    handlePaused(data) {
        this.state.isPaused = true;
        this.logger?.report('手机录音已暂停', data);
        this.onPause(data);
    }
    
    handleResumed(data) {
        this.state.isPaused = false;
        this.logger?.report('手机录音已继续', data);
        this.onResume(data);
    }
    
    handleStoped(data) {
        this.state.isRecording = false;
        this.state.isPaused = false;
        this.logger?.report('手机录音已停止', data);
        this.onStop(data);
    }
    
    /**
     * @typedef RecordingError
     * @property {number} code 错误码
     * @property {string} error_message 错误消息
     * @property {string} taskId 录音任务 ID
     */
    
    /**
     *
     * @param {RecordingError} error
     */
    handleRecordingError(error) {
        this.logger?.report('手机录音错误', { error });
        
        // 保存错误信息到状态中
        this.state.error = error;
        
        const {
            code,
        } = error;
        // TODO 处理断开
        if (code === 10000) {
            // APP 断开
            // 如果当前正在录音
            if (this.state.isRecording) {
                // 先通知暂停
                this.handlePaused();
                // 再通知异常
                this.onError(error);
            }
        } else {
            this.onError(error);
        }
    }
}
