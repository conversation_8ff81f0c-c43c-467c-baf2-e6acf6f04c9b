import { DATA_EVENTS } from './constants';

/**
 * 统一的ASR处理器
 * 提取并统一处理所有ASR相关的事件和回调逻辑
 * 消除 AppAsrRecorder 和 WebAudioSpeechRecognizer 中的重复代码
 *
 * @class AsrProcessor
 * <AUTHOR> Assistant
 * @date 2025-01-19
 */
export default class AsrProcessor {
    /**
     * 构造函数
     * @param {Object} options 配置选项
     * @param {Object} options.socket AsrSocket实例
     * @param {Object} options.callbacks ASR回调函数集合
     * @param {Function} options.callbacks.onRecognitionStart 识别开始回调
     * @param {Function} options.callbacks.onSentenceBegin 句子开始回调
     * @param {Function} options.callbacks.onRecognitionResultChange 识别结果变化回调
     * @param {Function} options.callbacks.onSentenceEnd 句子结束回调
     * @param {Function} options.callbacks.onRecognitionComplete 识别完成回调
     * @param {Function} options.callbacks.onError 错误回调
     * @param {Object} options.logger 日志记录器
     * @param {string} options.processorId 处理器ID（用于日志区分）
     */
    constructor(options = {}) {
        this.socket = options.socket;
        this.callbacks = options.callbacks || {};
        this.logger = options.logger;
        this.processorId = options.processorId || `asr_processor_${Date.now()}`;
        
        // ASR识别状态管理（统一从现有代码提取）
        this.isAsrStart = false; // 是否开始识别
        this.isSentenceBegin = false; // 是否一句话开始
        
        this.resultChangeTimer = null;
        
        // 是否已初始化
        this.isInitialized = false;
        
        this.logger?.report('AsrProcessor实例创建', {
            processorId: this.processorId,
            callbackCount: Object.keys(this.callbacks).length,
        });
    }
    
    /**
     * 初始化ASR事件监听器
     * 统一设置所有ASR相关的Socket事件监听
     */
    initialize() {
        if (this.isInitialized || !this.socket) {
            return;
        }
        
        this.logger?.report('AsrProcessor开始初始化', {
            processorId: this.processorId,
        });
        
        // 监听ASR结果事件
        this.socket.on(DATA_EVENTS.ASR_RESULT, (response) => {
            this.handleAsrResponse(response);
        });
        
        this.isInitialized = true;
        
        this.logger?.report('AsrProcessor初始化完成', {
            processorId: this.processorId,
        });
    }
    
    /**
     * 处理ASR响应数据（统一从现有代码提取和优化）
     * 根据 response.result.sliceType 分发到不同的细粒度回调
     * 整合了 AppAsrRecorder.handleAsrResponse 和 SpeechRecognizer 中的逻辑
     * @param {Object} response ASR响应数据
     */
    handleAsrResponse(response) {
        this.logger?.report('AsrProcessor收到ASR响应', {
            processorId: this.processorId,
            responseType: typeof response,
            hasResult: !!(response && response.result),
        });
        
        try {
            // 统一响应格式处理（从 speechrecognizer.js 提取）
            let processedResponse;
            if (typeof response === 'string') {
                processedResponse = JSON.parse(response);
            } else if (typeof response === 'object') {
                processedResponse = response;
            } else {
                this.logger?.report('ASR响应格式无效', {
                    processorId: this.processorId,
                    response,
                });
                return;
            }
            
            // 做下转换（统一处理）
            processedResponse.result = processedResponse.result || {};
            
            // 错误处理（从 speechrecognizer.js 提取）
            if (processedResponse.code !== 0) {
                this.logger?.report('ASR响应包含错误', {
                    processorId: this.processorId,
                    code: processedResponse.code,
                    response: processedResponse,
                });
                this.safeCallback('onError', processedResponse);
                return;
            }
            
            // 处理识别开始（统一逻辑）
            if (!this.isAsrStart) {
                this.isAsrStart = true;
                this.safeCallback('onRecognitionStart', processedResponse);
            }
            
            // 处理识别结果（统一 sliceType 处理逻辑）
            if (processedResponse.result && typeof processedResponse.result === 'object') {
                const { sliceType } = processedResponse.result;
                
                switch (sliceType) {
                    case 0:
                        // 句子开始
                        this.handleSentenceBegin(processedResponse);
                        break;
                        
                    case 2:
                        // 句子结束
                        this.handleSentenceEnd(processedResponse);
                        break;
                        
                    case 1:
                    default:
                        // 识别结果变化（带延迟处理，从 speechrecognizer.js 提取）
                        this.handleRecognitionResultChange(processedResponse);
                        break;
                }
            }
            
        } catch (error) {
            this.logger?.report('处理ASR响应失败', {
                processorId: this.processorId,
                error: error.message,
                response,
            });
            this.safeCallback('onError', error);
        }
    }
    
    /**
     * 处理句子开始事件
     * @param {Object} response ASR响应数据
     */
    handleSentenceBegin(response) {
        this.isSentenceBegin = true;
        this.safeCallback('onSentenceBegin', response);
        
        this.logger?.report('ASR句子开始', {
            processorId: this.processorId,
            hasVoiceText: !!(response.result && response.result.voiceText),
        });
    }
    
    /**
     * 处理句子结束事件
     * @param {Object} response ASR响应数据
     */
    handleSentenceEnd(response) {
        // 清除可能存在的延迟定时器（从 speechrecognizer.js 提取）
        if (this.resultChangeTimer) {
            clearTimeout(this.resultChangeTimer);
            this.resultChangeTimer = null;
        }
        
        // 如果句子还没开始，先触发开始事件（从 speechrecognizer.js 提取）
        if (!this.isSentenceBegin) {
            this.handleSentenceBegin(response);
        }
        
        this.safeCallback('onSentenceEnd', response);
        this.isSentenceBegin = false;
        
        this.logger?.report('ASR句子结束', {
            processorId: this.processorId,
            hasVoiceText: !!(response.result && response.result.voiceText),
        });
    }
    
    /**
     * 处理识别结果变化事件（带延迟处理）
     * 从 speechrecognizer.js 提取的 350ms 延迟逻辑
     * @param {Object} response ASR响应数据
     */
    handleRecognitionResultChange(response) {
        // 只有在句子开始后才处理结果变化（从 AppAsrRecorder 提取）
        if (!this.isSentenceBegin) {
            return;
        }
        
        // 清除之前的定时器，避免多次触发（从 speechrecognizer.js 提取）
        if (this.resultChangeTimer) {
            clearTimeout(this.resultChangeTimer);
            this.resultChangeTimer = null;
        }
        
        // 保存当前响应，用于延迟回调
        const currentResponse = { ...response };
        
        // 设置新的定时器（350ms延迟）
        this.resultChangeTimer = setTimeout(() => {
            this.safeCallback('onRecognitionResultChange', currentResponse);
            this.resultChangeTimer = null;
            
            this.logger?.report('ASR识别结果变化（延迟处理）', {
                processorId: this.processorId,
                hasVoiceText: !!(currentResponse.result && currentResponse.result.voiceText),
            });
        }, 350);
    }
    
    /**
     * 处理ASR完成事件
     */
    handleAsrCompleted() {
        // 清除可能存在的延迟定时器（从 speechrecognizer.js 提取）
        if (this.resultChangeTimer) {
            clearTimeout(this.resultChangeTimer);
            this.resultChangeTimer = null;
        }
        
        // this.safeCallback('onRecognitionComplete', data);
        
        // this.logger?.report('ASR识别完成', {
        //     processorId: this.processorId,
        //     data,
        // });
    }
    
    /**
     * 安全的回调函数调用
     * 确保回调函数存在且正确执行，避免因回调错误影响整体流程
     * @param {string} callbackName 回调函数名称
     * @param {*} data 回调数据
     */
    safeCallback(callbackName, data) {
        try {
            const callback = this.callbacks[callbackName];
            if (typeof callback === 'function') {
                callback(data);
            }
        } catch (error) {
            console.error(`AsrProcessor回调执行错误 [${callbackName}]:`, error);
            this.logger?.report('AsrProcessor回调执行错误', {
                processorId: this.processorId,
                callbackName,
                error: error.message,
            });
        }
    }
    
    /**
     * 重置ASR状态
     * 用于录音重新开始时清理状态
     */
    resetAsrState() {
        this.isAsrStart = false;
        this.isSentenceBegin = false;
        
        // 清理延迟定时器
        if (this.resultChangeTimer) {
            clearTimeout(this.resultChangeTimer);
            this.resultChangeTimer = null;
        }
        
        this.logger?.report('AsrProcessor状态已重置', {
            processorId: this.processorId,
        });
    }
    
    /**
     * 清理资源
     * 移除事件监听器，清理定时器
     */
    cleanup() {
        this.logger?.report('AsrProcessor开始清理', {
            processorId: this.processorId,
        });
        
        // 清理延迟定时器
        if (this.resultChangeTimer) {
            clearTimeout(this.resultChangeTimer);
            this.resultChangeTimer = null;
        }
        
        // 移除Socket事件监听器
        if (this.socket) {
            this.socket.off(DATA_EVENTS.ASR_RESULT);
        }
        
        // 重置状态
        this.resetAsrState();
        this.isInitialized = false;
        
        this.logger?.report('AsrProcessor清理完成', {
            processorId: this.processorId,
        });
    }
}
