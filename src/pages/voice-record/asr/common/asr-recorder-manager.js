import AsrSocket from './asr-socket';
import AsrRecorderWrapper from './asr-recorder-wrapper';
import {
    BUSINESS_EVENTS,
    DEVICE_EVENTS,
    SOCKET_EVENTS,
} from './constants';
import { checkMicPermission } from '../../helper/audio';

/**
 * 录音管理器
 * 统一管理手机、Web录音相关的所有逻辑，包括Socket连接、设备状态、录音器实例等
 *
 * @class AsrRecorderManager
 * <AUTHOR> Assistant
 * @date 2025-01-19
 */
export default class AsrRecorderManager {
    /**
     * 构造函数
     * @param {Object} options 配置选项
     * @param {Object} options.logger 日志记录器
     */
    constructor(options = {}) {
        // 房间号
        this.businessId = '';
        
        // 录制任务ID
        this.taskId = '';
        
        // 事件发布/订阅系统（只保留设备管理相关事件）
        this.eventListeners = {
            'device-connected': new Set(),
            'device-disconnected': new Set(),
            'device-status-updated': new Set(),
            'device-status-changed': new Set(),
        };

        // 日志记录器
        this.logger = options.logger;
        
        // Socket连接管理
        this.socket = null;
        this.isSocketConnected = false;
        
        // 设备状态管理
        this.isMobileConnected = false;
        this.connectedDevices = [];
        
        // 录音器实例管理
        this.recorder = null;

        // 初始化状态
        this.isInitialized = false;
    }

    /**
     * 添加事件监听器
     * @param {string} eventName 事件名称
     * @param {Function} listener 监听器函数
     * @param {string} listenerId 监听器唯一标识（用于后续移除）
     */
    addEventListener(eventName, listener, listenerId) {
        if (!this.eventListeners[eventName]) {
            throw new Error(`不支持的事件类型: ${eventName}`);
        }

        if (typeof listener !== 'function') {
            throw new Error('监听器必须是函数');
        }

        // 创建监听器对象，包含ID和函数
        const listenerObj = {
            id: listenerId || `listener_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            fn: listener,
        };

        this.eventListeners[eventName].add(listenerObj);

        this.logger?.report('添加事件监听器', {
            eventName,
            listenerId: listenerObj.id,
            listenerCount: this.eventListeners[eventName].size,
        });

        return listenerObj.id;
    }

    /**
     * 移除事件监听器
     * @param {string} eventName 事件名称
     * @param {string} listenerId 监听器ID
     */
    removeEventListener(eventName, listenerId) {
        if (!this.eventListeners[eventName]) {
            return false;
        }

        for (const listenerObj of this.eventListeners[eventName]) {
            if (listenerObj.id === listenerId) {
                this.eventListeners[eventName].delete(listenerObj);

                this.logger?.report('移除事件监听器', {
                    eventName,
                    listenerId,
                    remainingCount: this.eventListeners[eventName].size,
                });

                return true;
            }
        }

        return false;
    }

    /**
     * 触发事件
     * @param {string} eventName 事件名称
     * @param {*} data 事件数据
     */
    emitEvent(eventName, data) {
        if (!this.eventListeners[eventName]) {
            return;
        }

        const listeners = Array.from(this.eventListeners[eventName]);

        this.logger?.report('触发事件', {
            eventName,
            listenerCount: listeners.length,
            data: typeof data === 'object' ? JSON.stringify(data) : data,
        });

        // 异步执行所有监听器，避免阻塞
        listeners.forEach((listenerObj) => {
            try {
                // 使用 setTimeout 确保异步执行，避免监听器错误影响其他监听器
                const timerId = setTimeout(() => {
                    listenerObj.fn(data);
                    clearTimeout(timerId);
                }, 0);
            } catch (error) {
                console.error(`事件监听器执行错误 [${eventName}]:`, error);
                this.logger?.report('事件监听器执行错误', {
                    eventName,
                    listenerId: listenerObj.id,
                    error: error.message,
                });
            }
        });
    }

    /**
     * 初始化管理器
     * @param {string} businessId 房间ID
     * @returns {boolean} 是否初始化成功
     */
    initialize(businessId) {
        if (this.isInitialized) {
            return true;
        }
        
        this.businessId = businessId;

        try {
            // 创建Socket实例
            this.socket = new AsrSocket({
                supplierId: 'tencent',
                timeout: 5000,
                businessId: this.businessId,
                businessType: 0,
            });
            this.setupDeviceEventHandlers();

            // 触发设备状态检查（非阻塞，fire-and-forget模式）
            this.checkDeviceStatus().catch((error) => {
                console.warn('设备状态检查失败:', error);
                this.logger?.report('设备状态检查失败', { error });
            });

            this.isInitialized = true;
            this.logger?.report('AppAsrRecorderManager初始化成功');

            return true;
        } catch (error) {
            console.error('AppAsrRecorderManager初始化失败:', error);
            this.logger?.report('AppAsrRecorderManager初始化失败', { error });
            return false;
        }
    }
    
    /**
     * 设置设备事件处理器
     * 只监听与设备管理相关的事件，专注于设备状态管理
     */
    setupDeviceEventHandlers() {
        if (!this.socket) return;

        // 获取原始Socket对象进行事件监听
        const rawSocket = this.socket.getSocket();
        if (!rawSocket) return;

        // 监听Socket连接状态事件
        rawSocket.on(SOCKET_EVENTS.CONNECT, () => {
            this.isSocketConnected = true;
            this.logger?.report('Socket连接成功');

            // 检查设备状态
            this.checkDeviceStatus();
            this.notifyStatusChanged();
        });

        rawSocket.on(SOCKET_EVENTS.DISCONNECT, () => {
            this.isSocketConnected = false;
            this.isMobileConnected = false;
            this.connectedDevices = [];
            this.logger?.report('Socket连接断开');
            this.notifyStatusChanged();
        });

        rawSocket.on(SOCKET_EVENTS.ERROR, (error) => {
            console.error('Socket连接错误:', error);
            this.logger?.report('Socket连接错误', { error });
            this.isSocketConnected = false;
            this.isMobileConnected = false;
            this.connectedDevices = [];
            this.notifyStatusChanged();
        });

        // 只监听设备管理相关的事件
        rawSocket.on(DEVICE_EVENTS.APP_CONNECTED, (data) => {
            this.handleDeviceConnected(data);
        });

        rawSocket.on(DEVICE_EVENTS.APP_DISCONNECTED, (data) => {
            this.handleDeviceDisconnected(data);
        });

        rawSocket.on(DEVICE_EVENTS.APP_STATUS_CHANGED, (data) => {
            this.handleDeviceStatusResponse(data);
        });
    }

    /**
     * 处理设备连接事件
     * @param {Object} data 设备数据
     */
    handleDeviceConnected(data) {
        this.checkDeviceStatus();

        // 触发业务层事件
        this.emitEvent(BUSINESS_EVENTS.DEVICE_CONNECTED, {
            ...data,
            timestamp: Date.now(),
            managerStatus: this.getStatus(),
        });
    }
    /**
     * 处理设备断开事件
     * @param {DeviceShutdownEvent} data 断开连接事件
     */
    handleDeviceDisconnected(data) {
        if (data.loginSide !== 'app') {
            return;
        }
        this.checkDeviceStatus();

        // 触发业务层事件
        this.emitEvent(BUSINESS_EVENTS.DEVICE_DISCONNECTED, {
            ...data,
            timestamp: Date.now(),
            managerStatus: this.getStatus(),
        });
    }

    /**
     * 处理设备状态响应
     * @param {Object} deviceStatus 设备状态数据
     */
    handleDeviceStatusResponse(deviceStatus) {
        this.logger?.report('收到设备状态响应', deviceStatus);

        try {
            // 记录状态变化前的状态
            const wasConnected = this.isMobileConnected;
            const previousDeviceCount = this.connectedDevices.length;

            // 更新内部状态
            this.updateConnectedDevices(deviceStatus);

            // 触发状态变化通知
            this.notifyStatusChanged();

            // 记录状态更新日志
            this.logger?.report('设备状态已更新', {
                wasConnected,
                nowConnected: this.isMobileConnected,
                previousDeviceCount,
                currentDeviceCount: this.connectedDevices.length,
                deviceStatus,
            });

            // 触发业务事件
            this.emitDeviceStatusEvents(wasConnected, this.isMobileConnected, deviceStatus);

        } catch (error) {
            console.error('处理设备状态响应失败:', error);
            this.logger?.report('处理设备状态响应失败', {
                error, deviceStatus,
            });
        }
    }

    /**
     * 触发设备状态相关的业务事件
     * @param {boolean} wasConnected 之前的连接状态
     * @param {boolean} nowConnected 当前的连接状态
     * @param {Object} deviceStatus 设备状态数据
     */
    emitDeviceStatusEvents(wasConnected, nowConnected, deviceStatus) {
        const eventData = {
            ...deviceStatus,
            timestamp: Date.now(),
            managerStatus: this.getStatus(),
        };

        // 设备连接状态发生变化时触发相应事件
        if (!wasConnected && nowConnected) {
            // 从未连接变为已连接
            this.emitEvent(BUSINESS_EVENTS.DEVICE_CONNECTED, eventData);
            this.logger?.report('触发设备连接事件', eventData);
        } else if (wasConnected && !nowConnected) {
            // 从已连接变为未连接
            this.emitEvent(BUSINESS_EVENTS.DEVICE_DISCONNECTED, eventData);
            this.logger?.report('触发设备断开事件', eventData);
        } else if (nowConnected) {
            // 保持连接状态，但设备列表可能有变化
            this.emitEvent(BUSINESS_EVENTS.DEVICE_STATUS_CHANGED, eventData);
            this.logger?.report('触发设备状态更新事件', eventData);
        }
    }
    
    /**
     * 检查设备连接状态
     * @returns {Promise<boolean>} 是否有设备连接
     */
    async checkDeviceStatus() {
        const rawSocket = this.socket?.getSocket();
        if (!rawSocket || !rawSocket.isConnected) {
            this.isSocketConnected = false;
            this.isMobileConnected = false;
            this.connectedDevices = [];
            this.notifyStatusChanged();
            return false;
        }

        try {
            this.isSocketConnected = true;

            // 直接使用原始Socket对象进行通信
            const deviceStatus = await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('检查设备状态超时'));
                }, 5000);

                const handleResponse = (data) => {
                    clearTimeout(timeout);
                    rawSocket.off(DEVICE_EVENTS.APP_STATUS_CHANGED, handleResponse);
                    resolve(data);
                };

                rawSocket.on(DEVICE_EVENTS.APP_STATUS_CHANGED, handleResponse);
                // 检查设备状态
                rawSocket.emit(DEVICE_EVENTS.CHECK_DEVICE_STATUS, {
                    loginSide: 'app',
                });
            });
            
            this.updateConnectedDevices(deviceStatus);

            this.notifyStatusChanged();
            this.logger?.report('设备状态检查完成', {
                socketConnected: this.isSocketConnected,
                mobileConnected: this.isMobileConnected,
                deviceCount: this.connectedDevices.length,
            });

            return this.isMobileConnected;
        } catch (error) {
            console.warn('检查设备状态失败:', error);
            this.logger?.report('检查设备状态失败', { error });
            this.isMobileConnected = false;
            this.connectedDevices = [];
            this.notifyStatusChanged();
            return false;
        }
    }
    
    /**
     * 更新已连接设备列表
     * @param {Object} deviceStatus 设备状态信息
     */
    updateConnectedDevices(deviceStatus) {
        if (deviceStatus?.loginSide !== 'app') {
            return;
        }
        if (deviceStatus?.status === 1) {
            this.isMobileConnected = true;
            this.connectedDevices = [{
                deviceId: 'app',
                label: '我的手机',
                type: 'mobile',
                icon: 's-mobile-small-fill',
                isOnline: true,
            }];
        } else {
            this.isMobileConnected = false;
            this.connectedDevices = [];
        }
    }
    
    /**
     * 获取可用设备列表（统一设备管理接口）
     * @returns {Promise<Array>} 设备列表
     */
    async getAvailableDevices() {
        const [pcDevices, mobileDevices] = await Promise.all([
            this.getPcAudioDevices(),
            this.getMobileDevices(),
        ]);
        
        return [
            ...mobileDevices,
            ...pcDevices,
        ];
    }
    
    /**
     * 获取PC音频输入设备
     * @returns {Promise<Array>} PC设备列表
     */
    async getPcAudioDevices() {
        try {
            await checkMicPermission();
        } catch (e) {
            this.logger?.report(`getPcAudioDevices error: ${e.message}`);
        }
        if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
            return [];
        }
        
        try {
            const devices = await navigator.mediaDevices.enumerateDevices();
            const audioInputs = devices.filter((device) => device.deviceId && device.kind === 'audioinput');
            
            return audioInputs.map((device) => {
                let isSuggest = false;
                const words = device.label?.split(' ') || [];
                if (words.some((word) => word.toLowerCase() === 'emeet')) {
                    isSuggest = true;
                }
                
                return {
                    deviceId: device.deviceId,
                    label: device.label || '麦克风',
                    type: 'pc',
                    icon: isSuggest ? 's-emeet-fill' : 's-mic-fill',
                    isSuggest,
                };
            });
        } catch (error) {
            console.error('获取PC音频设备失败:', error);
            return [];
        }
    }
    
    /**
     * 获取手机App录音设备
     * @returns {Array} 手机设备列表
     */
    getMobileDevices() {
        if (this.isMobileConnected && this.connectedDevices.length > 0) {
            return [...this.connectedDevices];
        }
        
        return [{
            deviceId: 'app',
            label: '我的手机',
            type: 'mobile',
            icon: 's-mobile-small-fill',
            isOnline: false,
            isDemo: false,
        }];
    }
    
    /**
     * 创建录音器实例（统一录音器创建接口）
     * @param {string} deviceType 设备类型：'mobile' | 'pc'
     * @param {Object} options 录音器配置选项
     * @returns {Promise<Object>} 录音器实例
     */
    async createRecorder(deviceType, options = {}) {
        // 检查下 socket 状态，未连接时，重连一次
        if (!this.socket.isConnected()) {
            // 重连
            this.socket.reconnect();
        }
        
        // const { taskId } = await this.joinRoom(SOCKET_BUSINESS_TYPE.VOICE_MR_PC_JOIN_APP);
        // this.taskId = taskId;
        
        // 创建录音器实例，传递Socket实例和Manager引用
        const recorder = new AsrRecorderWrapper({
            ...options,
            deviceType,
            taskId: this.taskId,
            businessId: this.businessId,
            socket: this.socket?.getSocket(), // 获取原始Socket对象
            logger: this.logger,
            manager: this, // 传递Manager引用
        });
        
        await recorder.initialize();
        
        this.recorder = recorder;
        
        return recorder;
    }
    
    /**
     * 加入房间（统一的房间加入逻辑）
     * @param {AsrSocket} socket Socket实例
     * @param {number} businessType 业务类型
     * @returns {Promise<Object>} 加入结果
     */
    async joinRoom(businessType) {
        return new Promise((resolve, reject) => {
            const rawSocket = this.socket.getSocket();
            
            let handleJoinError;
            const handleJoin = (res) => {
                rawSocket.off(DEVICE_EVENTS.JOIN_ROOM_ERROR, handleJoinError);
                resolve(res);
            };
            
            handleJoinError = (error) => {
                rawSocket.off(DEVICE_EVENTS.JOIN_ROOM_ACK, handleJoin);
                reject(error);
            };
            
            rawSocket.once(DEVICE_EVENTS.JOIN_ROOM_ACK, handleJoin);
            rawSocket.once(DEVICE_EVENTS.JOIN_ROOM_ERROR, handleJoinError);
            
            // 发送加入房间请求
            rawSocket.emit(DEVICE_EVENTS.JOIN_ROOM, {
                businessId: this.businessId,
                businessType,
            });
        });
    }
    
    /**
     * 获取管理器状态
     * @returns {Object} 状态信息
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            isSocketConnected: this.isSocketConnected,
            isMobileConnected: this.isMobileConnected,
            connectedDeviceCount: this.connectedDevices.length,
        };
    }
    
    getTaskId() {
        return this.recorder?.getTaskId();
    }
    
    /**
     * 通知状态变化
     */
    notifyStatusChanged() {
        const status = this.getStatus();
        this.emitEvent(BUSINESS_EVENTS.DEVICE_STATUS_CHANGED, status);
    }
    
    /**
     * 清理所有资源
     */
    cleanup() {
        // 清理所有活跃的录音器实例
        this.recorder?.cleanup();

        // 清理Socket事件监听器（只清理设备管理相关的）
        if (this.socket) {
            const rawSocket = this.socket.getSocket();
            if (rawSocket) {
                rawSocket.off(SOCKET_EVENTS.CONNECT);
                rawSocket.off(SOCKET_EVENTS.DISCONNECT);
                rawSocket.off(SOCKET_EVENTS.ERROR);
                rawSocket.off(DEVICE_EVENTS.APP_CONNECTED);
                rawSocket.off(DEVICE_EVENTS.APP_DISCONNECTED);
                rawSocket.off(DEVICE_EVENTS.APP_STATUS_CHANGED);
            }
        }

        // 清理所有业务事件监听器
        for (const [, listeners] of Object.entries(this.eventListeners)) {
            listeners.clear();
        }

        // 销毁Socket实例（每个Manager实例都有独立的Socket）
        if (this.socket) {
            this.socket.destroy();
            this.socket = null;
        }

        // 重置状态
        this.isSocketConnected = false;
        this.isMobileConnected = false;
        this.connectedDevices = [];
        this.isInitialized = false;

        this.logger?.report('AppAsrRecorderManager实例资源已清理');
    }
    

}
