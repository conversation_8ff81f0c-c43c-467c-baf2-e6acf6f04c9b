import io from 'socket.io-client';
import { SOCKET_EVENTS } from './constants';
/**
 * 获取websocket协议
 * 兼容 abcyun:// 以及 own 和 本地环境
 */
export function getWebSocketProtocol() {
    return location.protocol === 'http:' ? 'ws:' : 'wss:';
}
/**
 * ASR Socket 通信类
 * 纯通信通道，只负责Socket.io连接管理，提供原始socket对象访问
 * 重构为实例模式，每个Manager实例都有独立的Socket连接
 */
export default class AsrSocket {
    constructor(options = {}) {
        this.socket = null;
        this.options = {
            supplierId: 'tencent',
            timeout: 5000,
            ...options,
        };
        // 生成实例ID用于日志区分
        this.instanceId = `socket_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
        this.initSocket();
    }

    initSocket() {
        // this.socket = io('http://localhost:3000', {
        //     path: '/asr',
        //     transports: ['websocket'],
        //     timeout: 5000,
        // });
        const socketQuery = {
            supplierId: this.options.supplierId,
        };
        if (this.options.businessId) {
            socketQuery.businessId = this.options.businessId;
            socketQuery.businessType = this.options.businessType;
        }
        this.socket = io(`${getWebSocketProtocol()}//${location.host}/cis`, {
            path: '/api/asr/socket.io',
            query: socketQuery,
            transports: ['websocket'],
            timeout: this.options.timeout,
        });

        this.setupBasicHandlers();
    }

    setupBasicHandlers() {
        if (!this.socket) return;

        // 只处理基本的连接状态，不做任何业务逻辑处理或事件转发
        this.socket.on(SOCKET_EVENTS.CONNECT, () => {
            this.socket.isConnected = true;
            console.log(`ASR Socket 实例连接成功 [${this.getInstanceId()}]`);
        });

        this.socket.on(SOCKET_EVENTS.DISCONNECT, () => {
            this.socket.isConnected = false;
            console.log(`ASR Socket 实例连接断开 [${this.getInstanceId()}]`);
        });

        this.socket.on(SOCKET_EVENTS.ERROR, (error) => {
            console.error(`ASR Socket 实例错误 [${this.getInstanceId()}]:`, error);
        });
    }

    /**
     * 获取原始Socket对象
     * @returns {Socket} 原始的Socket.io对象
     */
    getSocket() {
        return this.socket;
    }

    /**
     * 检查连接状态
     * @returns {boolean} 是否已连接
     */
    isConnected() {
        return this.socket && this.socket.isConnected;
    }

    /**
     * 断开连接
     */
    disconnect() {
        if (this.socket) {
            this.socket.disconnect();
        }
    }

    /**
     * 重新连接
     */
    reconnect() {
        if (this.socket) {
            this.socket.connect();
        }
    }



    /**
     * 获取实例ID
     * @returns {string} 实例ID
     */
    getInstanceId() {
        return this.instanceId;
    }

    /**
     * 完全销毁Socket实例
     */
    destroy() {
        if (this.socket) {
            console.log(`ASR Socket 实例开始销毁 [${this.getInstanceId()}]`);
            // 移除所有事件监听器
            this.socket.removeAllListeners();
            // 断开连接
            this.socket.disconnect();
            // 清空引用
            this.socket = null;
            console.log(`ASR Socket 实例已销毁 [${this.getInstanceId()}]`);
        }
    }

}
