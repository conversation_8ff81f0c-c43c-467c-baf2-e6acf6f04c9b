import {
    DATA_EVENTS, DEVICE_EVENTS, SOCKET_BUSINESS_TYPE,
} from '../common/constants';
import BaseRecorder from '../common/base-recorder';

/**
 * 手机App录音器类
 * 专注于手机录音的控制
 *
 * @class AppRecorder
 * <AUTHOR> Assistant
 * @date 2025-01-19
 */
export default class AppRecorder extends BaseRecorder {
    /**
     * 构造函数
     * @param {Object} options 配置选项
     * @param {Function} options.businessId 业务 id
     * @param {Function} options.businessType 业务 type
     * @param {Function} options.onStart 开始录音回调
     * @param {Function} options.onPause 暂停录音回调
     * @param {Function} options.onResume 继续录音回调
     * @param {Function} options.onStop 停止录音回调
     * @param {Function} options.onError 错误回调
     * @param {Function} options.onWaitingDevice 等待设备连接回调
     * @param {Function} options.onWaitingDeviceCancel 取消设备等待
     * @param {Function} options.onWaveformUpdate 波形数据回调
     * @param {Socket} options.socket 共享的Socket对象
     * @param {Object} options.logger 日志记录器
     */
    constructor(options = {}) {
        super({
            ...options,
            businessType: SOCKET_BUSINESS_TYPE.VOICE_MR_PC_JOIN_APP,
        });

        // 波形数据事件
        this.socket.on(DATA_EVENTS.WAVEFORM_DATA, (data) => {
            this.handleWaveformData(data);
        });

        this.socket.on(DEVICE_EVENTS.WAITING_DEVICE, () => {
            this.onWaitingDevice();
        });
    }


    handleWaveformData(data) {
        let { waveform_data: waveformData } = data;
        if (!waveformData) {
            return;
        }
        try {
            waveformData = JSON.parse(waveformData);
            waveformData = new Float32Array(waveformData);
            this.onWaveformUpdate(waveformData);
        } catch (e) {
            console.warn('handleWaveformData err', e);
        }
    }

    /**
     * 清理资源
     */
    cleanup() {
        super.cleanup();
        // 清理波形事件
        this.socket?.off(DATA_EVENTS.WAVEFORM_DATA);
        this.socket?.off(DEVICE_EVENTS.WAITING_DEVICE);
        this.logger?.report('AppAsrRecorder资源已清理');
    }
}
