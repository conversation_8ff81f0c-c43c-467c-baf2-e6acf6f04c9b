<template>
    <div
        class="play-pause-button"
        @click="handleClick"
    >
        <abc-icon
            v-if="!isPlaying"
            icon="s-play-fill"
            size="20"
            color="var(--abc-color-Y2)"
        ></abc-icon>

        <!-- 暂停图标 -->
        <abc-icon
            v-else
            icon="s-pause-fill"
            size="20"
            color="var(--abc-color-Y2)"
        ></abc-icon>
    </div>
</template>

<script>
    export default {
        name: 'PlayPauseButton',

        props: {
            // 是否正在播放状态
            isPlaying: {
                type: Boolean,
                default: false,
            },
        },

        methods: {
            handleClick() {
                // 触发播放/暂停事件
                this.$emit('toggle', !this.isPlaying);
            },
        },
    };
</script>

<style lang="scss" scoped>
@import "src/style/style.scss";

.play-pause-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    cursor: pointer;
    background: #ffffff;
    border-radius: 50%;
    box-shadow: 0 1px 2.5px 0 rgba(195, 195, 195, 0.24), 0 0 0 0.5px rgba(195, 195, 195, 0.12);
    transition: all 0.2s ease;

    // hover状态
    &:hover {
        background: rgba(246, 246, 246, 0.9);
    }

    // active状态（按下时）
    &:active {
        background: rgba(239, 239, 239, 0.9);
    }
}
</style>
