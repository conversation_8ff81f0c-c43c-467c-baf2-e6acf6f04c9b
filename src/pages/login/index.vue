<template>
    <div class="login-page">
        <abc-tabs-v2
            v-model="activeTab"
            :option="tabOptions"
            size="huge"
            center
            @change="handleTabChange"
        ></abc-tabs-v2>

        <abc-delete-icon
            variant="outline-square"
            size="hugely"
            class="login-close-btn"
            @delete="handleClose"
        ></abc-delete-icon>

        <!-- 登录内容区域 -->
        <div class="login-main-content">
            <img class="login-logo" :src="ImgLogo" />

            <!-- 微信扫码登录 -->
            <div v-if="activeTab === 'wechat'" class="wechat-login">
                <wechat-login-simple
                    @login-success="handleLoginSuccess"
                ></wechat-login-simple>
            </div>

            <!-- 手机验证码登录 -->
            <div v-if="activeTab === 'sms'" class="sms-login">
                <sms-login-simple @login-success="handleLoginSuccess"></sms-login-simple>
            </div>
        </div>
    </div>
</template>

<script>
    import WechatLogin from './components/wechat-login.vue';
    import SmsLogin from './components/sms-login.vue';
    import { useAuthStore } from '@/stores/auth';
    import { useIPC } from '@/utils/ipc-client';
    import ImgLogo from '@/assets/image/logo.svg';

    export default {
        name: 'LoginPage',
        components: {
            WechatLogin,
            SmsLogin,
        },
        data() {
            return {
                activeTab: 'wechat',
                ImgLogo,
                tabOptions: [
                    {
                        label: '微信登录', value: 'wechat',
                    },
                    {
                        label: '验证码登录', value: 'sms',
                    },
                ],
            };
        },
        computed: {
            authStore() {
                return useAuthStore();
            },
            loginLoading() {
                return this.authStore.loading;
            },
        },
        async mounted() {
            // 检查是否已经登录
            if (this.authStore.isLoggedIn) {
                this.redirectToMain();
            }
        },
        methods: {
            /**
             * 处理标签页切换
             */
            handleTabChange(value) {
                console.log('切换到:', value);
            },

            /**
             * 处理关闭窗口
             */
            async handleClose() {
                const ipc = useIPC();
                try {
                    await ipc.window.close();
                } catch (error) {
                    console.error('关闭窗口失败:', error);
                    window.close();
                }
            },

            /**
             * 处理登录成功
             */
            async handleLoginSuccess(loginData) {
                try {
                    // 使用 Pinia store 处理登录
                    const result = await this.authStore.login(loginData);

                    if (result.success) {
                        this.$handleSuccess('登录成功');

                        // 延迟跳转，让用户看到成功提示
                        // eslint-disable-next-line abc/no-timer-id
                        setTimeout(() => {
                            this.redirectToMain();
                        }, 1000);
                    } else {
                        throw new Error(result.error);
                    }
                } catch (error) {
                    this.$handleError(error, '登录处理失败');
                }
            },

            /**
             * 跳转到主页面
             */
            redirectToMain() {
                // 这里需要与 Electron 主进程通信，告知登录成功
                if (window.electronAPI) {
                    // 主进程会自动处理登录成功后的窗口跳转
                    console.log('登录成功，等待主进程处理窗口跳转');
                } else {
                    // 开发环境下的处理
                    window.location.href = '/consultation-list.html';
                }
            },
        },
    };
</script>

<style lang="scss">
    .login-page {
        position: relative;
        width: 380px;
        height: 520px;
        overflow: hidden;
        background: #ffffff;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);

        .login-close-btn {
            position: absolute;
            top: 8px;
            right: 8px;
        }

        .login-main-content {
            display: flex;
            flex-direction: column;
            align-items: center;

            .login-logo {
                width: 119px;
                height: 16px;
                margin-top: 60px;
            }

            .wechat-login,
            .sms-login {
                min-height: 300px;
            }
        }
    }
</style>
