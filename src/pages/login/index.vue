<template>
    <div class="login-page">
        <abc-tabs-v2
            v-model="activeTab"
            :option="tabOptions"
            size="huge"
            center
            @change="handleTabChange"
        ></abc-tabs-v2>

        <abc-delete-icon
            variant="outline-square"
            size="hugely"
            class="login-close-btn"
            @delete="handleClose"
        ></abc-delete-icon>

        <!-- 登录内容区域 -->
        <div class="login-main-content">
            <img class="login-logo" :src="ImgLogo" />

            <!-- 微信扫码登录 -->
            <div v-if="activeTab === 'wechat'" class="wechat-login">
                <wechat-login
                    @login-success="handleLoginSuccess"
                ></wechat-login>
            </div>

            <!-- 手机验证码登录 -->
            <div v-if="activeTab === 'sms'" class="sms-login">
                <sms-login @login-success="handleLoginSuccess"></sms-login>
            </div>
        </div>
    </div>
</template>

<script>
    import WechatLogin from './components/wechat-login.vue';
    import SmsLogin from './components/sms-login.vue';
    import { authManager } from '@/utils/auth';
    import ImgLogo from '@/assets/image/logo.svg';

    export default {
        name: 'LoginPage',
        components: {
            WechatLogin,
            SmsLogin,
        },
        data() {
            return {
                activeTab: 'wechat',
                loginLoading: false,
                ImgLogo,
                tabOptions: [
                    {
                        label: '微信登录', value: 'wechat',
                    },
                    {
                        label: '短信登录', value: 'sms',
                    },
                ],
            };
        },
        mounted() {
            // 检查是否已经登录
            if (authManager.isLoggedIn()) {
                this.redirectToMain();
            }
        },
        methods: {
            /**
             * 处理标签页切换
             */
            handleTabChange(value) {
                console.log('切换到:', value);
            },

            /**
             * 处理关闭窗口
             */
            handleClose() {
                if (window.electronAPI) {
                    window.electronAPI.closeWindow();
                } else {
                    window.close();
                }
            },

            /**
             * 处理登录成功
             */
            async handleLoginSuccess(loginData) {
                this.loginLoading = true;

                try {
                    // 保存登录信息
                    authManager.saveLoginData(loginData);

                    this.$handleSuccess('登录成功');

                    // 延迟跳转，让用户看到成功提示
                    // eslint-disable-next-line abc/no-timer-id
                    setTimeout(() => {
                        this.redirectToMain();
                    }, 1000);

                } catch (error) {
                    this.$handleError(error, '登录处理失败');
                    this.loginLoading = false;
                }
            },

            /**
             * 跳转到主页面
             */
            redirectToMain() {
                // 这里需要与 Electron 主进程通信，告知登录成功
                if (window.electronAPI) {
                    window.electronAPI.loginSuccess(authManager.getUser());
                } else {
                    // 开发环境下的处理
                    window.location.href = '/consultation-list.html';
                }
            },
        },
    };
</script>

<style lang="scss">
    .login-page {
        position: relative;
        width: 380px;
        height: 520px;
        overflow: hidden;
        background: #ffffff;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);

        .login-close-btn {
            position: absolute;
            top: 8px;
            right: 8px;
        }

        .login-main-content {
            display: flex;
            flex-direction: column;
            align-items: center;

            .login-logo {
                width: 119px;
                height: 16px;
                margin-top: 60px;
            }

            .wechat-login,
            .sms-login {
                min-height: 300px;
            }
        }
    }
</style>
