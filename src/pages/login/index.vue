<template>
  <div class="login-window">
    <!-- 窗口头部 -->
    <div class="window-header">
      <!-- ABC Logo -->
      <div class="logo">
        <abc-text size="small" theme="primary" bold>ABC</abc-text>
        <abc-text size="mini" theme="gray">数字医疗</abc-text>
      </div>

      <!-- 关闭按钮 -->
      <div class="close-btn" @click="handleClose">
        <abc-icon icon="close" size="16" color="#999" />
      </div>
    </div>

    <!-- 登录内容区域 -->
    <div class="login-content">
      <!-- Tab 切换 -->
      <abc-tabs-v2
        v-model="activeTab"
        :option="tabOptions"
        size="huge"
        @change="handleTabChange"
      />

      <!-- 微信扫码登录 -->
      <div v-if="activeTab === 'wechat'" class="wechat-login">
        <WechatLoginSimple @login-success="handleLoginSuccess" />
      </div>

      <!-- 手机验证码登录 -->
      <div v-if="activeTab === 'sms'" class="sms-login">
        <SmsLoginSimple @login-success="handleLoginSuccess" />
      </div>
    </div>

    <!-- 加载遮罩 -->
    <div v-if="loginLoading" class="login-loading">
      <abc-spin size="large" />
      <p>登录中...</p>
    </div>
  </div>
</template>

<script>
import WechatLoginSimple from '@/components/WechatLoginSimple.vue';
import SmsLoginSimple from '@/components/SmsLoginSimple.vue';
import { authManager } from '@/utils/auth';

export default {
  name: 'LoginPage',
  components: {
    WechatLoginSimple,
    SmsLoginSimple
  },
  data() {
    return {
      activeTab: 'wechat',
      loginLoading: false,
      tabOptions: [
        { label: '微信登录', value: 'wechat' },
        { label: '验证码登录', value: 'sms' }
      ]
    };
  },
  mounted() {
    // 检查是否已经登录
    if (authManager.isLoggedIn()) {
      this.redirectToMain();
    }
  },
  methods: {
    /**
     * 处理标签页切换
     */
    handleTabChange(value) {
      console.log('切换到:', value);
    },

    /**
     * 处理关闭窗口
     */
    handleClose() {
      if (window.electronAPI) {
        window.electronAPI.closeWindow();
      } else {
        window.close();
      }
    },

    /**
     * 处理登录成功
     */
    async handleLoginSuccess(loginData) {
      this.loginLoading = true;

      try {
        // 保存登录信息
        authManager.saveLoginData(loginData);

        this.$handleSuccess('登录成功');

        // 延迟跳转，让用户看到成功提示
        setTimeout(() => {
          this.redirectToMain();
        }, 1000);

      } catch (error) {
        this.$handleError(error, '登录处理失败');
        this.loginLoading = false;
      }
    },

    /**
     * 跳转到主页面
     */
    redirectToMain() {
      // 这里需要与 Electron 主进程通信，告知登录成功
      if (window.electronAPI) {
        window.electronAPI.loginSuccess(authManager.getUser());
      } else {
        // 开发环境下的处理
        window.location.href = '/consultation-list.html';
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.login-window {
  width: 400px;
  height: 500px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  position: relative;
  margin: 50px auto;
}

.window-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;

  .logo {
    display: flex;
    align-items: baseline;
    gap: 4px;
  }

  .close-btn {
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f5f5f5;
    }
  }
}

.login-content {
  padding: 20px;

  .abc-tabs-v2 {
    margin-bottom: 24px;
  }
}

.wechat-login,
.sms-login {
  min-height: 300px;
}

.login-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  z-index: 9999;

  p {
    margin-top: 16px;
    font-size: 14px;
    color: #666;
  }
}
</style>
