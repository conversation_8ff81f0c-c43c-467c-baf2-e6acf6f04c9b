<template>
    <div class="login-page">
        <!-- 背景装饰 -->
        <div class="background-decoration">
            <div class="decoration-circle circle-1"></div>
            <div class="decoration-circle circle-2"></div>
            <div class="decoration-circle circle-3"></div>
        </div>

        <!-- 主要内容区域 -->
        <div class="login-container">
            <!-- 左侧品牌区域 -->
            <div class="brand-section">
                <div class="brand-content">
                    <div class="logo">
                        <abc-icon icon="stethoscope" size="48" color="#409eff"></abc-icon>
                    </div>
                    <h1 class="brand-title">
                        Mira AI 医生助手
                    </h1>
                    <p class="brand-subtitle">
                        智能语音病历助手，让医疗记录更高效
                    </p>

                    <div class="features">
                        <div class="feature-item">
                            <abc-icon icon="microphone" size="20" color="#67c23a"></abc-icon>
                            <span>语音识别转录</span>
                        </div>
                        <div class="feature-item">
                            <abc-icon icon="robot" size="20" color="#e6a23c"></abc-icon>
                            <span>AI 智能生成病历</span>
                        </div>
                        <div class="feature-item">
                            <abc-icon icon="export" size="20" color="#f56c6c"></abc-icon>
                            <span>一键导入 HIS 系统</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧登录区域 -->
            <div class="login-section">
                <div class="login-card">
                    <div class="login-header">
                        <h2>欢迎使用</h2>
                        <p>请选择登录方式</p>
                    </div>

                    <!-- 登录方式切换 -->
                    <div class="login-tabs">
                        <abc-tabs v-model="activeTab" :option="tabOptions" @tab-click="handleTabClick">
                        </abc-tabs>

                        <wechat-login v-if="activeTab === 0" @login-success="handleLoginSuccess"></wechat-login>
                        <sms-login v-else @login-success="handleLoginSuccess"></sms-login>
                    </div>
                </div>

                <!-- 底部信息 -->
                <div class="login-footer">
                    <p>&copy; 2024 成都字节流科技有限公司</p>
                    <p>Mira AI 医生助手 v1.0.0</p>
                </div>
            </div>
        </div>

        <!-- 加载遮罩 -->
        <div v-if="loginLoading" class="login-loading">
            <abc-spin size="large"></abc-spin>
            <p>登录中...</p>
        </div>
    </div>
</template>

<script>
    import WechatLogin from '@/components/WechatLogin.vue';
    import SmsLogin from '@/components/SmsLogin.vue';
    import { authManager } from '@/utils/auth';

    export default {
        name: 'LoginPage',
        components: {
            WechatLogin,
            SmsLogin,
        },
        data() {
            return {
                activeTab: 'wechat',
                loginLoading: false,
                tabOptions: [
                    {
                        label: '微信登录', value: 0,
                    },
                    {
                        label: '手机登录', value: 1,
                    },
                ],
            };
        },
        mounted() {
            // 检查是否已经登录
            if (authManager.isLoggedIn()) {
                this.redirectToMain();
            }
        },
        methods: {
            /**
             * 处理标签页切换
             */
            handleTabClick(tab) {
                console.log('切换到:', tab.name);
            },

            /**
             * 处理登录成功
             */
            async handleLoginSuccess(loginData) {
                this.loginLoading = true;

                try {
                    // 保存登录信息
                    authManager.saveLoginData(loginData);

                    this.$handleSuccess('登录成功');

                    // 延迟跳转，让用户看到成功提示
                    setTimeout(() => {
                        this.redirectToMain();
                    }, 1000);

                } catch (error) {
                    this.$handleError(error, '登录处理失败');
                    this.loginLoading = false;
                }
            },

            /**
             * 跳转到主页面
             */
            redirectToMain() {
                // 这里需要与 Electron 主进程通信，告知登录成功
                if (window.electronAPI) {
                    window.electronAPI.loginSuccess(authManager.getUser());
                } else {
                    // 开发环境下的处理
                    window.location.href = '/consultation-list.html';
                }
            },
        },
    };
</script>

<style lang="scss">
    .login-page {
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
    }

    .background-decoration {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;

        .decoration-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);

            &.circle-1 {
                width: 200px;
                height: 200px;
                top: 10%;
                left: 10%;
                animation: float 6s ease-in-out infinite;
            }

            &.circle-2 {
                width: 150px;
                height: 150px;
                top: 60%;
                right: 15%;
                animation: float 8s ease-in-out infinite reverse;
            }

            &.circle-3 {
                width: 100px;
                height: 100px;
                bottom: 20%;
                left: 20%;
                animation: float 10s ease-in-out infinite;
            }
        }
    }

    @keyframes float {
        0%, 100% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(-20px);
        }
    }

    .login-container {
        display: flex;
        width: 100%;
        max-width: 1200px;
        margin: 0 auto;
        padding: 40px;
        gap: 60px;
    }

    .brand-section {
        flex: 1;
        display: flex;
        align-items: center;
        color: white;

        .brand-content {
            .logo {
                margin-bottom: 24px;
            }

            .brand-title {
                font-size: 36px;
                font-weight: 600;
                margin-bottom: 16px;
                line-height: 1.2;
            }

            .brand-subtitle {
                font-size: 18px;
                opacity: 0.9;
                margin-bottom: 40px;
                line-height: 1.5;
            }

            .features {
                .feature-item {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    margin-bottom: 16px;
                    font-size: 16px;
                    opacity: 0.9;
                }
            }
        }
    }

    .login-section {
        flex: 0 0 400px;
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .login-card {
        background: white;
        border-radius: 16px;
        padding: 40px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);

        .login-header {
            text-align: center;
            margin-bottom: 32px;

            h2 {
                font-size: 24px;
                font-weight: 600;
                color: #303133;
                margin-bottom: 8px;
            }

            p {
                color: #909399;
                font-size: 14px;
            }
        }
    }

    .login-footer {
        text-align: center;
        color: rgba(255, 255, 255, 0.8);
        font-size: 12px;

        p {
            margin: 4px 0;
        }
    }

    .login-loading {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: white;
        z-index: 9999;

        p {
            margin-top: 16px;
            font-size: 16px;
        }
    }

    // 响应式设计
    @media (max-width: 768px) {
        .login-container {
            flex-direction: column;
            padding: 20px;
            gap: 30px;
        }

        .brand-section {
            text-align: center;

            .brand-title {
                font-size: 28px;
            }

            .brand-subtitle {
                font-size: 16px;
            }
        }

        .login-section {
            flex: none;
        }

        .login-card {
            padding: 30px 20px;
        }
    }
</style>
