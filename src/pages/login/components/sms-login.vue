<template>
    <div class="mobile-login" style="position: relative;">
        <abc-form ref="postData" class="mobile-login-form">
            <abc-form-item-group grid grid-gap="12px">
                <abc-form-item :validate-event="handleMobileValidate" :error="mobileError">
                    <abc-input-mobile
                        v-model="postData.mobile"
                        class="login-input-mobile"
                        data-cy="login-input-mobile"
                        size="large"
                        :country-code.sync="postData.countryCode"
                        placeholder="手机号"
                        :width="300"
                        :country-code-width="81"
                        custom-space-border-style="solid"
                        @input-enter="focusCode"
                        @input-change="clearErrorMsg"
                    ></abc-input-mobile>
                </abc-form-item>
                <abc-form-item :validate-event="validateCode">
                    <abc-input
                        ref="verifyinput"
                        v-model="postData.verify"
                        placeholder="验证码"
                        size="large"
                        class="login-input-verify-code"
                        @enter="submit('postData')"
                    >
                        <template slot="appendInner">
                            <abc-button
                                v-if="sendCode"
                                variant="text"
                                theme="default"
                                :min-width="100"
                                disabled
                            >
                                {{ count }}s
                            </abc-button>
                            <abc-button
                                v-else
                                :min-width="100"
                                variant="text"
                                :disabled-code="disabledCode"
                                @click="throttleFetchCode"
                            >
                                获取验证码
                            </abc-button>
                        </template>
                    </abc-input>
                </abc-form-item>
                <abc-button
                    :loading="buttonLoading"
                    size="large"
                    style="margin-bottom: 60px; font-weight: 500;"
                    class="login-button"
                    data-cy="login-button-login"
                    :disabled="disabledCode"
                    @click="submit('postData')"
                >
                    登 录
                </abc-button>
            </abc-form-item-group>
        </abc-form>
    </div>
</template>


<script>
    import { debounce } from '@/utils/lodash.js';
    import {
        validateCode, validateMobile,
    } from '@/utils/validate.js';
    import { TIME_COUNT } from '../constants';
    import { useAuthStore } from '@/stores/auth.js';
    import {
        mapActions, mapState,
    } from 'pinia';
    import {
        defaultCountryCode, supportSMSCountryCodeList,
    } from '@/utils/country-codes';
    import { authAPI } from '@/api/auth';

    export default {
        name: 'LoginCard',
        props: {

        },
        data() {
            return {
                mobileError: {
                    error: false,
                    message: '',
                },
                errMessage: '',// 绑定微信失败提示
                postData: {
                    mobile: sessionStorage.getItem('_mobile_') || '',
                    countryCode: sessionStorage.getItem('_country_code_') || defaultCountryCode,
                    verify: '',
                },

                count: '', // 倒计时
                timer_: null, // 定时器
                sendCode: false,
                buttonLoading: false,
            };
        },
        computed: {
            ...mapState(useAuthStore, [
                'userInfo',
            ]),
            disabledCode() {
                // 验证码登录不支持发送验证码的区号
                return !supportSMSCountryCodeList.includes(this.postData.countryCode);
            },
        },
        created() {
            this.throttleFetchCode = debounce(this.fetchCode.bind(this), 1000);
        },

        async mounted() {
            if (this.userInfo) {
                const err = await this.getEmployeeInfo();
                if (!err) {
                    // 拉取出错，显示二维码扫码登录
                    // 有登录状态，拉取最近登录记录，让用户选取
                    await this.getLoginRecord();

                }
            }
        },
        beforeDestroy() {
            sessionStorage.removeItem('_mobile_');
        },
        methods: {
            validateCode,
            validateMobile,
            ...mapActions(useAuthStore, [
                'globalSwitchClinic',
                'globalLoginBySms',
                'getEmployeeInfo',
            ]),

            /**
             * desc [聚焦到验证码输入框]
             */
            focusCode() {
                this.$nextTick(() => {
                    this.$refs.verifyinput?.$refs?.abcinput?.focus();
                });
            },
            clearErrorMsg() {
                this.mobileError = {
                    error: false,
                    message: '',
                };
                this.errMessage = '';
            },
            // 展示错误的toast提示
            showErrorMessage(message, className) {
                const position = this.$refs.toastRef.$el.getBoundingClientRect();
                console.log('position', position);
                this.$Toast({
                    referenceEl: this.$refs.toastRef.$el,
                    type: 'error',
                    message,
                    customClass: className || 'abc-login-error-message-toast',
                });
            },

            /**
             * desc [验证手机号发送验证码]
             */
            async fetchCode() {
                // 不支持验证码 return
                if (this.disabledCode) {
                    this.showErrorMessage('海外短信无法接收验证码');
                    return;
                }
                if (this.sendCode) return false;
                let { mobile } = this.postData;

                mobile = `${mobile}`;
                if (!mobile) return;
                const virtualMobileReg = /120\d{8}/;
                if (this.postData.countryCode === '86' && virtualMobileReg.test(mobile)) {
                    this.mobileError = {
                        error: true,
                        message: '此账号为虚拟账号，不支持验证码登录，请使用密码登录',
                    };
                    this.showErrorMessage('此账号为虚拟账号，不支持验证码登录，请使用密码登录', 'abc-login-error-message-toast-line-2');
                    return;
                }
                this.validateMobile(mobile, async (res) => {
                    if (!res.validate) {
                        this.mobileError = {
                            error: true,
                            message: res.message,
                        };
                        return;
                    }
                    try {
                        let info = null;
                        info = await authAPI.sendSms({
                            mobile,
                            countryCode: this.postData.countryCode,
                            action: 'login',
                        });
                        if (info && info.data) {
                            sessionStorage.setItem('_time_count_', +new Date + TIME_COUNT * 1000);
                            sessionStorage.setItem('_mobile_', mobile);
                            sessionStorage.setItem('_country_code_', this.postData.countryCode);
                            this.handleTimer();
                        }
                        this.focusCode();
                    } catch (error) {
                        this.showErrorMessage(error.message);
                    }
                });
            },
            /**
             * desc [处理倒计时]
             */
            handleTimer(count = TIME_COUNT) {
                this.sendCode = true;
                if (!this.timer_) {
                    this.count = count;
                    this.sendCode = true;
                    this.timer_ = setInterval(() => {
                        if (this.count > 0 && this.count <= count) {
                            this.count--;
                        } else {
                            this.sendCode = false;
                            sessionStorage.removeItem('_time_count_');
                            sessionStorage.removeItem('_mobile_');
                            clearInterval(this.timer_);
                            this.timer_ = null;
                        }
                    }, 1000);
                }
            },

            /**
             * [短信登录验证]
             */
            submit(postForm) {
                let { mobile } = this.postData;
                mobile = `${mobile}` ;
                this.validateMobile(mobile, () => {
                    this.$refs[postForm].validate(async (valid) => {
                        if (valid) {
                            this.buttonLoading = true;
                            const err = await this.globalLoginBySms({
                                ...this.postData,
                            });
                            if (!err) {
                                // 登录成功
                            } else {
                                this.showErrorMessage(err.message);
                            }
                            this.buttonLoading = false;
                        } else {
                            return false;
                        }
                    });
                });
            },

            handleMobileValidate(values, callback) {
                const [countryCode = '', mobile = ''] = values;
                if (!countryCode) {
                    return callback({
                        validate: false, message: '无法确认手机号所在地区，请联系客服！',
                    });
                }

                const virtualMobileReg = /120\d{8}/;
                if (this.postData.countryCode === '86' && virtualMobileReg.test(mobile)) {
                    this.showErrorMessage('此账号为虚拟账号，不支持验证码登录，请使用微信扫码登录', 'abc-login-error-message-toast-line-2');
                    return callback({
                        validate: false,
                        message: '此账号为虚拟账号，不支持验证码登录，请使用微信扫码登录',
                    });
                }
                this.validateMobile(mobile, callback, this.postData.countryCode);
            },
        },
    };
</script>

<style lang="scss">
    .mobile-login {
        display: flex;
        flex-direction: column;
        align-items: center;

        .mobile-login-form {
            width: 300px;
            margin-top: 55px;

            .login-input-mobile,
            .login-input-verify-code {
                .abc-input__inner {
                    height: 48px;
                    padding-right: 16px;
                    padding-left: 16px;
                    background: var(--abc-color-cp-grey4, #f2f4f7);
                    border-color: transparent;
                    border-radius: 100px;
                }
            }

            .login-button {
                height: 48px;
                font-size: 17px;
                border-radius: 100px;
            }
        }
    }
</style>
