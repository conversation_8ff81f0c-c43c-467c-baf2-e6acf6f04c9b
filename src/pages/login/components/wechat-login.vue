<template>
    <div class="wechat-login">
        <!-- 初次登录 - 显示二维码 -->
        <div v-if="!isScanned && !userInfo" class="qr-login">
            <abc-qr-code
                custom-class="qr-container"
                :loading="loading"
                :need-refresh="isExpired"
                :src="qrCodeUrl"
                :width="192"
                :margin="0"
                @refresh="refreshQRCode"
            ></abc-qr-code>

            <abc-text size="small" theme="gray" style=" margin-top: 21px; font-size: 20px; text-align: center;">
                微信扫一扫立即使用
            </abc-text>
        </div>

        <!-- 扫码成功 - 显示用户信息 -->
        <div v-else-if="userInfo" class="user-confirm">
            <div class="user-avatar">
                <img :src="userInfo.avatar || defaultAvatar" alt="用户头像" />
            </div>

            <abc-text size="large" theme="black" style=" margin: 16px 0 8px; text-align: center;">
                {{ userInfo.nickname || '微信名称' }}
            </abc-text>

            <abc-button
                type="primary"
                size="large"
                :loading="confirming"
                style="width: 100%; margin-bottom: 16px;"
                @click="confirmLogin"
            >
                进入ABC医生助手
            </abc-button>

            <abc-text
                size="small"
                theme="primary"
                style="text-align: center; cursor: pointer;"
                @click="switchAccount"
            >
                切换账号
            </abc-text>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'WechatLoginSimple',
        data() {
            return {
                qrCodeUrl: '',
                qrCodeId: '',
                loading: false,
                isExpired: false,
                isScanned: false,
                userInfo: null,
                confirming: false,
                checkTimer: null,
                expireTimer: null,
                defaultAvatar: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzIiIGZpbGw9IiNGNUY1RjUiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSIyNCIgcj0iMTAiIGZpbGw9IiNEOUQ5RDkiLz4KPHBhdGggZD0iTTEyIDUyQzEyIDQzLjE2MzQgMTkuMTYzNCAzNiAyOCAzNkg0NEM1Mi44MzY2IDM2IDYwIDQzLjE2MzQgNjAgNTJWNjRIMTJWNTJaIiBmaWxsPSIjRDlEOUQ5Ii8+Cjwvc3ZnPgo=',
            };
        },
        mounted() {
            this.generateQRCode();
        },
        beforeDestroy() {
            this.clearTimers();
        },
        methods: {
            /**
             * 生成二维码
             */
            async generateQRCode() {
                this.loading = true;
                this.isExpired = false;
                this.isScanned = false;
                this.userInfo = null;

                try {
                    // 模拟 API 调用
                    await new Promise((resolve) => setTimeout(resolve, 1000));

                    // 模拟二维码数据
                    this.qrCodeUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
                    this.qrCodeId = `mock_qr_${Date.now()}`;

                    // 开始检查扫码状态
                    this.startCheckStatus();

                    // 设置二维码过期定时器（5分钟）
                    this.expireTimer = setTimeout(() => {
                        this.isExpired = true;
                        this.clearTimers();
                    }, 5 * 60 * 1000);

                } catch (error) {
                    this.$message.error('获取二维码失败');
                } finally {
                    this.loading = false;
                }
            },

            /**
             * 开始检查扫码状态
             */
            startCheckStatus() {
                if (this.checkTimer) {
                    clearInterval(this.checkTimer);
                }

                // this.checkTimer = setInterval(() => {
                //     // 模拟扫码状态检查
                //     if (!this.isScanned && Math.random() > 0.7) {
                //         this.isScanned = true;
                //         // 模拟用户信息
                //         this.userInfo = {
                //             nickname: '微信用户',
                //             avatar: this.defaultAvatar,
                //         };
                //         this.clearTimers();
                //     }
                // }, 3000); // 每3秒检查一次
            },

            /**
             * 确认登录
             */
            async confirmLogin() {
                this.confirming = true;

                try {
                    // 模拟登录 API 调用
                    await new Promise((resolve) => setTimeout(resolve, 1500));

                    const loginData = {
                        token: `mock_token_${Date.now()}`,
                        refreshToken: `mock_refresh_token_${Date.now()}`,
                        user: {
                            id: 'mock_user_id',
                            nickname: this.userInfo.nickname,
                            avatar: this.userInfo.avatar,
                            loginType: 'wechat',
                        },
                    };

                    this.$emit('login-success', loginData);

                } catch (error) {
                    this.$message.error('登录失败，请重试');
                } finally {
                    this.confirming = false;
                }
            },

            /**
             * 切换账号
             */
            switchAccount() {
                this.userInfo = null;
                this.isScanned = false;
                this.generateQRCode();
            },

            /**
             * 刷新二维码
             */
            refreshQRCode() {
                this.clearTimers();
                this.generateQRCode();
            },

            /**
             * 清除定时器
             */
            clearTimers() {
                if (this.checkTimer) {
                    clearInterval(this.checkTimer);
                    this.checkTimer = null;
                }
                if (this.expireTimer) {
                    clearTimeout(this.expireTimer);
                    this.expireTimer = null;
                }
            },
        },
    };
</script>

<style lang="scss">
.wechat-login {
    .qr-login {
        display: flex;
        flex-direction: column;
        align-items: center;

        .qr-container {
            width: 192px;
            height: 192px;
            margin-top: 48px !important;
        }

        .loading-state,
        .expired-state {
            color: #909399;
            text-align: center;
        }
    }

    .user-confirm {
        padding: 20px 0;
        text-align: center;

        .user-avatar {
            display: flex;
            justify-content: center;
            margin-bottom: 16px;

            img {
                width: 64px;
                height: 64px;
                border: 2px solid #f0f0f0;
                border-radius: 50%;
            }
        }
    }
}
</style>
