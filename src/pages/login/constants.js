/*
 * <AUTHOR>
 * @DateTime 2020-09-17 21:08:47
 */
const version = 'v2';

// 下次自动登录，KEY
export const KEY_REMEMBER = `${version}_remember_login`;
export const USER_INFO = '_user_info_';
export const CLINICS = '_clinics_';

export const CSRF_TOKEN = 'x-csrf-token';

export const CSRF_TOKEN_SIG = 'x-csrf-token.sig';

export const LOGIN_WAY_KEY = '_login_way';


// 默认倒计时时间(60s)
export const TIME_COUNT = 60;

/**
 * desc [登录额外信息]
 */
export const getClientInfo = () => {
    return {
        availWidth: window.screen.availWidth, // 屏幕的可用宽度
        availHeight: window.screen.availHeight, // 屏幕的可用高度
        screenWidth: window.screen.width, // 屏幕的宽度
        screenHeight: window.screen.height, // 屏幕的高度
    };
};
