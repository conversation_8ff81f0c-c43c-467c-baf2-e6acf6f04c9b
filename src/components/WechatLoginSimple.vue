<template>
  <div class="wechat-login-simple">
    <!-- 初次登录 - 显示二维码 -->
    <div v-if="!isScanned && !userInfo" class="qr-login">
      <div class="qr-container">
        <div v-if="qrCodeUrl && !isExpired" class="qr-code">
          <img :src="qrCodeUrl" alt="微信扫码登录" />
        </div>
        
        <div v-else-if="loading" class="loading-state">
          <abc-spin size="large" />
        </div>
        
        <div v-else-if="isExpired" class="expired-state">
          <abc-icon icon="refresh" size="48" color="#ccc" />
          <abc-button type="primary" @click="refreshQRCode" style="margin-top: 16px;">
            刷新二维码
          </abc-button>
        </div>
      </div>
      
      <abc-text size="small" theme="gray" style="text-align: center; margin-top: 16px;">
        微信扫一扫立即使用
      </abc-text>
    </div>

    <!-- 扫码成功 - 显示用户信息 -->
    <div v-else-if="userInfo" class="user-confirm">
      <div class="user-avatar">
        <img :src="userInfo.avatar || defaultAvatar" alt="用户头像" />
      </div>
      
      <abc-text size="large" theme="black" style="text-align: center; margin: 16px 0 8px;">
        {{ userInfo.nickname || '微信名称' }}
      </abc-text>
      
      <abc-button 
        type="primary" 
        size="large" 
        :loading="confirming"
        @click="confirmLogin"
        style="width: 100%; margin-bottom: 16px;"
      >
        进入ABC医生助手
      </abc-button>
      
      <abc-text 
        size="small" 
        theme="primary" 
        style="text-align: center; cursor: pointer;"
        @click="switchAccount"
      >
        切换账号
      </abc-text>
    </div>
  </div>
</template>

<script>
import { authAPI } from '@/api/auth';

export default {
  name: 'WechatLoginSimple',
  data() {
    return {
      qrCodeUrl: '',
      qrCodeId: '',
      loading: false,
      isExpired: false,
      isScanned: false,
      userInfo: null,
      confirming: false,
      checkTimer: null,
      expireTimer: null,
      defaultAvatar: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzIiIGZpbGw9IiNGNUY1RjUiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSIyNCIgcj0iMTAiIGZpbGw9IiNEOUQ5RDkiLz4KPHBhdGggZD0iTTEyIDUyQzEyIDQzLjE2MzQgMTkuMTYzNCAzNiAyOCAzNkg0NEM1Mi44MzY2IDM2IDYwIDQzLjE2MzQgNjAgNTJWNjRIMTJWNTJaIiBmaWxsPSIjRDlEOUQ5Ii8+Cjwvc3ZnPgo='
    };
  },
  mounted() {
    this.generateQRCode();
  },
  beforeDestroy() {
    this.clearTimers();
  },
  methods: {
    /**
     * 生成二维码
     */
    async generateQRCode() {
      this.loading = true;
      this.isExpired = false;
      this.isScanned = false;
      this.userInfo = null;

      try {
        // 模拟 API 调用
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 模拟二维码数据
        this.qrCodeUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
        this.qrCodeId = 'mock_qr_' + Date.now();
        
        // 开始检查扫码状态
        this.startCheckStatus();
        
        // 设置二维码过期定时器（5分钟）
        this.expireTimer = setTimeout(() => {
          this.isExpired = true;
          this.clearTimers();
        }, 5 * 60 * 1000);
        
      } catch (error) {
        this.$message.error('获取二维码失败');
      } finally {
        this.loading = false;
      }
    },

    /**
     * 开始检查扫码状态
     */
    startCheckStatus() {
      if (this.checkTimer) {
        clearInterval(this.checkTimer);
      }

      this.checkTimer = setInterval(() => {
        // 模拟扫码状态检查
        if (!this.isScanned && Math.random() > 0.7) {
          this.isScanned = true;
          // 模拟用户信息
          this.userInfo = {
            nickname: '微信用户',
            avatar: this.defaultAvatar
          };
          this.clearTimers();
        }
      }, 3000); // 每3秒检查一次
    },

    /**
     * 确认登录
     */
    async confirmLogin() {
      this.confirming = true;
      
      try {
        // 模拟登录 API 调用
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        const loginData = {
          token: 'mock_token_' + Date.now(),
          refreshToken: 'mock_refresh_token_' + Date.now(),
          user: {
            id: 'mock_user_id',
            nickname: this.userInfo.nickname,
            avatar: this.userInfo.avatar,
            loginType: 'wechat'
          }
        };
        
        this.$emit('login-success', loginData);
        
      } catch (error) {
        this.$message.error('登录失败，请重试');
      } finally {
        this.confirming = false;
      }
    },

    /**
     * 切换账号
     */
    switchAccount() {
      this.userInfo = null;
      this.isScanned = false;
      this.generateQRCode();
    },

    /**
     * 刷新二维码
     */
    refreshQRCode() {
      this.clearTimers();
      this.generateQRCode();
    },

    /**
     * 清除定时器
     */
    clearTimers() {
      if (this.checkTimer) {
        clearInterval(this.checkTimer);
        this.checkTimer = null;
      }
      if (this.expireTimer) {
        clearTimeout(this.expireTimer);
        this.expireTimer = null;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.wechat-login-simple {
  .qr-login {
    .qr-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 200px;
      margin-bottom: 16px;
    }
    
    .qr-code img {
      width: 160px;
      height: 160px;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      background: #f5f5f5;
    }
    
    .loading-state,
    .expired-state {
      text-align: center;
      color: #909399;
    }
  }
  
  .user-confirm {
    text-align: center;
    padding: 20px 0;
    
    .user-avatar {
      display: flex;
      justify-content: center;
      margin-bottom: 16px;
      
      img {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        border: 2px solid #f0f0f0;
      }
    }
  }
}
</style>
