<template>
    <div class="sms-login">
        <abc-form
            ref="loginForm"
            :model="form"
            :rules="rules"
            label-width="0"
            @submit.native.prevent="handleLogin"
        >
            <!-- 手机号输入 -->
            <abc-form-item prop="phone">
                <abc-input
                    v-model="form.phone"
                    placeholder="请输入手机号"
                    size="large"
                    maxlength="11"
                    @input="handlePhoneInput"
                >
                    <template #prefix>
                        <abc-icon icon="phone"></abc-icon>
                    </template>
                </abc-input>
            </abc-form-item>

            <!-- 验证码输入 -->
            <abc-form-item prop="code">
                <div class="code-input-group">
                    <abc-input
                        v-model="form.code"
                        placeholder="请输入验证码"
                        size="large"
                        maxlength="6"
                        @input="handleCodeInput"
                    >
                        <template #prefix>
                            <abc-icon icon="shield"></abc-icon>
                        </template>
                    </abc-input>

                    <abc-button
                        :disabled="!canSendCode || countdown.isRunning()"
                        :loading="sendingCode"
                        size="large"
                        @click="sendCode"
                    >
                        {{ getCodeButtonText() }}
                    </abc-button>
                </div>
            </abc-form-item>

            <!-- 登录按钮 -->
            <abc-form-item>
                <abc-button
                    type="primary"
                    size="large"
                    :loading="logging"
                    :disabled="!canLogin"
                    native-type="submit"
                    style="width: 100%"
                >
                    登录
                </abc-button>
            </abc-form-item>
        </abc-form>

        <!-- 用户协议 -->
        <div class="agreement">
            <abc-checkbox v-model="agreedToTerms" size="small">
                我已阅读并同意
                <a href="#" @click.prevent="showTerms">《用户协议》</a>
                和
                <a href="#" @click.prevent="showPrivacy">《隐私政策》</a>
            </abc-checkbox>
        </div>
    </div>
</template>

<script>
    import { authAPI } from '@/api/auth';
    import {
        phoneValidator, codeValidator, CountdownTimer,
    } from '@/utils/auth';

    export default {
        name: 'SmsLogin',
        data() {
            return {
                form: {
                    phone: '',
                    code: '',
                },
                rules: {
                    phone: [
                        {
                            required: true, message: '请输入手机号', trigger: 'blur',
                        },
                        {
                            validator: this.validatePhone, trigger: 'blur',
                        },
                    ],
                    code: [
                        {
                            required: true, message: '请输入验证码', trigger: 'blur',
                        },
                        {
                            validator: this.validateCode, trigger: 'blur',
                        },
                    ],
                },
                sendingCode: false,
                logging: false,
                agreedToTerms: false,
                countdown: new CountdownTimer(60),
                countdownText: '',
            };
        },
        computed: {
            canSendCode() {
                return phoneValidator.isValid(this.form.phone) && !this.countdown.isRunning();
            },
            canLogin() {
                return phoneValidator.isValid(this.form.phone) &&
                    codeValidator.isValid(this.form.code) &&
                    this.agreedToTerms;
            },
        },
        beforeDestroy() {
            this.countdown.stop();
        },
        methods: {
            /**
             * 手机号验证器
             */
            validatePhone(rule, value, callback) {
                if (!phoneValidator.isValid(value)) {
                    callback(new Error('请输入正确的手机号'));
                } else {
                    callback();
                }
            },

            /**
             * 验证码验证器
             */
            validateCode(rule, value, callback) {
                if (!codeValidator.isValid(value)) {
                    callback(new Error('请输入6位数字验证码'));
                } else {
                    callback();
                }
            },

            /**
             * 处理手机号输入
             */
            handlePhoneInput(value) {
                // 只允许输入数字
                this.form.phone = value.replace(/\D/g, '');
            },

            /**
             * 处理验证码输入
             */
            handleCodeInput(value) {
                // 只允许输入数字
                this.form.code = value.replace(/\D/g, '');
            },

            /**
             * 发送验证码
             */
            async sendCode() {
                if (!this.canSendCode) return;

                this.sendingCode = true;
                try {
                    await authAPI.sendSmsCode(this.form.phone);
                    this.$handleSuccess('验证码已发送');

                    // 开始倒计时
                    this.countdown.start((remaining) => {
                        if (remaining > 0) {
                            this.countdownText = `${remaining}s`;
                        } else {
                            this.countdownText = '';
                        }
                    });

                } catch (error) {
                    this.$handleError(error, '发送验证码失败');
                } finally {
                    this.sendingCode = false;
                }
            },

            /**
             * 获取验证码按钮文本
             */
            getCodeButtonText() {
                if (this.sendingCode) {
                    return '发送中...';
                }
                if (this.countdown.isRunning()) {
                    return this.countdownText;
                }
                return '获取验证码';
            },

            /**
             * 处理登录
             */
            async handleLogin() {
                if (!this.canLogin) return;

                // 表单验证
                try {
                    await this.$refs.loginForm.validate();
                } catch (error) {
                    return;
                }

                this.logging = true;
                try {
                    const loginData = await authAPI.loginWithSmsCode(this.form.phone, this.form.code);
                    this.$emit('login-success', loginData);
                } catch (error) {
                    this.$handleError(error, '登录失败');
                } finally {
                    this.logging = false;
                }
            },

            /**
             * 显示用户协议
             */
            showTerms() {
                this.$message.info('用户协议功能待实现');
            },

            /**
             * 显示隐私政策
             */
            showPrivacy() {
                this.$message.info('隐私政策功能待实现');
            },
        },
    };
</script>

<style lang="scss">
.sms-login {
  .code-input-group {
    display: flex;
    gap: 12px;

    .abc-input {
      flex: 1;
    }

    .abc-button {
      flex-shrink: 0;
      min-width: 120px;
    }
  }

  .agreement {
    margin-top: 20px;
    text-align: center;
    font-size: 12px;
    color: #909399;

    a {
      color: #409eff;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

// 表单项间距调整
.abc-form-item {
  margin-bottom: 20px;
}
</style>
