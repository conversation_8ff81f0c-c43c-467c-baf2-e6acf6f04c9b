<template>
    <div class="wechat-login">
        <div class="qr-container">
            <!-- 二维码显示区域 -->
            <div v-if="qrCodeUrl && !isExpired" class="qr-code">
                <img :src="qrCodeUrl" alt="微信扫码登录" />
                <div class="qr-tips">
                    <abc-icon icon="wechat" size="16" color="#07C160"></abc-icon>
                    <span>请使用微信扫码登录</span>
                </div>
            </div>

            <!-- 加载状态 -->
            <div v-else-if="loading" class="loading-state">
                <abc-spin size="large"></abc-spin>
                <p>正在生成二维码...</p>
            </div>

            <!-- 二维码过期状态 -->
            <div v-else-if="isExpired" class="expired-state">
                <abc-icon icon="refresh" size="48" color="#ccc"></abc-icon>
                <p>二维码已过期</p>
                <abc-button type="primary" @click="refreshQRCode">
                    刷新二维码
                </abc-button>
            </div>

            <!-- 错误状态 -->
            <div v-else-if="error" class="error-state">
                <abc-icon icon="warning" size="48" color="#f56c6c"></abc-icon>
                <p>{{ error }}</p>
                <abc-button type="primary" @click="refreshQRCode">
                    重新获取
                </abc-button>
            </div>
        </div>

        <!-- 登录状态提示 -->
        <div v-if="scanStatus" class="scan-status">
            <div v-if="scanStatus === 'scanned'" class="status-item">
                <abc-icon icon="check-circle" size="16" color="#67c23a"></abc-icon>
                <span>扫码成功，请在手机上确认登录</span>
            </div>
            <div v-else-if="scanStatus === 'confirmed'" class="status-item">
                <abc-icon icon="check-circle" size="16" color="#67c23a"></abc-icon>
                <span>登录确认中...</span>
            </div>
        </div>
    </div>
</template>

<script>
    import { authAPI } from '@/api/auth';

    export default {
        name: 'WechatLogin',
        data() {
            return {
                qrCodeUrl: '',
                qrCodeId: '',
                loading: false,
                error: '',
                isExpired: false,
                scanStatus: '', // 'scanned', 'confirmed'
                checkTimer: null,
                expireTimer: null,
            };
        },
        mounted() {
            this.generateQRCode();
        },
        beforeDestroy() {
            this.clearTimers();
        },
        methods: {
            /**
             * 生成二维码
             */
            async generateQRCode() {
                this.loading = true;
                this.error = '';
                this.isExpired = false;
                this.scanStatus = '';

                try {
                    const result = await authAPI.getWechatQRCode();
                    this.qrCodeUrl = result.qrCodeUrl;
                    this.qrCodeId = result.qrCodeId;

                    // 开始检查扫码状态
                    this.startCheckStatus();

                    // 设置二维码过期定时器（5分钟）
                    this.expireTimer = setTimeout(() => {
                        this.isExpired = true;
                        this.clearTimers();
                    }, 5 * 60 * 1000);

                } catch (error) {
                    this.error = error.message || '获取二维码失败';
                } finally {
                    this.loading = false;
                }
            },

            /**
             * 开始检查扫码状态
             */
            startCheckStatus() {
                if (this.checkTimer) {
                    clearInterval(this.checkTimer);
                }

                this.checkTimer = setInterval(async () => {
                    try {
                        const result = await authAPI.checkWechatScanStatus(this.qrCodeId);

                        if (result.status === 'scanned') {
                            this.scanStatus = 'scanned';
                        } else if (result.status === 'confirmed') {
                            this.scanStatus = 'confirmed';
                            // 登录成功
                            this.$emit('login-success', result.loginData);
                            this.clearTimers();
                        } else if (result.status === 'expired') {
                            this.isExpired = true;
                            this.clearTimers();
                        }
                    } catch (error) {
                        console.error('检查扫码状态失败:', error);
                    }
                }, 2000); // 每2秒检查一次
            },

            /**
             * 刷新二维码
             */
            refreshQRCode() {
                this.clearTimers();
                this.generateQRCode();
            },

            /**
             * 清除定时器
             */
            clearTimers() {
                if (this.checkTimer) {
                    clearInterval(this.checkTimer);
                    this.checkTimer = null;
                }
                if (this.expireTimer) {
                    clearTimeout(this.expireTimer);
                    this.expireTimer = null;
                }
            },
        },
    };
</script>

<style lang="scss">
.wechat-login {
  .qr-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 280px;
    justify-content: center;
  }

  .qr-code {
    text-align: center;

    img {
      width: 200px;
      height: 200px;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
    }

    .qr-tips {
      margin-top: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      color: #606266;
      font-size: 14px;
    }
  }

  .loading-state,
  .expired-state,
  .error-state {
    text-align: center;
    color: #909399;

    p {
      margin: 16px 0;
      font-size: 14px;
    }
  }

  .expired-state,
  .error-state {
    .abc-icon {
      margin-bottom: 16px;
    }
  }

  .scan-status {
    margin-top: 20px;
    text-align: center;

    .status-item {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 12px;
      background-color: #f0f9ff;
      border: 1px solid #b3d8ff;
      border-radius: 6px;
      color: #409eff;
      font-size: 14px;
    }
  }
}
</style>
