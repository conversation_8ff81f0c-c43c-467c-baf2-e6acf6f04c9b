/**
 * 主进程集成示例
 * 展示如何在主进程中集成认证管理器
 */

import { app, BrowserWindow } from 'electron';
import { createMainAuthManager } from './auth-manager.js';

/**
 * 模拟的共享偏好设置实现
 */
class MockSharedPreferences {
  constructor() {
    this.data = new Map();
  }

  setString(key, value) {
    this.data.set(key, value);
  }

  getString(key, defaultValue = null) {
    return this.data.get(key) || defaultValue;
  }

  setLong(key, value) {
    this.data.set(key, value);
  }

  getLong(key, defaultValue = null) {
    return this.data.get(key) || defaultValue;
  }

  setBoolean(key, value) {
    this.data.set(key, value);
  }

  getBoolean(key, defaultValue = false) {
    return this.data.get(key) || defaultValue;
  }

  remove(key) {
    this.data.delete(key);
  }

  clear() {
    this.data.clear();
  }
}

/**
 * 模拟的窗口管理器实现
 */
class MockWindowManager {
  constructor() {
    this.windows = new Set();
  }

  getAllWindows() {
    return Array.from(this.windows).filter(win => !win.isDestroyed());
  }

  getWindowByWebContents(webContents) {
    return this.getAllWindows().find(win => win.webContents === webContents) || null;
  }

  createWindow(options) {
    const window = new BrowserWindow(options);
    this.windows.add(window);
    
    window.on('closed', () => {
      this.windows.delete(window);
    });
    
    return window;
  }

  addWindow(window) {
    this.windows.add(window);
  }
}

/**
 * 主应用类
 */
class MiraMainApp {
  constructor() {
    this.sharedPreferences = new MockSharedPreferences();
    this.windowManager = new MockWindowManager();
    this.authManager = null;
    this.loginWindow = null;
    this.mainWindow = null;
  }

  /**
   * 初始化应用
   */
  async initialize() {
    // 创建认证管理器
    this.authManager = createMainAuthManager(
      this.sharedPreferences,
      this.windowManager
    );

    // 监听认证事件
    this._setupAuthEventListeners();

    // 检查登录状态并创建相应窗口
    await this._checkAuthStateAndCreateWindow();

    console.log('Mira 主应用初始化完成');
  }

  /**
   * 设置认证事件监听器
   * @private
   */
  _setupAuthEventListeners() {
    // 监听登录成功事件
    this.authManager.on('auth:login-success', (data) => {
      console.log('用户登录成功:', data.user.id);
      
      // 关闭登录窗口，打开主窗口
      if (this.loginWindow) {
        this.loginWindow.close();
        this.loginWindow = null;
      }
      
      this._createMainWindow();
    });

    // 监听登出事件
    this.authManager.on('auth:logout', () => {
      console.log('用户已登出');
      
      // 关闭主窗口，打开登录窗口
      if (this.mainWindow) {
        this.mainWindow.close();
        this.mainWindow = null;
      }
      
      this._createLoginWindow();
    });

    // 监听认证状态变更
    this.authManager.on('auth:state-changed', (authState) => {
      console.log('认证状态变更:', authState.isLoggedIn ? '已登录' : '未登录');
    });

    // 监听 Token 刷新事件
    this.authManager.on('auth:token-refreshed', (data) => {
      console.log('Token 已刷新，新过期时间:', new Date(data.expiresAt));
    });
  }

  /**
   * 检查认证状态并创建相应窗口
   * @private
   */
  async _checkAuthStateAndCreateWindow() {
    const authState = this.authManager.getAuthState();
    
    if (authState.isLoggedIn) {
      console.log('用户已登录，创建主窗口');
      this._createMainWindow();
    } else {
      console.log('用户未登录，创建登录窗口');
      this._createLoginWindow();
    }
  }

  /**
   * 创建登录窗口
   * @private
   */
  _createLoginWindow() {
    if (this.loginWindow) {
      this.loginWindow.focus();
      return;
    }

    this.loginWindow = this.windowManager.createWindow({
      width: 400,
      height: 500,
      resizable: false,
      center: true,
      title: 'Mira AI 医生助手 - 登录',
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js')
      }
    });

    // 加载登录页面
    if (process.env.NODE_ENV === 'development') {
      this.loginWindow.loadURL('http://localhost:3700/login.html');
      this.loginWindow.webContents.openDevTools();
    } else {
      this.loginWindow.loadFile(path.join(__dirname, '../dist/login.html'));
    }

    this.loginWindow.on('closed', () => {
      this.loginWindow = null;
    });

    console.log('登录窗口已创建');
  }

  /**
   * 创建主窗口
   * @private
   */
  _createMainWindow() {
    if (this.mainWindow) {
      this.mainWindow.focus();
      return;
    }

    this.mainWindow = this.windowManager.createWindow({
      width: 1200,
      height: 800,
      center: true,
      title: 'Mira AI 医生助手',
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js')
      }
    });

    // 加载主页面
    if (process.env.NODE_ENV === 'development') {
      this.mainWindow.loadURL('http://localhost:3700/consultation-list.html');
      this.mainWindow.webContents.openDevTools();
    } else {
      this.mainWindow.loadFile(path.join(__dirname, '../dist/consultation-list.html'));
    }

    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    console.log('主窗口已创建');
  }

  /**
   * 销毁应用
   */
  destroy() {
    if (this.authManager) {
      this.authManager.destroy();
      this.authManager = null;
    }

    if (this.loginWindow) {
      this.loginWindow.close();
      this.loginWindow = null;
    }

    if (this.mainWindow) {
      this.mainWindow.close();
      this.mainWindow = null;
    }

    console.log('Mira 主应用已销毁');
  }
}

/**
 * 应用启动函数
 */
async function startApp() {
  // 等待 Electron 准备就绪
  await app.whenReady();

  // 创建并初始化主应用
  const miraApp = new MiraMainApp();
  await miraApp.initialize();

  // 处理应用退出
  app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
      miraApp.destroy();
      app.quit();
    }
  });

  app.on('activate', async () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      await miraApp._checkAuthStateAndCreateWindow();
    }
  });

  app.on('before-quit', () => {
    miraApp.destroy();
  });
}

// 如果直接运行此文件，则启动应用
if (require.main === module) {
  startApp().catch(console.error);
}

export { MiraMainApp, startApp };
