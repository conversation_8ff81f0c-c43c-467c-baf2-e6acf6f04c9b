/**
 * Electron Preload 脚本
 * 为渲染进程提供安全的 IPC 通信接口
 */

const { contextBridge, ipcRenderer } = require('electron');

/**
 * 创建安全的 IPC 接口
 */
const electronAPI = {
  /**
   * 调用主进程方法
   * @param {string} channel - 通道名称
   * @param {*} data - 数据
   * @returns {Promise<*>}
   */
  invoke: (channel, data) => {
    // 验证允许的通道
    const allowedChannels = [
      'auth:login',
      'auth:logout',
      'auth:getUserInfo',
      'auth:getStatus',
      'auth:refreshToken',
      'window:close',
      'window:minimize',
      'window:maximize',
      'window:restore'
    ];

    if (!allowedChannels.includes(channel)) {
      throw new Error(`不允许的 IPC 通道: ${channel}`);
    }

    return ipcRenderer.invoke(channel, data);
  },

  /**
   * 发送消息到主进程
   * @param {string} channel - 通道名称
   * @param {*} data - 数据
   */
  send: (channel, data) => {
    // 验证允许的通道
    const allowedChannels = [
      'app:ready',
      'window:focus',
      'window:blur'
    ];

    if (!allowedChannels.includes(channel)) {
      throw new Error(`不允许的 IPC 通道: ${channel}`);
    }

    ipcRenderer.send(channel, data);
  },

  /**
   * 监听主进程事件
   * @param {string} channel - 通道名称
   * @param {Function} callback - 回调函数
   */
  on: (channel, callback) => {
    // 验证允许的通道
    const allowedChannels = [
      'auth:state-changed',
      'auth:login-success',
      'auth:logout',
      'auth:token-refreshed',
      'window:focus',
      'window:blur',
      'app:update-available',
      'app:update-downloaded'
    ];

    if (!allowedChannels.includes(channel)) {
      throw new Error(`不允许的 IPC 通道: ${channel}`);
    }

    // 包装回调函数以提供更好的错误处理
    const wrappedCallback = (event, data) => {
      try {
        callback(data);
      } catch (error) {
        console.error(`IPC 事件回调执行失败 [${channel}]:`, error);
      }
    };

    ipcRenderer.on(channel, wrappedCallback);

    // 返回清理函数
    return () => {
      ipcRenderer.removeListener(channel, wrappedCallback);
    };
  },

  /**
   * 移除事件监听
   * @param {string} channel - 通道名称
   * @param {Function} callback - 回调函数
   */
  off: (channel, callback) => {
    ipcRenderer.removeListener(channel, callback);
  },

  /**
   * 移除所有事件监听
   * @param {string} channel - 通道名称
   */
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  }
};

/**
 * 系统信息 API
 */
const systemAPI = {
  /**
   * 获取平台信息
   */
  getPlatform: () => process.platform,

  /**
   * 获取架构信息
   */
  getArch: () => process.arch,

  /**
   * 获取 Node.js 版本
   */
  getNodeVersion: () => process.versions.node,

  /**
   * 获取 Electron 版本
   */
  getElectronVersion: () => process.versions.electron,

  /**
   * 获取 Chrome 版本
   */
  getChromeVersion: () => process.versions.chrome,

  /**
   * 是否为开发环境
   */
  isDevelopment: () => process.env.NODE_ENV === 'development'
};

/**
 * 应用信息 API
 */
const appAPI = {
  /**
   * 获取应用版本
   */
  getVersion: () => electronAPI.invoke('app:getVersion'),

  /**
   * 获取应用名称
   */
  getName: () => electronAPI.invoke('app:getName'),

  /**
   * 退出应用
   */
  quit: () => electronAPI.invoke('app:quit'),

  /**
   * 重启应用
   */
  restart: () => electronAPI.invoke('app:restart')
};

/**
 * 窗口控制 API
 */
const windowAPI = {
  /**
   * 关闭窗口
   */
  close: () => electronAPI.invoke('window:close'),

  /**
   * 最小化窗口
   */
  minimize: () => electronAPI.invoke('window:minimize'),

  /**
   * 最大化窗口
   */
  maximize: () => electronAPI.invoke('window:maximize'),

  /**
   * 恢复窗口
   */
  restore: () => electronAPI.invoke('window:restore'),

  /**
   * 聚焦窗口
   */
  focus: () => electronAPI.send('window:focus'),

  /**
   * 失焦窗口
   */
  blur: () => electronAPI.send('window:blur')
};

/**
 * 认证 API
 */
const authAPI = {
  /**
   * 登录
   * @param {Object} loginData - 登录数据
   */
  login: (loginData) => electronAPI.invoke('auth:login', loginData),

  /**
   * 登出
   */
  logout: () => electronAPI.invoke('auth:logout'),

  /**
   * 获取用户信息
   */
  getUserInfo: () => electronAPI.invoke('auth:getUserInfo'),

  /**
   * 获取认证状态
   */
  getStatus: () => electronAPI.invoke('auth:getStatus'),

  /**
   * 刷新 Token
   */
  refreshToken: () => electronAPI.invoke('auth:refreshToken'),

  /**
   * 监听认证状态变更
   * @param {Function} callback - 回调函数
   */
  onStateChanged: (callback) => electronAPI.on('auth:state-changed', callback),

  /**
   * 监听登录成功事件
   * @param {Function} callback - 回调函数
   */
  onLoginSuccess: (callback) => electronAPI.on('auth:login-success', callback),

  /**
   * 监听登出事件
   * @param {Function} callback - 回调函数
   */
  onLogout: (callback) => electronAPI.on('auth:logout', callback),

  /**
   * 监听 Token 刷新事件
   * @param {Function} callback - 回调函数
   */
  onTokenRefreshed: (callback) => electronAPI.on('auth:token-refreshed', callback)
};

/**
 * 日志 API
 */
const logAPI = {
  /**
   * 记录信息日志
   * @param {string} message - 日志消息
   * @param {*} data - 附加数据
   */
  info: (message, data) => {
    console.log(`[INFO] ${message}`, data || '');
  },

  /**
   * 记录警告日志
   * @param {string} message - 日志消息
   * @param {*} data - 附加数据
   */
  warn: (message, data) => {
    console.warn(`[WARN] ${message}`, data || '');
  },

  /**
   * 记录错误日志
   * @param {string} message - 日志消息
   * @param {*} error - 错误对象
   */
  error: (message, error) => {
    console.error(`[ERROR] ${message}`, error || '');
  },

  /**
   * 记录调试日志
   * @param {string} message - 日志消息
   * @param {*} data - 附加数据
   */
  debug: (message, data) => {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[DEBUG] ${message}`, data || '');
    }
  }
};

// 将 API 暴露给渲染进程
contextBridge.exposeInMainWorld('electronAPI', electronAPI);
contextBridge.exposeInMainWorld('systemAPI', systemAPI);
contextBridge.exposeInMainWorld('appAPI', appAPI);
contextBridge.exposeInMainWorld('windowAPI', windowAPI);
contextBridge.exposeInMainWorld('authAPI', authAPI);
contextBridge.exposeInMainWorld('logAPI', logAPI);

// 兼容性支持：为了向后兼容，也暴露一个简化的接口
contextBridge.exposeInMainWorld('miraAPI', {
  // 基础 IPC 通信
  invoke: electronAPI.invoke,
  send: electronAPI.send,
  on: electronAPI.on,
  off: electronAPI.off,
  
  // 认证相关
  auth: authAPI,
  
  // 窗口控制
  window: windowAPI,
  
  // 系统信息
  system: systemAPI,
  
  // 应用信息
  app: appAPI,
  
  // 日志记录
  log: logAPI
});

console.log('Preload 脚本已加载，API 已暴露到渲染进程');
