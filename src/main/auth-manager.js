/**
 * 主进程认证管理器
 * 负责管理用户认证状态和与渲染进程的通信
 */

import { ipcMain } from 'electron';
import { EventEmitter } from 'events';
import axios from 'axios';

/**
 * 主进程认证管理器类
 */
export class MainAuthManager extends EventEmitter {
  constructor(sharedPreferences, windowManager) {
    super();
    this.sharedPreferences = sharedPreferences;
    this.windowManager = windowManager;
    this.apiClient = this._createApiClient();
    this.refreshTimer = null;
    
    // 存储键名
    this.STORAGE_KEYS = {
      TOKEN: 'mira_auth_token',
      REFRESH_TOKEN: 'mira_auth_refresh_token',
      USER_INFO: 'mira_auth_user_info',
      EXPIRES_AT: 'mira_auth_expires_at',
      LOGIN_TIME: 'mira_auth_login_time'
    };
    
    this._registerIpcHandlers();
    this._initializeAuthState();
  }

  /**
   * 创建 API 客户端
   * @private
   */
  _createApiClient() {
    const client = axios.create({
      baseURL: process.env.NODE_ENV === 'development' 
        ? 'http://localhost:3000/api' 
        : 'https://api.abcyun.cn/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // 请求拦截器 - 自动添加 token
    client.interceptors.request.use(config => {
      const token = this.getToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });

    // 响应拦截器 - 处理统一响应格式
    client.interceptors.response.use(
      response => {
        const { data } = response;
        if (data.code === 0) {
          return data.data;
        } else {
          throw new Error(data.message || '请求失败');
        }
      },
      error => {
        if (error.response?.status === 401) {
          // Token 过期，清除认证状态
          this.clearAuthData();
          this._broadcastAuthState();
        }
        throw error;
      }
    );

    return client;
  }

  /**
   * 初始化认证状态
   * @private
   */
  _initializeAuthState() {
    const token = this.getToken();
    const expiresAt = this.getExpiresAt();
    
    if (token && expiresAt) {
      const now = Date.now();
      
      if (now < expiresAt) {
        // Token 未过期，设置自动刷新
        this._scheduleTokenRefresh();
        this.emit('auth:state-changed', { isLoggedIn: true, user: this.getUserInfo() });
      } else {
        // Token 已过期，清除状态
        this.clearAuthData();
        this.emit('auth:state-changed', { isLoggedIn: false, user: null });
      }
    }
  }

  /**
   * 注册 IPC 处理器
   * @private
   */
  _registerIpcHandlers() {
    // 处理用户登录请求
    ipcMain.handle('auth:login', async (event, loginData) => {
      try {
        console.log('收到登录请求:', loginData);
        
        // 验证登录数据
        if (!loginData || !loginData.user) {
          throw new Error('登录数据无效');
        }

        // 保存登录状态
        this.saveLoginData(loginData);
        
        return {
          success: true,
          data: this.getAuthState()
        };
      } catch (error) {
        console.error('登录处理失败:', error);
        return {
          success: false,
          error: error.message
        };
      }
    });

    // 处理用户登出
    ipcMain.handle('auth:logout', async (event) => {
      try {
        console.log('收到登出请求');
        
        // 调用服务器登出接口
        try {
          await this.apiClient.post('/auth/logout');
        } catch (error) {
          console.warn('服务器登出失败，继续本地登出:', error.message);
        }
        
        // 清除本地认证状态
        this.logout();
        
        return {
          success: true,
          data: null
        };
      } catch (error) {
        console.error('登出处理失败:', error);
        return {
          success: false,
          error: error.message
        };
      }
    });

    // 获取用户信息
    ipcMain.handle('auth:getUserInfo', async (event) => {
      try {
        const authState = this.getAuthState();
        
        if (!authState.isLoggedIn) {
          return {
            success: false,
            error: '用户未登录'
          };
        }

        // 尝试从服务器获取最新用户信息
        try {
          const userInfo = await this.apiClient.get('/auth/user');
          
          // 更新本地用户信息
          const currentLoginData = {
            token: this.getToken(),
            refreshToken: this.getRefreshToken(),
            user: userInfo,
            expiresIn: Math.floor((this.getExpiresAt() - Date.now()) / 1000)
          };
          
          this.saveLoginData(currentLoginData);
          
          return {
            success: true,
            data: userInfo
          };
        } catch (error) {
          console.warn('获取服务器用户信息失败，返回本地缓存:', error.message);
          return {
            success: true,
            data: authState.user
          };
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
        return {
          success: false,
          error: error.message
        };
      }
    });

    // 获取当前登录状态
    ipcMain.handle('auth:getStatus', async (event) => {
      try {
        const authState = this.getAuthState();
        return {
          success: true,
          data: authState
        };
      } catch (error) {
        console.error('获取登录状态失败:', error);
        return {
          success: false,
          error: error.message
        };
      }
    });

    // 刷新用户 token
    ipcMain.handle('auth:refreshToken', async (event) => {
      try {
        const refreshToken = this.getRefreshToken();
        
        if (!refreshToken) {
          throw new Error('刷新令牌不存在');
        }

        console.log('开始刷新 Token');
        
        const result = await this.apiClient.post('/auth/refresh', {
          refreshToken
        });

        // 更新本地 token
        this.refreshToken(
          result.token,
          result.refreshToken,
          result.expiresIn
        );

        return {
          success: true,
          data: {
            token: result.token,
            expiresAt: this.getExpiresAt()
          }
        };
      } catch (error) {
        console.error('刷新 Token 失败:', error);
        
        // 刷新失败，清除认证状态
        this.clearAuthData();
        
        return {
          success: false,
          error: error.message
        };
      }
    });

    // 关闭窗口
    ipcMain.handle('window:close', async (event) => {
      try {
        const webContents = event.sender;
        const window = this.windowManager.getWindowByWebContents(webContents);
        
        if (window) {
          window.close();
        }
        
        return { success: true };
      } catch (error) {
        console.error('关闭窗口失败:', error);
        return {
          success: false,
          error: error.message
        };
      }
    });
  }

  /**
   * 保存登录数据
   * @param {Object} loginData - 登录数据
   */
  saveLoginData(loginData) {
    const { token, refreshToken, user, expiresIn = 7200 } = loginData;
    const expiresAt = Date.now() + (expiresIn * 1000);
    
    // 保存到持久化存储
    this.sharedPreferences.setString(this.STORAGE_KEYS.TOKEN, token);
    this.sharedPreferences.setString(this.STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
    this.sharedPreferences.setString(this.STORAGE_KEYS.USER_INFO, JSON.stringify(user));
    this.sharedPreferences.setLong(this.STORAGE_KEYS.EXPIRES_AT, expiresAt);
    this.sharedPreferences.setLong(this.STORAGE_KEYS.LOGIN_TIME, Date.now());
    
    // 设置自动刷新
    this._scheduleTokenRefresh();
    
    // 触发状态变更事件
    this.emit('auth:state-changed', { isLoggedIn: true, user });
    this.emit('auth:login-success', { user });
    
    // 广播到所有窗口
    this._broadcastAuthState();
    
    console.log('登录数据已保存:', { userId: user.id, loginType: user.loginType });
  }

  /**
   * 获取访问令牌
   * @returns {string|null}
   */
  getToken() {
    return this.sharedPreferences.getString(this.STORAGE_KEYS.TOKEN, null);
  }

  /**
   * 获取刷新令牌
   * @returns {string|null}
   */
  getRefreshToken() {
    return this.sharedPreferences.getString(this.STORAGE_KEYS.REFRESH_TOKEN, null);
  }

  /**
   * 获取用户信息
   * @returns {Object|null}
   */
  getUserInfo() {
    const userInfoStr = this.sharedPreferences.getString(this.STORAGE_KEYS.USER_INFO, null);
    if (userInfoStr) {
      try {
        return JSON.parse(userInfoStr);
      } catch (error) {
        console.error('解析用户信息失败:', error);
        return null;
      }
    }
    return null;
  }

  /**
   * 获取令牌过期时间
   * @returns {number|null}
   */
  getExpiresAt() {
    return this.sharedPreferences.getLong(this.STORAGE_KEYS.EXPIRES_AT, null);
  }

  /**
   * 获取登录时间
   * @returns {number|null}
   */
  getLoginTime() {
    return this.sharedPreferences.getLong(this.STORAGE_KEYS.LOGIN_TIME, null);
  }

  /**
   * 检查是否已登录
   * @returns {boolean}
   */
  isLoggedIn() {
    const token = this.getToken();
    const expiresAt = this.getExpiresAt();
    
    if (!token || !expiresAt) {
      return false;
    }
    
    return Date.now() < expiresAt;
  }

  /**
   * 获取当前认证状态
   * @returns {Object}
   */
  getAuthState() {
    const isLoggedIn = this.isLoggedIn();
    const user = isLoggedIn ? this.getUserInfo() : null;
    
    return {
      isLoggedIn,
      user,
      token: isLoggedIn ? this.getToken() : null,
      loginTime: this.getLoginTime(),
      expiresAt: this.getExpiresAt()
    };
  }

  /**
   * 刷新令牌
   * @param {string} newToken - 新的访问令牌
   * @param {string} newRefreshToken - 新的刷新令牌
   * @param {number} expiresIn - 过期时间（秒）
   */
  refreshToken(newToken, newRefreshToken, expiresIn = 7200) {
    const expiresAt = Date.now() + (expiresIn * 1000);
    
    this.sharedPreferences.setString(this.STORAGE_KEYS.TOKEN, newToken);
    this.sharedPreferences.setString(this.STORAGE_KEYS.REFRESH_TOKEN, newRefreshToken);
    this.sharedPreferences.setLong(this.STORAGE_KEYS.EXPIRES_AT, expiresAt);
    
    // 重新设置自动刷新
    this._scheduleTokenRefresh();
    
    this.emit('auth:token-refreshed', { token: newToken, expiresAt });
    this._broadcastEvent('auth:token-refreshed', { token: newToken, expiresAt });
    
    console.log('Token 已刷新');
  }

  /**
   * 清除认证数据
   */
  clearAuthData() {
    // 清除定时器
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
    
    // 清除存储数据
    Object.values(this.STORAGE_KEYS).forEach(key => {
      this.sharedPreferences.remove(key);
    });
    
    // 触发状态变更事件
    this.emit('auth:state-changed', { isLoggedIn: false, user: null });
    this.emit('auth:logout', {});
    
    // 广播到所有窗口
    this._broadcastAuthState();
    
    console.log('认证数据已清除');
  }

  /**
   * 登出
   */
  logout() {
    this.clearAuthData();
  }

  /**
   * 安排令牌自动刷新
   * @private
   */
  _scheduleTokenRefresh() {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }
    
    const expiresAt = this.getExpiresAt();
    if (!expiresAt) return;
    
    // 在过期前 5 分钟刷新
    const refreshTime = expiresAt - Date.now() - (5 * 60 * 1000);
    
    if (refreshTime > 0) {
      this.refreshTimer = setTimeout(async () => {
        try {
          const refreshToken = this.getRefreshToken();
          if (refreshToken) {
            const result = await this.apiClient.post('/auth/refresh', {
              refreshToken
            });

            this.refreshToken(
              result.token,
              result.refreshToken,
              result.expiresIn
            );
          }
        } catch (error) {
          console.error('自动刷新 Token 失败:', error);
          this.clearAuthData();
        }
      }, refreshTime);
      
      console.log(`Token 将在 ${Math.round(refreshTime / 1000 / 60)} 分钟后自动刷新`);
    }
  }

  /**
   * 广播认证状态到所有窗口
   * @private
   */
  _broadcastAuthState() {
    const authState = this.getAuthState();
    this._broadcastEvent('auth:state-changed', authState);
  }

  /**
   * 广播事件到所有窗口
   * @private
   */
  _broadcastEvent(eventName, data) {
    const windows = this.windowManager.getAllWindows();
    
    windows.forEach(window => {
      if (window && !window.isDestroyed()) {
        window.webContents.send(eventName, data);
      }
    });
  }

  /**
   * 销毁实例
   */
  destroy() {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
    
    // 移除所有 IPC 处理器
    const handlers = [
      'auth:login',
      'auth:logout', 
      'auth:getUserInfo',
      'auth:getStatus',
      'auth:refreshToken',
      'window:close'
    ];
    
    handlers.forEach(handler => {
      ipcMain.removeHandler(handler);
    });
    
    this.removeAllListeners();
  }
}

/**
 * 创建主进程认证管理器实例
 * @param {Object} sharedPreferences - 共享偏好设置实例
 * @param {Object} windowManager - 窗口管理器实例
 * @returns {MainAuthManager}
 */
export function createMainAuthManager(sharedPreferences, windowManager) {
  return new MainAuthManager(sharedPreferences, windowManager);
}
