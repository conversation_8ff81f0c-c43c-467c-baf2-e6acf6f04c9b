/**
 * Pinia 认证状态管理 Store
 * 负责前端认证状态的管理和与主进程的通信
 */

import { defineStore } from 'pinia';
import { ref, computed, watch } from 'vue';

/**
 * 认证状态管理 Store
 */
export const useAuthStore = defineStore('auth', () => {
  // 状态定义
  const isLoggedIn = ref(false);
  const user = ref(null);
  const token = ref(null);
  const loginTime = ref(null);
  const expiresAt = ref(null);
  const loading = ref(false);
  const error = ref(null);

  // 计算属性
  const isTokenExpired = computed(() => {
    if (!expiresAt.value) return true;
    return Date.now() >= expiresAt.value;
  });

  const timeUntilExpiry = computed(() => {
    if (!expiresAt.value) return 0;
    return Math.max(0, expiresAt.value - Date.now());
  });

  const userDisplayName = computed(() => {
    if (!user.value) return '';
    return user.value.nickname || user.value.phone || '用户';
  });

  const loginTypeText = computed(() => {
    if (!user.value) return '';
    return user.value.loginType === 'wechat' ? '微信登录' : '手机登录';
  });

  // Actions
  /**
   * 初始化认证状态
   * 从主进程同步最新状态
   */
  const initializeAuth = async () => {
    try {
      loading.value = true;
      error.value = null;

      if (window.electronAPI) {
        const result = await window.electronAPI.invoke('auth:getStatus');

        if (result.success) {
          updateAuthState(result.data);
        } else {
          console.warn('获取认证状态失败:', result.error);
          clearAuthState();
        }
      } else {
        // 开发环境下从 localStorage 获取
        const localToken = localStorage.getItem('mira_token');
        const localUser = localStorage.getItem('mira_user');

        if (localToken && localUser) {
          try {
            updateAuthState({
              isLoggedIn: true,
              token: localToken,
              user: JSON.parse(localUser),
              loginTime: Date.now(),
              expiresAt: Date.now() + 2 * 60 * 60 * 1000 // 2小时后过期
            });
          } catch (e) {
            console.error('解析本地用户信息失败:', e);
            clearAuthState();
          }
        }
      }
    } catch (err) {
      console.error('初始化认证状态失败:', err);
      error.value = err.message;
      clearAuthState();
    } finally {
      loading.value = false;
    }
  };
  /**
   * 处理用户登录
   * @param {Object} loginData - 登录数据
   */
  const login = async (loginData) => {
    try {
      loading.value = true;
      error.value = null;

      if (window.electronAPI) {
        const result = await window.electronAPI.invoke('auth:login', loginData);

        if (result.success) {
          updateAuthState(result.data);
          return { success: true };
        } else {
          throw new Error(result.error);
        }
      } else {
        // 开发环境下保存到 localStorage
        localStorage.setItem('mira_token', loginData.token);
        localStorage.setItem('mira_user', JSON.stringify(loginData.user));

        updateAuthState({
          isLoggedIn: true,
          token: loginData.token,
          user: loginData.user,
          loginTime: Date.now(),
          expiresAt: Date.now() + 2 * 60 * 60 * 1000
        });

        return { success: true };
      }
    } catch (err) {
      console.error('登录失败:', err);
      error.value = err.message;
      return { success: false, error: err.message };
    } finally {
      loading.value = false;
    }
  };

  /**
   * 处理用户登出
   */
  const logout = async () => {
    try {
      loading.value = true;
      error.value = null;

      if (window.electronAPI) {
        const result = await window.electronAPI.invoke('auth:logout');

        if (!result.success) {
          console.warn('服务器登出失败:', result.error);
        }
      } else {
        // 开发环境下清除 localStorage
        localStorage.removeItem('mira_token');
        localStorage.removeItem('mira_user');
      }

      clearAuthState();
      return { success: true };
    } catch (err) {
      console.error('登出失败:', err);
      error.value = err.message;
      return { success: false, error: err.message };
    } finally {
      loading.value = false;
    }
  };

  /**
   * 获取用户信息
   */
  const getUserInfo = async () => {
    try {
      loading.value = true;
      error.value = null;

      if (window.electronAPI) {
        const result = await window.electronAPI.invoke('auth:getUserInfo');

        if (result.success) {
          user.value = result.data;
          return { success: true, data: result.data };
        } else {
          throw new Error(result.error);
        }
      } else {
        // 开发环境下返回当前用户信息
        return { success: true, data: user.value };
      }
    } catch (err) {
      console.error('获取用户信息失败:', err);
      error.value = err.message;
      return { success: false, error: err.message };
    } finally {
      loading.value = false;
    }
  };

  /**
   * 刷新 Token
   */
  const refreshToken = async () => {
    try {
      loading.value = true;
      error.value = null;

      if (window.electronAPI) {
        const result = await window.electronAPI.invoke('auth:refreshToken');

        if (result.success) {
          token.value = result.data.token;
          expiresAt.value = result.data.expiresAt;
          return { success: true };
        } else {
          throw new Error(result.error);
        }
      } else {
        // 开发环境下模拟刷新
        expiresAt.value = Date.now() + 2 * 60 * 60 * 1000;
        return { success: true };
      }
    } catch (err) {
      console.error('刷新 Token 失败:', err);
      error.value = err.message;
      clearAuthState();
      return { success: false, error: err.message };
    } finally {
      loading.value = false;
    }
  };

  /**
   * 更新认证状态
   * @param {Object} authState - 认证状态数据
   */
  const updateAuthState = (authState) => {
    isLoggedIn.value = authState.isLoggedIn || false;
    user.value = authState.user || null;
    token.value = authState.token || null;
    loginTime.value = authState.loginTime || null;
    expiresAt.value = authState.expiresAt || null;
    error.value = null;

    console.log('认证状态已更新:', {
      isLoggedIn: isLoggedIn.value,
      userId: user.value?.id,
      loginType: user.value?.loginType
    });
  };

  /**
   * 清除认证状态
   */
  const clearAuthState = () => {
    isLoggedIn.value = false;
    user.value = null;
    token.value = null;
    loginTime.value = null;
    expiresAt.value = null;
    error.value = null;

    console.log('认证状态已清除');
  };

  /**
   * 设置错误信息
   * @param {string} errorMessage - 错误信息
   */
  const setError = (errorMessage) => {
    error.value = errorMessage;
  };

  /**
   * 清除错误信息
   */
  const clearError = () => {
    error.value = null;
  };

  // 监听 Token 过期
  watch(isTokenExpired, (expired) => {
    if (expired && isLoggedIn.value) {
      console.warn('Token 已过期，自动登出');
      logout();
    }
  });

  return {
    // 状态
    isLoggedIn,
    user,
    token,
    loginTime,
    expiresAt,
    loading,
    error,

    // 计算属性
    isTokenExpired,
    timeUntilExpiry,
    userDisplayName,
    loginTypeText,

    // 方法
    initializeAuth,
    login,
    logout,
    getUserInfo,
    refreshToken,
    updateAuthState,
    clearAuthState,
    setError,
    clearError
  };
});

/**
 * 认证状态管理工具函数
 */
export const authUtils = {
  /**
   * 格式化剩余时间
   * @param {number} milliseconds - 毫秒数
   * @returns {string}
   */
  formatTimeRemaining(milliseconds) {
    if (milliseconds <= 0) return '已过期';

    const hours = Math.floor(milliseconds / (1000 * 60 * 60));
    const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    } else {
      return `${minutes}分钟`;
    }
  },

  /**
   * 检查是否需要刷新 Token
   * @param {number} expiresAt - 过期时间戳
   * @returns {boolean}
   */
  shouldRefreshToken(expiresAt) {
    if (!expiresAt) return false;

    // 在过期前 10 分钟刷新
    const refreshThreshold = 10 * 60 * 1000;
    return (expiresAt - Date.now()) <= refreshThreshold;
  }
};
