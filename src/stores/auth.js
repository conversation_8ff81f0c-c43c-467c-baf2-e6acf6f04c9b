import { defineStore } from 'pinia';
import { authAPI } from '../api/auth';


export const useAuthStore = defineStore('login', {
    state: () => {
        return {
            // 拿到 code 之后，需要进入 auth-callback 页面，默认是使用后台接口返回的地址，如果指定了该 URL，将优先使用该地址
            rememberLogin: false,
            userInfo: null,
            loginWay: '',
        };
    },
    getters: {
        isLogin(state) {
            return state.userInfo && state.userInfo.id;
        },
    },
    actions: {
        async setUserInfo(userInfo) {
            this.userInfo = this.userInfo && userInfo ? Object.assign(this.userInfo, userInfo) : userInfo;

            if (userInfo) {
                // hack 如果moduleIds里面包含0，包含所有模块，直接将moduleIds赋值 为0
                let moduleIds = userInfo.moduleIds?.split(',') || [];
                moduleIds = moduleIds.includes('0') ? '0' : moduleIds.join(',');
                this.userInfo.moduleIds = moduleIds;
            }
        },
        async getUserInfo() {
            try {
                // const { data } = await GlobalLoginAPI.fetchEmployeeInfo();
                // await this.handleLoginSuccess({
                //     employee: {
                //         ...data,
                //     },
                // });
            } catch (error) {
               
                return error;
            }
        },
        // 验证码登录
        async globalLoginBySms(params) {
            try {
                const { data } = await authAPI.loginWithSmsCode(params.mobile, params.verify);
                await this.handleLoginSuccess(data);
            } catch (error) {
                return error;
            }
        },
        // 扫描二维码登录
        async globalLoginByScan(params) {
            try {
                // const { data } = await GlobalLoginAPI.loginByScan({
                //     ...params,
                //     ...getClientInfo(),
                // });
                const data = params;
                await this.handleLoginSuccess(data);
            } catch (error) {
                return error;
            }
        },
       
   
        async handleLoginSuccess(data) {
            const { employee } = data;
            // 设置用户信息
            await this.setUserInfo(employee);
        },
        
        // 全局登出
        async globalLogout() {
            try {
                // await GlobalLoginAPI.logout();
            } catch (e) {
                console.log('logout err', e);
            }
            await this.setUserInfo(null);
        },

    },
});
