import Vue from 'vue';
import AbcUI from '@abc/ui-pc';
import '@abc/ui-pc/lib/ui-pc.min.css';
import '@/style/reset.scss';
import '@/style/var.scss';
import {
    createPinia, PiniaVuePlugin,
} from 'pinia';
import { IPCPlugin, setupElectronAPI, initializeIPC } from '@/utils/ipc-client';

// 安装 Pinia
Vue.use(PiniaVuePlugin);
const pinia = createPinia();

// 全局注册 ABC UI 组件库
Vue.use(AbcUI, {
    theme: 'pharmacy',
});

// 安装 IPC 插件
Vue.use(IPCPlugin);

// 设置开发环境 Electron API（如果需要）
if (process.env.NODE_ENV === 'development' && !window.electronAPI) {
  setupElectronAPI();
}

// 全局配置
Vue.config.productionTip = false;

// 全局混入
Vue.mixin({
  methods: {
    // 通用的错误处理方法
    $handleError(error, message = '操作失败') {
      console.error(error);
      this.$message.error(message);
    },

    // 通用的成功提示方法
    $handleSuccess(message = '操作成功') {
      this.$message.success(message);
    },

    // 通用的加载状态管理
    $loading(promise, message = '加载中...') {
      const loading = this.$loading({
        lock: true,
        text: message,
        spinner: 'abc-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      return promise.finally(() => {
        loading.close();
      });
    }
  }
});

// 全局过滤器
Vue.filter('formatDate', function (value) {
  if (!value) return '';
  const date = new Date(value);
  return date.toLocaleString('zh-CN');
});

Vue.filter('formatTime', function (value) {
  if (!value) return '00:00';
  const minutes = Math.floor(value / 60);
  const seconds = Math.floor(value % 60);
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
});

// 全局常量
Vue.prototype.$constants = {
  // API 基础地址
  API_BASE_URL: process.env.NODE_ENV === 'development'
    ? 'http://localhost:3000/api'
    : 'https://api.abcyun.cn/api',

  // 登录方式
  LOGIN_TYPES: {
    WECHAT_SCAN: 'wechat_scan',
    SMS_CODE: 'sms_code'
  },

  // 录音状态
  RECORDING_STATUS: {
    IDLE: 'idle',
    RECORDING: 'recording',
    PAUSED: 'paused',
    STOPPED: 'stopped'
  },

  // 问诊状态
  CONSULTATION_STATUS: {
    DRAFT: 'draft',
    COMPLETED: 'completed',
    EXPORTED: 'exported'
  }
};

// 全局事件总线
Vue.prototype.$eventBus = new Vue();

// 导出 pinia 实例和初始化函数
export { pinia, initializeIPC };
