// controls.cpp : 定义应用程序的入口点。
//

#include "stdafx.h"
#include "Main.h"
#include "ui/SettingDlg.h"
//#include "ScreenshotDlg.h"



enum ThreadId
{
	kThreadUI,
	kThreadGlobalMisc
};


int APIENTRY wWinMain(_In_ HINSTANCE hInstance,
                     _In_opt_ HINSTANCE hPrevInstance,
                     _In_ LPWSTR    lpCmdLine,
                     _In_ int       nCmdShow)
{
	UNREFERENCED_PARAMETER(hPrevInstance);
	UNREFERENCED_PARAMETER(lpCmdLine);


	HANDLE mutex = CreateMutex(NULL, TRUE, _T("ABCUIDesignerInstanceMutex"));
	if (mutex == NULL)
	{
		return FALSE;
	}


	if (GetLastError() == ERROR_ALREADY_EXISTS)
	{
		HWND hProgramWnd = ::FindWindow(NULL, APP_TITLE);
		if (hProgramWnd)
		{
			//将运行的程序窗口还原成正常状态
			SetFocus(hProgramWnd);

			DWORD windowThreadProcessId = GetWindowThreadProcessId(hProgramWnd, LPDWORD(0));
			DWORD currentThreadId = GetCurrentThreadId();
			DWORD CONST_SW_SHOW = 5;
			AttachThreadInput(windowThreadProcessId, currentThreadId, true);
			BringWindowToTop(hProgramWnd);
			ShowWindow(hProgramWnd, CONST_SW_SHOW);
			AttachThreadInput(windowThreadProcessId, currentThreadId, false);
		}
		//关闭进程互斥体

		CloseHandle(mutex);
		mutex = NULL;
		return 0;
	}

	MainThread().RunOnCurrentThreadWithLoop(nbase::MessageLoop::kUIMessageLoop);

	CloseHandle(mutex);
	mutex = NULL;

	return 0;
}


void MiscThread::Init()
{
	nbase::ThreadManager::RegisterThread(thread_id_);
}

void MiscThread::Cleanup()
{
	nbase::ThreadManager::UnregisterThread();
}

void MainThread::Init()
{
	nbase::ThreadManager::RegisterThread(kThreadUI);

	// 启动杂事处理线程
	misc_thread_.reset(new MiscThread(kThreadGlobalMisc, "Global Misc Thread"));
	misc_thread_->Start();

	// 获取资源路径，初始化全局参数
	std::wstring app_dir = nbase::win32::GetCurrentModuleDirectory();
	//ui::GlobalManager::InitBeforeStart();
	ui::GlobalManager::Startup(app_dir + L"resources\\", ui::CreateControlCallback(), false);
	ui::GlobalManager::AddFont(L"inner_font_system_14", L"system", 14, false, false, 0, false, false);

	//ui::GlobalManager::EnableAutomation();

	//// 创建一个默认带有阴影的居中窗口
	SettingDlg* window = new SettingDlg();
	window->Create(NULL, SettingDlg::kClassName.c_str(), WS_OVERLAPPEDWINDOW & ~WS_MAXIMIZEBOX, 0);
	window->CenterWindow();
	window->ShowWindow();
}

void MainThread::Cleanup()
{
	ui::GlobalManager::Shutdown();

	misc_thread_->Stop();
	misc_thread_.reset(nullptr);

	SetThreadWasQuitProperly(true);
	nbase::ThreadManager::UnregisterThread();
}
