// controls.cpp : 定义应用程序的入口点。
//

#include "stdafx.h"
#include "PreviewWindow.h"
//#include "ScreenshotDlg.h"


#include <iterator>






const std::wstring PreviewWindow::kClassName = L"PreviewWindow";

PreviewWindow::PreviewWindow(const std::wstring& skinFile): m_strSkinFile(skinFile)
{

}

std::wstring PreviewWindow::GetSkinFolder()
{
	return L"";
}



std::wstring PreviewWindow::GetSkinFile()
{
	return m_strSkinFile;
}

std::wstring PreviewWindow::GetWindowClassName(void) const
{
	return kClassName;
}

void PreviewWindow::InitWindow()
{

}

ui::Control* PreviewWindow::CreateControl(const std::wstring& pstrClass)
{
	auto control = ui::GlobalManager::CreateControl(pstrClass);
	if (!control)
		control = new ui::Control();

	return control;
}
