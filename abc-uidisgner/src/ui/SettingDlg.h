#pragma once

#include "resource.h"


class PreviewWindow;

class SettingDlg: public ui::WindowImplBase {

public:
    static const std::wstring kClassName;

    PreviewWindow* m_previewWindow;

private:
    std::wstring m_strResRootDir;
    std::wstring m_strResXmlFileName;

public:
    SettingDlg();
    ~SettingDlg();

protected:
    /**
     * @brief 创建窗口时被调用，由子类实现用以获取窗口皮肤目录
     * @return 子类需实现并返回窗口皮肤目录
     */
    virtual std::wstring GetSkinFolder() override;

    /**
     * @brief 创建窗口时被调用，由子类实现用以获取窗口皮肤 XML 描述文件
     * @return 子类需实现并返回窗口皮肤 XML 描述文件
     */
    virtual std::wstring GetSkinFile() override;

    /**
     * @brief 创建窗口时被调用，由子类实现用以获取窗口唯一的类名称
     * @return 子类需实现并返回窗口唯一的类名称
     */
    virtual std::wstring GetWindowClassName(void) const override;

    /**
     * 收到 WM_CREATE 消息时该函数会被调用，通常做一些控件初始化的操作
     */
    virtual void InitWindow() override;

    /**
     * 接收窗口事件，这里用于处理快捷键
     */
    virtual LRESULT HandleMessage(UINT uMsg, WPARAM wParam, LPARAM lParam) override;

    /**
	 * @brief 当收到窗口关闭消息时被调用
	 * @param[in] uMsg 消息内容
	 * @param[in] wParam 消息附加参数
	 * @param[in] lParam 消息附加参数
	 * @param[out] bHandled 返回 false 则继续派发该消息，否则不再派发该消息
	 * @return 返回消息处理结果
	 */
    virtual LRESULT OnClose(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL& bHandled);

private:
    /**
     * 配置文件存储路径
     */
    std::wstring ConfigFile() const;

    /**
     * 加载配置（上次编辑的资源文件路径等配置）
     */
    void LoadConfigFile();

    /**
     * 保存当前配置
     */
    void SaveConfigFile();

    /**
     * 根据当前配置加载设计窗口预览
     */
    void ShowDesignWindow();

    /**
     * 刷新预览窗口
     */
    void RefreshPreviewWindow();

    /**
     * 注册快捷键
     */
    void RegisterShortcutKey();
};