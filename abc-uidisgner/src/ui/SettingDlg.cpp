// controls.cpp : 定义应用程序的入口点。
//

#include "stdafx.h"
#include "SettingDlg.h"
#include "PreviewWindow.h"

#include <iterator>

const std::wstring SettingDlg::kClassName = L"SettingDlg";

const wchar_t* kResRootConfigKey = L"resRootDir";
const wchar_t* kResXmlName= L"resXmlName";

#define ID_HOTKEY_REFRESH 0x1201

SettingDlg::SettingDlg(): m_previewWindow(NULL)
{

}

SettingDlg::~SettingDlg()
{
	if (m_previewWindow) {
		m_previewWindow->Close();
		m_previewWindow = NULL;
	}
}

std::wstring SettingDlg::GetSkinFolder()
{
	return L"";
}


std::wstring LoadXMLRes(int resId) {
	HRSRC hRsrc = FindResource(NULL, MAKEINTRESOURCE(resId), L"XML");
	if (NULL == hRsrc) {
		return L"";
	}

	DWORD dwSize = SizeofResource(NULL, hRsrc);
	if (0 == dwSize) {
		return L"";
	}

	HGLOBAL hGlobal = LoadResource(NULL, hRsrc);
	if (NULL == hGlobal)
		return L"";

	LPVOID pBuffer = LockResource(hGlobal);
	if (NULL == pBuffer)
		return L"";
	char* pByte = new char[dwSize + 1] {0};
	memcpy(pByte, pBuffer, dwSize);
	GlobalUnlock(hGlobal);

	auto str = abc::StringUtils::UTF8TOWStr((char*)pByte);
	delete [] pByte;
	pByte = NULL;

	return str;
}

std::wstring SettingDlg::GetSkinFile()
{
	std::wstring str = LoadXMLRes(IDR_XML_SETTING_DLG);
	return  str;
}

std::wstring SettingDlg::GetWindowClassName(void) const
{
	return kClassName;
}

void SettingDlg::InitWindow()
{
	RegisterShortcutKey();
	LoadConfigFile();
	ui::Button* reloadBtn = static_cast<ui::Button*>(FindControl(L"reloadConfigBtn"));
	auto resRootDirEdit = static_cast<ui::RichEdit*>(FindControl(L"resRootDir"));
	auto resXmlNameEdit = static_cast<ui::RichEdit*>(FindControl(L"resXmlName"));
	resRootDirEdit->SetText(m_strResRootDir);
	resXmlNameEdit->SetText(m_strResXmlFileName);
	if (reloadBtn) {
		reloadBtn->AttachClick(ToWeakCallback([this, resRootDirEdit, resXmlNameEdit](ui::EventArgs* args) {
			auto resRootDir = resRootDirEdit->GetText();
			auto resXmlName = resXmlNameEdit->GetText();

			if (resRootDir != m_strResRootDir || resXmlName != m_strResXmlFileName) {
				m_strResRootDir  = resRootDir;
				m_strResXmlFileName = resXmlName;
				SaveConfigFile();
				ShowDesignWindow();
			}
			
			return true;
			}));
	}

	ui::Button* refreshBtn = static_cast<ui::Button*>(FindControl(L"refresh"));
	if (refreshBtn) {
		refreshBtn->AttachClick(ToWeakCallback([this](ui::EventArgs* args) {
			RefreshPreviewWindow();
			return true;
			}));
	}

	ShowDesignWindow();
}

LRESULT SettingDlg::HandleMessage(UINT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_HOTKEY) {

		if (ID_HOTKEY_REFRESH == wParam) {
			RefreshPreviewWindow();
			return S_OK;
		}
	}

	return ui::WindowImplBase::HandleMessage(uMsg, wParam, lParam);
}

LRESULT SettingDlg::OnClose(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL& bHandled)
{
	PostQuitMessage(0L);
	return __super::OnClose(uMsg, wParam, lParam, bHandled);
}

std::wstring SettingDlg::ConfigFile() const
{
	std::wstring app_dir = nbase::win32::GetCurrentModuleDirectory();
	return app_dir + L"\\config.ini";
}

void SettingDlg::LoadConfigFile()
{
	std::wstring configFile = ConfigFile();
	if (!nbase::FilePathIsExist(configFile, false))
		return;

	std::string fileContent;
	std::wstring wFileContent;
	nbase::ReadFileToString(configFile, fileContent);

	wFileContent = abc::StringUtils::UTF8TOWStr(fileContent);


	auto lines = nbase::StringTokenize(wFileContent.c_str(), L"\r\n");

	std::map<std::wstring, std::wstring> configs;
	for (auto& it : lines) {
		auto keyValuePair = nbase::StringTokenize(it.c_str(), L"=");
		std::wstring key = keyValuePair.front();
		keyValuePair.pop_front();
		std::wstring value = keyValuePair.size() ? keyValuePair.front() : L"";

		configs[key] = value;
	}

	m_strResRootDir = configs[kResRootConfigKey];
	m_strResXmlFileName = configs[kResXmlName];
}

void SettingDlg::SaveConfigFile()
{
	std::map<std::wstring, std::wstring> configs = {
		{kResRootConfigKey , m_strResRootDir},
		{kResXmlName,  m_strResXmlFileName}
	};

	std::vector<std::wstring> configLines;
	std::transform(configs.begin(), configs.end(), std::back_inserter(configLines), [](std::pair<std::wstring, std::wstring> it) {
		return it.first + L"=" + it.second;
		});

	std::wstring wFileContent;
	abc::StringUtils::Join(configLines, L"\r\n", wFileContent);


	std::wstring configFile = ConfigFile();
	std::string fileContent = abc::StringUtils::WCharToUTF8(wFileContent.c_str());
	nbase::WriteFile(configFile, fileContent);
}

void SettingDlg::ShowDesignWindow()
{
	if (!nbase::FilePathIsExist(m_strResRootDir, true)) {
		MessageBox(GetHWND(), L"path not exist", L"", 0);
		return;
	}

	ui::GlobalManager::Startup(m_strResRootDir + L"\\", ui::CreateControlCallback(), false);

	auto resPath = ui::GlobalManager::GetResourcePath();
	auto fullXmlName = m_strResRootDir + L"\\" + m_strResXmlFileName;
	if (nbase::FilePathIsExist(fullXmlName, false)) {
		MessageBox(GetHWND(), L"file not exist", L"", 0);
		return;
	}

	RefreshPreviewWindow();
}

void SettingDlg::RefreshPreviewWindow()
{
	if (m_previewWindow) {
		m_previewWindow->Close();
		m_previewWindow = NULL;
	}

	PreviewWindow* previewWindow = new PreviewWindow(m_strResXmlFileName);
	previewWindow->Create(NULL, PreviewWindow::kClassName.c_str(), WS_OVERLAPPEDWINDOW & ~WS_MAXIMIZEBOX, 0);
	previewWindow->CenterWindow();
	previewWindow->ShowWindow();

	DWORD pid = ::GetCurrentProcessId();
	DWORD currentThreadId = GetCurrentThreadId();
	DWORD CONST_SW_SHOW = 5;
	AttachThreadInput(pid, currentThreadId, true);
	BringWindowToTop(previewWindow->GetHWND());
	//ShowWindow(hProgramWnd, CONST_SW_SHOW);
	//AttachThreadInput(currentThreadId, currentThreadId, false);
	m_previewWindow = previewWindow;
	auto pos = m_previewWindow->GetPos();
	m_previewWindow->SetPos(pos, false, SWP_SHOWWINDOW, HWND_TOPMOST);

	previewWindow->AttachWindowClose(ToWeakCallback([this, previewWindow](ui::EventArgs* args) {
		if (m_previewWindow == previewWindow) {
			m_previewWindow = NULL;
		}
		return true;
		}));
}

void SettingDlg::RegisterShortcutKey()
{
	RegisterHotKey(GetHWND(), ID_HOTKEY_REFRESH, MOD_CONTROL | MOD_WIN, 'R');
}

