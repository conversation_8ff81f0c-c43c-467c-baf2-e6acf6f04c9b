#pragma once

#include "resource.h"

class PreviewWindow: public ui::WindowImplBase {

public:
    static const std::wstring kClassName;

private:
    std::wstring m_strSkinFile;
   
public:
    PreviewWindow(const std::wstring& skinFile);

protected:
    /**
     * @brief 创建窗口时被调用，由子类实现用以获取窗口皮肤目录
     * @return 子类需实现并返回窗口皮肤目录
     */
    virtual std::wstring GetSkinFolder() override;

    /**
     * @brief 创建窗口时被调用，由子类实现用以获取窗口皮肤 XML 描述文件
     * @return 子类需实现并返回窗口皮肤 XML 描述文件
     */
    virtual std::wstring GetSkinFile() override;

    /**
     * @brief 创建窗口时被调用，由子类实现用以获取窗口唯一的类名称
     * @return 子类需实现并返回窗口唯一的类名称
     */
    virtual std::wstring GetWindowClassName(void) const override;

    /**
     * 收到 WM_CREATE 消息时该函数会被调用，通常做一些控件初始化的操作
     */
    virtual void InitWindow() override;

    /**
     * @brief 当要创建的控件不是标准的控件名称时会调用该函数
     * @param[in] pstrClass 控件名称
     * @return 返回一个自定义控件指针，一般情况下根据 pstrClass 参数创建自定义的控件
     */
    virtual ui::Control* CreateControl(const std::wstring& pstrClass) override;
};